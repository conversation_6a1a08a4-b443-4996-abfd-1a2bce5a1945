package com.shida.sso.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.shida.enums.RestCode;
import com.shida.exceptions.ServiceException;
import com.shida.sso.config.smsConfig.LingKaiConfig;
import com.shida.sso.config.smsConfig.ShuZhengJuConfig;
import com.shida.sso.config.smsConfig.ShuZhengJuSmsTemplate;
import com.shida.sso.config.smsConfig.SmsServiceProperties;
import com.shida.sso.service.ISmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class SmsServiceImpl implements ISmsService {
    @Resource
    private SmsServiceProperties smsServiceProperties;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    private List<String> whiteList = CollUtil.newArrayList("18132075529");



    @Override
    public String sendSmsCode(String mobile) {
        // 检测一小时内发送短信次数
        checkSendSmsCountOneHour(mobile);
        // 检测用户一天发送短信的次数，每天最多20条
        checkSendSmsCountOneDay(mobile);
        String smsCode = RandomUtil.randomNumbers(6);
        // 凌凯
        if (smsServiceProperties.getServiceProvider().equals("ling-kai")) {
            Integer result = sendLingKaiSms(mobile, StrUtil.format("验证码：{}，您正在尝试登录，请妥善保管账户信息。", smsCode));
            if (result <= 0) {
                throw new ServiceException(RestCode.ARGUMENT_ERROR, "短信发送失败，请稍后重试");
            }
        }
        // 数政局
        else {
            Map<String, Object> params = MapUtil.ofEntries(MapUtil.entry("code", smsCode));
            Integer result = sendShuZhengJuSms(mobile, params);
            if (result <= 0) {
                throw new ServiceException(RestCode.ARGUMENT_ERROR, "短信发送失败，请稍后重试");
            }
        }
        redisTemplate.opsForValue().set(USER_LOGIN_SMS_CODE_CACHE_KEY + mobile, smsCode, 10, TimeUnit.MINUTES);
        return smsCode;
    }

    /**
     * 检测用户一小时内发送短信的次数，每小时最多5条
     *
     * @param mobile 手机号
     */
    private void checkSendSmsCountOneHour(String mobile) {
        if (whiteList.contains(mobile)) {
            return;
        }
        String key = USER_LOGIN_SMS_CODE_COUNT_CACHE_KEY + mobile;
        if (redisTemplate.hasKey(key)) {
            Integer count = (Integer) redisTemplate.opsForValue().get(key);
            if (count >= 5) {
                throw new SecurityException("对不起，每小时最多可以发送5条短信，您已超过上限");
            }
            redisTemplate.opsForValue().increment(key);
        } else {
            redisTemplate.opsForValue().set(key, 1, 1, TimeUnit.HOURS);
        }
    }

    private void checkSendSmsCountOneDay(String mobile) {
        // 每日获取短信验证码上限20次 开始
        // 设置过期时间为明天零点
        LocalDateTime tomorrow = LocalDate.now().plusDays(1).atStartOfDay();
        LocalDateTime now = LocalDateTime.now();
        // 当前时间与明天时间间隔秒
        long seconds = tomorrow.toEpochSecond(ZoneOffset.UTC) - now.toEpochSecond(ZoneOffset.UTC);
        String key = USER_LOGIN_SMS_CODE_COUNT_CACHE_KEY + mobile;
        if (redisTemplate.hasKey(key)) {
            Integer count = (Integer) redisTemplate.opsForValue().get(key);
            if (count >= 20) {
                throw new SecurityException("对不起，每天最多可以发送20条短信，您已超过上限");
            }
            redisTemplate.opsForValue().increment(key);
            redisTemplate.expire(key, seconds, TimeUnit.SECONDS);
        } else {
            redisTemplate.opsForValue().set(key, 1, seconds, TimeUnit.SECONDS);
        }
    }

    private Integer sendLingKaiSms(String mobile, String content) {
        try {
            String smsContent = URLEncoder.encode(content.replaceAll("<br/>", " ") + "【" + smsServiceProperties.getLingKai().getSign() + "】", "GBK");
            LingKaiConfig config = smsServiceProperties.getLingKai();
            String url = config.getApiPath() + "?CorpId=" + config.getAppId() + "&Pwd=" + config.getPassword() + "&Mobile=" + mobile + "&Content=" + smsContent + "&Cell=SendTime=";
            HttpRequest request = HttpUtil.createPost(url);
            HttpResponse response = request.execute(true);
            String code = response.body();
            if (NumberUtil.parseInt(code) <= 0) {
                log.error("send ling-kai error", mobile + " 短信发送失败，错误码：" + code);
            }
            return NumberUtil.parseInt(code);
        } catch (UnsupportedEncodingException e) {
            log.error("sendLingKaiSms:" + mobile, e);
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "短信发送失败，请稍后重试");
        }
    }

    /**
     * 数政局短信接口
     *
     * @param mobile
     * @param params
     * @return
     */
    private Integer sendShuZhengJuSms(String mobile, Map<String, Object> params) {
        try {
            ShuZhengJuConfig config = smsServiceProperties.getShuZhengJu();
            ShuZhengJuSmsTemplate template = config.getTemplates().stream().filter(t -> t.getName().equals("登录短信验证码"))
                    .findFirst().orElse(null);
            if (template == null) {
                return -1;
            }
            JSONObject jsonObject = new JSONObject();
            template.getParams().forEach(p -> jsonObject.set(p, params.get(p)));
            String paramsJson = jsonObject.toJSONString(0);
            HttpRequest request = HttpUtil.createPost(config.getApiPath());
            JSONObject formData = new JSONObject();
            formData.set("sendPlatform", 100001)
                    .set("sendTime", "")
                    .set("phoneNum", mobile)
                    .set("templateId", template.getCode())
                    .set("templateParam", paramsJson);
            JSONObject body = new JSONObject();
            body.set("sendOnlyMessageReq", formData);

            String timestamp = LocalDateTimeUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss SSS");
            // 随机数
            String transId = RandomUtil.randomString(6);
            // 设置请求头
            request.header("Content-Type", "application/json")
                    .header("Accept-Encoding", "UTF-8")
                    .header("appCode", config.getAppCode())
                    .header("timestamp", LocalDateTimeUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss SSS"))
                    .header("transId", transId)
                    .header("token", tokenValue(config.getAppCode(), timestamp, transId, config.getAppSecret()));
            // 设置请求体
            request.body(body.toJSONString(0));
            HttpResponse response = request.execute(true);
            String jsonStr = response.body();
            JSONObject jsonResponse = JSONUtil.parseObj(jsonStr);
            Integer code = jsonResponse.getInt("code");
            if (code == 0) {
                return 1;
            } else {
                log.error("sendShuZhengJuSms error", jsonStr);
                return -1;
            }
        } catch (Exception e) {
            log.error("sendShuJuKuSms:" + mobile, e);
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "短信发送失败，请稍后重试");
        }
    }


    public static String tokenValue(String app_id, String timestamp, String trans_id, String app_secret)
            throws NoSuchAlgorithmException {
        String sign = "appCode" + app_id +
                "appSecret" + app_secret +
                "timestamp" + timestamp +
                "transId" + trans_id;
        byte[] hash;
        hash = MessageDigest.getInstance("MD5").digest(sign.getBytes(StandardCharsets.UTF_8));
        StringBuilder hex = new StringBuilder(hash.length * 2);
        for (byte b : hash) {
            if ((b & 0xFF) < 0x10) {
                hex.append("0");
            }
            hex.append(Integer.toHexString(b & 0xFF));
        }
        return hex.toString();
    }
}
