package com.shida.sso.service;

import cn.dev33.satoken.stp.StpUtil;
import com.shida.constant.RedisKeyConstant;
import com.shida.pojo.vo.KeyValue;
import com.shida.sso.pojo.from.ChangePasswordForm;
import com.shida.sso.pojo.from.LoginForm;
import com.shida.sso.pojo.from.SmsLoginForm;
import com.shida.pojo.vo.AccountInfo;
import com.shida.sso.pojo.from.VerifyCodeLoginForm;
import com.shida.sso.pojo.vo.VerifyCode;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;

public interface IAccountService {

    String VERIFY_CODE_KEY = "shida-app:verify:code:";

    String USER_LOGIN_TIMES_CACHE_KEY = "shida-app:password:error:times:";

    VerifyCode generateVerifyCodeImageData(String sessionId, HttpServletResponse response) throws IOException;

    AccountInfo login(VerifyCodeLoginForm form);

    AccountInfo adminLogin(SmsLoginForm form);

    static void setUserSession(AccountInfo currentUser) {
        StpUtil.getSession().set(RedisKeyConstant.CURRENT_USER_SESSION, currentUser);
    }

    Boolean logout();

    String changeSelfPassword(ChangePasswordForm form);

    String changeFieldValue(KeyValue<String, ?> form);

    String getSmsVerifyCode(LoginForm form);

}
