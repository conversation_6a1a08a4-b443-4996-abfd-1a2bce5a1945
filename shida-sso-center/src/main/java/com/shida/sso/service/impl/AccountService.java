package com.shida.sso.service.impl;

import cn.dev33.satoken.secure.SaSecureUtil;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.ShearCaptcha;
import cn.hutool.captcha.generator.MathGenerator;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.shida.AccountCommonService;
import com.shida.config.AppConfigure;
import com.shida.constant.RedisKeyConstant;
import com.shida.constant.RoleConstant;
import com.shida.enums.RestCode;
import com.shida.exceptions.ServiceException;
import com.shida.pojo.vo.AccountInfo;
import com.shida.pojo.vo.KeyValue;
import com.shida.sso.pojo.from.ChangePasswordForm;
import com.shida.sso.pojo.from.LoginForm;
import com.shida.sso.pojo.from.SmsLoginForm;
import com.shida.sso.pojo.from.VerifyCodeLoginForm;
import com.shida.sso.pojo.vo.VerifyCode;
import com.shida.sso.service.IAccountService;
import com.shida.sso.service.ISmsService;
import com.shida.userCenter.api.IFeignUserService;
import com.shida.userCenter.dto.ChangeUserPasswordDto;
import com.shida.userCenter.dto.UserDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class AccountService implements IAccountService {

    private final MathGenerator mathGenerator = new MathGenerator(1);

    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private IFeignUserService feignUserService;
    @Resource
    private ISmsService smsService;


    @Override
    public VerifyCode generateVerifyCodeImageData(String sessionId, HttpServletResponse response) throws IOException {
        ShearCaptcha captcha = CaptchaUtil.createShearCaptcha(120, 34, 4, 2);
        // 自定义验证码内容为四则运算方式
        captcha.setGenerator(mathGenerator);
        // 重新生成code
        captcha.createCode();
        String code = UUID.randomUUID().toString();
        redisTemplate.opsForValue().set(VERIFY_CODE_KEY + code, captcha.getCode(), 5, TimeUnit.MINUTES);
        VerifyCode verifyCode = new VerifyCode();
        verifyCode.setCode(code);
        verifyCode.setImgData("data:image/image/jpeg;base64," + captcha.getImageBase64());
        return verifyCode;
    }

    @Override
    public AccountInfo login(VerifyCodeLoginForm form) {
        if (AppConfigure.isProdMode()) {
            String cacheKey = VERIFY_CODE_KEY + form.getCode();
            if (!redisTemplate.hasKey(cacheKey)) {
                throw new SecurityException("验证码已过期，请重新输入");
            } else {
                String localCode = (String) redisTemplate.opsForValue().get(cacheKey);
                redisTemplate.delete(cacheKey);
                if (!mathGenerator.verify(localCode, form.getVerifyCode())) {
                    throw new SecurityException("验证码错误，请重新输入");
                }
            }
        }

        UserDto userDto = feignUserService.getUserDto(form.getUsername());
        if (userDto == null) {
            throw new ServiceException(RestCode.AUTHORIZED_ERROR, RestCode.AUTHORIZED_ERROR.message());
        }

        Object errorTimes = redisTemplate.opsForValue().get(USER_LOGIN_TIMES_CACHE_KEY + userDto.getId());
        if (errorTimes == null) {
            errorTimes = 0;
        }
        if ((Integer) errorTimes >= 5) {
            long seconds = redisTemplate.getExpire(USER_LOGIN_TIMES_CACHE_KEY + userDto.getId(), TimeUnit.SECONDS);
            if (seconds > 60) {
                long minutes = redisTemplate.getExpire(USER_LOGIN_TIMES_CACHE_KEY + userDto.getId(), TimeUnit.MINUTES);
                throw new ServiceException(RestCode.AUTHORIZED_ERROR, "您的账户或密码错误次数超过5次，请" + minutes + "分钟后重试");
            } else {
                throw new ServiceException(RestCode.AUTHORIZED_ERROR, "您的账户或密码错误次数超过5次，请" + seconds + "秒后重试");
            }
        }

        String password = SaSecureUtil.md5BySalt(form.getPassword(), userDto.getSalt());

        if (!userDto.getPassword().equals(password)) {
            this.updateErrorPasswordTimes(userDto.getId());
            throw new ServiceException(RestCode.AUTHORIZED_ERROR, "您的账户或密码错误" + ((Integer) errorTimes + 1) + "次，密码错误5次您的账户将被锁定30分钟");
        } else {
            redisTemplate.delete(USER_LOGIN_TIMES_CACHE_KEY + userDto.getId());
        }

        if (Objects.nonNull(userDto.getExpireTime()) && userDto.getExpireTime().isBefore(LocalDateTime.now())) {
            throw new ServiceException(RestCode.AUTHORIZED_ERROR, "您的账户已过期，请联系管理员");
        }

        if (userDto.getStatus().equals(0)) {
            throw new ServiceException(RestCode.AUTHORIZED_ERROR, "您的账户已被禁用，请联系管理员");
        }
        if (CollUtil.isEmpty(userDto.getRoles())) {
            throw new ServiceException(RestCode.AUTHORIZED_ERROR, "您的账户未配置角色，请联系管理员");
        }

        List<String> roleCodes = CollUtil.newArrayList(RoleConstant.TEACHER_CODE, RoleConstant.PARENT_CODE, RoleConstant.STUDENT_CODE);


        if (userDto.getRoles().stream().noneMatch(role -> roleCodes.contains(role.getRoleCode())
            || (StrUtil.isNotBlank(role.getSysRoleCode()) && roleCodes.contains(role.getSysRoleCode()))
        )) {
            throw new ServiceException(RestCode.AUTHORIZED_ERROR, "对不起，您无权登录，请勿非法操作");
        }

        AccountInfo result = BeanUtil.copyProperties(userDto, AccountInfo.class);
        StpUtil.login(result.getId());
        String token = StpUtil.getTokenValue();
        result.setToken(token);
        IAccountService.setUserSession(result);
        return result;
    }

    @Override
    public AccountInfo adminLogin(SmsLoginForm form) {
        UserDto userDto = feignUserService.getUserDto(form.getUsername());
        if (userDto == null) {
            throw new ServiceException(RestCode.AUTHORIZED_ERROR, RestCode.AUTHORIZED_ERROR.message());
        }
        if (AppConfigure.isProdMode()) {
            String cacheKey = ISmsService.USER_LOGIN_SMS_CODE_CACHE_KEY + userDto.getMobile();
            if (!redisTemplate.hasKey(cacheKey)) {
                throw new SecurityException("验证码已过期，请重新输入");
            } else {
                String localCode = (String) redisTemplate.opsForValue().get(cacheKey);
                if (form.getVerifyCode().equals(localCode)) {
                    throw new SecurityException("验证码错误，请重新输入");
                } else {
                    redisTemplate.delete(cacheKey);
                }
            }
        } else if (AppConfigure.isTestMode()) {
            if (!form.getVerifyCode().equals("9999")) {
                String cacheKey = ISmsService.USER_LOGIN_SMS_CODE_CACHE_KEY + userDto.getMobile();
                if (!redisTemplate.hasKey(cacheKey)) {
                    throw new SecurityException("验证码已过期，请重新输入");
                } else {
                    String localCode = (String) redisTemplate.opsForValue().get(cacheKey);
                    if (form.getVerifyCode().equals(localCode)) {
                        throw new SecurityException("验证码错误，请重新输入");
                    } else {
                        redisTemplate.delete(cacheKey);
                    }
                }
            }
        }


        Object errorTimes = redisTemplate.opsForValue().get(USER_LOGIN_TIMES_CACHE_KEY + userDto.getId());
        if (errorTimes == null) {
            errorTimes = 0;
        }
        if ((Integer) errorTimes >= 5) {
            long seconds = redisTemplate.getExpire(USER_LOGIN_TIMES_CACHE_KEY + userDto.getId(), TimeUnit.SECONDS);
            if (seconds > 60) {
                long minutes = redisTemplate.getExpire(USER_LOGIN_TIMES_CACHE_KEY + userDto.getId(), TimeUnit.MINUTES);
                throw new ServiceException(RestCode.AUTHORIZED_ERROR, "您的账户或密码错误次数超过5次，请" + minutes + "分钟后重试");
            } else {
                throw new ServiceException(RestCode.AUTHORIZED_ERROR, "您的账户或密码错误次数超过5次，请" + seconds + "秒后重试");
            }
        }

        String password = SaSecureUtil.md5BySalt(form.getPassword(), userDto.getSalt());

        if (!userDto.getPassword().equals(password)) {
            this.updateErrorPasswordTimes(userDto.getId());
            throw new ServiceException(RestCode.AUTHORIZED_ERROR, "您的账户或密码错误" + ((Integer) errorTimes + 1) + "次，密码错误5次您的账户将被锁定30分钟");
        } else {
            redisTemplate.delete(USER_LOGIN_TIMES_CACHE_KEY + userDto.getId());
        }

        if (Objects.nonNull(userDto.getExpireTime()) && userDto.getExpireTime().isBefore(LocalDateTime.now())) {
            throw new ServiceException(RestCode.AUTHORIZED_ERROR, "您的账户已过期，请联系管理员");
        }

        if (userDto.getStatus().equals(0)) {
            throw new ServiceException(RestCode.AUTHORIZED_ERROR, "您的账户已被禁用，请联系管理员");
        }
        if (CollUtil.isEmpty(userDto.getRoles())) {
            throw new ServiceException(RestCode.AUTHORIZED_ERROR, "您的账户未配置角色，请联系管理员");
        }

        if (userDto.getRoles().stream().noneMatch(role -> role.getAdminFlag().equals(1))) {
            throw new ServiceException(RestCode.AUTHORIZED_ERROR, "您的账户未配置管理员角色，请联系管理员");
        }

        AccountInfo result = BeanUtil.copyProperties(userDto, AccountInfo.class);
        StpUtil.login(result.getId());
        String token = StpUtil.getTokenValue();
        result.setToken(token);
        IAccountService.setUserSession(result);
        return result;
    }

    @Override
    public Boolean logout() {
        if (StpUtil.isLogin()) {
            StpUtil.getSession().removeTokenSign(RedisKeyConstant.CURRENT_USER_SESSION);
            StpUtil.logout();
        }
        return true;
    }

    @Override
    public String changeSelfPassword(ChangePasswordForm form) {
        AccountInfo currentUser = AccountCommonService.getCurrentUser();
        assert currentUser != null;
        ChangeUserPasswordDto dto = new ChangeUserPasswordDto();
        BeanUtil.copyProperties(form, dto);
        dto.setUserId(currentUser.getId());
        String result = feignUserService.changeUserPassword(dto);
        if ("ok".equals(result)) {
            StpUtil.logout();
        }
        return result;
    }

    @Override
    public String changeFieldValue(KeyValue<String, ?> form) {
        AccountInfo currentUser = AccountCommonService.getCurrentUser();
        assert currentUser != null;
        String result = feignUserService.changeFieldValue(currentUser.getId(), form);
        if (result.equals("ok")) {
            if (form.getKey().equals("nickname")) {
                currentUser.setNickname(form.getValue().toString());
            }
            if (form.getKey().equals("mobile")) {
                currentUser.setMobile(form.getValue().toString());
            }
            if (form.getKey().equals("email")) {
                currentUser.setEmail(form.getValue().toString());
            }
            IAccountService.setUserSession(currentUser);
        }
        return result;
    }

    @Override
    public String getSmsVerifyCode(LoginForm form) {
        UserDto userDto = feignUserService.getUserDto(form.getUsername());
        if (userDto == null) {
            throw new ServiceException(RestCode.AUTHORIZED_ERROR, RestCode.AUTHORIZED_ERROR.message());
        }
        Object errorTimes = redisTemplate.opsForValue().get(USER_LOGIN_TIMES_CACHE_KEY + userDto.getId());
        if (errorTimes == null) {
            errorTimes = 0;
        }
        if ((Integer) errorTimes >= 5) {
            long seconds = redisTemplate.getExpire(USER_LOGIN_TIMES_CACHE_KEY + userDto.getId(), TimeUnit.SECONDS);
            if (seconds > 60) {
                long minutes = redisTemplate.getExpire(USER_LOGIN_TIMES_CACHE_KEY + userDto.getId(), TimeUnit.MINUTES);
                throw new ServiceException(RestCode.AUTHORIZED_ERROR, "您的账户或密码错误次数超过5次，请" + minutes + "分钟后重试");
            } else {
                throw new ServiceException(RestCode.AUTHORIZED_ERROR, "您的账户或密码错误次数超过5次，请" + seconds + "秒后重试");
            }
        }

        String password = SaSecureUtil.md5BySalt(form.getPassword(), userDto.getSalt());

        if (!userDto.getPassword().equals(password)) {
            this.updateErrorPasswordTimes(userDto.getId());
            throw new ServiceException(RestCode.AUTHORIZED_ERROR, "您的账户或密码错误" + ((Integer) errorTimes + 1) + "次，密码错误5次您的账户将被锁定30分钟");
        } else {
            redisTemplate.delete(USER_LOGIN_TIMES_CACHE_KEY + userDto.getId());
        }

        if (Objects.nonNull(userDto.getExpireTime()) && userDto.getExpireTime().isBefore(LocalDateTime.now())) {
            throw new ServiceException(RestCode.AUTHORIZED_ERROR, "您的账户已过期，请联系管理员");
        }

        if (userDto.getStatus().equals(0)) {
            throw new ServiceException(RestCode.AUTHORIZED_ERROR, "您的账户已被禁用，请联系管理员");
        }
        if (CollUtil.isEmpty(userDto.getRoles())) {
            throw new ServiceException(RestCode.AUTHORIZED_ERROR, "您的账户未配置角色，请联系管理员");
        }
        if (StrUtil.isBlank(userDto.getMobile())) {
            throw new ServiceException(RestCode.AUTHORIZED_ERROR, "您还未绑定手机号码，请先绑定手机号码");
        }
        // 发送短信
        smsService.sendSmsCode(userDto.getMobile());
        return "success";
    }


    //记录密码错误次数
    private Integer updateErrorPasswordTimes(Long userId) {
        Long result = redisTemplate.opsForValue().increment(USER_LOGIN_TIMES_CACHE_KEY + userId);
        redisTemplate.expire(USER_LOGIN_TIMES_CACHE_KEY + userId, 30, TimeUnit.MINUTES);
        if (result == null) {
            result = 1L;
        }
        return result.intValue();
    }
}
