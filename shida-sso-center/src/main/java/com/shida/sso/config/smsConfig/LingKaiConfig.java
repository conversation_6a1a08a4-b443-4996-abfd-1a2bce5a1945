package com.shida.sso.config.smsConfig;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class LingKaiConfig implements Serializable {

    /**
     * api路径
     */
    private String apiPath;
    /**
     * appId
     */
    private String appId;
    /**
     * 密码
     */
    private String password;
    /**
     * 签名
     */
    private String sign;
}
