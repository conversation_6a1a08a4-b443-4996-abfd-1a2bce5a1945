package com.shida.sso.config.smsConfig;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
public class ShuZhengJuConfig implements Serializable {

    /**
     * 短信接口地址
     */
    private String apiPath;
    /**
     * 短信接口appcode
     */
    private String appCode;

    /**
     * 短信接口appSecret
     */
    private String appSecret;
    /**
     * 短信模板
     */
    private List<ShuZhengJuSmsTemplate> templates;
}
