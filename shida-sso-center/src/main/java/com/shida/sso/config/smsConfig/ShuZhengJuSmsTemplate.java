package com.shida.sso.config.smsConfig;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
public class ShuZhengJuSmsTemplate implements Serializable {
    /**
     * 短信模板编号
     */
    private Integer code;
    /**
     * 短信模板名称
     */
    private String name;

    /**
     * 短信模板参数
     */
    private List<String> params;
}
