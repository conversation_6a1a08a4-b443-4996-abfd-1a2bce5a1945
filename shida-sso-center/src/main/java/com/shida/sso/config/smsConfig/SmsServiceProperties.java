package com.shida.sso.config.smsConfig;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.io.Serializable;

@Data
@Component
@ConfigurationProperties(prefix = "sms-service")
public class SmsServiceProperties implements Serializable {

    /**
     * 短信服务提供商
     */
    private String serviceProvider;

    /**
     * 灵开短信配置
     */
    private LingKaiConfig lingKai;

    /**
     * 数据库短信模板配置
     */
    private ShuZhengJuConfig shuZhengJu;

}
