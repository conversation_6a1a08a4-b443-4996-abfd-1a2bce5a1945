package com.shida.sso.controller.feign;

import cn.dev33.satoken.stp.StpUtil;
import com.shida.IFeignAccountService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "Feign-账户服务")
@RestController
public class AccountFeignController implements IFeignAccountService {

    @Operation(summary = "踢出用户")
    @Override
    public String kickOutUser(Long userId) {
        try {
            StpUtil.kickout(userId);
        } catch (Exception ignored) {
        }
        return "ok";
    }
}
