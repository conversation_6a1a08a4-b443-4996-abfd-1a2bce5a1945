package com.shida.sso.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.shida.AccountCommonService;
import com.shida.autolog.AutoLog;
import com.shida.autolog.LogType;
import com.shida.autolog.OperateType;
import com.shida.config.AppConfigure;
import com.shida.noRepeatSubmit.NoRepeatSubmit;
import com.shida.pojo.vo.KeyValue;
import com.shida.sso.pojo.from.ChangePasswordForm;
import com.shida.sso.pojo.from.LoginForm;
import com.shida.sso.pojo.from.SmsLoginForm;
import com.shida.pojo.vo.AccountInfo;
import com.shida.sso.pojo.from.VerifyCodeLoginForm;
import com.shida.sso.pojo.vo.VerifyCode;
import com.shida.sso.service.IAccountService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;

@Tag(name = "sso-认证中心")
@RestController
@RequestMapping("/account")
public class AccountController {

    @Resource
    private IAccountService accountService;

    @Operation(summary = "获取验证码图片")
    @GetMapping("getVerifyCodeImage")
    public VerifyCode getVerifyCodeImage(HttpServletResponse response, HttpServletRequest request) throws IOException {
        String sessionId = request.getSession().getId();
        return accountService.generateVerifyCodeImageData(sessionId, response);
    }

    @Operation(summary = "获取短信验证码")
    @PostMapping("getSmsVerifyCode")
    public String getSmsVerifyCode(@RequestBody @Valid LoginForm form) {
        return accountService.getSmsVerifyCode(form);
    }

    @Operation(summary = "获取安全模式状态")
    @PostMapping("securityStatus")
    public Boolean getSecurityStatus() {
        return AppConfigure.securityStatus();
    }


    @AutoLog(logType = LogType.LOGIN)
    @Operation(summary = "管理员登录")
    @PostMapping("/admin/login")
    public AccountInfo adminLogin(@RequestBody @Valid SmsLoginForm form) {
        return accountService.adminLogin(form);
    }

    @AutoLog(logType = LogType.LOGIN)
    @Operation(summary = "学生|家长|教师登录")
    @PostMapping("/login")
    public AccountInfo login(@RequestBody @Valid VerifyCodeLoginForm form) {
        return accountService.login(form);
    }

    @Operation(summary = "获取当前用户信息")
    @PostMapping("getSelfInfo")
    public AccountInfo getSelfInfo() {
        return AccountCommonService.getCurrentUser();
    }

    @Operation(summary = "检查登录状态")
    @PostMapping("checkLoginStatus")
    public Boolean checkLoginStatus() {
        return StpUtil.isLogin();
    }


    @AutoLog(logType = LogType.LOGOUT)
    @Operation(summary = "退出")
    @PostMapping("logout")
    public Boolean logout() {
        return accountService.logout();
    }


    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    @Operation(summary = "修改密码")
    @PostMapping("changeSelfPassword")
    public String changeSelfPassword(@RequestBody @Valid ChangePasswordForm form) {
        return accountService.changeSelfPassword(form);
    }

    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    @Operation(summary = "修改资料")
    @PostMapping("changeFieldValue")
    public String changeFieldValue(@RequestBody KeyValue<String, ?> form) {
        return accountService.changeFieldValue(form);
    }
}
