package com.shida.sso.pojo.from;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

@Data
@Accessors(chain = true)
@Schema(name = "LoginForm", description = "登录表单")
public class LoginForm {
    @Schema(description = "账户名")
    @NotBlank(message = "账户名不能为空")
    private String username;
    @Schema(description = "密码")
    @NotBlank(message = "密码不能为空")
    private String password;
}
