package com.shida.sso.pojo.from;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date 2024/4/26 13:54
 * @PackageName:com.shida.pojo.from
 * @ClassName: VerifyLoginForm
 * @Version 1.0
 */
@Data
@Accessors(chain = true)
@Schema(name = "VerifyLoginForm", description = "登录表单")
public class SmsLoginForm extends LoginForm {
    @Schema(description = "验证码")
    @NotBlank(message = "验证码不能为空")
    private String verifyCode;
}
