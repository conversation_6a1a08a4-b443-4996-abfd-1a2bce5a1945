package com.shida.sso.pojo.from;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

@Data
@Accessors(chain = true)
@Schema(name = "VerifyCodeLoginForm", description = "登录表单")
public class VerifyCodeLoginForm  extends LoginForm{
    @Schema(description = "图片验证码")
    @NotBlank(message = "验证码不能为空")
    private String verifyCode;
    @Schema(description = "验证码标识")
    private String code;
}
