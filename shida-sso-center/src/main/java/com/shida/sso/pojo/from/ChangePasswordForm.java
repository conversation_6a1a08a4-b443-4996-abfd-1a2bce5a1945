package com.shida.sso.pojo.from;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
@Accessors(chain = true)
@Schema(name = "ChangePasswordForm", description = "修改密码")
public class ChangePasswordForm implements Serializable {
    @Schema(description = "旧密码")
    @NotBlank(message = "旧密码不能为空")
    private String oldPassword;
    @Schema(description = "新密码")
    @NotBlank(message = "新密码不能为空")
    @Size(min = 6, max = 32, message = "密码长度为6-32位")
    private String newPassword;
    @Schema(description = "确认密码")
    @NotBlank(message = "确认密码不能为空")
    @Size(min = 6, max = 32, message = "确认密码长度为6-32位")
    private String confirmPassword;
}
