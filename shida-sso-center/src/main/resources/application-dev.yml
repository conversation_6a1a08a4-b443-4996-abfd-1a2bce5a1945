server:
  port: 11000

spring:
  config:
    import:
      - optional:nacos:redis.yaml?namespace=sjz-healthy
      - optional:nacos:mongodb.yaml?namespace=sjz-healthy
      - optional:nacos:sa-token.yaml?namespace=sjz-healthy
  cloud:
    nacos:
      discovery:
        # 开启nacos作为服务注册中心，默认值：true
        enabled: true
        # nacos集群服务注册地址
        server-addr: http://127.0.0.1:8848
        # nacos用户名
        username: nacos
        # nacos密码
        password: nacos
        # 命名空间，默认 public,可设置dev,pro等，相同特征的服务分类，先去nacos命名空间创建
        namespace: sjz-healthy
        # 分组，默认 DEFAULT_GROUP 相同特征的服务划分的更细
        group: DEFAULT_GROUP
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        username: ${spring.cloud.nacos.discovery.username}
        password: ${spring.cloud.nacos.discovery.password}
        namespace: ${spring.cloud.nacos.discovery.namespace}
        group: ${spring.cloud.nacos.discovery.group}


knife4j:
  # 开启文档增强
  enable: true
  # 生成时设置为true
  production: false

# 日志配置
logging:
  file:
    path: /var/log/${spring.application.name}

# openFeign配置
feign:
  circuitBreaker:
    enabled: true
  # 由于feign默认使用线程池，所以更换为okhttp
  okhttp:
    enabled: true
  client:
    config:
      default:
        connect-timeout: 5000
        read-timeout: 5000
        logger-level: full

ok:
  http:
    connect-timeout: 60
    read-timeout: 60
    write-timeout: 60
    # 连接池中整体的空闲连接的最大数量
    max-idle-connections: 200
    # 连接空闲时间最多为 300 秒
    keep-alive-duration: 300


# 是否启用前后端安全传输
sd-security:
  # 排除urls
  exclude-urls:
    /securityStatus,
    /account/getVerifyCodeImage

############## Sa-Token 配置 (文档: https://sa-token.cc) ##############
sa-token:
  # 不检查 token 的 url
  not-check-login:
    /doc.html,
    /favicon.ico,
    /webjars/**,
    /v3/api-docs/**,
    /static/**,
    /account/**,
    /securityStatus,



# 静态资源
upload:
  # 物理路径配置
  file:
    # 根目录
    root-path: /data/${spring.application.name}/upload/
    # 头像目录
    avatar-path: ${upload.file.root-path}avatar/
    # 图片目录
    image-path: ${upload.file.root-path}images/
    # 其他文件目录
    other-path: ${upload.file.root-path}other/
    # 临时文件目录
    temp-path: ${upload.file.root-path}temp/
    # 富文本目录
    editor-path: ${upload.file.root-path}editor/
  url:
    # 头像url
    avatar: /upload/avatar/
    # 图片url
    image: /upload/images/
    # 富文本url
    editor: /upload/editor/

sms-service:
  service-provider: ling-kai
  ling-kai:
    api-path: https://mb345.com/ws/BatchSend2.aspx
    app-id: sjzlkj0006548
    password: zh9527@111
    sign: 石家庄市教育局
  shu-zheng-ju:
    api-path: http://10.24.157.187:8095/api/json_message_sendOnlyMessage/v1
    app-code: fK8w4KIe27Ja
    app-secret: U14t2Bf98jvZwQlmsR2M3BpRLeN247Se
    templates:
      - code: 497
        name: 登录短信验证码
        params: code
