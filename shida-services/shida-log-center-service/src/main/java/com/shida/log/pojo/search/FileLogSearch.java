package com.shida.log.pojo.search;

import com.shida.pojo.search.Search;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@Schema(name = "FileLogSearch", description = "文件日志搜索条件")
public class FileLogSearch extends Search {
    @Schema(description = "文件名称")
    private String filename;
    @Schema(description = "账户名")
    private String username;
    @Schema(description = "开始时间")
    private LocalDateTime startDateTime;
    @Schema(description = "结束时间")
    private LocalDateTime endDateTime;
}
