package com.shida.log.pojo.excel;

import com.shida.excel.ExcelAlias;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
public class FileExcel implements Serializable {
    @ExcelAlias(value = "文件ID")
    private String id;
    @ExcelAlias(value = "原文件名称")
    private String name;
    @ExcelAlias(value = "服务器上文件名称")
    private String serverFileName;
    @ExcelAlias(value = "url")
    private String url;
    @ExcelAlias(value = "存储地址")
    private String filePath;
    @ExcelAlias(value = "上传时间")
    private LocalDateTime createTime;
    @ExcelAlias(value = "上传用户ID")
    private String userId;
    @ExcelAlias(value = "上传用户名称")
    private String username;
    @ExcelAlias(value = "部门编码")
    private String deptCode;
}
