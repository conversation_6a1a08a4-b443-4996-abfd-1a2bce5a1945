package com.shida.log.service;

import com.shida.autolog.SystemLog;
import com.shida.pojo.LogSearch;
import com.shida.pojo.vo.PageInfo;
import org.bson.Document;
import org.springframework.data.mongodb.core.query.Query;

import javax.servlet.http.HttpServletResponse;

public interface ILogService {
    /**
     * 查询分页数据
     * @param pageNumber
     * @param pageSize
     * @param query
     * @return
     */
    PageInfo<SystemLog> getPageData(Integer pageNumber, Integer pageSize, Query query);

    PageInfo<SystemLog> getPageData(Integer pageNumber, Integer pageSize, Document query);

    PageInfo<SystemLog> getPageDataBySearch(LogSearch search);

    void exportOperateExcel(LogSearch search, HttpServletResponse response);
}
