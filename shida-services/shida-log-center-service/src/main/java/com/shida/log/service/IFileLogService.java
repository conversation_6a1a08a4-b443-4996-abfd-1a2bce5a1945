package com.shida.log.service;

import com.shida.log.pojo.search.FileLogSearch;
import com.shida.pojo.vo.FileInfoLog;
import com.shida.pojo.vo.PageInfo;
import org.bson.Document;

import javax.servlet.http.HttpServletResponse;

public interface IFileLogService {
    PageInfo<FileInfoLog> getPageData(Integer pageNumber, Integer pageSize, Document query);

    PageInfo<FileInfoLog> getPageDataBySearch(FileLogSearch search);

    void exportFileExcel(FileLogSearch search, HttpServletResponse response);
}
