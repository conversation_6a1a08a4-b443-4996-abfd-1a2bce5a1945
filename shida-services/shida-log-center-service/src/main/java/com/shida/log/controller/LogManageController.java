package com.shida.log.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.shida.AccountCommonService;
import com.shida.autolog.SystemLog;
import com.shida.config.AppConfigure;
import com.shida.log.pojo.search.FileLogSearch;
import com.shida.log.service.IFileLogService;
import com.shida.log.service.ILogService;
import com.shida.noRepeatSubmit.NoRepeatSubmit;
import com.shida.pojo.LogSearch;
import com.shida.pojo.vo.AccountInfo;
import com.shida.pojo.vo.FileInfoLog;
import com.shida.pojo.vo.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.bson.Document;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;

@Tag(name = "log-日志管理")
@RestController
@RequestMapping("/log/manage")
public class LogManageController {

    @Resource
    private ILogService logService;

    @Resource
    private IFileLogService fileLogService;


    @NoRepeatSubmit(value = 20)
    @Operation(summary = "获取操作日志列表")
    @SaCheckPermission("operate:log:list")
    @PostMapping("/getPageData")
    public PageInfo<SystemLog> getPageData(@RequestBody LogSearch search) {
        return logService.getPageDataBySearch(search);
    }



    @NoRepeatSubmit(value = 20)
    @Operation(summary = "获取上传文件信息列表")
    @SaCheckPermission("file:log:list")
    @PostMapping("/getFileLogPageData")
    public PageInfo<FileInfoLog> getFileLogPageData(@RequestBody FileLogSearch search) {
        return fileLogService.getPageDataBySearch(search);
    }



    @NoRepeatSubmit(value = 20)
    @Operation(summary = "导出操作日志列表", description = "导出操作日志列表，最多10000条记录")
    @SaCheckPermission("operate:log:export")
    @PostMapping("/exportOperateExcel")
    public void exportOperateExcel(@RequestBody LogSearch search, HttpServletResponse response){
        logService.exportOperateExcel(search, response);
    }


    @NoRepeatSubmit(value = 20)
    @Operation(summary = "导出文件日志列表", description = "导出文件日志列表，最多10000条记录")
    @SaCheckPermission("file:log:export")
    @PostMapping("/exportFileExcel")
    public void exportFileExcel(@RequestBody FileLogSearch search, HttpServletResponse response){
        fileLogService.exportFileExcel(search, response);
    }
}
