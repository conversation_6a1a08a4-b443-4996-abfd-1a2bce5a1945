package com.shida.log.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.shida.AccountCommonService;
import com.shida.autolog.LogType;
import com.shida.autolog.OperateType;
import com.shida.autolog.SystemLog;
import com.shida.config.AppConfigure;
import com.shida.excel.ExcelHelper;
import com.shida.log.pojo.excel.FileExcel;
import com.shida.log.pojo.excel.OperateExcel;
import com.shida.log.pojo.search.FileLogSearch;
import com.shida.log.service.IFileLogService;
import com.shida.pojo.vo.AccountInfo;
import com.shida.pojo.vo.FileInfoLog;
import com.shida.pojo.vo.PageInfo;
import com.shida.service.impl.MongodbServiceImpl;
import org.bson.Document;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

@Service
public class FileLogServiceImpl extends MongodbServiceImpl<Long, FileInfoLog> implements IFileLogService {
    @Override
    public PageInfo<FileInfoLog> getPageData(Integer pageNumber, Integer pageSize, Document query) {
        return super.page(Long.valueOf(pageNumber), Long.valueOf(pageSize), query);
    }

    @Override
    public PageInfo<FileInfoLog> getPageDataBySearch(FileLogSearch search) {
        AccountInfo info = AccountCommonService.getCurrentUser();
        Document query = new Document();
        LocalDateTime startDateTime = search.getStartDateTime();
        LocalDateTime endDateTime = search.getEndDateTime();
        if (ObjectUtil.isNotEmpty(startDateTime) && ObjectUtil.isNotEmpty(endDateTime)) {
            query.append("createTime", new Document("$gte", startDateTime.plusHours(-8l)).append("$lte", endDateTime.plusHours(-8l)));
        } else if (ObjectUtil.isNotEmpty(startDateTime)) {
            query.append("createTime", new Document("$gte", startDateTime.plusHours(-8l)));
        } else if (ObjectUtil.isNotEmpty(endDateTime)) {
            query.append("createTime", new Document("$lte", endDateTime.plusHours(-8l)));
        }
        if (!info.getRegionCode().equals(AppConfigure.getCityCode())) {
            query.append("deptCode", new Document("$eq", info.getRegionCode()));
        }
        if (StrUtil.isNotBlank(search.getFilename())) {
            query.append("name", new Document("$regex", search.getFilename()));
        }
        if (StrUtil.isNotBlank(search.getUsername())) {
            query.append("username", new Document("$regex", search.getUsername()));
        }
        return this.getPageData(search.getPageNumber(), search.getPageSize(), query);
    }

    @Override
    public void exportFileExcel(FileLogSearch search, HttpServletResponse response) {
        ExcelHelper<FileExcel> excelHelper = ExcelHelper.create(FileExcel.class);
        search.setPageNumber(1).setPageSize(10000);
        PageInfo<FileInfoLog> pageInfo = this.getPageDataBySearch(search);
        List<FileExcel> excelList = BeanUtil.copyToList(pageInfo.getRecords(), FileExcel.class);
        excelHelper.writeToWebResponse(excelList, response, "文件日志");
    }
}
