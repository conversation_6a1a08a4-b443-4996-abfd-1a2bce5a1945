package com.shida.log.pojo.excel;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.shida.excel.ExcelAlias;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;


@Data
@Accessors(chain = true)
public class OperateExcel implements Serializable {
    @ExcelAlias(value = "主键ID")
    private String id;

    @ExcelAlias(value = "操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ExcelAlias(value = "耗时")
    private Integer costTime;

    @ExcelAlias(value = "ip地址")
    private String ip;

    @ExcelAlias(value = "入参")
    private String requestParam;

    @ExcelAlias(value = "响应结果")
    private String responseResult;

    @ExcelAlias(value = "请求类型")
    private String requestType;

    @ExcelAlias(value = "请求路径")
    private String requestUrl;

    @ExcelAlias(value = "请求方法")
    private String method;

    @ExcelAlias(value = "操作人")
    private String username;

    @ExcelAlias(value = "操作人ID")
    private String userId;

    @ExcelAlias(value = "操作人部门ID")
    private String deptId;

    @ExcelAlias(value = "操作人部门code")
    private String deptCode;

    @ExcelAlias(value = "操作人角色")
    private String deptName;

    @ExcelAlias(value = "操作人角色ID")
    private String roleId;

    @ExcelAlias(value = "操作人角色")
    private String roleName;

    @ExcelAlias(value = "操作人机构ID")
    private String orgId;

    @ExcelAlias(value = "操作人机构名称")
    private String orgName;

    @ExcelAlias(value = "操作内容")
    private String logContent;

    @ExcelAlias(value = "日志类型")
    private String logType;

    @ExcelAlias(value = "操作类型")
    private String operateType;

    @ExcelAlias(value = "操作对象")
    private String operateObject;

    @ExcelAlias(value = "详情描述")
    private String description;
}
