package com.shida.log.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.shida.AccountCommonService;
import com.shida.autolog.LogType;
import com.shida.autolog.OperateType;
import com.shida.autolog.SystemLog;
import com.shida.config.AppConfigure;
import com.shida.excel.ExcelHelper;
import com.shida.log.pojo.excel.OperateExcel;
import com.shida.log.service.ILogService;
import com.shida.pojo.LogSearch;
import com.shida.pojo.vo.AccountInfo;
import com.shida.pojo.vo.PageInfo;
import com.shida.service.impl.MongodbServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;


@Slf4j
@Service
public class LogServiceImpl extends MongodbServiceImpl<Long, SystemLog> implements ILogService {


    @Override
    public PageInfo<SystemLog> getPageData(Integer pageNumber, Integer pageSize, Query query) {
        return super.page(Long.valueOf(pageNumber), Long.valueOf(pageSize), query);
    }

    @Override
    public PageInfo<SystemLog> getPageData(Integer pageNumber, Integer pageSize, Document query) {
        return super.page(Long.valueOf(pageNumber), Long.valueOf(pageSize), query);
    }

    @Override
    public PageInfo<SystemLog> getPageDataBySearch(LogSearch search) {
        AccountInfo info = AccountCommonService.getCurrentUser();
        Document query = new Document();
        if (ObjectUtil.isNotEmpty(search.getOperateType())) {
            query.put("operateType", new Document("$eq", search.getOperateType()));
        }
        if (StrUtil.isNotBlank(search.getKeywords())) {
            query.put("username", new Document("$eq", search.getKeywords()));
        }
        LocalDateTime startDateTime = search.getStartDateTime();
        LocalDateTime endDateTime = search.getEndDateTime();
        if (ObjectUtil.isNotEmpty(startDateTime) && ObjectUtil.isNotEmpty(endDateTime)) {
            query.append("createTime", new Document("$gte", startDateTime.plusHours(-8l)).append("$lte", endDateTime.plusHours(-8l)));
        } else if (ObjectUtil.isNotEmpty(startDateTime)) {
            query.append("createTime", new Document("$gte", startDateTime.plusHours(-8l)));
        } else if (ObjectUtil.isNotEmpty(endDateTime)) {
            query.append("createTime", new Document("$lte", endDateTime.plusHours(-8l)));
        }
        assert info != null;
        if (!info.getRegionCode().equals(AppConfigure.getCityCode())) {
            query.append("deptCode", new Document("$eq", info.getRegionCode()));
        }

        return this.getPageData(search.getPageNumber(), search.getPageSize(), query);
    }

    @Override
    public void exportOperateExcel(LogSearch search, HttpServletResponse response) {
        ExcelHelper<OperateExcel> excelHelper = ExcelHelper.create(OperateExcel.class);
        search.setPageNumber(1).setPageSize(10000);
        PageInfo<SystemLog> pageInfo = this.getPageDataBySearch(search);
        List<OperateExcel> excelList = BeanUtil.copyToList(pageInfo.getRecords(), OperateExcel.class);
        for (OperateExcel operateExcel : excelList) {
            Arrays.stream(LogType.values()).filter(t -> t.getValue().equals(Integer.valueOf(operateExcel.getLogType())))
                .findFirst().ifPresent(logType -> operateExcel.setLogType(logType.getDesc()));
            Arrays.stream(OperateType.values()).filter(t -> t.getValue().equals(Integer.valueOf(operateExcel.getOperateType())))
                .findFirst().ifPresent(operateType -> operateExcel.setOperateType(operateType.getDesc()));
        }
        excelHelper.writeToWebResponse(excelList, response, "操作日志");
    }

}
