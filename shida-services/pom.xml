<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.shida</groupId>
        <artifactId>shida-micro-service</artifactId>
        <version>0.1.0</version>
    </parent>

    <artifactId>shida-services</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>shida-user-center-service</module>
        <module>shida-region-business-service</module>
        <module>shida-log-center-service</module>
    </modules>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-openapi3-spring-boot-starter</artifactId>
            <version>${knife4j.version}</version>
        </dependency>
        <!-- 服务发现：OpenFeign服务调用 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-seata</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.seata</groupId>
                    <artifactId>seata-spring-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- Seata 独立版本控制 -->
        <dependency>
            <groupId>io.seata</groupId>
            <artifactId>seata-spring-boot-starter</artifactId>
            <version>1.5.2</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>druid</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>1.2.23</version>
        </dependency>
        <dependency>
            <groupId>com.shida</groupId>
            <artifactId>shida-commons</artifactId>
            <version>0.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.shida</groupId>
            <artifactId>shida-exception-starter</artifactId>
            <version>0.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.shida</groupId>
            <artifactId>shida-redis-starter</artifactId>
            <version>0.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.shida</groupId>
            <artifactId>shida-autolog-starter</artifactId>
            <version>0.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.shida</groupId>
            <artifactId>shida-security-starter</artifactId>
            <version>0.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.shida</groupId>
            <artifactId>shida-mybatis-plus-config-starter</artifactId>
            <version>0.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.shida</groupId>
            <artifactId>shida-knife4j-starter</artifactId>
            <version>0.1.0</version>
        </dependency>
    </dependencies>

</project>
