package com.shida.total.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.shida.total.service.ITiJianJingFeiService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Tag(name = "total-体检经费")
@RestController
@RequestMapping("/total/tijianjingfei")
public class TiJianJingFeiTotalController {

    @Resource
    private ITiJianJingFeiService tijianjingfeiService;

    @SaCheckPermission("total:tijianjingfei:list")
    @PostMapping("/getPageData")
    public String getList() {
        return tijianjingfeiService.getTiJianJingFeiTotalList();
    }

}
