package com.shida.total;

import com.shida.utils.IpAddressUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;

@Slf4j
@SpringBootApplication
@ComponentScan(basePackages = "com.shida")
@EnableFeignClients(basePackages = "com.shida")
public class TotalApplication {
    public static void main(String[] args) {
        ConfigurableApplicationContext context = SpringApplication.run(TotalApplication.class, args);
        String port = context.getEnvironment().getProperty("server.port");
        // 获取本机IP地址
        String ipaddress = IpAddressUtil.getLocalIp();
        log.debug("TotalService已启动，doc地址：http://{}:{}/doc.html", ipaddress, port);
    }
}
