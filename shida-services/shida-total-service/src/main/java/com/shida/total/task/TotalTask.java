package com.shida.total.task;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.cron.CronUtil;
import cn.hutool.cron.task.Task;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

@Component
public class TotalTask implements ApplicationRunner {
    @Resource
    private RedissonClient redissonClient;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        CronUtil.schedule("0 */10 * * * *", new Task() {
            @Override
            public void execute() {
                RLock lock = redissonClient.getLock("lockers:TotalTask-1");
                if (lock.tryLock()) {
                    Console.log("{}开始执行定时任务{}", LocalDateTimeUtil.format( LocalDateTime.now(),"yyyy-MM-dd HH:mm:ss SSS"), "initTiJianJingFeiTotalList");
                    try {
                        initTiJianJingFeiTotalList();
                    } catch (Exception e) {
                        e.printStackTrace();
                    } finally {
                        lock.unlock();
                    }
                }
            }
        });

        // 支持秒级别定时任务
        CronUtil.setMatchSecond(true);
        if (!CronUtil.getScheduler().isStarted()) {
            CronUtil.start();
        }
    }


    public void initTiJianJingFeiTotalList() {

        // 从 user-center 获取区县列表

        // 从 region-service 获取区县统计数据

        // 存入redis
    }
}
