server:
  port: 14000


spring:
  config:
    import:
      - optional:nacos:redis.yaml?namespace=sjz-healthy
      - optional:nacos:mongodb.yaml?namespace=sjz-healthy
      - optional:nacos:sa-token.yaml?namespace=sjz-healthy
      - optional:nacos:mysql.yaml?namespace=sjz-healthy
  cloud:
    nacos:
      discovery:
        # 开启nacos作为服务注册中心，默认值：true
        enabled: true
        # nacos集群服务注册地址
        server-addr: http://127.0.0.1:8848
        # nacos用户名
        username: nacos
        # nacos密码
        password: nacos
        # 命名空间，默认 public,可设置dev,pro等，相同特征的服务分类，先去nacos命名空间创建
        namespace: sjz-healthy
        # 分组，默认 DEFAULT_GROUP 相同特征的服务划分的更细
        group: DEFAULT_GROUP
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        username: ${spring.cloud.nacos.discovery.username}
        password: ${spring.cloud.nacos.discovery.password}
        namespace: ${spring.cloud.nacos.discovery.namespace}
        group: ${spring.cloud.nacos.discovery.group}
  datasource:
    driver-class-name: ${mysql.driver-class-name}
    username: ${mysql.username}
    password: ${mysql.password}
    url: jdbc:mysql://${mysql.host}:${mysql.port}/tzjk-user-center-dev?useUnicode=true&useSSL=false&characterEncoding=utf8&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true


seata:
  application-id: ${spring.application.name}
  tx-service-group: my_tx_group
  service:
    vgroup-mapping:
      my_tx_group: default
  registry:
    type: nacos
    nacos:
      server-addr: *************:8848
      username: nacos
      password: nacos
      namespace: sjz-healthy
      group: DEFAULT_GROUP


mybatis-plus:
  global-config:
    enable-sql-runner: true
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    default-enum-type-handler: com.baomidou.mybatisplus.core.handlers.MybatisEnumTypeHandler

knife4j:
  # 开启文档增强
  enable: true
  # 生成时设置为true
  production: false

# 日志配置
logging:
  file:
    path: /var/log/${spring.application.name}


# openFeign配置
feign:
  circuitBreaker:
    enabled: true
  # 由于feign默认使用线程池，所以更换为okhttp
  okhttp:
    enabled: true
  client:
    config:
      default:
        connect-timeout: 5000
        read-timeout: 5000
        logger-level: full

ok:
  http:
    connect-timeout: 60
    read-timeout: 60
    write-timeout: 60
    # 连接池中整体的空闲连接的最大数量
    max-idle-connections: 200
    # 连接空闲时间最多为 300 秒
    keep-alive-duration: 300

# 启用前后端安全传输
sd-security:
  # 排除urls
  exclude-urls:
    /securityStatus

############## Sa-Token 配置 (文档: https://sa-token.cc) ##############
sa-token:
  # 不检查 token 的 url
  not-check-login:
    /doc.html,
    /favicon.ico,
    /favicon.ico,
    /webjars/**,
    /v3/api-docs/**,
    /static/**,
    /account/**,
    /securityStatus,
    /upload/editor/**,
    /upload/avatar/**,
    /druid/**


# 静态资源
upload:
  # 物理路径配置
  file:
    # 根目录
    root-path: /data/${spring.application.name}/upload/
    # 头像目录
    avatar-path: /avatar/
    # 图片目录
    image-path: /images/
    # 其他文件目录
    other-path: /other/
    # 临时文件目录
    temp-path: /temp/
    # 富文本目录
    editor-path: /editor/
  url:
    root-url: /upload/
    # 头像url
    avatar: /avatar/
    # 图片url
    image: /images/
    # 富文本url
    editor: /editor/

