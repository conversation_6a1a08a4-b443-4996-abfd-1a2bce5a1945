CREATE TABLE `b_student_adjust` (
                                    `id` bigint NOT NULL,
                                    `adjust_id` varchar(18) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '异动ID',
                                    `adjust_type` varchar(2) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '异动类型：1休学 2复学，3市内转入，4市内转出，5市外转入，6市外转出，7留级，8跳级',
                                    `XJH` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '学籍号',
                                    `student_id` bigint DEFAULT NULL COMMENT '学生ID',
                                    `school_id` bigint DEFAULT NULL COMMENT '学校ID',
                                    `old_region_id` bigint DEFAULT NULL COMMENT '原区县ID',
                                    `new_region_id` bigint DEFAULT NULL COMMENT '新区县ID',
                                    `suspend_end_date` date DEFAULT NULL COMMENT '休学预计结束日期',
                                    `suspend_begin_date` date DEFAULT NULL COMMENT '休学开始日期',
                                    `suspend_reason` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '休学原因',
                                    `attachment` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '附件地址',
                                    `create_time` datetime DEFAULT NULL COMMENT '申请时间',
                                    `status` int DEFAULT NULL COMMENT '1待审核 2 审核通过 3审核驳回',
                                    `audit_process` longtext COLLATE utf8mb4_general_ci COMMENT '审核过程',
                                    `update_time` datetime DEFAULT NULL COMMENT '审核完成时间',
                                    `apply_user_id` bigint DEFAULT NULL COMMENT '操作人',
                                    `into_school_id` bigint DEFAULT NULL COMMENT '转入学校ID',
                                    `into_clazz_id` bigint DEFAULT NULL COMMENT '转入班级ID',
                                    `into_grade_id` bigint DEFAULT NULL COMMENT '转入年级ID',
                                    `out_city` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '转出城市',
                                    `out_school` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '转出学校',
                                    `out_grade` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '转出年级',
                                    `out_clazz` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '转出班级',
                                    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='学籍异动' ROW_FORMAT = DYNAMIC;

CREATE TABLE `b_sport_rate_record`  (
                                        `id` bigint NOT NULL,
                                        `create_time` datetime NULL DEFAULT NULL,
                                        `student_id` bigint NULL DEFAULT NULL COMMENT '学生ID',
                                        `clazz_id` bigint NULL DEFAULT NULL COMMENT '班级ID',
                                        `school_id` bigint NULL DEFAULT NULL COMMENT '学校ID',
                                        `term_id` bigint NULL DEFAULT NULL COMMENT '学期ID',
                                        `YFMC` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '月份名称',
                                        `SFSB` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否伤病',
                                        `SFCJ` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否残疾',
                                        `BJCS` int NOT NULL DEFAULT 0 COMMENT '病假次数',
                                        `SJCS` int NOT NULL DEFAULT 0 COMMENT '事假次数',
                                        `QQCS` int NOT NULL DEFAULT 0 COMMENT '缺勤次数',
                                        `XQZKS` int NOT NULL DEFAULT 0 COMMENT '学期总课时',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '体育课出勤率' ROW_FORMAT = DYNAMIC;

CREATE TABLE `b_clazz_adjust_record` (
                                         `id` int NOT NULL,
                                         `student_id` bigint DEFAULT NULL COMMENT '学生id',
                                         `original_clazz` bigint DEFAULT NULL COMMENT '原班级ID',
                                         `original_clazz_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '原班级名称',
                                         `new_clazz` bigint DEFAULT NULL COMMENT '调入班级ID',
                                         `new_clazz_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '调入班级名称',
                                         `adjust_date` date DEFAULT NULL COMMENT '调班日期',
                                         `operator` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作人',
                                         `operator_time` datetime DEFAULT NULL COMMENT '操作时间',
                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='校内调班管理' ROW_FORMAT = DYNAMIC;

CREATE TABLE `t_clazz`  (
                            `id` bigint NOT NULL,
                            `create_time` datetime NULL DEFAULT NULL,
                            `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '班级名称',
                            `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '班级代码',
                            `grade_id` bigint NULL DEFAULT NULL COMMENT '班级ID',
                            `period` int NULL DEFAULT NULL COMMENT '学段，1小学，2初中，3高中',
                            `enroll_year` int NULL DEFAULT NULL COMMENT '入学年份',
                            `school_id` bigint NULL DEFAULT NULL COMMENT '学校ID',
                            `status` bit(1) NULL DEFAULT NULL COMMENT '状态，0停用，1正常，2已毕业',
                            PRIMARY KEY (`id`) USING BTREE,
                            UNIQUE INDEX `code`(`code` ASC) USING BTREE,
                            INDEX `code_school_id`(`school_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '班级信息表' ROW_FORMAT = DYNAMIC;

CREATE TABLE `t_delete_record`  (
                                    `id` bigint NOT NULL,
                                    `create_time` datetime NULL DEFAULT NULL,
                                    `entity_id` bigint NOT NULL COMMENT '对象id',
                                    `entity_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '对象名称',
                                    `entity_signage` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '对象标识',
                                    `entity_json` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '对象json字符串',
                                    `parent_entity_id` bigint NULL DEFAULT NULL COMMENT '关联父对象ID',
                                    `operator_id` bigint NULL DEFAULT NULL COMMENT '操作账户ID',
                                    `reason` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除原因',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '数据删除记录表' ROW_FORMAT = Dynamic;

CREATE TABLE `t_device`  (
                             `id` bigint NOT NULL,
                             `create_time` datetime NULL DEFAULT NULL,
                             `device_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备ID',
                             `device_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备名称',
                             `project_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '检测项目',
                             `school_id` bigint NULL DEFAULT NULL COMMENT '所属学校ID',
                             `location` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '放置位置',
                             `online_status` bit(1) NULL DEFAULT NULL COMMENT '在线状态',
                             `description` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '设备描述',
                             PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '设备信息表' ROW_FORMAT = DYNAMIC;


CREATE TABLE `t_school`  (
                             `id` bigint NOT NULL,
                             `create_time` datetime NULL DEFAULT NULL,
                             `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '学校名称',
                             `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '学校代码',
                             `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '简介',
                             `region_id` bigint NULL DEFAULT NULL COMMENT '区县ID',
                             `period` int NULL DEFAULT NULL COMMENT '学段，1小学，2初中，3高中，4九年一贯制',
                             `school_type` int NULL DEFAULT NULL COMMENT '办学性质，1公办，2民办',
                             `contacts` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系人',
                             `telephone` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系电话',
                             PRIMARY KEY (`id`) USING BTREE,
                             UNIQUE INDEX `code_unique`(`code` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '学校信息表' ROW_FORMAT = Dynamic;


CREATE TABLE `t_student`  (
                              `id` bigint NOT NULL,
                              `create_time` datetime NULL DEFAULT NULL,
                              `clazz_id` bigint NULL DEFAULT NULL COMMENT '班级ID',
                              `school_id` bigint NULL DEFAULT NULL COMMENT '学校ID',
                              `user_id` bigint NULL DEFAULT NULL COMMENT '账户ID',
                              `status` int NULL DEFAULT NULL COMMENT '学生状态，1在读，2毕业，3休学',
                              `XM` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '姓名',
                              `XJH` varchar(19) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '学籍号',
                              `SFZH` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '身份证号',
                              `XB` int NULL DEFAULT NULL COMMENT '性别',
                              `SJHM` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号码',
                              `JTZZ` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '家庭住址',
                              `XSTX` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '学生头像',
                              `MZ` int NULL DEFAULT NULL COMMENT '民族',
                              `is_clazz_adjust` int NULL COMMENT '是否校内调班 0否 ; 1是',
                              PRIMARY KEY (`id`) USING BTREE,
                              UNIQUE INDEX `sfzh`(`SFZH` ASC) USING BTREE,
                              UNIQUE INDEX `idx_xjh`(`XJH` ASC) USING BTREE,
                              INDEX `clazz_id`(`clazz_id` ASC) USING BTREE,
                              INDEX `school_id`(`school_id` ASC) USING BTREE,
                              INDEX `status`(`status` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '学生信息表' ROW_FORMAT = DYNAMIC;


CREATE TABLE `t_teacher`  (
                              `id` bigint NOT NULL,
                              `create_time` datetime NULL DEFAULT NULL,
                              `school_id` bigint NULL DEFAULT NULL COMMENT '学校ID',
                              `user_id` bigint NULL DEFAULT NULL COMMENT '账户ID',
                              `JSXM` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '教师姓名',
                              `JSLB` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '教师类型，1班主任，2体育老师',
                              `SFZH` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '身份证号',
                              `JSTX` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '教师头像',
                              `SJHM` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号码',
                              `JSXB` int NULL DEFAULT NULL COMMENT '教师性别',
                              PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;


CREATE TABLE `t_teacher_clazz`  (
                                    `id` bigint NOT NULL,
                                    `create_time` datetime NULL DEFAULT NULL,
                                    `teacher_id` bigint NOT NULL COMMENT '教师ID',
                                    `clazz_id` bigint NOT NULL COMMENT '班级ID',
                                    `type` int NOT NULL COMMENT '关系类型：1班主任，2体育教师',
                                    PRIMARY KEY (`id`) USING BTREE,
                                    INDEX `teacher_id`(`teacher_id` ASC) USING BTREE,
                                    INDEX `clazz_id`(`clazz_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教师班级关联关系表' ROW_FORMAT = DYNAMIC;


CREATE TABLE `undo_log`  (
                             `id` bigint NOT NULL AUTO_INCREMENT,
                             `branch_id` bigint NOT NULL COMMENT 'branch transaction id',
                             `xid` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'global transaction id',
                             `context` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'undo_log context,such as serialization',
                             `rollback_info` longblob NOT NULL COMMENT 'rollback info',
                             `log_status` int NOT NULL COMMENT '0:normal status,1:defense status',
                             `log_created` datetime(6) NOT NULL COMMENT 'create datetime',
                             `log_modified` datetime(6) NOT NULL COMMENT 'modify datetime',
                             PRIMARY KEY (`id`) USING BTREE,
                             UNIQUE INDEX `ux_undo_log`(`xid` ASC, `branch_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'AT transaction mode undo table' ROW_FORMAT = DYNAMIC;


CREATE TABLE `h_mouth`  (
                            `id` bigint NOT NULL,
                            `create_time` datetime NULL DEFAULT NULL,
                            `student_id` bigint NULL DEFAULT NULL COMMENT '学生ID',
                            `term_id` bigint NULL DEFAULT NULL COMMENT '学期ID',
                            `SFQC` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否龋齿',
                            `SFYZJB` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否牙周疾病',
                            PRIMARY KEY (`id`) USING BTREE,
                            UNIQUE INDEX `mouth_term`(`student_id` ASC, `term_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '学生口腔检查表' ROW_FORMAT = DYNAMIC;


CREATE TABLE `h_tuberculosis`  (
                                   `id` bigint NOT NULL,
                                   `create_time` datetime NULL DEFAULT NULL,
                                   `student_id` bigint NULL DEFAULT NULL COMMENT '学生ID',
                                   `term_id` bigint NULL DEFAULT NULL COMMENT '学期ID',
                                   `SFJC` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否检查',
                                   `SFFB` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否发病',
                                   PRIMARY KEY (`id`) USING BTREE,
                                   UNIQUE INDEX `mouth_term`(`student_id` ASC, `term_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '学生结核病检查表' ROW_FORMAT = DYNAMIC;

CREATE TABLE `h_ebh_record`  (
                                 `id` bigint NOT NULL,
                                 `create_time` datetime NULL DEFAULT NULL,
                                 `student_id` bigint NULL DEFAULT NULL COMMENT '学生ID',
                                 `clazz_id` bigint NULL DEFAULT NULL COMMENT '班级ID',
                                 `school_id` bigint NULL DEFAULT NULL COMMENT '学校ID',
                                 `term_id` bigint NULL DEFAULT NULL COMMENT '学期ID',
                                 `TL` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '听力',
                                 `WEDYGM` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外耳道与骨膜',
                                 `WB` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外鼻',
                                 `XJ` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '嗅觉',
                                 `BTT` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '扁桃体',
                                 PRIMARY KEY (`id`) USING BTREE,
                                 UNIQUE INDEX `studentId_termId`(`student_id` ASC, `term_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '学生耳鼻喉纪录表' ROW_FORMAT = Dynamic;

CREATE TABLE `h_nei_record`  (
                                 `id` bigint NOT NULL,
                                 `create_time` datetime NULL DEFAULT NULL,
                                 `student_id` bigint NULL DEFAULT NULL COMMENT '学生ID',
                                 `clazz_id` bigint NULL DEFAULT NULL COMMENT '班级ID',
                                 `school_id` bigint NULL DEFAULT NULL COMMENT '学校ID',
                                 `term_id` bigint NULL DEFAULT NULL COMMENT '学期ID',
                                 `XZ` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '心脏',
                                 `GZ` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '肝脏',
                                 `PZ` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '脾脏',
                                 `FB` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '肺布',
                                 `XY` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '血压',
                                 `FHL` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '肺活量',
                                 PRIMARY KEY (`id`) USING BTREE,
                                 UNIQUE INDEX `studentId_termId`(`student_id` ASC, `term_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '学生内科纪录表' ROW_FORMAT = Dynamic;

CREATE TABLE `h_wai_record`  (
                                 `id` bigint NOT NULL,
                                 `create_time` datetime NULL DEFAULT NULL,
                                 `student_id` bigint NULL DEFAULT NULL COMMENT '学生ID',
                                 `clazz_id` bigint NULL DEFAULT NULL COMMENT '班级ID',
                                 `school_id` bigint NULL DEFAULT NULL COMMENT '学校ID',
                                 `term_id` bigint NULL DEFAULT NULL COMMENT '学期ID',
                                 `TB` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '头部',
                                 `JB` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '颈部',
                                 `JZ` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '脊柱',
                                 `SZ` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '四肢',
                                 `PF` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '皮肤',
                                 `LBJ` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '淋巴结',
                                 PRIMARY KEY (`id`) USING BTREE,
                                 UNIQUE INDEX `studentId_termId`(`student_id` ASC, `term_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '学生外科纪录表' ROW_FORMAT = Dynamic;

