<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.region.business.mapper.VisionRecordMapper">

    <select id="getPageData" resultType="com.shida.region.business.pojo.vo.VisionRecordVo">
        select h.*,
               su.XM studentName,
               su.XJH cardNum,
               su.SFZH card,
               su.XB sex  from h_vision_record h
        INNER JOIN t_student su ON su.id=h.student_id
        inner join t_school sc on su.school_id = sc.id
        inner join t_clazz cla on su.clazz_id = cla.id
        <where>
            <if test="search.termId != null and search.termId != ''">
                h.term_id=#{search.termId}
            </if>
            <if test="search.keywords != null and search.keywords != ''">
                AND ( su.XM LIKE CONCAT('%',#{search.keywords},'%') OR su.sfzh LIKE CONCAT('%',#{search.keywords},'%') OR su.xjh LIKE CONCAT('%',#{search.keywords},'%') )
            </if>
            <if test="search.schoolId != null and search.schoolId != ''">
                AND su.school_id = #{search.schoolId}
            </if>
            <if test="search.clazzId != null and search.clazzId != ''">
                AND su.clazz_id = #{search.clazzId}
            </if>
            <if test="search.period != null and search.period != ''">
                AND cla.period = #{search.period}
            </if>
            <if test="search.gradeId != null and search.gradeId != ''">
                AND cla.grade_id = #{search.gradeId}
            </if>
            <if test="search.studentStatus != null and search.studentStatus != ''">
                AND su.status = #{search.studentStatus}
            </if>
        </where>

    </select>
    <select id="getViSionList" resultType="com.shida.region.business.pojo.vo.VisionRecordVo">
        select h.*,
               su.XM studentName,
               su.XJH cardNum,
               su.SFZH card,
               su.XB sex  from h_vision_record h
        INNER JOIN t_student su ON su.id=h.student_id
        inner join t_school sc on su.school_id = sc.id
        inner join t_clazz cla on su.clazz_id = cla.id
        <where>
            <if test="search.termId != null and search.termId != ''">
                h.term_id=#{search.termId}
            </if>
            <if test="search.keywords != null and search.keywords != ''">
                AND ( su.XM LIKE CONCAT('%',#{search.keywords},'%') OR su.sfzh LIKE CONCAT('%',#{search.keywords},'%') OR su.xjh LIKE CONCAT('%',#{search.keywords},'%') )
            </if>
            <if test="search.schoolId != null and search.schoolId != ''">
                AND su.school_id = #{search.schoolId}
            </if>
            <if test="search.clazzId != null and search.clazzId != ''">
                AND su.clazz_id = #{search.clazzId}
            </if>
            <if test="search.period != null and search.period != ''">
                AND cla.period = #{search.period}
            </if>
            <if test="search.gradeId != null and search.gradeId != ''">
                AND cla.grade_id = #{search.gradeId}
            </if>
            <if test="search.studentStatus != null and search.studentStatus != ''">
                AND su.status = #{search.studentStatus}
            </if>
        </where>

    </select>

    <select id="getExportStu" resultType="com.shida.region.business.pojo.vo.VisionRecordVo">
    select
    su.id studentId,
    su.XM studentName,
    su.XJH cardNum,
    su.SFZH card,
    su.XB sex,
    cla.name className,
    sc.name schoolName
    from t_student su
    inner join t_school sc on su.school_id = sc.id
    inner join t_clazz cla on su.clazz_id = cla.id
    <where>
        <if test="search.termId != null and search.termId != ''">
            h.term_id=#{search.termId}
        </if>
        <if test="search.keywords != null and search.keywords != ''">
            AND ( su.XM LIKE CONCAT('%',#{search.keywords},'%') OR su.sfzh LIKE CONCAT('%',#{search.keywords},'%') OR su.xjh LIKE CONCAT('%',#{search.keywords},'%') )
        </if>
        <if test="search.schoolId != null and search.schoolId != ''">
            AND su.school_id = #{search.schoolId}
        </if>
        <if test="search.clazzId != null and search.clazzId != ''">
            AND su.clazz_id = #{search.clazzId}
        </if>
        <if test="search.period != null and search.period != ''">
            AND cla.period = #{search.period}
        </if>
        <if test="search.gradeId != null and search.gradeId != ''">
            AND cla.grade_id = #{search.gradeId}
        </if>
        <if test="search.studentStatus != null and search.studentStatus != ''">
            AND su.status = #{search.studentStatus}
        </if>
    </where>
    </select>
</mapper>
