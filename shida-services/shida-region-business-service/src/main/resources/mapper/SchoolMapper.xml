<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.region.business.mapper.SchoolMapper">

    <select id="getPageData" resultType="com.shida.region.business.entity.School">
        SELECT id, name, code, description, region_id, period, contacts, telephone, school_type
        FROM t_school
        <where>
            <if test="search.keywords != null and search.keywords != ''">
                AND ( `name` LIKE CONCAT('%',#{search.keywords},'%') OR `code` LIKE CONCAT('%',#{search.keywords},'%') )
            </if>
            <if test="search.regionId != null">
                AND region_id = #{search.regionId}
            </if>
            <if test="search.schoolType != null">
                AND school_type = #{search.schoolType}
            </if>
            <if test="search.schoolPeriod!=null">
                AND `period` = #{search.schoolPeriod}
            </if>
        </where>
        ORDER BY id DESC
    </select>
</mapper>
