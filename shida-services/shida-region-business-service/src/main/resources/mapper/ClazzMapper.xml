<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.region.business.mapper.ClazzMapper">

    <select id="getPageData" resultType="com.shida.region.business.pojo.vo.ClazzInfo">
        SELECT t1.id,
        t1.name,
        t1.code,
        t1.grade_id,
        t1.enroll_year,
        t1.school_id,
        t1.status,
        t1.period,
        t2.region_id,
        t2.name AS school_name,
        t2.period AS school_period,
        t2.school_type AS school_type,
        t3.teacher_id AS master_teacher_id,
        t5.jsxm as master_teacher_name,
        t5.sjhm as master_teacher_phone,
        t4.teacher_id AS sport_teacher_id,
        t6.jsxm as sport_teacher_name,
        t6.sjhm as sport_teacher_phone,
        (SELECT count(1) FROM t_student ts WHERE ts.clazz_id = t1.id) AS student_count
        FROM t_clazz t1
        LEFT JOIN t_school t2 ON t1.school_id = t2.id
        LEFT JOIN t_teacher_clazz t3 ON t1.id = t3.clazz_id AND t3.type = 1
        LEFT JOIN t_teacher_clazz t4 ON t1.id = t4.clazz_id AND t4.type = 2
        LEFT JOIN t_teacher t5 ON t3.teacher_id = t5.id
        LEFT JOIN t_teacher t6 ON t4.teacher_id = t6.id
        <where>
            <if test="search.schoolPeriod != null">
                AND t2.period = #{search.schoolPeriod}
            </if>
            <if test="search.gradeId != null">
                AND t1.grade_id = #{search.gradeId}
            </if>
            <if test="search.schoolId != null">
                AND t1.school_id = #{search.schoolId}
            </if>
        </where>
        ORDER BY t1.id DESC
    </select>
</mapper>
