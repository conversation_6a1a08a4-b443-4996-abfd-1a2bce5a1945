<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.region.business.mapper.JieHeBingRecordMapper">
    <select id="getPageData" resultType="com.shida.region.business.pojo.vo.StudentJieHeBingInfo">
        select jhb.id,jhb.term_id,jhb.school_id,jhb.clazz_id,jhb.sfjc,jhb.sffb,jhb.create_time,
        su.user_id,su.XM,su.SFZH,su.XJH,su.XB,
        sc.name as schoolName,
        cla.name as clazzName,
        cla.period,
        sc.school_type,
        cla.grade_id,
        cla.id as clazzId
        from h_jie_he_bing_record jhb left join t_student su on jhb.student_id=su.id
        left join t_school sc on su.school_id = sc.id left join t_clazz cla on jhb.clazz_id = cla.id
        <where>
            <if test="search.keywords != null and search.keywords != ''">
                AND ( su.XM LIKE CONCAT('%',#{search.keywords},'%') OR su.sfzh LIKE CONCAT('%',#{search.keywords},'%') OR su.xjh LIKE CONCAT('%',#{search.keywords},'%') )
            </if>
            <if test="search.schoolId != null">
                AND jhb.school_id = #{search.schoolId}
            </if>
            <if test="search.clazzId != null">
                AND jhb.clazz_id = #{search.clazzId}
            </if>
            <if test="search.period != null">
                AND cla.period = #{search.period}
            </if>
            <if test="search.gradeId != null">
                AND cla.grade_id = #{search.gradeId}
            </if>
            <if test="search.termId != null">
                AND jhb.term_id = #{search.termId}
            </if>
        </where>
    </select>
</mapper>
