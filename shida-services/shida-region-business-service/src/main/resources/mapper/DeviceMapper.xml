<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.region.business.mapper.DeviceMapper">

    <select id="getPageData" resultType="com.shida.region.business.pojo.vo.DeviceInfo">
        SELECT t1.id,
        t1.device_id,
        t1.device_name,
        t1.project_name,
        t1.school_id,
        t1.location,
        t1.online_status,
        t1.description,
        t1.create_time,
        t2.name AS school_name
        FROM t_device t1
        LEFT JOIN t_school t2 ON t1.school_id = t2.id
        <where>
            <if test="search.schoolId != null">
                AND t1.school_id = #{search.schoolId}
            </if>
            <if test="search.onlineStatus != null">
                AND t1.online_status = #{search.onlineStatus}
            </if>
            <if test="search.keywords != null and search.keywords != ''">
                AND (t1.device_name LIKE CONCAT('%', #{search.keywords}, '%')
                OR t1.device_id LIKE CONCAT('%', #{search.keywords}, '%'))
            </if>
            <if test="search.sportProjectCode != null and search.sportProjectCode!=''">
                AND t1.project_name = #{search.sportProjectCode}
            </if>
        </where>
        ORDER BY t1.id DESC
    </select>
</mapper>
