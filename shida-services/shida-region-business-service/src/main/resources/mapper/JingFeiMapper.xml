<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.region.business.mapper.JingFeiMapper">

    <select id="getPageData" resultType="com.shida.region.business.pojo.vo.JingFeiInfo">
        SELECT
        hj.id ,
        hj.operator,
        hj.operator_time,
        hj.ZJF,
        hj.RJJF ,
        hj.school_id,
        hj.student_count,
        hj.term_id
        FROM
        h_jing_fei hj
        <where>
            <if test="search.termId != null">
                AND term_id = #{search.termId}
            </if>
            <if test="search.schoolId != null">
                AND school_id = #{search.schoolId}
            </if>
        </where>
        ORDER BY id DESC
    </select>

    <select id="getPageDataNoPage" resultType="com.shida.region.business.pojo.vo.JingFeiInfo">
        SELECT
        hj.id ,
        hj.operator,
        hj.operator_time,
        hj.Z<PERSON>,
        hj.RJJF ,
        hj.school_id,
        hj.student_count,
        hj.term_id
        FROM
        h_jing_fei hj
        <where>
            <if test="search.termId != null">
                AND term_id = #{search.termId}
            </if>
            <if test="search.schoolId != null">
                AND school_id = #{search.schoolId}
            </if>
            <if test="search.termIds != null and search.termIds.size > 0">
                ADN hj.term_id in
                <foreach collection="termIds" item="termId" open="(" close=")" separator=",">
                    #{termId}
                </foreach>
            </if>
        </where>
        ORDER BY id DESC
    </select>

</mapper>
