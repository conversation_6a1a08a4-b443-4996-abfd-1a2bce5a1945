<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.region.business.mapper.TeacherMapper">

    <select id="getPageData" resultType="com.shida.region.business.pojo.vo.TeacherInfo">
        select t.id,t.school_id,t.user_id,t.jsxm,t.jslb,t.jsxb,t.sfzh,t.jstx,t.sjhm,t.create_time,s.name as schoolName
        from t_teacher t left join t_school s on t.school_id = s.id
        <where>
            <if test="search.keywords != null and search.keywords != ''">
                AND ( t.jsxm LIKE CONCAT('%',#{search.keywords},'%') OR t.sfzh LIKE CONCAT('%',#{search.keywords},'%') )
            </if>
            <if test="search.jslb != null">
                AND t.jslb like CONCAT('%',#{search.jslb},'%')
            </if>
            <if test="search.schoolId != null">
                AND t.school_id = #{search.schoolId}
            </if>

        </where>
    </select>
    <select id="getClazzList" resultType="com.shida.region.business.entity.TeacherClazz">
        select tc.clazz_id,c.name AS clazzName ,tc.teacher_id from t_clazz c left join t_teacher_clazz tc on c.id = tc.clazz_id where tc.teacher_id in
        <foreach collection="teacherIds" item="teacherId" open="(" close=")" separator=",">
            #{teacherId}
        </foreach>
    </select>
</mapper>
