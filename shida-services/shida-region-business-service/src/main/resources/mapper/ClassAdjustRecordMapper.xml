<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.region.business.mapper.ClassAdjustRecordMapper">

    <select id="getPageData" resultType="com.shida.region.business.pojo.vo.ClassAdjustRecordInfo">
        select
        bcar.id,
        su.id as studentId,
        su.XM,
        su.XJH,
        su.XB,
        su.status as studentStatus,
        su.clazz_id,
        su.is_clazz_adjust as isClassAdjust,
        cla.name as clazzName,
        cla.period,
        cla.id as clazzId,
        bcar.adjust_date,
        bcar.operator,
        bcar.create_time
        from t_student su inner join t_school sc on su.school_id = sc.id inner join t_clazz cla on su.clazz_id = cla.id
        left join (
        select * from b_clazz_adjust_record b1
        where not exists (
        select 1 from b_clazz_adjust_record b2
        where b2.student_id = b1.student_id
        and b2.create_time > b1.create_time
        )
        ) bcar on bcar.student_id = su.id
        <where>
            <if test="search.keywords != null and search.keywords != ''">
                AND ( su.XM LIKE CONCAT('%',#{search.keywords},'%') OR su.sfzh LIKE CONCAT('%',#{search.keywords},'%') OR su.xjh LIKE CONCAT('%',#{search.keywords},'%') )
            </if>
            <if test="search.schoolId != null">
                AND su.school_id = #{search.schoolId}
            </if>
            <if test="search.clazzId != null">
                AND su.clazz_id = #{search.clazzId}
            </if>
            <if test="search.period != null">
                AND cla.period = #{search.period}
            </if>
            <if test="search.gradeId != null">
                AND cla.grade_id = #{search.gradeId}
            </if>
            <if test="search.studentStatus != null">
                AND su.status = #{search.studentStatus}
            </if>
            <if test="search.isClassAdjust != null and search.isClassAdjust != '' ">
                AND su.is_clazz_adjust = #{search.isClassAdjust}
            </if>
        </where>
    </select>

    <select id="getClazzList" resultType="java.lang.Long">
        select
        cla.grade_id
        from t_student su inner join t_clazz cla on su.clazz_id = cla.id
        WHERE su.id in
        <foreach item="item" collection="search.studentIds" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

</mapper>
