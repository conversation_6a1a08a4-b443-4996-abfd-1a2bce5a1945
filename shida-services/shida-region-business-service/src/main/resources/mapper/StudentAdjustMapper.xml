<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.region.business.mapper.StudentAdjustMapper">

    <select id="getSuspendPageData" resultType="com.shida.region.business.pojo.vo.StudentSuspendInfo">
        select
            sa.id,
                sa.adjust_id,
               sa.xjh, sa.adjust_type,
               sa.adjust_reason as suspendReason,
               sa.adjust_begin_date as suspendBeginDate,
               sa.suspend_end_date as suspendEndDate,
               sa.student_id,
               s.xm as studentName,
               c.name as clazzName,
               sc.name as schoolName,
               s.xb,
               c.grade_id,
               sa.apply_user_id,
               sa.create_time,
               s.sfzh
        from b_student_adjust sa
        inner join t_student s on s.id = sa.student_id
        inner join t_clazz c on c.id = s.clazz_id
        inner join t_school sc on sc.id = s.school_id
        <where>
            <if test="search.adjustType == null or search.adjustType = ''">
                AND (sa.adjust_type = '1' or sa.adjust_type = '2')
            </if>
            <if test="search.adjustType != null and search.adjustType != ''">
                AND sa.adjust_type = #{search.adjustType}
            </if>
            <if test="search.studentName != null and search.studentName != ''">
                AND s.xm LIKE CONCAT('%',#{search.studentName},'%')
            </if>
            <if test="search.xjh != null and search.xjh!=''">
                AND sa.xjh = #{search.xjh}
            </if>
            <if test="search.suspendBeginDate != null and search.suspendBeginDate != ''">
                AND sa.suspend_begin_date = #{search.suspendBeginDate}
            </if>
            <if test="search.schoolId != null">
                AND s.school_id = #{search.schoolId}
            </if>
        </where>
        order by sa.id desc
    </select>
    <select id="getShiWaiOutPageData" resultType="com.shida.region.business.pojo.vo.StudentShiWaiOutInfo">
        select
        sa.id,
        sa.adjust_id,
        sa.xjh, sa.adjust_type,
        sa.adjust_reason as outReason,
        sa.adjust_begin_date as applyDate,
        sa.student_id,
        sa.xb,
        sa.sfzh,
        sa.student_name as studentName,
        sa.apply_user_id,
        sa.create_time,
        sa.into_city_name,
        sa.into_school_name
        from b_student_adjust sa
        <where>
            <if test="search.adjustType == null or search.adjustType = ''">
                AND (sa.adjust_type = '6' )
            </if>
            <if test="search.studentName != null and search.studentName != ''">
                AND sa.student_name LIKE CONCAT('%',#{search.studentName},'%')
            </if>
            <if test="search.xjh != null and search.xjh!=''">
                AND sa.xjh = #{search.xjh}
            </if>
            <if test="search.suspendBeginDate != null and search.suspendBeginDate != ''">
                AND sa.suspend_begin_date = #{search.suspendBeginDate}
            </if>
            <if test="search.schoolId != null">
                AND sa.school_id = #{search.schoolId}
            </if>
        </where>
        order by sa.id desc
    </select>
</mapper>
