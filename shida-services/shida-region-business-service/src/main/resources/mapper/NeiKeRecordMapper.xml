<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.region.business.mapper.NeiKeRecordMapper">

    <select id="getPageData" resultType="com.shida.region.business.pojo.vo.NeiKeInfo">
        SELECT
        hnr.id,
        hnr.term_id AS termId,
        hnr.school_id,
        hnr.clazz_id,
        hnr.XZ,
        hnr.GZ,
        hnr.PZ,
        hnr.FB,
        hnr.XY,
        hnr.FHL,
        ts.XB as gender,
        ts.XJH AS studentNumber,
        ts.XM AS studentName,
        tc.NAME AS clazzName,
        tsc.NAME AS schoolName,
        tsc.region_id AS regionId
        FROM
        h_nei_record hnr
        INNER JOIN t_student ts ON ts.id = hnr.student_id
        LEFT JOIN t_school tsc ON hnr.school_id = tsc.id
        LEFT JOIN t_clazz tc ON hnr.clazz_id = tc.id
        <where>
            <if test="search.schoolId != null">
                AND tsc.id = #{search.schoolId}
            </if>
            <if test="search.gradeId != null">
                AND tc.grade_id = #{search.gradeId}
            </if>
            <if test="search.clazzId != null">
                AND tc.id = #{search.clazzId}
            </if>
            <if test="search.keywords != null and search.keywords != ''">
                AND (ts.XM LIKE CONCAT('%', #{search.keywords}, '%')
                OR ts.XJH LIKE CONCAT('%', #{search.keywords}, '%')
                OR ts.SFZH LIKE CONCAT('%', #{search.keywords}, '%'))
            </if>
        </where>
        ORDER BY hnr.id DESC
    </select>


</mapper>
