<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.region.business.mapper.SportRateRecordMapper">

    <select id="getPageData" resultType="com.shida.region.business.pojo.vo.SportRateInfo">
        SELECT DISTINCT t1.id AS studentId,
        t1.xm AS studentName,
        t1.xb AS xb,
        t1.xjh AS xjh,
        t1.clazz_id AS clazzId,
        t3.`name` AS clazzName,
        t1.school_id AS schoolId,
        t4.NAME AS schoolName,
        t2.sfsb AS sfsb,
        t2.sfcj AS sfcj,
        t2.term_id AS termId,
        t2.xqzks AS xqzks
        from t_student t1
        LEFT JOIN b_sport_rate_record t2 ON t1.id = t2.student_id
        LEFT JOIN t_clazz t3 ON t1.clazz_id = t3.id
        LEFT JOIN t_school t4 ON t1.school_id = t4.id
        <where>
            <if test="search.termId != null">
                AND t2.term_id = #{search.termId}
            </if>
            <if test="search.schoolId != null">
                AND t1.school_id = #{search.schoolId}
            </if>
            <if test="search.clazzId != null">
                AND t1.clazz_id = #{search.clazzId}
            </if>
            <if test="search.sfcj != null">
                AND t2.sfcj = #{search.sfcj}
            </if>
            <if test="search.sfsb != null">
                AND t2.sfsb = #{search.sfsb}
            </if>
            <if test="search.keywords != null and search.keywords != ''">
                AND (t1.xm LIKE CONCAT('%', #{search.keywords}, '%')
                OR t1.xjh LIKE CONCAT('%', #{search.keywords}, '%'))
            </if>
        </where>
        ORDER BY t1.id ASC
    </select>
</mapper>
