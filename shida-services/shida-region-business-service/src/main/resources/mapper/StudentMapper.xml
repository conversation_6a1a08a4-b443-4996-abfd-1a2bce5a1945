<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.region.business.mapper.StudentMapper">

    <select id="getPageData" resultType="com.shida.region.business.pojo.vo.StudentInfo">
        select su.id,
            su.XM,
            su.SFZH,
            su.XJH,
            su.XB,
            su.SJHM,
            su.SJHM,
            su.user_id,
            su.status as studentStatus,
            su.create_time,
            su.JTZZ,
            su.XSTX,
            su.MZ,
            su.school_id,
            su.clazz_id,
            sc.name as schoolName,
            cla.name as clazzName,
            cla.period,
            sc.school_type,
            cla.grade_id,
            cla.id as clazzId
        from t_student su inner join t_school sc on su.school_id = sc.id inner join t_clazz cla on su.clazz_id = cla.id
        <where>
            <if test="search.keywords != null and search.keywords != ''">
                AND ( su.XM LIKE CONCAT('%',#{search.keywords},'%') OR su.sfzh LIKE CONCAT('%',#{search.keywords},'%') OR su.xjh LIKE CONCAT('%',#{search.keywords},'%') )
            </if>
            <if test="search.schoolId != null">
                AND su.school_id = #{search.schoolId}
            </if>
            <if test="search.clazzId != null">
                AND su.clazz_id = #{search.clazzId}
            </if>
            <if test="search.period != null">
                AND cla.period = #{search.period}
            </if>
            <if test="search.gradeId != null">
                AND cla.grade_id = #{search.gradeId}
            </if>
            <if test="search.studentStatus != null">
                AND su.status = #{search.studentStatus}
            </if>
        </where>
    </select>
    <select id="getDetailById" resultType="com.shida.region.business.pojo.vo.StudentInfo">
        select su.id,
            su.XM,
            su.SFZH,
            su.XJH,
            su.XB,
            su.SJHM,
            su.SJHM,
            su.status,
            su.JTZZ,
            su.XSTX,
            su.MZ,
            su.school_id,
            su.clazz_id,
            su.user_id,
            sc.name as schoolName,
            cla.name as clazzName,
            cla.period,
            sc.school_type,
            cla.grade_id
            from t_student su inner join t_school sc on su.school_id = sc.id inner join t_clazz cla on su.clazz_id = cla.id
        where su.id = #{id}
    </select>
    <select id="getDetailByCertNo" resultType="com.shida.region.business.pojo.vo.StudentInfo">
        select su.id,
               su.XM,
               su.SFZH,
               su.XJH,
               su.XB,
               su.SJHM,
               su.SJHM,
               su.status,
               su.JTZZ,
               su.XSTX,
               su.MZ,
               su.school_id,
               su.clazz_id,
               su.user_id,
               sc.name as schoolName,
               cla.name as clazzName,
               cla.period,
               sc.school_type,
               cla.grade_id
        from t_student su inner join t_school sc on su.school_id = sc.id inner join t_clazz cla on su.clazz_id = cla.id
        where su.XJH = #{certNo} or su.sfzh = #{certNo}
    </select>
    <select id="getStudentList" resultType="com.shida.region.business.pojo.vo.StudentInfo">
        select su.id,
               su.XM,
               su.SFZH,
               su.XJH,
               su.XB,
               su.SJHM,
               su.SJHM,
               su.user_id,
               su.status as studentStatus,
               su.create_time,
               su.JTZZ,
               su.XSTX,
               su.MZ,
               su.school_id,
               su.clazz_id,
               sc.name as schoolName,
               cla.name as clazzName,
               cla.period,
               sc.school_type,
               cla.grade_id,
               cla.id as clazzId
        from t_student su inner join t_school sc on su.school_id = sc.id inner join t_clazz cla on su.clazz_id = cla.id
        where su.id in
            <foreach collection="studentIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
    </select>

    <select id="getListByStudentNumber" resultType="com.shida.region.business.pojo.vo.StudentInfo">
        select su.id,
               su.XM,
               su.XJH
        from t_student su
        where su.XJH in
        <foreach collection="numberList" item="number" open="(" close=")" separator=",">
            #{number}
        </foreach>
    </select>


    <select id="getStudentAllList" resultType="com.shida.region.business.pojo.vo.StudentInfo">
        select su.id,
        su.XM,
        su.xjh,
        su.school_id,
        su.clazz_id,
        sc.name as schoolName,
        cla.name as clazzName,
        cla.period,
        cla.grade_id,
        cla.enroll_year,
        cla.id as clazzId
        from t_student su inner join t_school sc on su.school_id = sc.id inner join t_clazz cla on su.clazz_id = cla.id
        <where>
            su.xjh in
            <foreach collection="xjhList" item="xjh" open="(" close=")" separator=",">
                #{xjh}
            </foreach>
            <if test="search.schoolId != null">
                AND su.school_id = #{search.schoolId}
            </if>
        </where>
    </select>
</mapper>
