<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.region.business.mapper.WaiKeRecordMapper">
    <select id="getPageData" resultType="com.shida.region.business.pojo.vo.WaiKeInfo">
        SELECT
        hwr.id,
        hwr.term_id AS termId,
        hwr.school_id,
        hwr.clazz_id,
        hwr.TB,
        hwr.JB,
        hwr.JZ,
        hwr.SZ,
        hwr.XB,
        hwr.PF,
        hwr.LBJ,
        ts.XB as gender,
        ts.XJH AS studentNumber,
        ts.XM AS studentName,
        tc.NAME AS clazzName,
        tsc.NAME AS schoolName,
        tsc.region_id AS regionId
        FROM
        h_wai_record hwr
        INNER JOIN t_student ts ON ts.id = hwr.student_id
        LEFT JOIN t_school tsc ON hwr.school_id = tsc.id
        LEFT JOIN t_clazz tc ON hwr.clazz_id = tc.id
        <where>
            <if test="search.schoolId != null">
                AND tsc.id = #{search.schoolId}
            </if>
            <if test="search.gradeId != null">
                AND tc.grade_id = #{search.gradeId}
            </if>
            <if test="search.clazzId != null">
                AND tc.id = #{search.clazzId}
            </if>
            <if test="search.keywords != null and search.keywords != ''">
                AND (ts.XM LIKE CONCAT('%', #{search.keywords}, '%')
                OR ts.XJH LIKE CONCAT('%', #{search.keywords}, '%')
                OR ts.SFZH LIKE CONCAT('%', #{search.keywords}, '%'))
            </if>
        </where>
        ORDER BY hwr.id DESC
    </select>
</mapper>
