<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.region.business.mapper.MianhuankaoshengMapper">

	<select id="getPageData" resultType="com.shida.region.business.pojo.vo.MianhuankaoshengVo">
        SELECT id, student_name, XJH, sex, clazz_name, school_name, region_name, apply_for_categary,
        	term_limit,county_review_status,city_review_status,update_time,items
        FROM b_mianhuankaosheng
        <where>
            <if test="search.regionId != null">
                AND region_id = #{search.regionId}
            </if>
            <if test="search.schoolId != null">
                AND school_id = #{search.schoolId}
            </if>
            <if test="search.gradeId != null">
                AND grade_id = #{search.gradeId}
            </if>
            <if test="search.clazzId != null">
                AND clazz_id = #{search.clazzId}
            </if>
            <if test="search.applyForCategary != null">
                AND apply_for_categary = #{search.applyForCategary}
            </if>
            <if test="search.items != null and search.items != ''">
                AND items LIKE CONCAT('%',#{search.items},'%')
            </if>
            <if test="search.countyReviewStatus != null">
                AND county_review_status = #{search.countyReviewStatus}
            </if>
            <if test="search.cityReviewStatus != null">
                AND city_review_status = #{search.cityReviewStatus}
            </if>
            <if test="search.keywords != null and search.keywords != ''">
                AND ( student_name LIKE CONCAT('%',#{search.keywords},'%') OR XJH LIKE CONCAT('%',#{search.keywords},'%')
                	OR SFZH LIKE CONCAT('%',#{search.keywords},'%') )
            </if>
        </where>
        ORDER BY update_time DESC
    </select>
    
</mapper>
