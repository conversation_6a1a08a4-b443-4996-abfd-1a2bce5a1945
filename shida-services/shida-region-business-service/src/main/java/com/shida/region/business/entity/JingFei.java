package com.shida.region.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shida.entity.BaseEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 体检经费管理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-04
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("h_jing_fei")
@Schema(name = "HJingFei", description = "体检经费管理")
public class JingFei extends BaseEntity<JingFei> {

    private static final long serialVersionUID = 1L;

    @Schema(description = "操作人")
    private String operator;

    @Schema(description = "操作人ID")
    private Long operatorId;

    @Schema(description = "学期ID")
    private Long termId;

    @Schema(description = "学生人数")
    private Integer studentCount;

    @Schema(description = "体检总经费（元）")
    private BigDecimal zjf;

    @Schema(description = "人均体检费用（元）")
    private BigDecimal rjjf;

    @Schema(description = "学校ID")
    private Long schoolId;

    @Schema(description = "操作时间")
    protected LocalDateTime operatorTime;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
