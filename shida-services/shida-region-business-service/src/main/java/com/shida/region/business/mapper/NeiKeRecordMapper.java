package com.shida.region.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.shida.region.business.entity.NeiKeRecord;
import com.shida.region.business.pojo.search.HealthSearch;
import com.shida.region.business.pojo.vo.NeiKeInfo;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 学生内科纪录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-02
 */
public interface NeiKeRecordMapper extends BaseMapper<NeiKeRecord> {

    /**
     * 列表页查询
     *
     * @param page
     * @param search
     * @return
     */
    IPage<NeiKeInfo> getPageData(IPage<NeiKeInfo> page, @Param("search") HealthSearch search);

}
