package com.shida.region.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shida.AccountCommonService;
import com.shida.enums.RestCode;
import com.shida.exceptions.ServiceException;
import com.shida.pojo.vo.AccountInfo;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.business.entity.JingFei;
import com.shida.region.business.mapper.JingFeiMapper;
import com.shida.region.business.pojo.form.TiJianJingFeiForm;
import com.shida.region.business.pojo.search.HealthSearch;
import com.shida.region.business.pojo.vo.JingFeiInfo;
import com.shida.region.business.service.IJingFeiService;
import com.shida.userCenter.api.IFeignTermService;
import com.shida.userCenter.dto.TermDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 体检经费管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-04
 */
@Service
public class JingFeiServiceImpl extends ServiceImpl<JingFeiMapper, JingFei> implements IJingFeiService {

    @Resource
    private IFeignTermService feignTermService;

    @Override
    public PageInfo<JingFeiInfo> getPageData(HealthSearch search) {
        Page<JingFeiInfo> res = new Page<>();
        PageInfo<TermDto> termLis = feignTermService.getPageData(search.getPageNumber(), search.getPageSize());
        List<JingFeiInfo> allJingFeiList = baseMapper.getPageDataNoPage(search);
        BeanUtil.copyProperties(termLis, res);
        List<JingFeiInfo> jingFeiList = new ArrayList<>();
        List<TermDto> searchTearmList;
        if (search.getTermId() != null) {
            searchTearmList = termLis.getRecords().stream().filter(term -> term.getId().equals(search.getTermId())).collect(Collectors.toList());
        } else {
            searchTearmList = termLis.getRecords();
        }
        searchTearmList.forEach(termDto -> {
            JingFeiInfo jingFeiInfo = new JingFeiInfo();
            jingFeiInfo.setTermId(termDto.getId());
            jingFeiInfo.setTermName(termDto.getTermName());
            Optional<JingFeiInfo> findJingFei = allJingFeiList.stream().filter(jingFei -> jingFei.getTermId().equals(termDto.getId())).findFirst();
            if (findJingFei.isPresent()) {
                JingFeiInfo dbJingFei = findJingFei.get();
                jingFeiInfo.setStudentCount(dbJingFei.getStudentCount());
                jingFeiInfo.setZjf(dbJingFei.getZjf());
                jingFeiInfo.setRjjf(dbJingFei.getRjjf());
                jingFeiInfo.setOperator(dbJingFei.getOperator());
                jingFeiInfo.setOperatorTime(dbJingFei.getOperatorTime());
            }
            jingFeiList.add(jingFeiInfo);
        });
        res.setRecords(jingFeiList);
        return PageInfo.fromMybatisPage(res);
    }

    @Override
    public String updateJingFei(TiJianJingFeiForm form) {
        if (Objects.isNull(form.getTermId())) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "学期ID不能为空");
        }
        LambdaQueryWrapper<JingFei> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(JingFei::getTermId, form.getTermId());
        JingFei jingFei = super.getOne(queryWrapper);
        if (Objects.nonNull(jingFei)) {
            baseMapper.updateById(jingFei);
        } else {
            AccountInfo currentUser = AccountCommonService.getCurrentUser();
            JingFei dbInsert = new JingFei();
            assert currentUser != null;
            dbInsert.setOperator(currentUser.getNickname());
            dbInsert.setOperatorId(currentUser.getId());
            dbInsert.setOperatorTime(LocalDateTime.now());
            dbInsert.setTermId(form.getTermId());
            dbInsert.setSchoolId(currentUser.getSchoolId());
            dbInsert.setStudentCount(form.getStudentCount());
            dbInsert.setRjjf(form.getRjjf());
            baseMapper.insert(dbInsert);
        }
        return "ok";
    }


}
