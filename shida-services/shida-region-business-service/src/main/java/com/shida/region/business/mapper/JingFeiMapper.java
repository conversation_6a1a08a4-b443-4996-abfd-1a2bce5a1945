package com.shida.region.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.shida.region.business.entity.JingFei;
import com.shida.region.business.pojo.search.HealthSearch;
import com.shida.region.business.pojo.vo.JingFeiInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 体检经费管理 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-04
 */
public interface JingFeiMapper extends BaseMapper<JingFei> {

    /**
     * 列表页查询
     *
     * @param page
     * @param search
     * @return
     */
    IPage<JingFeiInfo> getPageData(IPage<JingFeiInfo> page, @Param("search") HealthSearch search);

    List<JingFeiInfo> getPageDataNoPage(@Param("search") HealthSearch search);
}
