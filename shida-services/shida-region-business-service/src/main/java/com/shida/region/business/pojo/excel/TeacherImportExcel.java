package com.shida.region.business.pojo.excel;

import cn.hutool.core.util.StrUtil;
import com.shida.excel.ExcelAlias;
import com.shida.excel.ExcelBatchImportResponse;
import com.shida.region.business.pojo.form.TeacherForm;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.excel
 * @ClassName: TeacherImportExcel
 * @Version 1.0
 * @date 2025/8/14 15:37
 */
@Data
@Accessors(chain = true)
@Schema(name = "TeacherImportExcel", description = "教师导入信息")
public class TeacherImportExcel implements Serializable {
    @ExcelAlias(value = "姓名")
    private String jsxm;
    @ExcelAlias(value = "性别")
    private String jsxb ;
    @ExcelAlias(value =  "账号")
    private String userName ;
    @ExcelAlias(value =  "身份证号")
    private String sfzh ;
    @ExcelAlias(value =  "手机号码")
    private String sjhm ;
    @ExcelAlias(value =  "教师类别")
    private String jslb;
    @ExcelAlias(value = "角色")
    private String roleName;
    @ExcelAlias(value = "所属学校")
    private String shcoolName;

    public List<ExcelBatchImportResponse> validateData(Integer rowIndex) {
        List<ExcelBatchImportResponse> result = new ArrayList<>();
        if (StrUtil.isBlank(jsxm)) {
            result.add(new ExcelBatchImportResponse().setMessage("姓名不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(jsxb)) {
            result.add(new ExcelBatchImportResponse().setMessage("性别不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(userName)) {
            result.add(new ExcelBatchImportResponse().setMessage("账号不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(sfzh)) {
            result.add(new ExcelBatchImportResponse().setMessage("身份证号不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(sjhm)) {
            result.add(new ExcelBatchImportResponse().setMessage("手机号码不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(jslb)) {
            result.add(new ExcelBatchImportResponse().setMessage("教师类别不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(roleName)) {
            result.add(new ExcelBatchImportResponse().setMessage("角色不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(shcoolName)) {
            result.add(new ExcelBatchImportResponse().setMessage("所属学校不能为空").setType("error").setRowIndex(rowIndex));
        }
        return result;
    }
}
