package com.shida.region.business.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shida.AccountCommonService;
import com.shida.enums.RestCode;
import com.shida.exceptions.ServiceException;
import com.shida.pojo.vo.AccountInfo;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.business.entity.StudentAdjust;
import com.shida.region.business.mapper.StudentAdjustMapper;
import com.shida.region.business.pojo.form.StudentSuspendForm;
import com.shida.region.business.pojo.search.StudentAdjustSearch;
import com.shida.region.business.pojo.vo.StudentInfo;
import com.shida.region.business.pojo.vo.StudentShiWaiOutInfo;
import com.shida.region.business.pojo.vo.StudentSuspendInfo;
import com.shida.region.business.service.IStudentAdjustService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shida.region.business.service.IStudentService;
import com.shida.region.business.utils.StudentNameUtil;
import com.shida.userCenter.api.IFeignUserService;
import com.shida.userCenter.dto.UserDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 学籍异动 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-02
 */
@Service
public class StudentAdjustServiceImpl extends ServiceImpl<StudentAdjustMapper, StudentAdjust> implements IStudentAdjustService {
    @Resource
    private IFeignUserService feignUserService;
    @Resource
    private StudentNameUtil studentNameUtil;
    @Resource
    private IStudentService studentService;


    private static final String   APPLYID_PREFIX = "10";
    @Override
    public PageInfo<StudentSuspendInfo> getSuspendPageData(StudentAdjustSearch search) {
        AccountInfo account = AccountCommonService.getCurrentUser();
        IPage<StudentSuspendInfo> page = new Page<>(search.getPageNumber(), search.getPageSize());
        if (Objects.isNull(search.getSchoolId())) {
            search.setSchoolId(account.getSchoolId());
        }
        page = baseMapper.getSuspendPageData(page, search);
        if(page.getRecords().isEmpty()){
            return PageInfo.fromMybatisPage(page, StudentSuspendInfo.class);
        }
        List<Long> userIds = page.getRecords().stream().map(StudentSuspendInfo::getApplyUserId).collect(Collectors.toList());
        List<Long> studentIds = page.getRecords().stream().map(StudentSuspendInfo::getStudentId).collect(Collectors.toList());
        List<StudentInfo> studentList = studentService.getList(studentIds);
        List<UserDto> userList = CollUtil.isNotEmpty(userIds) ? feignUserService.getUserDtoList(userIds) : new ArrayList<>();
        page.getRecords().forEach(item -> {
            StudentInfo student = studentList.stream().filter(s -> s.getId().equals(item.getStudentId())).findFirst().orElse(null);
            item.setGradeName(student.getGradeName());
            item.setClassName(student.getClazzName());
            item.setGradeId(student.getGradeId());
            item.setGradeName(student.getGradeName());
            item.setApplyUserName(userList.stream().filter(u -> u.getId().equals(item.getApplyUserId())).findFirst().orElse(null).getNickname());
        });
        PageInfo<StudentSuspendInfo> result = PageInfo.fromMybatisPage(page, StudentSuspendInfo.class);
        return result;
    }

    @Override
    public PageInfo<StudentShiWaiOutInfo> getShiWaiOutPageData(StudentAdjustSearch search) {
        AccountInfo account = AccountCommonService.getCurrentUser();
        IPage<StudentShiWaiOutInfo> page = new Page<>(search.getPageNumber(), search.getPageSize());
        if (Objects.isNull(search.getSchoolId())) {
            search.setSchoolId(account.getSchoolId());
        }
        page = baseMapper.getShiWaiOutPageData(page, search);
        if(page.getRecords().isEmpty()){
            return PageInfo.fromMybatisPage(page, StudentShiWaiOutInfo.class);
        }
        List<Long> userIds = page.getRecords().stream().map(StudentShiWaiOutInfo::getApplyUserId).collect(Collectors.toList());
        List<UserDto> userList = CollUtil.isNotEmpty(userIds) ? feignUserService.getUserDtoList(userIds) : new ArrayList<>();
        page.getRecords().forEach(item -> {
            item.setApplyUserName(userList.stream().filter(u -> u.getId().equals(item.getApplyUserId())).findFirst().orElse(null).getNickname());
        });
        PageInfo<StudentShiWaiOutInfo> result = PageInfo.fromMybatisPage(page, StudentShiWaiOutInfo.class);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer saveStudentSuspend(StudentSuspendForm form) {
        Long count = baseMapper.selectCount(new LambdaQueryWrapper<StudentAdjust>().eq(StudentAdjust::getXjh, form.getXjh()).eq(StudentAdjust::getAdjustType,"1"));
        if(count > 0){
            throw new ServiceException(RestCode.ARGUMENT_ERROR,"该学生已申请休学");
        }
        AccountInfo account = AccountCommonService.getCurrentUser();
        StudentInfo studentInfo = studentService.getDetailByCertNo(form.getXjh());
        if(studentInfo == null){
            throw new ServiceException(RestCode.ARGUMENT_ERROR,"学生不存在");
        }
        StudentAdjust studentAdjust = new StudentAdjust();
        studentAdjust.setApplyUserId(account.getId());
        String studentAdjustNum = studentNameUtil.getStudentAdjustNum(APPLYID_PREFIX, account.getRegionId());
        studentAdjust.setStudentId(studentInfo.getId());
        studentAdjust.setAdjustType("1");
        if(form.getAttachmentList() != null){
            studentAdjust.setAttachment(form.getAttachmentList().stream().collect(Collectors.joining(",")));
        }
        studentAdjust.setNewRegionId(account.getRegionId()).setOldRegionId(account.getRegionId());
        studentAdjust.setSchoolId(studentInfo.getSchoolId());
        studentAdjust.setStatus(2);
        studentAdjust.setAdjustBeginDate(form.getSuspendBeginDate());
        studentAdjust.setAdjustReason(form.getSuspendReason());
        studentAdjust.setAdjustId(studentAdjustNum);
        studentAdjust.setXjh(form.getXjh());
        studentAdjust.setSfzh(studentInfo.getSfzh());
        studentAdjust.setSfzh(studentInfo.getXm());
        studentAdjust.setXb(studentInfo.getXb());
        studentService.updateStatus(studentInfo.getId(), 3);
        return baseMapper.insert(studentAdjust);
    }

    @Override
    public Integer updateStudentSuspend(StudentSuspendForm form) {
        if (ObjectUtil.isEmpty(form.getClazzId()) || ObjectUtil.isEmpty(form.getGradeId())) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR,"复学年级、班级不能为空");
        }
        if (ObjectUtil.isEmpty(form.getId())) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR,"ID为空");
        }
        AccountInfo account = AccountCommonService.getCurrentUser();
        StudentAdjust studentAdjust = baseMapper.selectById(form.getId());
        if (studentAdjust == null) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR,"休学记录不存在");
        }
        studentAdjust.setAdjustType("2");//复学
        studentAdjust.setAdjustEndDate(form.getSuspendEndDate());
        studentAdjust.setIntoClazzId(form.getClazzId());
        studentAdjust.setIntoGradeId(form.getGradeId());
        studentAdjust.setIntoSchoolId(account.getSchoolId());
        studentService.updateStatus(studentAdjust.getStudentId(), 1);
        return baseMapper.updateById(studentAdjust);
    }
}
