package com.shida.region.business.mapper;

import com.shida.region.business.entity.Mianhuankaosheng;
import com.shida.region.business.pojo.search.MianhuankaoshengSearch;
import com.shida.region.business.pojo.vo.MianhuankaoshengVo;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * <p>
 * 免考缓考生管理表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-04
 */
@Mapper
public interface MianhuankaoshengMapper extends BaseMapper<Mianhuankaosheng> {

	/**
	 * 查询-分页列表
	 * 
	 * @param page
	 * @param search
	 * @return
	 */
	IPage<MianhuankaoshengVo> getPageData(IPage<MianhuankaoshengVo> page,
			@Param("search") MianhuankaoshengSearch search);

}
