package com.shida.region.business.pojo.excel;

import com.shida.excel.ExcelAlias;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 学生体质测试导出Excel实体
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
@Accessors(chain = true)
public class StudentTiZhiCeShiExportExcel {

    @ExcelAlias(value = "序号", orderNo = 1)
    private Integer rowNum;

    @ExcelAlias(value = "学生姓名", orderNo = 2)
    private String studentName;

    @ExcelAlias(value = "学籍号", orderNo = 3)
    private String xjh;

    @ExcelAlias(value = "性别", orderNo = 4)
    private String xb;

    @ExcelAlias(value = "班级名称", orderNo = 5)
    private String clazzName;

    @ExcelAlias(value = "学校名称", orderNo = 6)
    private String schoolName;

    @ExcelAlias(value = "区县名称", orderNo = 7)
    private String regionName;

    @ExcelAlias(value = "是否残疾", orderNo = 8)
    private String sfcj;

    @ExcelAlias(value = "是否伤病", orderNo = 9)
    private String sfsb;

    @ExcelAlias(value = "身高(cm)", orderNo = 10)
    private BigDecimal sg;

    @ExcelAlias(value = "体重(kg)", orderNo = 11)
    private BigDecimal tz;

    @ExcelAlias(value = "BMI指数", orderNo = 12)
    private BigDecimal bmi;

    @ExcelAlias(value = "BMI分数", orderNo = 13)
    private BigDecimal bmiPoints;

    @ExcelAlias(value = "肺活量成绩", orderNo = 14)
    private String fhlResult;

    @ExcelAlias(value = "肺活量分数", orderNo = 15)
    private BigDecimal fhlScore;

    @ExcelAlias(value = "50米跑成绩", orderNo = 16)
    private String wsmResult;

    @ExcelAlias(value = "50米跑分数", orderNo = 17)
    private BigDecimal wsmScore;

    @ExcelAlias(value = "坐位体前屈成绩", orderNo = 18)
    private String zwtqqResult;

    @ExcelAlias(value = "坐位体前屈分数", orderNo = 19)
    private BigDecimal zwtqqScore;

    @ExcelAlias(value = "立定跳远成绩", orderNo = 20)
    private String ldtyResult;

    @ExcelAlias(value = "立定跳远分数", orderNo = 21)
    private BigDecimal ldtyScore;

    @ExcelAlias(value = "引体向上/仰卧起坐成绩", orderNo = 22)
    private String powerResult;

    @ExcelAlias(value = "引体向上/仰卧起坐分数", orderNo = 23)
    private BigDecimal powerScore;

    @ExcelAlias(value = "800米/1000米跑成绩", orderNo = 24)
    private String enduranceResult;

    @ExcelAlias(value = "800米/1000米跑分数", orderNo = 25)
    private BigDecimal enduranceScore;

    @ExcelAlias(value = "总分", orderNo = 26)
    private BigDecimal totalScore;

    @ExcelAlias(value = "等级", orderNo = 27)
    private String grade;
}
