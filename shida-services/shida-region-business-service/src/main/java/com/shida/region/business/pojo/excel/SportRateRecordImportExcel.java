package com.shida.region.business.pojo.excel;

import cn.hutool.core.util.StrUtil;
import com.shida.enums.RestCode;
import com.shida.excel.ExcelAlias;
import com.shida.exceptions.ServiceException;
import com.shida.userCenter.dto.TermDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/8/21 15:52
 */
@Data
@Accessors(chain = true)
@Schema(name = "SportRateRecordImportExcel", description = "体育课出勤记录导入")
public class SportRateRecordImportExcel implements Serializable {
    private static final long serialVersionUID = 1L;

    @ExcelAlias(value = "姓名")
    private String name;
    @ExcelAlias(value = "学籍号")
    private String xjh;
    @ExcelAlias(value = "性别")
    private String gather;
    @ExcelAlias(value = "所属班级")
    private String className;
    @ExcelAlias(value = "所属学校")
    private String schoolName;
    @ExcelAlias(value = "所属区县")
    private String regionName;
    @ExcelAlias(value = "是否残疾免试")
    private String sfcj;
    @ExcelAlias(value = "是否伤病免试")
    private String sfsb;
    @ExcelAlias(value = "所属学期")
    private String ssxq;
    @ExcelAlias(value = "学期总课时")
    private String xqzks;
    /**
     * 动态月份考勤 : key为月份（如"3月"），value为该月考勤
     */
    private Map<String, SportRateRecordMonthAttendance> monthAttendances = new HashMap<>();


    public static SportRateRecordImportExcel readBuilder(List<Object> items, TermDto term) {
        SportRateRecordImportExcel excel = new SportRateRecordImportExcel();
        excel.setName(items.get(1).toString());
        excel.setXjh(items.get(2).toString());
        excel.setGather(items.get(3).toString());
        excel.setClassName(items.get(4).toString());
        excel.setSchoolName(items.get(5).toString());
        excel.setRegionName(items.get(6).toString());
        excel.setSfcj(items.get(7).toString());
        excel.setSfsb(items.get(8).toString());
        excel.setSsxq(items.get(9).toString());
        excel.setXqzks(items.get(10).toString());
        // 拼装月份考勤
        String months = term.getMonths();
        if (StrUtil.isBlank(months)) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "导入失败，学期【" + term.getTermName() + "】未配置月份");
        }
        String[] monthArray = months.split(",");
        int monthTitleSize = monthArray.length * 3;
        Map<String, SportRateRecordMonthAttendance> stringSportRateRecordMonthAttendanceHashMap = new LinkedHashMap<>();
        int monthIndex = 0;
        for (int i = 0; i < monthTitleSize; i += 3) {
            int index = i + 11;
            SportRateRecordMonthAttendance monthAttendance = new SportRateRecordMonthAttendance();
            monthAttendance.setSickLeaveCount(Integer.parseInt(items.get(index).toString()));
            monthAttendance.setPersonalLeaveCount(Integer.parseInt(items.get(index + 1).toString()));
            monthAttendance.setAbsentCount(Integer.parseInt(items.get(index + 2).toString()));
            stringSportRateRecordMonthAttendanceHashMap.put(monthArray[monthIndex], monthAttendance);
            monthIndex++;
        }
        excel.setMonthAttendances(stringSportRateRecordMonthAttendanceHashMap);

        return excel;
    }


    @Data
    public static class SportRateRecordMonthAttendance {
        /**
         * 病假次数
         */
        private Integer sickLeaveCount;
        /**
         * 事假次数
         */
        private Integer personalLeaveCount;
        /**
         * 缺勤次数
         */
        private Integer absentCount;
    }
}
