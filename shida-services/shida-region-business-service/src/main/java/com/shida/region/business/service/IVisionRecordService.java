package com.shida.region.business.service;

import com.shida.excel.ExcelBatchImportResponse;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.business.entity.VisionRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.shida.region.business.pojo.search.TeacherSearch;
import com.shida.region.business.pojo.search.VisionSearch;
import com.shida.region.business.pojo.vo.TeacherInfo;
import com.shida.region.business.pojo.vo.VisionRecordVo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * 学生视力记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
public interface IVisionRecordService extends IService<VisionRecord> {

    /**
     * 学生视力列表
     */
    PageInfo<VisionRecordVo> getPageData(VisionSearch search);

    /**
     * 下载模板
     */
    void downTemplateByVison();
    /**
     * 导入学生
     */
    List<ExcelBatchImportResponse> importDataByVison(MultipartFile file, Long termId);

    /**
     * 导出
     */
    void exportByVison(VisionSearch search);
}
