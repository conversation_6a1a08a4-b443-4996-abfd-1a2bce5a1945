package com.shida.region.business.service;

import com.shida.pojo.vo.PageInfo;
import com.shida.region.business.entity.StudentAdjust;
import com.baomidou.mybatisplus.extension.service.IService;
import com.shida.region.business.pojo.form.StudentSuspendForm;
import com.shida.region.business.pojo.search.StudentAdjustSearch;
import com.shida.region.business.pojo.vo.StudentShiWaiOutInfo;
import com.shida.region.business.pojo.vo.StudentSuspendInfo;

/**
 * <p>
 * 学籍异动 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-02
 */
public interface IStudentAdjustService extends IService<StudentAdjust> {

    PageInfo<StudentSuspendInfo> getSuspendPageData(StudentAdjustSearch search);
    PageInfo<StudentShiWaiOutInfo> getShiWaiOutPageData(StudentAdjustSearch search);

    Integer saveStudentSuspend(StudentSuspendForm form);

    Integer updateStudentSuspend(StudentSuspendForm form);
}
