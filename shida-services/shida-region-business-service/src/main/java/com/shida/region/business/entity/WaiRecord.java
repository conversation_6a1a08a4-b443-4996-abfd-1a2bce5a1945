package com.shida.region.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shida.entity.BaseEntity;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 学生外科纪录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-02
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("h_wai_record")
@Schema(name = "HWaiRecord", description = "学生外科纪录表")
public class WaiRecord extends BaseEntity<WaiRecord> {

    private static final long serialVersionUID = 1L;

    @Schema(description = "学生ID")
    private Long studentId;

    @Schema(description = "班级ID")
    private Long clazzId;

    @Schema(description = "学校ID")
    private Long schoolId;

    @Schema(description = "学期ID")
    private Long termId;

    @Schema(description = "头部")
    private String tb;

    @Schema(description = "颈部")
    private String jb;

    @Schema(description = "脊柱")
    private String jz;

    @Schema(description = "四肢")
    private String sz;

    @Schema(description = "皮肤")
    private String pf;

    @Schema(description = "淋巴结")
    private String lbj;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
