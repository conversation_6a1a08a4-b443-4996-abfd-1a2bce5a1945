package com.shida.region.business.service;

import com.shida.excel.ExcelBatchImportResponse;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.business.entity.Student;
import com.baomidou.mybatisplus.extension.service.IService;
import com.shida.region.business.pojo.form.StudentForm;
import com.shida.region.business.pojo.search.StudentSearch;
import com.shida.region.business.pojo.vo.StudentInfo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * 学生信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
public interface IStudentService extends IService<Student> {

    PageInfo<StudentInfo> getPageData(StudentSearch search);
    List<StudentInfo> getList(List<Long> studentIds);
    Integer saveStudent(StudentForm student);
    Integer updateStudent(StudentForm student);
    Integer updateStatus(Long id, Integer status);
    StudentInfo getDetailById(Long id);
    StudentInfo getDetailByCertNo(String xjhOrSfzh);
    Integer deleteStudent(Long id);
    List<ExcelBatchImportResponse> importStudent(MultipartFile file, Long regoinId);
    void exportStudent(StudentSearch search, HttpServletResponse response);
}
