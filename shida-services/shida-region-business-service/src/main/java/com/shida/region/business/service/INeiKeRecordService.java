package com.shida.region.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.shida.excel.ExcelBatchImportResponse;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.business.entity.NeiKeRecord;
import com.shida.region.business.pojo.search.HealthSearch;
import com.shida.region.business.pojo.vo.NeiKeInfo;
import io.seata.spring.annotation.GlobalTransactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * 学生内科纪录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-02
 */
public interface INeiKeRecordService extends IService<NeiKeRecord> {

    /**
     * 列表页查询数据
     *
     * @param search
     * @return
     */
    PageInfo<NeiKeInfo> getPageData(HealthSearch search);

    /**
     * 批量导入
     *
     * @param file
     * @return
     */
    @GlobalTransactional(rollbackFor = Exception.class)
    List<ExcelBatchImportResponse> importStudent(MultipartFile file);

    void exportStudentNeiKe(HealthSearch search, HttpServletResponse response);
}
