package com.shida.region.business.controller.common;

import com.shida.pojo.vo.KeyValue;
import com.shida.region.business.service.IClazzService;
import com.shida.region.business.service.ISchoolService;
import com.shida.region.business.service.ITeacherService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Tag(name = "common-通用接口")
@RestController
@RequestMapping("/common")
public class CommonController {
    @Resource
    private IClazzService clazzService;
    @Resource
    private ISchoolService schoolService;
    @Resource
    private ITeacherService teacherService;


    @Operation(summary = "获取学校下拉列表")
    @PostMapping("/school/getSchoolSelectList")
    public List<KeyValue<Long, String>> getSchoolSelectList(@RequestHeader("X-Tenant-Id") @Schema(description = "区域id") Long regionId) {
        return schoolService.getSchoolSelectList(regionId);
    }

    @Operation(summary = "获取班级下拉列表")
    @PostMapping("/clazz/getClazzSelectList")
    public List<KeyValue<Long, String>> getClazzSelectList(@RequestHeader("X-Tenant-Id") @Schema(description = "区域id") Long regionId,
                                                           @RequestParam("schoolId") @Schema(description = "学校id") Long schoolId,
                                                           @RequestParam(value = "gradeId", required = false) @Schema(description = "年级id") Long gradeId,
                                                           @RequestParam(value = "period", required = false) @Schema(description = "学段") Long period) {
        return clazzService.getClazzSelectList(schoolId, gradeId,period);
    }


    @Operation(summary = "获取教师列表")
    @PostMapping("/teacher/getTeacherSelectList")
    public List<KeyValue<Long, String>> getTeacherSelectList(@RequestHeader("X-Tenant-Id") @Schema(description = "区域id") Long regionId,
                                                             @RequestParam @Schema(description = "学校id") Long schoolId) {
        return teacherService.getTeacherSelectList(schoolId);
    }
}
