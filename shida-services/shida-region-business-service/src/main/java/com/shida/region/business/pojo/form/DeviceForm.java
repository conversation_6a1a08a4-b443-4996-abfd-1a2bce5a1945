package com.shida.region.business.pojo.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
@Accessors(chain = true)
@Schema(name = "DeviceForm", description = "设备表单")
public class DeviceForm implements Serializable {
    @Schema(description = "设备ID，更新时必填")
    private Long id;

    @Schema(description = "设备ID")
    @NotBlank(message = "设备ID不能为空")
    @Size(max = 64, message = "设备ID长度不能超过64")
    private String deviceId;

    @Schema(description = "设备名称")
    @NotBlank(message = "设备名称不能为空")
    @Size(max = 64, message = "设备名称长度不能超过64")
    private String deviceName;

    @Schema(description = "检测项目")
    @NotBlank(message = "检测项目不能为空")
    @Size(max = 32, message = "检测项目长度不能超过32")
    private String projectName;

    @Schema(description = "所属学校ID")
    @NotNull(message = "所属学校ID不能为空")
    private Long schoolId;

    @Schema(description = "所属区域ID")
    @NotNull(message = "所属区域ID不能为空")
    private Long regionId;

    @Schema(description = "放置位置")
    @Size(max = 128, message = "放置位置长度不能超过128")
    private String location;

    @Schema(description = "设备描述")
    private String description;

}
