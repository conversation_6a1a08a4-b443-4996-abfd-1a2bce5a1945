package com.shida.region.business.service;


import com.shida.region.business.pojo.form.UploadFileForm;
import com.shida.pojo.vo.FileInfoLog;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

public interface IFileService {
    String TEMP_FILE_CACHE_KEY = "file:temp:";
    String DBF_FILE_CACHE_KEY = "file:dbf:";

    FileInfoLog uploadFile(MultipartFile file, UploadFileForm form, String domain);

    String deleteById(Long fileId);

    String deleteByUrl(String url);

    void downloadExcelTemplate(String templateName, String downloadFileName, HttpServletResponse response);

}
