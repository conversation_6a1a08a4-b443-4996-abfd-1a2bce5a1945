package com.shida.region.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shida.entity.BaseEntity;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 学生口腔检查表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("h_kou_qiang_record")
@Schema(name = "KouQiangRecord", description = "学生口腔检查表")
public class KouQiangRecord extends BaseEntity<KouQiangRecord> {

    private static final long serialVersionUID = 1L;
    @Schema(description = "学校ID")
    private Long schoolId;

    @Schema(description = "班级ID")
    private Long clazzId;

    @Schema(description = "学生ID")
    private Long studentId;

    @Schema(description = "学期ID")
    private Long termId;

    @Schema(description = "是否龋齿")
    private Boolean sfqc;

    @Schema(description = "是否牙周疾病")
    private Boolean sfyzjb;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
