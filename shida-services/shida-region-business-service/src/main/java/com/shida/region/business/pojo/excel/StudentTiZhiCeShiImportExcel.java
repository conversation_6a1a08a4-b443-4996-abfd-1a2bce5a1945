package com.shida.region.business.pojo.excel;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 学生体质测试导入Excel实体
 * 使用动态字段来处理不同的体育项目
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
@Accessors(chain = true)
public class StudentTiZhiCeShiImportExcel {

    private String studentName;  // 学生姓名
    private String xjh;          // 学籍号
    private String xb;           // 性别
    private String clazzName;    // 班级名称
    private String sfcj;         // 是否残疾
    private String sfsb;         // 是否伤病
    private BigDecimal sg;       // 身高(cm)
    private BigDecimal tz;       // 体重(kg)

    // 动态体育项目成绩，key为项目代码，value为成绩
    private Map<String, String> projectResults;
}
