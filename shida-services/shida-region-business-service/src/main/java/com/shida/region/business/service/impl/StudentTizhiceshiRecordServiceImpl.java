package com.shida.region.business.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shida.AccountCommonService;
import com.shida.enums.RestCode;
import com.shida.excel.ExcelBatchImportResponse;
import com.shida.excel.ExcelHelper;
import com.shida.exceptions.ServiceException;
import com.shida.pojo.vo.AccountInfo;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.business.entity.StudentTizhiceshiRecord;
import com.shida.region.business.mapper.StudentTizhiceshiRecordMapper;
import com.shida.region.business.pojo.excel.StudentTiZhiCeShiExportExcel;
import com.shida.region.business.pojo.excel.StudentTiZhiCeShiImportExcel;
import com.shida.region.business.pojo.search.StudentTiZhiCeShiSearch;
import com.shida.region.business.pojo.vo.StudentTiZhiCeShiInfo;
import com.shida.region.business.service.IStudentTizhiceshiRecordService;
import com.shida.region.business.tenant.TenantContextHolder;
import com.shida.userCenter.api.IFeignSportProjectService;
import com.shida.userCenter.api.IFeignTermService;
import com.shida.userCenter.dto.OrgDto;
import com.shida.userCenter.dto.SportProjectDto;
import com.shida.userCenter.dto.TermDto;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <p>
 * 体质健康标准测试结果记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Slf4j
@Service
public class StudentTizhiceshiRecordServiceImpl extends ServiceImpl<StudentTizhiceshiRecordMapper, StudentTizhiceshiRecord> implements IStudentTizhiceshiRecordService {

    @Resource
    private IFeignTermService feignTermService;

    @Resource
    private IFeignSportProjectService feignSportProjectService;

    @Resource
    private TenantContextHolder tenantContextHolder;

    @Override
    public PageInfo<StudentTiZhiCeShiInfo> getPageData(StudentTiZhiCeShiSearch search) {
        IPage<StudentTiZhiCeShiInfo> page = new Page<>(search.getPageNumber(), search.getPageSize());
        page = baseMapper.getPageData(page, search);

        // 获取租户信息
        OrgDto orgDto = tenantContextHolder.getTenant();

        // 获取学期信息
        TermDto termDto = feignTermService.getById(search.getTermId());

        // 获取体育项目列表
        List<SportProjectDto> sportProjects = feignSportProjectService.getFitnessTestProjects();

        // 处理每条记录
        for (StudentTiZhiCeShiInfo record : page.getRecords()) {
            // 设置区域和学期信息
            record.setRegionName(orgDto.getName());
            if (termDto != null) {
                record.setTermName(termDto.getTermName());
            }



            // 初始化项目测试结果
            initializeProjectResults(record, sportProjects);

            // 计算总分和等级
            calculateTotalScoreAndGrade(record);
        }

        return PageInfo.fromMybatisPage(page);
    }


    /**
     * 初始化项目测试结果
     */
    private void initializeProjectResults(StudentTiZhiCeShiInfo record, List<SportProjectDto> sportProjects) {
        Map<String, StudentTiZhiCeShiInfo.ProjectTestResult> projectResults = new HashMap<>();

        for (SportProjectDto project : sportProjects) {
            if (!"BMI".equals(project.getCode())) { // BMI已经单独处理
                StudentTiZhiCeShiInfo.ProjectTestResult result = new StudentTiZhiCeShiInfo.ProjectTestResult();
                result.setProjectCode(project.getCode());
                result.setProjectName(project.getName());
                result.setResult("--"); // 默认值，实际应该从数据库查询
                result.setScore(BigDecimal.ZERO);
                result.setGrade("--");
                projectResults.put(project.getCode(), result);
            }
        }

        record.setProjectResults(projectResults);
    }

    /**
     * 计算总分和等级
     */
    private void calculateTotalScoreAndGrade(StudentTiZhiCeShiInfo record) {
        BigDecimal totalScore = BigDecimal.ZERO;
        // 加上其他项目分数
        if (record.getProjectResults() != null) {
            for (StudentTiZhiCeShiInfo.ProjectTestResult result : record.getProjectResults().values()) {
                if (result.getScore() != null) {
                    totalScore = totalScore.add(result.getScore());
                }
            }
        }

        record.setTotalScore(totalScore);

        // 根据总分计算等级
        if (totalScore.compareTo(new BigDecimal("90")) >= 0) {
            record.setGrade("优秀");
        } else if (totalScore.compareTo(new BigDecimal("80")) >= 0) {
            record.setGrade("良好");
        } else if (totalScore.compareTo(new BigDecimal("60")) >= 0) {
            record.setGrade("及格");
        } else {
            record.setGrade("不及格");
        }
    }

    @Override
    public void downloadBatchImportTemplate(HttpServletResponse response, String regionId, Long termId) {
        TermDto termDto = feignTermService.getById(termId);
        if (termDto == null) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "学期信息不存在");
        }

        // 从数据库查询体育项目
        List<SportProjectDto> sportProjects = feignSportProjectService.getFitnessTestProjects();

        ExcelWriter excelWriter = ExcelUtil.getBigWriter();

        // 设置基础表头
        List<String> headers = new ArrayList<>();
        headers.add("学生姓名");
        headers.add("学籍号");
        headers.add("性别");
        headers.add("班级名称");
        headers.add("是否残疾");
        headers.add("是否伤病");
        headers.add("身高(cm)");
        headers.add("体重(kg)");

        // 动态添加体育项目表头
        for (SportProjectDto project : sportProjects) {
            headers.add(project.getName());
        }

        // 写入表头
        excelWriter.writeHeadRow(headers);

        // 导出Excel
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
        try {
            response.setHeader("Content-Disposition", "attachment;filename=" +
                java.net.URLEncoder.encode(termDto.getTermName() + "体质测试导入模板.xlsx", "UTF-8"));
        } catch (Exception e) {
            response.setHeader("Content-Disposition", "attachment;filename=" + termDto.getTermName() + "体质测试导入模板.xlsx");
        }

        try {
            excelWriter.flush(response.getOutputStream(), true);
        } catch (IOException e) {
            log.error("模板下载失败", e);
            throw new ServiceException(RestCode.EXCEL_REPORT_ERROR, "模板下载失败");
        } finally {
            excelWriter.close();
        }
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public List<ExcelBatchImportResponse> batchImport(MultipartFile file, Long termId) {
        AccountInfo accountInfo = AccountCommonService.getCurrentUser();

        // 权限验证
        String schoolName = accountInfo.getSchoolName();
        if (StrUtil.isBlank(schoolName)) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "导入失败，体质测试请使用学校账号导入");
        }

        // 获取合法的学期信息
        TermDto term = feignTermService.getById(termId);
        if (ObjectUtil.isEmpty(term)) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "非法学期");
        }
        if(term.getStatus().equals(3)){
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "当前学期已归档，无法导入学生信息");
        }
        // 读取Excel数据 - 使用通用方式读取
        List<List<Object>> excelData;
        try {
            ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
            excelData = reader.read();
        } catch (Exception e) {
            log.error("读取Excel文件失败", e);
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "读取Excel文件失败");
        }

        if (CollUtil.isEmpty(excelData) || excelData.size() <= 1) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "Excel文件内容为空");
        }

        // 获取表头
        List<Object> headers = excelData.get(0);
        log.info("Excel表头: {}", headers);

        // 获取数据行
        List<List<Object>> dataRows = excelData.subList(1, excelData.size());



        // 这里应该添加数据验证和转换逻辑
        // 暂时返回空，表示导入成功
        return new ArrayList<>();
    }

    @Override
    public boolean deleteData(Long studentId, Long termId) {
        return super.remove(new LambdaQueryWrapper<StudentTizhiceshiRecord>()
                .eq(StudentTizhiceshiRecord::getStudentId, studentId)
                .eq(StudentTizhiceshiRecord::getTermId, termId));
    }

    @Override
    public void exportTiZhiCeShi(StudentTiZhiCeShiSearch search, HttpServletResponse response) {
        OrgDto region = tenantContextHolder.getTenant();
        search.setPageNumber(1).setPageSize(1000);
        PageInfo<StudentTiZhiCeShiInfo> pageInfo;
        ExcelHelper<StudentTiZhiCeShiExportExcel> excelHelper = ExcelHelper.create(StudentTiZhiCeShiExportExcel.class);
        AtomicInteger index = new AtomicInteger(1);

        do {
            pageInfo = this.getPageData(search);
            List<StudentTiZhiCeShiExportExcel> excelList = pageInfo.getRecords().stream().map(info -> {
                StudentTiZhiCeShiExportExcel exportExcel = new StudentTiZhiCeShiExportExcel();
                exportExcel.setRowNum(index.getAndIncrement());
                exportExcel.setStudentName(info.getStudentName());
                exportExcel.setXjh(info.getXjh());
                exportExcel.setXb(info.getXb() != null ? (info.getXb() == 1 ? "男" : "女") : "");
                exportExcel.setClazzName(info.getClazzName());
                exportExcel.setSchoolName(info.getSchoolName());
                exportExcel.setRegionName(info.getRegionName());
                exportExcel.setSfcj(info.getSfcj() != null ? (info.getSfcj() ? "是" : "否") : "");
                exportExcel.setSfsb(info.getSfsb() != null ? (info.getSfsb() ? "是" : "否") : "");
                exportExcel.setSg(info.getSg());
                exportExcel.setTz(info.getTz());
                exportExcel.setTotalScore(info.getTotalScore());
                exportExcel.setGrade(info.getGrade());

                // 设置项目成绩和分数
                if (info.getProjectResults() != null) {
                    info.getProjectResults().forEach((code, result) -> {
                        switch (code) {
                            case "VITAL_CAPACITY":
                                exportExcel.setFhlResult(result.getResult());
                                exportExcel.setFhlScore(result.getScore());
                                break;
                            case "50M_RUN":
                                exportExcel.setWsmResult(result.getResult());
                                exportExcel.setWsmScore(result.getScore());
                                break;
                            case "SIT_AND_REACH":
                                exportExcel.setZwtqqResult(result.getResult());
                                exportExcel.setZwtqqScore(result.getScore());
                                break;
                            case "STANDING_LONG_JUMP":
                                exportExcel.setLdtyResult(result.getResult());
                                exportExcel.setLdtyScore(result.getScore());
                                break;
                            case "PULL_UP":
                            case "SIT_UP":
                                exportExcel.setPowerResult(result.getResult());
                                exportExcel.setPowerScore(result.getScore());
                                break;
                            case "800M_RUN":
                            case "1000M_RUN":
                                exportExcel.setEnduranceResult(result.getResult());
                                exportExcel.setEnduranceScore(result.getScore());
                                break;
                        }
                    });
                }

                return exportExcel;
            }).collect(java.util.stream.Collectors.toList());

            excelHelper.writeData(excelList);
            search.setPageNumber(search.getPageNumber() + 1);
        } while (pageInfo.hasNext());

        excelHelper.writeToWebResponse(response, region.getName() + "-体质测试结果");
    }
}
