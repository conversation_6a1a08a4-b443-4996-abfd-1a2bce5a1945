package com.shida.region.business.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shida.AccountCommonService;
import com.shida.entity.BaseEntity;
import com.shida.enums.RestCode;
import com.shida.enums.TermStatudEnum;
import com.shida.excel.ExcelBatchImportResponse;
import com.shida.exceptions.ServiceException;
import com.shida.pojo.vo.AccountInfo;
import com.shida.pojo.vo.KeyValue;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.business.entity.Clazz;
import com.shida.region.business.entity.BInterclassExerciseRecord;
import com.shida.region.business.entity.Student;
import com.shida.region.business.info.SportDeepCheckDataInfo;
import com.shida.region.business.info.SportDeepCheckExcelDataInfo;
import com.shida.region.business.mapper.BInterclassExerciseRecordMapper;
import com.shida.region.business.pojo.excel.InterclassExerciseRecordImportExcel;
import com.shida.region.business.pojo.search.InterclassExerciseSearch;
import com.shida.region.business.pojo.vo.MonthInterclassExerciseInfo;
import com.shida.region.business.pojo.vo.InterclassExerciseInfo;
import com.shida.region.business.service.IClazzService;
import com.shida.region.business.service.IBInterclassExerciseRecordService;
import com.shida.region.business.service.IStudentService;
import com.shida.region.business.tenant.TenantContextHolder;
import com.shida.userCenter.api.IFeignTermService;
import com.shida.userCenter.dto.OrgDto;
import com.shida.userCenter.dto.TermDto;
import com.shida.utils.FormulaUtils;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <p>
 * 课间操出勤率 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@Service
@Slf4j
public class BInterclassExerciseRecordServiceImpl extends ServiceImpl<BInterclassExerciseRecordMapper, BInterclassExerciseRecord> implements IBInterclassExerciseRecordService {

    @Resource
    private IFeignTermService feignTermService;
    @Resource
    private IClazzService clazzService;
    @Resource
    private IStudentService studentService;
    @Resource
    private TenantContextHolder tenantContextHolder;
    @Resource
    private InterclassExerciseHelper helper;

    @Override
    public PageInfo<InterclassExerciseInfo> getPageData(InterclassExerciseSearch search) {
        IPage<InterclassExerciseInfo> page = new Page<>(search.getPageNumber(), search.getPageSize());
        page = baseMapper.getPageData(page, search);

        List<Long> studentIds = page.getRecords().stream().map(InterclassExerciseInfo::getStudentId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(studentIds)) {
            List<BInterclassExerciseRecord> records = super.list(new LambdaQueryWrapper<BInterclassExerciseRecord>()
                .eq(BInterclassExerciseRecord::getTermId, search.getTermId())
                .in(BInterclassExerciseRecord::getStudentId, studentIds));
            Map<String, List<BInterclassExerciseRecord>> recordMap = records.stream().collect(Collectors.groupingBy(BInterclassExerciseRecord::getYfmc));
            TermDto termDto = feignTermService.getById(search.getTermId());

            OrgDto orgDto = tenantContextHolder.getTenant();
            for (InterclassExerciseInfo record : page.getRecords()) {
                record.setTermName(termDto.getTermName());
                record.setRegionId(orgDto.getId()).setRegionName(orgDto.getName());
                record.setMonthInterclassExerciseInfoList(new ArrayList<>());
                // 总缺勤数
                int totalQqcs = 0;
                for (String month : termDto.getMonths().split(",")) {
                    List<BInterclassExerciseRecord> interclassExerciseRecords = recordMap.get(month);
                    BInterclassExerciseRecord interclassExerciseRecord = null;
                    if (interclassExerciseRecords != null) {
                        interclassExerciseRecord = interclassExerciseRecords.stream().filter(r -> r.getStudentId().equals(record.getStudentId()))
                            .findFirst().orElse(null);
                    }
                    if (interclassExerciseRecord != null) {
                        MonthInterclassExerciseInfo monthInterclassExerciseInfo = new MonthInterclassExerciseInfo();
                        monthInterclassExerciseInfo.setId(interclassExerciseRecord.getId())
                            .setStudentId(interclassExerciseRecord.getStudentId())
                            .setMonth(month)
                            .setBjcs(interclassExerciseRecord.getBjcs())
                            .setSjcs(interclassExerciseRecord.getSjcs())
                            .setQqcs(interclassExerciseRecord.getQqcs());

                        // 存在值计算月份缺勤数加入到总缺勤数
                        log.debug("计算月份缺勤数：{}，病假次数：{}，事假次数：{}，旷课次数：{}，总缺勤数：{}", month, interclassExerciseRecord.getBjcs(), interclassExerciseRecord.getSjcs(), interclassExerciseRecord.getQqcs(), totalQqcs);
                        totalQqcs += FormulaUtils.getQqcs(interclassExerciseRecord.getBjcs(), interclassExerciseRecord.getSjcs(), interclassExerciseRecord.getQqcs());
                        record.getMonthInterclassExerciseInfoList().add(monthInterclassExerciseInfo);
                    } else {
                        record.getMonthInterclassExerciseInfoList().add(
                            new MonthInterclassExerciseInfo()
                                .setId(null)
                                .setStudentId(record.getStudentId())
                                .setMonth(month)
                                .setBjcs(0)
                                .setSjcs(0)
                                .setQqcs(0));
                    }
                }
                // 如果学生伤病/残疾 出勤率显示'--',缺勤天数'--'
                if (record.getSfcj() || record.getSfsb()) {
                    record.setQqs("--");
                    record.setCql("--");
                    // 残疾学生不能参加的，过程性考核成绩按满分记入
                    if (record.getSfcj()) {
                        record.setCqScore(FormulaUtils.PHYSICAL_EDUCATION_SCORE_MIDDLE);
                    }
                    // 伤病学生不能参加的，按该项满分的60%记入
                    if (record.getSfsb()) {
                        record.setCqScore(FormulaUtils.PHYSICAL_EDUCATION_SCORE_SICK);
                    }
                }else {
                    // 计算出勤率，缺勤天数，出勤得分
                    record.setQqs(String.valueOf(totalQqcs));
                    BigDecimal cql = FormulaUtils.getCql(record.getXqzks(), totalQqcs);
                    // 转换为百分比
                    cql = cql.multiply(new BigDecimal(100));
                    record.setCql(String.valueOf(cql));
                    record.setCqScore(FormulaUtils.getPhysicalEducationScore(cql));
                }
            }
        }

        return PageInfo.fromMybatisPage(page);
    }

    @Override
    public void downloadBatchImportTemplate(HttpServletResponse response, String regionId, Long termId) {
        TermDto termDto = feignTermService.getById(termId);
        ExcelWriter excelWriter = ExcelUtil.getBigWriter();
        // 构建 Excel 表头
        List<KeyValue<String, String>> headerList = helper.buildHeaderList(termDto);

        JSONObject jsonObject = new JSONObject();
        for (KeyValue<String, String> header : headerList) {
            excelWriter.addHeaderAlias(header.getKey(), header.getValue());
            jsonObject.set(header.getKey(), "");
        }
        excelWriter.setOnlyAlias(true);
        JSONArray jsonArray = new JSONArray();
        jsonArray.add(jsonObject);
        jsonArray.add(jsonObject);
        jsonArray.add(jsonObject);
        jsonArray.add(jsonObject);
        jsonArray.add(jsonObject);
        excelWriter.write(jsonArray);
        // 合并单元格
        helper.mergeCells(excelWriter, headerList);

        // 导出excel
        helper.exportToResponse(response, excelWriter, termDto.getTermName() + "导入模板.xlsx");
    }

    @Override
    public boolean deleteData(Long studentId, Long termId) {
        // 获取学期+校验学期合法性
        getValidTermById(termId);
        return super.lambdaUpdate().eq(BInterclassExerciseRecord::getStudentId, studentId).remove();
    }

    @Override
    public void exportInterclassExercise(InterclassExerciseSearch search, HttpServletResponse response) {
        // 获取学期信息
        TermDto termDto = feignTermService.getById(search.getTermId());
        // 构建 Excel 表头
        List<KeyValue<String, String>> headerList = helper.buildHeaderList(termDto);
        // 获取数据
        PageInfo<InterclassExerciseInfo> pageData = getPageData(search);
        List<InterclassExerciseInfo> dataRecords = pageData.getRecords();
        // 将业务数据转换为 JSONArray（适配 ExcelWriter 的写入格式）
        JSONArray jsonArray = helper.convertToJsonArray(dataRecords);
        // 初始化 ExcelWriter 并设置表头别名
        ExcelWriter excelWriter = ExcelUtil.getBigWriter();
        // 设置数值起始行
        excelWriter.setCurrentRow(2);
        // 写入数据
        excelWriter.write(jsonArray);
        // 合并单元格（与模板逻辑一致的合并规则）
        helper.mergeCells(excelWriter, headerList);
        // 导出 Excel 到前端
        helper.exportToResponse(response, excelWriter, termDto.getTermName() + "数据导出.xlsx");
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public List<ExcelBatchImportResponse> batchImport(MultipartFile file, Long termId) {
        AccountInfo accountInfo = AccountCommonService.getCurrentUser();

        // 拿到学校
        String schoolName = accountInfo.getSchoolName();
        if (StrUtil.isBlank(schoolName)) {
            log.error("该用户获取学校姓名异常：{}", accountInfo.getUsername());
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "导入失败，课间操出勤率请使用学校或体育老师账号导入");
        }
        // 获取合法的学期信息
        TermDto term = getValidTermById(termId);
        // 读取模板相关信息
        List<List<Object>> readResult = null;
        try {
            ExcelReader excelReader = ExcelUtil.getReader(file.getInputStream());
            readResult = excelReader.read();
        } catch (IOException exception) {
            log.error("读取模板数据发生错误，请稍后重试", exception);
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "读取模板数据发生错误");
        }
        if (CollUtil.isEmpty(readResult)) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "模板数据为空，请下载最新模板");
        }

        // 校验模板标题是否符合标准
        helper.checkExcelTemplate(readResult.get(0), term);

        // 结果集（去标题后结果集）
        List<List<Object>> resultData = readResult.subList(2, readResult.size());
        if (ObjectUtil.isEmpty(resultData)) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "模板内容数据为空，请填写数据");
        }

        // 基础数据检查
        List<ExcelBatchImportResponse> errors = helper.baseCheckExcelData(resultData, accountInfo, term);

        // 深度校验数据的准确性以及合法性（进行数据库层面的校验）
        SportDeepCheckExcelDataInfo deepCheckResultInfo = helper.deepCheckExcelData(resultData, errors, accountInfo);
        // 数据库中学生对象
        Map<String, Student> studentStutusMap = deepCheckResultInfo.getStudentStutusMap();
        // 数据库中的ID对象
        Map<Long, Student> studentIdMap = deepCheckResultInfo.getStudentIdMap();
        // 数据库中班级对象
        Map<String, Clazz> clazzMap = deepCheckResultInfo.getClazzMap();

        if (CollUtil.isNotEmpty(errors)) {
            return errors;
        }

        // 转换为BO对象让数据库存储
        List<BInterclassExerciseRecord> interclassExerciseRecordList = helper.convertInterclassExerciseRecord(resultData, studentStutusMap, clazzMap, term, accountInfo);
        // 先删除原数据
        List<Long> studentIds = studentIdMap.values().stream().map(BaseEntity::getId).collect(Collectors.toList());
        super.lambdaUpdate().in(BInterclassExerciseRecord::getStudentId, studentIds).remove();
        // 导入新数据
        super.saveBatch(interclassExerciseRecordList);

        return null;
    }

    /**
     * 获取合法学期
     *
     * @param termId 学期ID
     * @return 合法学期
     */
    private TermDto getValidTermById(Long termId) {
        TermDto term = feignTermService.getById(termId);
        if (ObjectUtil.isEmpty(term)) {
            log.error("未找到该学期信息，{}", termId);
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "非法学期");
        }
        Integer status = term.getStatus();
        if (TermStatudEnum.ARCHIVE.getCode().equals(status.toString())) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "该学期已经归档，无法进行操作");
        } else if (TermStatudEnum.DISABLED.getCode().equals(status.toString())) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "该学期已经禁用，无法进行操作");
        }
        return term;
    }
}