package com.shida.region.business.service;

import com.shida.excel.ExcelBatchImportResponse;
import com.shida.pojo.vo.KeyValue;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.business.entity.Clazz;
import com.baomidou.mybatisplus.extension.service.IService;
import com.shida.region.business.pojo.form.ClazzForm;
import com.shida.region.business.pojo.search.ClazzSearch;
import com.shida.region.business.pojo.vo.ClazzInfo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 班级信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-03
 */
public interface IClazzService extends IService<Clazz> {

    PageInfo<ClazzInfo> getPageData(ClazzSearch search);

    String create( ClazzForm form);

    String updateClazz( ClazzForm form);

    String deleteByClazzId(Long id);

    List<KeyValue<Long, String>> getClazzSelectList(Long schoolId, Long gradeId,Long period);

    String changeStatus(Long id,Integer status);

    List<ExcelBatchImportResponse> batchImport(MultipartFile file, Long schoolId);

    List<ClazzInfo> getClazzList(List<Long> schoolId, List<Long> gradeId);
}
