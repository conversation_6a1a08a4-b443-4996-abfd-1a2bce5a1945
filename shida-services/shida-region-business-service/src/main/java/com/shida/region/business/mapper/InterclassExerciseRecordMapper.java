package com.shida.region.business.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.shida.region.business.entity.InterclassExerciseRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shida.region.business.pojo.search.InterclassExerciseSearch;
import com.shida.region.business.pojo.vo.InterclassExerciseInfo;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 课间操出勤率 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
public interface InterclassExerciseRecordMapper extends BaseMapper<InterclassExerciseRecord> {

    IPage<InterclassExerciseInfo> getPageData(IPage<InterclassExerciseInfo> page, @Param("search") InterclassExerciseSearch search);
}
