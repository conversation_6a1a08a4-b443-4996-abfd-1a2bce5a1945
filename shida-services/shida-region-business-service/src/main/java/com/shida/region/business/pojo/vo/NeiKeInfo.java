package com.shida.region.business.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@Schema(name = "NeiKeInfo", description = "学生体检-内科纪录")
public class NeiKeInfo implements Serializable {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "学生姓名")
    private String studentName;

    @Schema(description = "学籍号")
    private String studentNumber;

    @Schema(description = "性别")
    private Integer gender;

    @Schema(description = "班级ID")
    private Long clazzId;

    @Schema(description = "所属编辑")
    private String clazzName;

    @Schema(description = "学校ID")
    private Long schoolId;

    @Schema(description = "所属学校")
    private String schoolName;

    @Schema(description = "所属学期ID")
    private Long termId;

    @Schema(description = "所属学期")
    private String termName;

    @Schema(description = "所属区县ID")
    private Long regionId;

    @Schema(description = "所属区县")
    private String regionName;

    @Schema(description = "心脏")
    private String xz;

    @Schema(description = "肝脏")
    private String gz;

    @Schema(description = "脾脏")
    private String pz;

    @Schema(description = "肺布")
    private String fb;

    @Schema(description = "血压")
    private String xy;

    @Schema(description = "肺活量")
    private String fhl;

}
