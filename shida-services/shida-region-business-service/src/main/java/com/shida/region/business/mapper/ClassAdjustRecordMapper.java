package com.shida.region.business.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.shida.region.business.entity.ClassAdjustRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shida.region.business.pojo.search.ClazzAdjustSearch;
import com.shida.region.business.pojo.search.StudentSearch;
import com.shida.region.business.pojo.vo.ClassAdjustRecordInfo;
import com.shida.region.business.pojo.vo.ClazzInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 校内调班管理 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-02
 */
public interface ClassAdjustRecordMapper extends BaseMapper<ClassAdjustRecord> {
    IPage<ClassAdjustRecordInfo> getPageData(IPage<ClassAdjustRecordInfo> page, @Param("search") StudentSearch search);
    List<Long> getClazzList(@Param("search") ClazzAdjustSearch search);
}
