package com.shida.region.business.pojo.excel;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.StrUtil;
import com.shida.constant.SchoolPeriodConstant;
import com.shida.constant.SchoolTypeConstant;
import com.shida.excel.ExcelAlias;
import com.shida.excel.ExcelBatchImportResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@Accessors(chain = true)
@Schema(name = "SchoolImportExcel", description = "班级导入信息")
public class SchoolImportExcel implements Serializable {
    @ExcelAlias(value = "学校名称")
    private String name;
    @ExcelAlias(value = "学校代码")
    private String code;
    @ExcelAlias(value = "所属区县")
    private String regionName;
    @ExcelAlias(value = "学段")
    private String period;
    @ExcelAlias(value = "办学性质")
    private String schoolType;
    @ExcelAlias(value = "联系人")
    private String contacts;
    @ExcelAlias(value = "联系电话")
    private String telephone;
    @ExcelAlias(value = "管理员账号")
    private String adminUsername;
    @ExcelAlias(value = "管理员姓名")
    private String adminRealName;
    @ExcelAlias(value = "管理员手机号")
    private String adminMobile;


    private static List<String> schoolPeriods = CollUtil.newArrayList("小学", "初中", "高中", "九年一贯制", "十二年一贯制", "初高连读");

    public List<ExcelBatchImportResponse> validateData(Integer rowIndex) {
        List<ExcelBatchImportResponse> result = new ArrayList<>();
        if (StrUtil.isBlank(name)) {
            result.add(new ExcelBatchImportResponse().setMessage("学校名称不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(code)) {
            result.add(new ExcelBatchImportResponse().setMessage("学校代码不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(regionName)) {
            result.add(new ExcelBatchImportResponse().setMessage("所属区县不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(period)) {
            result.add(new ExcelBatchImportResponse().setMessage("学校名称不能为空").setType("error").setRowIndex(rowIndex));
        } else if (!SchoolPeriodConstant.SCHOOL_PERIOD_LIST.contains(period)) {
            result.add(new ExcelBatchImportResponse().setMessage("学段只能选择：" + StrUtil.join("，", SchoolPeriodConstant.SCHOOL_PERIOD_LIST) + "中的一个")
                    .setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(schoolType)) {
            result.add(new ExcelBatchImportResponse().setMessage("办学性质不能为空").setType("error").setRowIndex(rowIndex));
        } else if (!SchoolTypeConstant.SCHOOL_TYPE_LIST.contains(schoolType)) {
            result.add(new ExcelBatchImportResponse().setMessage("办学性质只能选择：" + StrUtil.join("，", SchoolTypeConstant.SCHOOL_TYPE_LIST) + "中的一个")
                    .setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(contacts)) {
            result.add(new ExcelBatchImportResponse().setMessage("联系人不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(telephone)) {
            result.add(new ExcelBatchImportResponse().setMessage("联系电话不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(adminUsername)) {
            result.add(new ExcelBatchImportResponse().setMessage("管理员账号不能为空").setType("error").setRowIndex(rowIndex));
        }
        // 管理员账号只能为字母与数字的组合，并且长度在4-16位之间
        else if (!adminUsername.matches("^[a-zA-Z0-9]{4,16}$")) {
            result.add(new ExcelBatchImportResponse().setMessage("管理员账号只能为字母与数字的组合，并且长度在4-16位之间").setType("error").setRowIndex(rowIndex));
        }

        if (StrUtil.isBlank(adminRealName)) {
            result.add(new ExcelBatchImportResponse().setMessage("管理员姓名不能为空").setType("error").setRowIndex(rowIndex));
        }

        if (StrUtil.isBlank(adminMobile)) {
            result.add(new ExcelBatchImportResponse().setMessage("管理员手机号码不能为空").setType("error").setRowIndex(rowIndex));
        } else if (!Validator.isMobile(adminMobile)) {
            result.add(new ExcelBatchImportResponse().setMessage("管理员手机号码格式错误").setType("error").setRowIndex(rowIndex));
        }

        return result;
    }
}
