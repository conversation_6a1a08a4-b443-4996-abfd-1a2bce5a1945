package com.shida.region.business.controller.common;

import com.shida.noRepeatSubmit.NoRepeatSubmit;
import com.shida.region.business.pojo.form.UploadFileForm;
import com.shida.pojo.vo.FileInfoLog;
import com.shida.region.business.service.IFileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 文件管理
 *
 * <AUTHOR>
 */
@Tag(name = "file-文件管理")
@RestController
@RequestMapping("/file/manage")
public class FileManageController {
    @Resource
    private IFileService fileService;


    @NoRepeatSubmit
    @Operation(summary = "上传文件")
    @PostMapping("/upload")
    public FileInfoLog uploadFile(@RequestPart @RequestParam(value = "file") MultipartFile file,
                                  @Valid UploadFileForm form,
                                  HttpServletRequest request) {
        StringBuffer url = request.getRequestURL();
        String domain = url.delete(url.length() - request.getRequestURI().length(), url.length()).append(request.getSession().getServletContext().getContextPath()).toString();
        return fileService.uploadFile(file, form, domain);
    }
}
