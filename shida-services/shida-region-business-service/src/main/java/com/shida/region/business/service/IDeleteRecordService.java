package com.shida.region.business.service;

import com.shida.region.business.entity.DeleteRecord;
import com.baomidou.mybatisplus.extension.service.IService;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 数据删除记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
public interface IDeleteRecordService extends IService<DeleteRecord> {

    <T extends Serializable> String createRecord(Long entityId, Class<T> entityClass, T entity, String entitySignage);

    <T extends Serializable> String createRecord(Long entityId, Class<T> entityClass, T entity, String entitySignage, Long parentEntityId);

    <T extends Serializable> String createRecord(Long entityId, Class<T> entityClass, T entity, String entitySignage, Long parentEntityId, Long userId, String reason);

    <T extends Serializable> String createRecordList(Class<T> entityClass, List<T> entities, String entitySignageFieldName);

    <T extends Serializable> String createRecordList(Class<T> entityClass, List<T> entities, String entitySignageFieldName, List<Long> parentEntityIds);

    <T extends Serializable> String createRecordList(Class<T> entityClass, List<T> entities, String entitySignageFieldName, List<Long> parentEntityIds, Long userId, String reason);

}
