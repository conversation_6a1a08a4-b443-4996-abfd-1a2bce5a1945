package com.shida.region.business.pojo.excel;

import com.shida.excel.ExcelAlias;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.excel
 * @ClassName: StudentSuspendExportExcel
 * @Version 1.0
 * @date 2025/9/5 16:46
 */
@Data
@Accessors(chain = true)
@Schema(name = "StudentShiNeiInExportExcel", description = "导出的市内转入信息")
public class StudentShiNeiInExportExcel {
    @ExcelAlias(value = "序号")
    private Integer rowNum;
    @ExcelAlias(value = "申请ID")
    private String adjustId;
    @ExcelAlias(value = "学生姓名")
    private String studentName;
    @ExcelAlias(value = "学籍号")
    private String xjh;
    @ExcelAlias(value = "性别")
    private String xb;
    @ExcelAlias(value = "原学校")
    private String outSchoolName;
    @ExcelAlias(value = "原年级")
    private String outGradeName;
    @ExcelAlias(value = "原班级")
    private String outClazzName;

    @ExcelAlias(value = "转入年级")
    private String inGradeName;
    @ExcelAlias(value = "转入班级")
    private String intoClazzName;
    @ExcelAlias(value = "申请日期")
    private LocalDate applyDate;
    @ExcelAlias(value = "审核状态")
    private String status;
    @ExcelAlias(value = "操作日期")
    private LocalDate auditDate;
}
