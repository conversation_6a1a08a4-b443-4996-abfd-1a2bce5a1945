package com.shida.region.business.controller.XueShengTiZhi.XueShengTiJian;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.shida.AccountCommonService;
import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.constant.RoleConstant;
import com.shida.enums.RestCode;
import com.shida.excel.ExcelBatchImportResponse;
import com.shida.exceptions.ServiceException;
import com.shida.noRepeatSubmit.NoRepeatSubmit;
import com.shida.pojo.vo.AccountInfo;
import com.shida.pojo.vo.PageInfo;
import com.shida.pojo.vo.RoleInfo;
import com.shida.region.business.pojo.search.HealthSearch;
import com.shida.region.business.pojo.search.StudentSearch;
import com.shida.region.business.pojo.vo.*;
import com.shida.region.business.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;


@Tag(name = "tijian-学生体检管理")
@RestController
@RequestMapping("/tiJian/manage")
public class TiJianManagerController {

    @Resource
    private INeiKeRecordService neiKeRecordService;
    @Resource
    private IWaiKeRecordService waiKeRecordService;
    @Resource
    private IFileService fileService;
    @Resource
    private IKouQiangRecordService kqRecordService;
    @Resource
    private IJieHeBingRecordService jhbRecordService;
    @Resource
    private IEbhRecordService iEbhRecordService;

    @Operation(summary = "获取内科列表")
    @SaCheckPermission("neiKe:list")
    @PostMapping("/getNeiKePageData")
    public PageInfo<NeiKeInfo> getNeiKePageData(@RequestBody HealthSearch search) {
        return neiKeRecordService.getPageData(search);
    }

    @NoRepeatSubmit
    @Operation(summary = "下载内科导入模板")
    @PostMapping("/downloadNeiKeBatchImportTemplate")
    public void downloadNeiKeBatchImportTemplate(HttpServletResponse response) {
        fileService.downloadExcelTemplate("neike-import-template", "学生内科数据批量导入模板", response);
    }


    @NoRepeatSubmit
    @SaCheckPermission("neiKe:export")
    @Operation(summary = "导出内科信息")
    @AutoLog(operateType = OperateType.OPERATE_EXPORT)
    @PostMapping("/exportNeiKe")
    public void batchExportNeiKe(HttpServletResponse response, @RequestBody HealthSearch search) {
        neiKeRecordService.exportStudentNeiKe(search, response);
    }

    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_IMPORT)
    @Operation(summary = "批量导入学生内科信息")
    @SaCheckPermission("neiKe:import")
    @PostMapping("/neiKeBatchImport")
    public List<ExcelBatchImportResponse> neiKeBatchImport(@RequestBody MultipartFile file) {
        AccountInfo currentUser = AccountCommonService.getCurrentUser();
        assert currentUser != null;
        List<RoleInfo> roles = currentUser.getRoles();
        if (roles.stream().anyMatch(role -> role.getRoleCode().equals(RoleConstant.SCHOOL_ADMIN_CODE)
            || (role.getSysRoleCode() != null && role.getSysRoleCode().equals(RoleConstant.SUPER_ADMIN_CODE)))) {
            return neiKeRecordService.importStudent(file);
        } else {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "对不起，只有学校级账号才可以进行此项操作");
        }
    }


    @Operation(summary = "获取外科列表")
    @SaCheckPermission("waiKe:list")
    @PostMapping("/getWaiKePageData")
    public PageInfo<WaiKeInfo> getWaiKePageData(@RequestBody HealthSearch search) {
        return waiKeRecordService.getPageData(search);
    }

    @NoRepeatSubmit
    @Operation(summary = "下载外科导入模板")
    @PostMapping("/downloadWaiKeBatchImportTemplate")
    public void downloadWaiKeBatchImportTemplate(HttpServletResponse response) {
        fileService.downloadExcelTemplate("waike-import-template", "学生外科数据批量导入模板", response);
    }


    @NoRepeatSubmit
    @SaCheckPermission("waiKe:export")
    @Operation(summary = "导出外科信息")
    @AutoLog(operateType = OperateType.OPERATE_EXPORT)
    @PostMapping("/exportWaiKe")
    public void exportWaiKe(HttpServletResponse response, @RequestBody HealthSearch search) {
        waiKeRecordService.exportStudentWaiKe(search, response);
    }

    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_IMPORT)
    @Operation(summary = "批量导入学生外科信息")
    @SaCheckPermission("waiKe:import")
    @PostMapping("/waiKeBatchImport")
    public List<ExcelBatchImportResponse> waiKeBatchImport(@RequestBody MultipartFile file) {
        AccountInfo currentUser = AccountCommonService.getCurrentUser();
        assert currentUser != null;
        List<RoleInfo> roles = currentUser.getRoles();
        if (roles.stream().anyMatch(role -> role.getRoleCode().equals(RoleConstant.SCHOOL_ADMIN_CODE)
            || (role.getSysRoleCode() != null && role.getSysRoleCode().equals(RoleConstant.SUPER_ADMIN_CODE)))) {
            return waiKeRecordService.importStudent(file);
        } else {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "对不起，只有学校级账号才可以进行此项操作");
        }
    }


    @SaCheckPermission("kouQiang:list")
    @Operation(summary = "查询学生口腔检查列表")
    @PostMapping("/getStudentKouQiangPageData")
    public PageInfo<StudentKouQiangInfo> getStudentKouQiangPageData(@RequestBody HealthSearch search) {
        return kqRecordService.getStudentKouQiangPageData(search);
    }

    @NoRepeatSubmit
    @Operation(summary = "下载学生口腔检查导入模板")
    @PostMapping("/downloadStudentKouQiangImportTemplate")
    public void downloadStudentKouQiangImportTemplate(HttpServletResponse response) {
        fileService.downloadExcelTemplate("student-kou-qiang-import-template", "学生口腔检查批量导入模板", response);
    }

    /**
     * 导入学生口腔检查
     *
     * @param file
     * @return
     */
    @SaCheckPermission("kouQiang:import")
    @Operation(summary = "导入学生口腔检查")
    @PostMapping("/importStudentKouQiang")
    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_IMPORT, value = "导入学生口腔检查")
    public List<ExcelBatchImportResponse> importStudentKouQiang(@RequestBody MultipartFile file, @RequestHeader("X-Tenant-Id") Long regionId) {
        return kqRecordService.importStudentKouQiang(file, regionId);
    }

    @NoRepeatSubmit
    @SaCheckPermission("kouQiang:export")
    @Operation(summary = "导出学生口腔检查")
    @AutoLog(operateType = OperateType.OPERATE_EXPORT)
    @PostMapping("/exportStudentKouQiang")
    public void exportStudentKouQiang(HttpServletResponse response, @RequestBody HealthSearch search) {
        kqRecordService.exportStudentKouQiang(search, response);
    }

    @SaCheckPermission("jieHeBing:list")
    @Operation(summary = "查询学生结核病检查列表")
    @PostMapping("/getStudentJieHeBingPageData")
    public PageInfo<StudentJieHeBingInfo> getStudentJieHeBingPageData(@RequestBody HealthSearch search) {
        return jhbRecordService.getStudentJieHeBingPageData(search);
    }

    @NoRepeatSubmit
    @Operation(summary = "下载学生结核病检查导入模板")
    @PostMapping("/downloadStudentJieHeBingImportTemplate")
    public void downloadStudentJieHeBingImportTemplate(HttpServletResponse response) {
        fileService.downloadExcelTemplate("student-jie-he-bing-import-template", "学生结核病检查批量导入模板", response);
    }

    /**
     * 导入学生结核病查
     *
     * @param file
     * @return
     */
    @SaCheckPermission("jieHeBing:import")
    @Operation(summary = "导入学生结核病检查")
    @PostMapping("/importStudentJieHeBing")
    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_IMPORT, value = "导入学生结核病检查")
    public List<ExcelBatchImportResponse> importStudentJieHeBing(@RequestBody MultipartFile file, @RequestHeader("X-Tenant-Id") Long regionId) {
        return jhbRecordService.importStudentJieHeBing(file, regionId);
    }

    @NoRepeatSubmit
    @SaCheckPermission("jieHeBing:export")
    @Operation(summary = "导出学生结核病检查")
    @AutoLog(operateType = OperateType.OPERATE_EXPORT)
    @PostMapping("/exportStudentJieHeBing")
    public void exportStudentJieHeBing(HttpServletResponse response, @RequestBody HealthSearch search) {
        jhbRecordService.exportStudentJieHeBing(search, response);
    }



    @Operation(summary = "获取耳鼻喉列表")
    @SaCheckPermission("ebh:list")
    @PostMapping("/getErBiHouPageData")
    public PageInfo<EbhInfo> getErBiHouPageData(@RequestBody HealthSearch search) {
        return iEbhRecordService.getPageData(search);
    }

    @NoRepeatSubmit
    @Operation(summary = "下载耳鼻喉导入模板")
    @PostMapping("/downloadErBiHouBatchImportTemplate")
    public void downloadErBiHouBatchImportTemplate(HttpServletResponse response) {
        fileService.downloadExcelTemplate("erbihou-import-template", "学生耳鼻喉数据批量导入模板", response);
    }


    @NoRepeatSubmit
    @SaCheckPermission("ebh:export")
    @Operation(summary = "导出耳鼻喉信息")
    @AutoLog(operateType = OperateType.OPERATE_EXPORT)
    @PostMapping("/exportErBiHou")
    public void exportErBiHou(HttpServletResponse response, @RequestBody HealthSearch search) {
        iEbhRecordService.exportStudentEbh(search, response);
    }

    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_IMPORT)
    @Operation(summary = "批量导入学生耳鼻喉信息")
    @SaCheckPermission("ebh:import")
    @PostMapping("/erBiHouBatchImport")
    public List<ExcelBatchImportResponse> erBiHouBatchImport(@RequestBody MultipartFile file) {
        AccountInfo currentUser = AccountCommonService.getCurrentUser();
        assert currentUser != null;
        List<RoleInfo> roles = currentUser.getRoles();
        if (roles.stream().anyMatch(role -> role.getRoleCode().equals(RoleConstant.SCHOOL_ADMIN_CODE)
            || (role.getSysRoleCode() != null && role.getSysRoleCode().equals(RoleConstant.SUPER_ADMIN_CODE)))) {
            return iEbhRecordService.importStudent(file);
        } else {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "对不起，只有学校级账号才可以进行此项操作");
        }
    }

}
