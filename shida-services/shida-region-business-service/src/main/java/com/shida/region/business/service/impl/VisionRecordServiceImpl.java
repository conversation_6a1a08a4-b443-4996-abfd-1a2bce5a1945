package com.shida.region.business.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shida.AccountCommonService;
import com.shida.enums.RestCode;
import com.shida.excel.ExcelBatchImportResponse;
import com.shida.exceptions.ServiceException;
import com.shida.pojo.vo.AccountInfo;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.business.entity.VisionRecord;
import com.shida.region.business.mapper.VisionRecordMapper;
import com.shida.region.business.pojo.search.VisionSearch;
import com.shida.region.business.pojo.vo.VisionRecordVo;
import com.shida.region.business.service.IVisionRecordService;
import com.shida.region.business.utils.ExcelCustomUtil;
import com.shida.userCenter.api.IFeignTermService;
import com.shida.userCenter.dto.TermDto;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 学生视力记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
@Service
public class VisionRecordServiceImpl extends ServiceImpl<VisionRecordMapper, VisionRecord> implements IVisionRecordService {

    @Autowired
    private IFeignTermService feignTermService;
    @Override
    public PageInfo<VisionRecordVo> getPageData(VisionSearch search) {
        List<TermDto> allTermList = feignTermService.getAllTermList();
        allTermList.sort(Comparator.comparing(TermDto::getId).reversed());
        Long termIdByPre = 0L;
        Long termIdByCur = search.getTermId();
        int i = 0;
        for (TermDto termDto : allTermList) {
            if (i == 1) {
                termIdByPre = termDto.getId();
                break;
            }
            if (Objects.equals(termDto.getId(), termIdByCur)) {
                i++;
            }

        }
        IPage<VisionRecordVo> page = new Page<>(search.getPageNumber(), search.getPageSize());
        page = baseMapper.getPageData(page, search);
        List<VisionRecordVo> records = page.getRecords();
        List<Long> studentIds = records.stream().map(VisionRecordVo::getStudentId).collect(Collectors.toList());
        Map<Long, VisionRecord> visionRecordMap = new HashMap<>();
        if (!CollUtil.isEmpty(studentIds)) {
            visionRecordMap = this.list(Wrappers.<VisionRecord>lambdaQuery()
                .eq(VisionRecord::getTermId, termIdByPre)
                .in(VisionRecord::getStudentId, studentIds)
            ).stream().collect(Collectors.toMap(VisionRecord::getStudentId, c -> c, (k1, k2) -> k2));
        }
        for (VisionRecordVo record : records) {
            VisionRecord visionRecord = visionRecordMap.get(record.getStudentId());
            record.setSex(record.getSex().equals("1") ? "男" : "女");
            if (null != visionRecord) {
                String yylyslPre = visionRecord.getYylysl();
                String zylyslPre = visionRecord.getZylysl();
                String yylysl = record.getYylysl();
                String zylysl = record.getZylysl();
                String subBYyylysl = String.valueOf(new BigDecimal(yylysl).subtract(new BigDecimal(yylyslPre)).abs());
                String subBZyylysl = String.valueOf(new BigDecimal(zylysl).subtract(new BigDecimal(zylyslPre)).abs());
                String fuhaoBYyylysl = new BigDecimal(yylysl).compareTo(new BigDecimal(yylyslPre)) > 0 ? "⬆" : new BigDecimal(yylysl).compareTo(new BigDecimal(yylyslPre)) < 0 ? "⬇" : "-";
                String fuhaoBZyylysl = new BigDecimal(zylysl).compareTo(new BigDecimal(zylyslPre)) > 0 ? "⬆" : new BigDecimal(zylysl).compareTo(new BigDecimal(zylyslPre)) < 0 ? "⬇" : "-";
                String yylyslNew = yylysl+"("+fuhaoBYyylysl+subBYyylysl+ ")";
                String zylyslNew = zylysl+"("+fuhaoBZyylysl+subBZyylysl+ ")";
                record.setTermCompareByYylysl(yylyslNew).setTermCompareByZylysl(zylyslNew);
            }
        }
        return PageInfo.fromMybatisPage(page, VisionRecordVo.class);
    }

    @SneakyThrows
    @Override
    public void downTemplateByVison() {
        AccountInfo accountInfo = AccountCommonService.getCurrentUser();
        String regionName = accountInfo.getRegionName();
        VisionSearch search = new VisionSearch();
        if (accountInfo.getSchoolAdminFlag()) {
            search.setSchoolId(accountInfo.getSchoolId());
        }
        List<VisionRecordVo> exportStu = baseMapper.getExportStu(search);
        exportStu.forEach(item->item.setCountyName(regionName));
        exportStu.forEach(item->item.setSex(item.getSex().equals("1") ? "男" : "女"));
        if (CollUtil.isEmpty(exportStu)) {
            exportStu.add(new VisionRecordVo());
        }
        ExcelWriter writer = ExcelUtil.getWriter();
        writer.addHeaderAlias("studentName", "姓名");
        writer.addHeaderAlias("cardNum", "学籍号");
        writer.addHeaderAlias("sex", "性别");
        writer.addHeaderAlias("className", "所属班级");
        writer.addHeaderAlias("schoolName", "所属学校");
        writer.addHeaderAlias("countyName", "所属区县");
        writer.addHeaderAlias("yylysl", "右眼裸眼视力");
        writer.addHeaderAlias("zylysl", "左眼裸眼视力");
        writer.addHeaderAlias("yyqj", "右眼球镜S");
        writer.addHeaderAlias("yyzj", "右眼柱镜C");
        writer.addHeaderAlias("yyzw", "右眼轴位A");
        writer.addHeaderAlias("yyqgd", "右眼屈光度");
        writer.addHeaderAlias("zyqj", "左眼球镜S");
        writer.addHeaderAlias("zyzj", "左眼柱镜C");
        writer.addHeaderAlias("zyzw", "左眼轴位A");
        writer.addHeaderAlias("zyqgd", "左眼屈光度");
        writer.addHeaderAlias("status", "是否为角膜塑形镜（OK镜）佩戴者");
        writer.setOnlyAlias(true);
        writer.autoSizeColumnAll();
        writer.write(exportStu, true);
        int columnCount = writer.getColumnCount();
        ExcelCustomUtil.setSizeColumn(writer.getSheet(), columnCount - 1);
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        assert requestAttributes != null;
        HttpServletResponse respones = requestAttributes.getResponse();

        respones.setContentType("application/octet-stream");
        respones.setHeader("Content-disposition", "attachment;filename="+URLEncoder.encode("学生视力记录模板")+".xls");
        respones.flushBuffer();
        OutputStream output = respones.getOutputStream();
        writer.flush(output, true);
        writer.close();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ExcelBatchImportResponse> importDataByVison(MultipartFile file, Long termId) {
        AccountInfo account = AccountCommonService.getCurrentUser();
        Integer status = feignTermService.getById(termId).getStatus();
        if (status==2) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "该学期已归档,不能导入!");
        }
        Map<String, String> headerAlias = new HashMap<>();
        headerAlias.put("学籍号", "cardNum");
        headerAlias.put("右眼裸眼视力", "yylysl");
        headerAlias.put("左眼裸眼视力", "zylysl");
        headerAlias.put("右眼球镜S", "yyqj");
        headerAlias.put("右眼柱镜C", "yyzj");
        headerAlias.put("右眼轴位A", "yyzw");
        headerAlias.put("右眼屈光度", "yyqgd");
        headerAlias.put("左眼球镜S", "zyqj");
        headerAlias.put("左眼柱镜C", "zyzj");
        headerAlias.put("左眼轴位A", "zyzw");
        headerAlias.put("左眼屈光度", "zyqgd");
        headerAlias.put("是否为角膜塑形镜（OK镜）佩戴者", "status");
        List<VisionRecordVo> visionRecordVoList = ExcelCustomUtil.importExcel(0, file, null, headerAlias, VisionRecordVo.class);
        VisionSearch search = new VisionSearch();
        assert account != null;
        if (account.getSchoolAdminFlag()) {
            search.setSchoolId(account.getSchoolId());
        }
        List<VisionRecordVo> exportStu = baseMapper.getExportStu(search);
        List<String> cardNumList = exportStu.stream().map(VisionRecordVo::getCardNum).collect(Collectors.toList());
        Map<String, Long> getStudentIdMap = exportStu.stream().collect(Collectors.toMap(VisionRecordVo::getCardNum, VisionRecordVo::getStudentId));
        List<ExcelBatchImportResponse> result = new ArrayList<>();
        int i = 2;
        List<String> existList = new ArrayList<>();
        for (VisionRecordVo visionRecordVo : visionRecordVoList) {
            visionRecordVo.setCardNum(ExcelCustomUtil.trimByExcelResult(visionRecordVo.getCardNum(),result,i,"学籍号"));
            visionRecordVo.setYylysl(ExcelCustomUtil.trimByExcelResult(visionRecordVo.getYylysl(),result,i,"右眼裸眼视力"));
            visionRecordVo.setZylysl(ExcelCustomUtil.trimByExcelResult(visionRecordVo.getZylysl(),result,i,"左眼裸眼视力"));
            visionRecordVo.setYyqj(ExcelCustomUtil.trimByExcelResult(visionRecordVo.getYyqj(),result,i,"右眼球镜S"));
            visionRecordVo.setYyzj(ExcelCustomUtil.trimByExcelResult(visionRecordVo.getYyzj(),result,i,"右眼柱镜C"));
            visionRecordVo.setYyzw(ExcelCustomUtil.trimByExcelResult(visionRecordVo.getYyzw(),result,i,"右眼轴位A"));
            visionRecordVo.setYyqgd(ExcelCustomUtil.trimByExcelResult(visionRecordVo.getYyqgd(),result,i,"右眼屈光度"));
            visionRecordVo.setZyqj(ExcelCustomUtil.trimByExcelResult(visionRecordVo.getZyqj(),result,i,"左眼球镜S"));
            visionRecordVo.setZyzj(ExcelCustomUtil.trimByExcelResult(visionRecordVo.getZyzj(),result,i,"左眼柱镜C"));
            visionRecordVo.setZyzw(ExcelCustomUtil.trimByExcelResult(visionRecordVo.getZyzw(),result,i,"左眼轴位A"));
            visionRecordVo.setZyqgd(ExcelCustomUtil.trimByExcelResult(visionRecordVo.getZyqgd(),result,i,"左眼屈光度"));
            visionRecordVo.setStatus(ExcelCustomUtil.trimByExcelResult(visionRecordVo.getStatus(),result,i,"是否为角膜塑形镜（OK镜）佩戴者"));
            visionRecordVo.setTermId(termId);
            visionRecordVo.setStudentId(getStudentIdMap.get(visionRecordVo.getCardNum()));
            visionRecordVo.setCreateTime(LocalDateTime.now());
            visionRecordVo.setCreateBy(account.getUsername());
            if (!cardNumList.contains(visionRecordVo.getCardNum())) {
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中学籍号【" + visionRecordVo.getCardNum() + "】在系统不存在").setRowIndex(i));
            }
            if (existList.contains(visionRecordVo.getCardNum())) {
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中学籍号【" + visionRecordVo.getCardNum() + "】重复").setRowIndex(i));
            }
            existList.add(visionRecordVo.getCardNum());
            i++;
        }
        if (CollUtil.isNotEmpty(result)) {
            return result;
        }
        List<Long> stuIds = visionRecordVoList.stream().map(VisionRecordVo::getStudentId).collect(Collectors.toList());
        this.remove(Wrappers.<VisionRecord>lambdaQuery()
            .eq(VisionRecord::getTermId,termId)
            .in(VisionRecord::getStudentId,stuIds)
        );
        this.saveBatch(Convert.toList(VisionRecord.class, visionRecordVoList));
        return result;
    }

    @SneakyThrows
    @Override
    public void exportByVison(VisionSearch search) {
        AccountInfo accountInfo = AccountCommonService.getCurrentUser();
        String regionName = accountInfo.getRegionName();
        List<VisionRecordVo> visionRecordVoList = baseMapper.getViSionList(search);
        visionRecordVoList.forEach(item->item.setCountyName(regionName).setSex(item.getSex().equals("1") ? "男" : "女"));
        if (CollUtil.isEmpty(visionRecordVoList)) {
            visionRecordVoList.add(new VisionRecordVo());
        }
        ExcelWriter writer = ExcelUtil.getWriter();
        writer.addHeaderAlias("studentName", "姓名");
        writer.addHeaderAlias("cardNum", "学籍号");
        writer.addHeaderAlias("sex", "性别");
        writer.addHeaderAlias("className", "所属班级");
        writer.addHeaderAlias("schoolName", "所属学校");
        writer.addHeaderAlias("countyName", "所属区县");
        writer.addHeaderAlias("yylysl", "右眼裸眼视力");
        writer.addHeaderAlias("zylysl", "左眼裸眼视力");
        writer.addHeaderAlias("yyqj", "右眼球镜S");
        writer.addHeaderAlias("yyzj", "右眼柱镜C");
        writer.addHeaderAlias("yyzw", "右眼轴位A");
        writer.addHeaderAlias("yyqgd", "右眼屈光度");
        writer.addHeaderAlias("zyqj", "左眼球镜S");
        writer.addHeaderAlias("zyzj", "左眼柱镜C");
        writer.addHeaderAlias("zyzw", "左眼轴位A");
        writer.addHeaderAlias("zyqgd", "左眼屈光度");
        writer.addHeaderAlias("status", "是否为角膜塑形镜（OK镜）佩戴者");

        writer.setOnlyAlias(true);
        writer.autoSizeColumnAll();

        writer.write(visionRecordVoList, true);
        int columnCount = writer.getColumnCount();
        ExcelCustomUtil.setSizeColumn(writer.getSheet(), columnCount - 1);
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        assert requestAttributes != null;
        HttpServletResponse respones = requestAttributes.getResponse();

        respones.setContentType("application/octet-stream");
        respones.setHeader("Content-disposition", "attachment;filename="+URLEncoder.encode("学生视力记录")+".xls");
        respones.flushBuffer();
        OutputStream output = respones.getOutputStream();
        writer.flush(output, true);
        writer.close();
    }



}
