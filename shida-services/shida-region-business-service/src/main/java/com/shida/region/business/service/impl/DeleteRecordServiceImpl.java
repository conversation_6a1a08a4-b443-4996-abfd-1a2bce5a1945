package com.shida.region.business.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.shida.AccountCommonService;
import com.shida.pojo.vo.AccountInfo;
import com.shida.region.business.entity.DeleteRecord;
import com.shida.region.business.mapper.DeleteRecordMapper;
import com.shida.region.business.service.IDeleteRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 数据删除记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@Service
public class DeleteRecordServiceImpl extends ServiceImpl<DeleteRecordMapper, DeleteRecord> implements IDeleteRecordService {

    /**
     * 创建数据删除记录
     * @param entityId 实体ID
     * @param entityClass 实体类
     * @param entity 实体对象
     * @param entitySignage 实体标识
     * @return
     * @param <T>
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public <T extends Serializable> String createRecord(Long entityId, Class<T> entityClass, T entity, String entitySignage) {
        AccountInfo currentUser = AccountCommonService.getCurrentUser();
        DeleteRecord deleteRecord = new DeleteRecord();
        deleteRecord.setEntityId(entityId)
            .setEntityJson(Objects.nonNull(entity) ? JSONUtil.toJsonStr(entity) : null)
            .setEntityName(entityClass.getName())
            .setEntitySignage(entitySignage)
            .setOperatorId(Objects.nonNull(currentUser) ? currentUser.getId() : null);
        deleteRecord.insert();
        return "success";
    }

    /**
     * 创建数据删除记录
     * @param entityId 实体ID
     * @param entityClass 实体类
     * @param entity 实体对象
     * @param entitySignage 实体标识
     * @param parentEntityId 父对象实体ID
     * @return
     * @param <T>
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public <T extends Serializable> String createRecord(Long entityId, Class<T> entityClass, T entity, String entitySignage, Long parentEntityId) {
        AccountInfo currentUser = AccountCommonService.getCurrentUser();
        DeleteRecord deleteRecord = new DeleteRecord();
        deleteRecord.setEntityId(entityId)
            .setEntityJson(Objects.nonNull(entity) ? JSONUtil.toJsonStr(entity) : null)
            .setEntityName(entityClass.getName())
            .setEntitySignage(entitySignage)
            .setOperatorId(Objects.nonNull(currentUser) ? currentUser.getId() : null)
            .setParentEntityId(parentEntityId);
        deleteRecord.insert();
        return "success";
    }

    /**
     * 创建数据删除记录
     * @param entityId 实体ID
     * @param entityClass 实体类
     * @param entity 实体对象
     * @param entitySignage 实体标识
     * @param parentEntityId 父对象实体ID
     * @param userId 操作账户ID
     * @param reason 删除原因
     * @return
     * @param <T>
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public <T extends Serializable> String createRecord(Long entityId, Class<T> entityClass, T entity, String entitySignage, Long parentEntityId, Long userId, String reason) {
        DeleteRecord deleteRecord = new DeleteRecord();
        deleteRecord.setEntityId(entityId)
            .setEntityJson(Objects.nonNull(entity) ? JSONUtil.toJsonStr(entity) : null)
            .setEntityName(entityClass.getName())
            .setEntitySignage(entitySignage)
            .setParentEntityId(parentEntityId)
            .setOperatorId(userId)
            .setReason(reason);
        deleteRecord.insert();
        return "success";
    }

    /**
     * 批量添加数据删除记录
     * @param entityClass 实体类
     * @param entities 实体对象列表
     * @param entitySignageFieldName 实体标识字段名
     * @return
     * @param <T>
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public <T extends Serializable> String createRecordList(Class<T> entityClass, List<T> entities, String entitySignageFieldName) {
        if (CollUtil.isEmpty(entities)) {
            return "success";
        }
        AccountInfo currentUser = AccountCommonService.getCurrentUser();
        List<DeleteRecord> deleteRecords = new ArrayList<>();
        for (T entity : entities) {
            if (Objects.isNull(entity)) {
                continue;
            }
            JSONObject entityJson = JSONUtil.parseObj(entity);
            String entitySignage = entityJson.getStr(entitySignageFieldName);
            Long entityId = entityJson.getLong("id");
            DeleteRecord deleteRecord = new DeleteRecord();
            deleteRecord.setEntityId(entityId)
                .setEntityJson(entityJson.toJSONString(0))
                .setEntityName(entityClass.getName())
                .setEntitySignage(entitySignage)
                .setOperatorId(Objects.nonNull(currentUser) ? currentUser.getId() : null);
            deleteRecords.add(deleteRecord);
        }
        super.saveBatch(deleteRecords);
        return "success";
    }

    /**
     * 批量添加数据删除记录
     * @param entityClass 实体类
     * @param entities 实体对象列表
     * @param entitySignageFieldName 实体标识字段名
     * @param parentEntityIds 父对象实体ID列表
     * @return
     * @param <T>
     */
    @Override
    public <T extends Serializable> String createRecordList(Class<T> entityClass, List<T> entities, String entitySignageFieldName, List<Long> parentEntityIds) {
        if (CollUtil.isEmpty(entities)) {
            return "success";
        }
        AccountInfo currentUser = AccountCommonService.getCurrentUser();
        List<DeleteRecord> deleteRecords = new ArrayList<>();
        for (int i = 0; i < entities.size(); i++) {
            if (Objects.isNull(entities.get(i))) {
                continue;
            }
            JSONObject entityJson = JSONUtil.parseObj(entities.get(i));
            String entitySignage = entityJson.getStr(entitySignageFieldName);
            Long entityId = entityJson.getLong("id");
            Long parentEntityId = parentEntityIds.get(i);
            DeleteRecord deleteRecord = new DeleteRecord();
            deleteRecord.setEntityId(entityId)
                .setEntityJson(entityJson.toJSONString(0))
                .setEntityName(entityClass.getName())
                .setEntitySignage(entitySignage)
                .setOperatorId(Objects.nonNull(currentUser) ? currentUser.getId() : null)
                .setParentEntityId(parentEntityId);
            deleteRecords.add(deleteRecord);
        }
        super.saveBatch(deleteRecords);
        return "success";
    }

    /**
     * 批量添加数据删除记录
     * @param entityClass 实体类
     * @param entities 实体对象列表
     * @param entitySignageFieldName 实体标识字段名
     * @param parentEntityIds 父对象实体ID列表
     * @param userId 操作账户ID
     * @param reason 删除原因
     * @return
     * @param <T>
     */
    @Override
    public <T extends Serializable> String createRecordList(Class<T> entityClass, List<T> entities, String entitySignageFieldName, List<Long> parentEntityIds, Long userId, String reason) {
        if (CollUtil.isEmpty(entities)) {
            return "success";
        }
        List<DeleteRecord> deleteRecords = new ArrayList<>();
        for (int i = 0; i < entities.size(); i++) {
            if (Objects.isNull(entities.get(i))) {
                continue;
            }
            JSONObject entityJson = JSONUtil.parseObj(entities.get(i));
            String entitySignage = entityJson.getStr(entitySignageFieldName);
            Long entityId = entityJson.getLong("id");
            Long parentEntityId = parentEntityIds.get(i);
            DeleteRecord deleteRecord = new DeleteRecord();
            deleteRecord.setEntityId(entityId)
                .setEntityJson(entityJson.toJSONString(0) )
                .setEntityName(entityClass.getName())
                .setEntitySignage(entitySignage)
                .setParentEntityId(parentEntityId)
                .setOperatorId(userId)
                .setReason(reason);
            deleteRecords.add(deleteRecord);
        }
        super.saveBatch(deleteRecords);
        return "success";
    }
}
