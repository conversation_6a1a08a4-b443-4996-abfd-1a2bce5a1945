package com.shida.region.business.pojo.form;

import com.shida.pojo.vo.RoleInfo;
import com.shida.region.business.entity.Clazz;
import com.shida.region.business.entity.TeacherClazz;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;
import java.util.TreeSet;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.form
 * @ClassName: TeacherForm
 * @Version 1.0
 * @date 2025/8/12 18:31
 */
@Data
@Accessors(chain = true)
@Schema(name = "TeacherForm", description = "教师表单")
public class TeacherForm implements Serializable {
    @Schema(description = "教师ID")
    private Long id;

    @Schema(description = "学校ID")
    @NotNull
    private Long schoolId;

    @Schema(description = "账户ID")
    private Long userId;

    @Schema(description = "教师姓名")
    @NotBlank(message = "教师姓名不能为空")
    private String jsxm;

    @Schema(description = "教师类型，1班主任，2体育教师")
    private String jslb;
    @Schema(description = "教师类型，1班主任，2体育教师")
    @NotNull(message = "教师类型不能为空")
    private TreeSet<String> jslbList;

    @Schema(description = "身份证号")
    @NotBlank(message = "身份证号不能为空")
    @Size(max = 18, message = "身份证号长度不能超过18")
    @Pattern(regexp = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$", message = "身份证号格式错误")
    private String sfzh;

    @Schema(description = "头像")
    private String jstx;

    @Schema(description = "用户账号")
    @NotBlank(message = "用户账号不能为空")
    @Size(min = 4,max = 20, message = "用户名长度在4-20个字符之间")
    private String userName;

    @Schema(description = "用户状态")
    private Integer status;

    @Schema(description = "手机号码")
    @NotBlank(message = "手机号码不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号码格式错误")
    private String sjhm;

    @Schema(description = "教师性别，1男，2女")
    @NotNull(message = "教师性别不能为空")
    private Integer jsxb;

    @Schema(description = "教师昵称")
    private String nickName;

    @Schema(description = "教师班级关联关系列表")
    private List<Long> clazzIds;

    @Schema(description = "区域ID")
    private Long regionId;
    @Schema(description = "角色列表")
    private List<String> roleCode;

}
