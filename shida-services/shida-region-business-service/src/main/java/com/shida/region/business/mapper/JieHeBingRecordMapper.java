package com.shida.region.business.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.shida.region.business.entity.JieHeBingRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shida.region.business.pojo.search.HealthSearch;
import com.shida.region.business.pojo.vo.StudentJieHeBingInfo;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 学生结核病检查表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
public interface JieHeBingRecordMapper extends BaseMapper<JieHeBingRecord> {
    IPage<StudentJieHeBingInfo> getPageData(IPage<StudentJieHeBingInfo> page, @Param("search") HealthSearch search);
}
