package com.shida.region.business.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@Schema(name = "MonthSportRateInfo", description = "月体育课出勤率")
public class MonthSportRateInfo implements Serializable {
    @Schema(description = "ID")
    private Long id;

    @Schema(description = "学生ID")
    private Long studentId;

    @Schema(description = "月份")
    private String month;

    @Schema(description = "病假次数")
    private Integer bjcs;

    @Schema(description = "事假次数")
    private Integer sjcs;

    @Schema(description = "缺勤次数")
    private Integer qqcs;
}
