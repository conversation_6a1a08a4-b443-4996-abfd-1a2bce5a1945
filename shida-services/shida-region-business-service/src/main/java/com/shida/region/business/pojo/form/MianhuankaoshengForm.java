package com.shida.region.business.pojo.form;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
@Schema(name = "MianhuankaoshengForm", description = "免考缓考生对象接收类")
public class MianhuankaoshengForm implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "学生id")
    @NotNull(message = "学生ID不能为空")
    private Long studentId;

    @Schema(description = "学生姓名")
    private String studentName;

    @Schema(description = "学籍号")
    @NotBlank(message = "学籍号不能为空")
    private String xjh;
    
    @Schema(description = "身份证号")
    @NotBlank(message = "身份证号不能为空")
    private String sfzh;

    @Schema(description = "性别")
    private String sex;

    @Schema(description = "年级id")
    private Long gradeId;
    
    @Schema(description = "班级id")
    private Long clazzId;
    
    @Schema(description = "所在班级名称")
    private String clazzName;

    @Schema(description = "学校id")
    private Long schoolId;
    
    @Schema(description = "所属学校名称")
    private String schoolName;

    @Schema(description = "区县id")
    private Long regionId;
    
    @Schema(description = "所属区县名称")
    private String regionName;

    @Schema(description = "申请类别")
    @NotNull(message = "申请类别不能为空")
    private Integer applyForCategary;

    @Schema(description = "类别情况说明")
    private String categaryDescription;

    @Schema(description = "免考/缓考项目")
    @NotBlank(message = "免考/缓考项目不能为空")
    private String items;

    @Schema(description = "免考/缓考期限")
    @NotBlank(message = "免考/缓考期限不能为空")
    private String termLimit;

    @Schema(description = "佐证材料")
    private String zuozhengResource;

}
