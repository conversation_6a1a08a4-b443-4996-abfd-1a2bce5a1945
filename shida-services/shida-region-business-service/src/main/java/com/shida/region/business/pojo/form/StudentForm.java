package com.shida.region.business.pojo.form;

import com.shida.pojo.vo.RoleInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.form
 * @ClassName: StudentForm
 * @Version 1.0
 * @date 2025/8/19 9:43
 */
@Data
@Accessors(chain = true)
@Schema(name = "StudentForm", description = "学生表单")
public class StudentForm implements Serializable {
    @Schema(description = "学生ID")
    @NotNull
    private Long id;
    @Schema(description = "学校ID")
    private Long schoolId;
    @Schema(description = "账户ID")
    private Long userId;
    @Schema(description = "用户名")
    private String userName;

    @Schema(description = "学生姓名")
    @NotEmpty(message = "学生姓名不能为空")
    private String xm;
    @Schema(description = "学生性别")
    @NotNull(message = "学生性别不能为空")
    private Integer xb;
    @Schema(description = "手机号")
    @NotNull(message = "手机号不能为空")
    @Pattern(regexp = "^1[3456789]\\d{9}$", message = "手机号格式错误")
    private String sjhm;
    @Schema(description = "班级ID")
    @NotNull(message = "班级ID不能为空")
    private Long clazzId;
    @Schema(description = "班级名称")
    private String clazzName;
    @Schema(description = "学校名称")
    private String schoolName;
    @Schema(description = "年级ID")
    @NotNull(message = "年级ID不能为空")
    private Long gradeId;
    @Schema(description = "年级名称")
    private String gradeName;
    @Schema(description = "学生状态,1在读，2毕业，3休学")
    private Integer studentStatus;
    @Schema(description = "账号状态")
    private Integer status;
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    @Schema(description = "区县ID")
    private Long regionId;
    @Schema(description = "学籍号")
    @NotEmpty(message = "学籍号不能为空")
    private String xjh;
    @Schema(description = "身份证号")
    @NotEmpty(message = "身份证号不能为空")
    private String sfzh;
    @Schema(description = "民族")
    private Integer mz;
    @Schema(description = "头像")
    private String xstx;
    @Schema(description = "家庭住址")
    @NotNull(message = "家庭住址不能为空")
    private String jtzz;
    @Schema(description = "学段")
    @NotNull(message = "学段不能为空")
    private Long period;
    @Schema(description = "角色列表")
    private List<RoleInfo> roleList;
    @Schema(description = "角色名称")
    private String roleName;
    @Schema(description = "角色代码")
    private List<String> roleCode;
}
