package com.shida.region.business.service;

import com.shida.excel.ExcelBatchImportResponse;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.business.entity.JieHeBingRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.shida.region.business.pojo.search.HealthSearch;
import com.shida.region.business.pojo.vo.StudentJieHeBingInfo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * 学生结核病检查表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
public interface IJieHeBingRecordService extends IService<JieHeBingRecord> {
    PageInfo<StudentJieHeBingInfo> getStudentJieHeBingPageData(HealthSearch search);
    List<ExcelBatchImportResponse> importStudentJieHeBing(MultipartFile file, Long regoinId);
    void exportStudentJieHeBing(HealthSearch search, HttpServletResponse response);
}
