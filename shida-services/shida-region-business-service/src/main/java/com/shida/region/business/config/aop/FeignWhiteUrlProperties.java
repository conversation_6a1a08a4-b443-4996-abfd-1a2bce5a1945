package com.shida.region.business.config.aop;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.config.aop
 * @ClassName: FeignWhiteUrlProperties
 * @Version 1.0
 * @date 2025/9/4 19:26
 */
@Data
@Component
@ConfigurationProperties(prefix = "white-feign")
public class FeignWhiteUrlProperties {
    private List<String> urls;
}
