package com.shida.region.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shida.entity.BaseEntity;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 教师班级关联关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-10
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_teacher_clazz")
@Schema(name = "TeacherClazz", description = "教师班级关联关系表")
public class TeacherClazz extends BaseEntity<TeacherClazz> {

    private static final long serialVersionUID = 1L;

    @Schema(description = "教师ID")
    private Long teacherId;

    @Schema(description = "班级ID")
    private Long clazzId;

    @Schema(description = "班级名称")
    @TableField(exist = false)
    private String clazzName;
    @Schema(description = "关系类型：1班主任，2体育教师")
    private Integer type;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
