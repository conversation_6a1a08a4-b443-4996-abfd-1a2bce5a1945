package com.shida.region.business.controller.TiYu<PERSON><PERSON><PERSON>;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.excel.ExcelBatchImportResponse;
import com.shida.noRepeatSubmit.NoRepeatSubmit;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.business.pojo.search.StudentTiZhiCeShiSearch;
import com.shida.region.business.pojo.vo.StudentTiZhiCeShiInfo;
import com.shida.region.business.service.IStudentTizhiceshiRecordService;
import com.shida.userCenter.api.IFeignSportProjectService;
import com.shida.userCenter.dto.SportProjectDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

@Tag(name = "StudentTiZhiBiaoZhunCeShiController-学生体质健康标准测试结果记录表")
@RestController
@RequestMapping("/tizhiceshi/manage")
public class StudentTiZhiBiaoZhunCeShiController {

    @Resource
    private IStudentTizhiceshiRecordService studentTizhiceshiRecordService;

    @Resource
    private IFeignSportProjectService feignSportProjectService;

    @SaCheckPermission(value = "tizhiceshi:list")
    @Operation(summary = "获取学生体质健康标准测试结果列表")
    @PostMapping("/getPageData")
    @AutoLog(value = "查询学生体质测试结果", operateType = OperateType.OPERATE_SEARCH)
    public PageInfo<StudentTiZhiCeShiInfo> getPageData(@RequestBody @Valid StudentTiZhiCeShiSearch search) {
        return studentTizhiceshiRecordService.getPageData(search);
    }

    @SaCheckPermission(value = "tizhiceshi:list")
    @Operation(summary = "获取体质测试项目列表")
    @GetMapping("/getSportProjects")
    @AutoLog(value = "获取体质测试项目列表", operateType = OperateType.OPERATE_SEARCH)
    public List<SportProjectDto> getSportProjects() {
        return feignSportProjectService.getFitnessTestProjects();
    }

    @SaCheckPermission(value = "tizhiceshi:import")
    @Operation(summary = "下载导入模板")
    @PostMapping("/downloadBatchImportTemplate")
    public void downloadBatchImportTemplate(HttpServletResponse response,
                                            @RequestHeader("X-Tenant-Id") String regionId,
                                            @RequestParam @Schema(description = "学期ID") Long termId) {
        studentTizhiceshiRecordService.downloadBatchImportTemplate(response, regionId, termId);
    }

    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_IMPORT)
    @SaCheckPermission(value = "tizhiceshi:import")
    @Operation(summary = "批量导入体质测试结果")
    @PostMapping("/batchImport")
    public List<ExcelBatchImportResponse> batchImport(MultipartFile file,
                                                      @RequestParam @Schema(description = "学期ID") Long termId) {
        return studentTizhiceshiRecordService.batchImport(file, termId);
    }

    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_DELETE)
    @SaCheckPermission(value = "tizhiceshi:delete")
    @Operation(summary = "删除学生体质测试数据")
    @PostMapping("/deleteData")
    public boolean deleteData(@RequestParam @Schema(description = "学生ID") Long studentId,
                              @RequestParam @Schema(description = "学期ID") Long termId) {
        return studentTizhiceshiRecordService.deleteData(studentId, termId);
    }

    @SaCheckPermission("tizhiceshi:export")
    @NoRepeatSubmit
    @Operation(summary = "导出体质测试结果列表")
    @PostMapping("/export")
    @AutoLog(value = "导出体质测试结果列表", operateType = OperateType.OPERATE_EXPORT)
    public void export(@RequestBody StudentTiZhiCeShiSearch search, HttpServletResponse response) {
        studentTizhiceshiRecordService.exportTiZhiCeShi(search, response);
    }
}
