package com.shida.region.business.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@Schema(name = "ClazzInfo", description = "班级信息")
public class ClazzInfo implements Serializable {

    @Schema(description = "班级ID")
    private Long id;

    @Schema(description = "班级名称")
    private String name;

    @Schema(description = "班级代码")
    private String code;

    @Schema(description = "年级ID")
    private Long gradeId;

    @Schema(description = "年级名称")
    private String gradeName;

    @Schema(description = "入学年份")
    private Integer enrollYear;

    @Schema(description = "班级学段，1小学，2初中，3高中")
    private Integer period;

    @Schema(description = "学校ID")
    private Long schoolId;

    @Schema(description = "学校名称")
    private String schoolName;

    @Schema(description = "学校学段，1小学，2初中，3高中，4九年一贯制，5十二年一贯制，6初高连读")
    private Integer schoolPeriod;

    @Schema(description = "学校类型,1公办，2民办，3公民同办")
    private Integer schoolType;

    @Schema(description = "区域ID")
    private Long regionId;

    @Schema(description = "区域名称")
    private String regionName;

    @Schema(description = "状态，0停用，1启用，2毕业")
    private Integer status;

    @Schema(description = "班主任教师ID")
    private Long masterTeacherId;

    @Schema(description = "班主任名称")
    private String masterTeacherName;

    @Schema(description = "班主任手机")
    private String masterTeacherPhone;

    @Schema(description = "体育老师ID")
    private Long sportTeacherId;

    @Schema(description = "体育老师名称")
    private String sportTeacherName;

    @Schema(description = "体育老师手机")
    private String sportTeacherPhone;

    @Schema(description = "学生数量")
    private Integer studentCount;

}
