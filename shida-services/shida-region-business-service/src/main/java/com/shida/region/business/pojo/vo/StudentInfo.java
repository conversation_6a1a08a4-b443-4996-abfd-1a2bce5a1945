package com.shida.region.business.pojo.vo;

import com.shida.pojo.vo.RoleInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.vo
 * @ClassName: StudentInfo
 * @Version 1.0
 * @date 2025/8/18 18:19
 */
@Data
@Accessors(chain = true)
@Schema(name = "StudentInfo", description = "学生信息")
public class StudentInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    @Schema(description = "学生ID")
    private Long id;
    @Schema(description = "用户ID")
    private Long userId;
    @Schema(description = "学生账号")
    private String userName;
    @Schema(description = "昵称")
    private String nickname;
    @Schema(description = "默认密码")
    private String defaultPassword;
    @Schema(description = "是否默认密码")
    private Boolean defaultPasswordFlag;
    @Schema(description = "学生姓名")
    private String xm;
    @Schema(description = "学生性别")
    private Integer xb;
    @Schema(description = "手机号")
    private String sjhm;
    @Schema(description = "班级ID")
    private Long clazzId;
    @Schema(description = "班级名称")
    private String clazzName;
    @Schema(description = "学校ID")
    private Long schoolId;
    @Schema(description = "学校名称")
    private String schoolName;
    @Schema(description = "年级ID")
    private Long gradeId;
    @Schema(description = "年级名称")
    private String gradeName;
    @Schema(description = "入学年份")
    private Integer enrollYear;
    @Schema(description = "民族")
    private Integer mz;
    @Schema(description = "民族名称")
    private String mxName;
    @Schema(description = "学生状态,1在读，2毕业，3休学")
    private Integer studentStatus;
    @Schema(description = "账号状态")
    private Integer status;
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    @Schema(description = "区县ID")
    private Long regionId;
    @Schema(description = "区县名称")
    private String regionName;
    @Schema(description = "学籍号")
    private String xjh;
    @Schema(description = "身份证号")
    private String sfzh;
    @Schema(description = "头像")
    private String xstx;
    @Schema(description = "家庭住址")
    private String jtzz;
    @Schema(description = "学段")
    private Integer period;
    @Schema(description = "学段名称")
    private String periodName;
    @Schema(description = "角色列表")
    private List<RoleInfo> roleList;
    @Schema(description = "角色名称")
    private String roleName;
    @Schema(description = "角色代码")
    private List<String> roleCode;
}
