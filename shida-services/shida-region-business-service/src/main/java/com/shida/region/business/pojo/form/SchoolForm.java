package com.shida.region.business.pojo.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
@Accessors(chain = true)
@Schema(name = "SchoolForm", description = "学校表单")
public class SchoolForm implements Serializable {

    @Schema(description = "学校ID，更新时必填")
    private Long id;

    @Schema(description = "学校名称")
    @NotBlank(message = "学校名称不能为空")
    @Size(max = 64, message = "学校名称长度不能超过64")
    private String name;

    @Schema(description = "学校代码")
    @NotBlank(message = "学校代码不能为空")
    @Size(max = 32, message = "学校代码长度不能超过32")
    private String code;

    @Schema(description = "简介")
    private String description;

    @Schema(description = "区县ID")
    @NotNull(message = "区县ID不能为空")
    private Long regionId;

    @Schema(description = "学段，1小学，2初中，3高中，4九年一贯制")
    @NotNull(message = "学段不能为空")
    private Integer period;

    @Schema(description = "联系人")
    private String contacts;

    @Schema(description = "联系电话")
    private String telephone;

    @Schema(description = "学校类型,1公办，2民办")
    @NotNull(message = "学校类型不能为空")
    private Integer schoolType;

}
