package com.shida.region.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shida.constant.RoleConstant;
import com.shida.constant.SchoolPeriodConstant;
import com.shida.constant.SchoolTypeConstant;
import com.shida.dto.SchoolDto;
import com.shida.enums.RestCode;
import com.shida.excel.ExcelBatchImportResponse;
import com.shida.excel.ExcelHelper;
import com.shida.exceptions.ServiceException;
import com.shida.pojo.vo.KeyValue;
import com.shida.pojo.vo.PageInfo;
import com.shida.pojo.vo.RoleInfo;
import com.shida.region.business.entity.School;
import com.shida.region.business.mapper.SchoolMapper;
import com.shida.region.business.pojo.excel.SchoolAccountExcel;
import com.shida.region.business.pojo.excel.SchoolImportExcel;
import com.shida.region.business.pojo.form.SchoolForm;
import com.shida.region.business.pojo.search.SchoolSearch;
import com.shida.region.business.pojo.vo.SchoolInfo;
import com.shida.region.business.service.ISchoolService;
import com.shida.region.business.tenant.TenantContextHolder;
import com.shida.userCenter.api.IFeignRoleService;
import com.shida.userCenter.api.IFeignUserService;
import com.shida.userCenter.dto.OrgDto;
import com.shida.userCenter.dto.RoleDto;
import com.shida.userCenter.dto.UserDto;
import com.shida.utils.IdUtil;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <p>
 * 学校信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Slf4j
@Service
public class SchoolServiceImpl extends ServiceImpl<SchoolMapper, School> implements ISchoolService {

    @Resource
    private IFeignUserService feignUserService;
    @Resource
    private IFeignRoleService feignRoleService;

    @Resource
    private TenantContextHolder tenantContextHolder;

    @Override
    public PageInfo<SchoolInfo> getPageData(SchoolSearch search) {
        IPage<School> page = new Page<>(search.getPageNumber(), search.getPageSize());
        page = baseMapper.getPageData(page, search);
        List<Long> schoolIds = page.getRecords().stream().map(School::getId).collect(Collectors.toList());
        List<UserDto> masterList = CollUtil.isNotEmpty(schoolIds) ? feignUserService.getMasterUserListBySchoolIds(search.getRegionId(), schoolIds) : new ArrayList<>();

        PageInfo<SchoolInfo> result = PageInfo.fromMybatisPage(page, SchoolInfo.class);
        OrgDto region = tenantContextHolder.getTenant();
        result.getRecords().forEach(schoolInfo -> {
            schoolInfo.setDescription(null);
            List<UserDto> masters = masterList.stream().filter(userDto -> userDto.getSchoolId().equals(schoolInfo.getId())).collect(Collectors.toList());
            schoolInfo.setMasterList(masters);
            schoolInfo.getMasterList().forEach(userDto -> {
                userDto.setSchoolCode(schoolInfo.getCode())
                        .setSchoolName(schoolInfo.getName());
            });
            if (Objects.nonNull(region)) {
                schoolInfo.setRegionId(region.getId())
                        .setRegionCode(region.getCode())
                        .setRegionName(region.getName());
            }
        });
        return result;
    }

    @Override
    public String updateSchool(SchoolForm form) {
        if (Objects.isNull(form.getId())) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "学校ID不能为空");
        }
        if (super.count(new LambdaQueryWrapper<School>().ne(School::getId, form.getId()).eq(School::getCode, form.getCode())) > 0) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "学校代码已存在，请更改代码");
        }
        School school = super.getById(form.getId());
        BeanUtil.copyProperties(form, school, "regionId");
        school.updateById();
        return "ok";
    }

    @Override
    public SchoolDto getByRegionIdAndSchoolId(Long regionId, Long schoolId) {
        School school = super.getById(schoolId);
        return BeanUtil.copyProperties(school, SchoolDto.class);
    }

    @Override
    public List<SchoolDto> getByRegionId(Long regionId) {
        List<School> list = super.list(new LambdaQueryWrapper<School>().orderByAsc(School::getId));
        return BeanUtil.copyToList(list, SchoolDto.class);
    }

    @Override
    public List<SchoolDto> getByRegionIdAndSchoolIds(Long regionId, List<Long> schoolIds) {
        List<School> list = super.list(new LambdaQueryWrapper<School>().in(School::getId, schoolIds).orderByAsc(School::getId));
        return BeanUtil.copyToList(list, SchoolDto.class);
    }

    @Override
    public SchoolInfo getDetailById(Long schoolId) {
        School school = super.getById(schoolId);
        SchoolInfo result = BeanUtil.copyProperties(school, SchoolInfo.class);
        if (Objects.nonNull(school)) {
            List<UserDto> masterUserList = feignUserService.getMasterUserListBySchoolId(school.getRegionId(), schoolId);
            result.setMasterList(masterUserList);
            OrgDto region = tenantContextHolder.getTenant();
            if (Objects.nonNull(region)) {
                result.setRegionId(region.getId())
                        .setRegionCode(region.getCode())
                        .setRegionName(region.getName());
            }
            for (UserDto userDto : masterUserList) {
                userDto.setSchoolCode(result.getCode())
                        .setSchoolName(result.getName());
            }
        }
        return result;
    }

    @Override
    public String createSchool(SchoolForm form) {
        if (super.count(new LambdaQueryWrapper<School>().eq(School::getCode, form.getCode())) > 0) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "学校代码已存在，请更改代码");
        }
        School school = BeanUtil.copyProperties(form, School.class);
        school.setRegionId(tenantContextHolder.getTenant().getId());
        school.setId(null);
        school.insert();
        return "success";
    }

    @Override
    public List<KeyValue<Long, String>> getSchoolSelectList(Long regionId) {
        List<School> list = super.list(new LambdaQueryWrapper<School>().eq(School::getRegionId, regionId).orderByAsc(School::getId));
        return list.stream().map(school -> new KeyValue<>(school.getId(), school.getName(),school.getPeriod().toString())).collect(Collectors.toList());
    }


    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public List<ExcelBatchImportResponse> batchImport(MultipartFile file, Long regionId) {
        List<ExcelBatchImportResponse> result = new ArrayList<>();
        List<SchoolImportExcel> excelList = new ArrayList<>();
        try {
            excelList = ExcelHelper.readData(file.getInputStream(), SchoolImportExcel.class, 0);
        } catch (Exception e) {
            log.error("读取Excel发生错误，请稍后重试", e);
            result.add(new ExcelBatchImportResponse().setMessage("读取Excel发生错误，请稍后重试").setRowIndex(1));
        }
        if (CollUtil.isEmpty(excelList)) {
            return result;
        }

        List<String> codeList = excelList.stream().map(SchoolImportExcel::getCode).distinct().collect(Collectors.toList());
        List<School> schoolList = CollUtil.isNotEmpty(codeList) ? super.list(new LambdaQueryWrapper<School>().eq(School::getRegionId, regionId).in(School::getCode, codeList)) : new ArrayList<>();
        List<UserDto> userDtoList = feignUserService.getListByUsernames(excelList.stream().map(SchoolImportExcel::getAdminUsername).distinct().collect(Collectors.toList()));

        List<School> addSchoolList = new ArrayList<>();
        List<UserDto> addUserList = new ArrayList<>();

        for (int i = 0; i < excelList.size(); i++) {
            SchoolImportExcel excel = excelList.get(i);
            // 判定学校代码有无重复
            if (excelList.stream().filter(school -> school.getCode().equals(excel.getCode())).count() > 1) {
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中学校代码【" + excel.getCode() + "】存在重复数据").setRowIndex(i + 1));
            }
            // 判定学校库中有无重复
            if (schoolList.stream().anyMatch(school -> school.getCode().equals(excel.getCode()))) {
                result.add(new ExcelBatchImportResponse().setMessage("该区县学校库中已存在代码为【" + excel.getCode() + "】的学校信息").setRowIndex(i + 1));
            }
            // 判定管理员账号有无重复
            if (excelList.stream().filter(school -> school.getAdminUsername().equals(excel.getAdminUsername())).count() > 1) {
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中管理员账号为【" + excel.getAdminUsername() + "】存在重复数据").setRowIndex(i + 1));
            }
            // 判定账户库中有无重复
            if (userDtoList.stream().anyMatch(userDto -> userDto.getUsername().equals(excel.getAdminUsername()))) {
                result.add(new ExcelBatchImportResponse().setMessage("账户库已存在管理员账号为【" + excel.getAdminUsername() + "】的学校信息").setRowIndex(i + 1));
            }
            if (CollUtil.isEmpty(result)) {
                School school = new School();
                school.setName(excel.getName())
                        .setCode(excel.getCode())
                        .setRegionId(regionId)
                        .setPeriod(SchoolPeriodConstant.SCHOOL_PERIOD_MAP.get(excel.getPeriod()))
                        .setSchoolType(SchoolTypeConstant.SCHOOL_TYPE_MAP.get(excel.getSchoolType()))
                        .setContacts(excel.getContacts())
                        .setTelephone(excel.getTelephone());
                school.setId(IdUtil.getSnowflakeNextId());
                addSchoolList.add(school);
                UserDto userDto = new UserDto()
                        .setUsername(excel.getAdminUsername())
                        .setNickname(excel.getAdminRealName())
                        .setMobile(excel.getAdminMobile())
                        .setRegionId(regionId)
                        .setSchoolId(school.getId())
                        .setAdminFlag(true);
                List<RoleInfo> roleInfoList = new ArrayList<>();
                roleInfoList.add(new RoleInfo().setRoleCode(RoleConstant.SCHOOL_ADMIN_CODE));
                userDto.setRoles(roleInfoList);
                addUserList.add(userDto);
            }
        }

        if (result.isEmpty()) {
            super.saveBatch(addSchoolList);
            feignUserService.batchCreateUser(addUserList);
        }
        return result;
    }

    @Override
    public void exportAdminCount(HttpServletResponse response, Long regionId, SchoolSearch search) {
        List<School> schoolList = list(new LambdaQueryWrapper<School>().eq(School::getRegionId, regionId)
                .eq(Objects.nonNull(search.getSchoolId()), School::getId, search.getSchoolId())
                .eq(Objects.nonNull(search.getSchoolType()), School::getSchoolType, search.getSchoolType())
                .eq(Objects.nonNull(search.getSchoolPeriod()), School::getPeriod, search.getSchoolPeriod())
                .and(StrUtil.isNotBlank(search.getKeywords()),
                        c -> c.like(School::getName, search.getKeywords())
                                .or().like(School::getCode, search.getKeywords()))
        );

        List<SchoolAccountExcel> excelList = new ArrayList<>();
        OrgDto region = tenantContextHolder.getTenant();
        if (schoolList.isEmpty()) {
            ExcelHelper.writeToResponse(SchoolAccountExcel.class, excelList, region.getName() + "-学校账户信息.xlsx", response);
        }

        List<UserDto> userList = feignUserService.getMasterUserListBySchoolIds(regionId, schoolList.stream().map(School::getId).collect(Collectors.toList()));


        excelList = BeanUtil.copyToList(userList, SchoolAccountExcel.class);

        AtomicInteger index = new AtomicInteger(1);
        RoleDto roleDto = feignRoleService.getByCode(RoleConstant.SCHOOL_ADMIN_CODE);

        excelList.forEach(excel -> {
            UserDto userInfo = userList.stream().filter(u -> u.getId().equals(excel.getId())).findFirst().orElse(new UserDto());
            excel.setIndex(index.getAndIncrement());
            School school = schoolList.stream().filter(s -> s.getId().equals(userInfo.getSchoolId())).findFirst().orElse(new School());
            excel.setNickname(userInfo.getNickname())
                    .setEmail(userInfo.getEmail())
                    .setRoleName(roleDto.getName())
                    .setSchoolCode(school.getCode())
                    .setSchoolName(school.getName())
                    .setSchoolPeriod(SchoolPeriodConstant.SCHOOL_PERIOD_MAP_REVERSE.get(school.getPeriod()))
                    .setSchoolType(SchoolTypeConstant.SCHOOL_TYPE_MAP_REVERSE.get(school.getSchoolType()));
        });

        if (Objects.nonNull(search.getSchoolId())) {
            School school = baseMapper.selectById(search.getSchoolId());
            ExcelHelper.writeToResponse(SchoolAccountExcel.class, excelList, school.getName() + "-管理员账号信息.xlsx", response);
        } else {
            ExcelHelper.writeToResponse(SchoolAccountExcel.class, excelList, region.getName() + "-学校管理员账号信息.xlsx", response);
        }
    }
    @Override
    public List<School> getSchoolBySchoolNames(List<String> schoolNames) {
        return baseMapper.selectList(new LambdaQueryWrapper<School>().in(School::getName, schoolNames));
    }
}
