package com.shida.region.business.pojo.vo;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.shida.enums.ApplyForCategaryEnum;
import com.shida.enums.ReviewStatusEnum;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Data
@Schema(name = "MianhuankaoshengVo", description = "免考缓考生管理Vo")
public class MianhuankaoshengVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "id")
    private Long id;

    @Schema(description = "学生姓名")
    private String studentName;

    @Schema(description = "学籍号")
    private String xjh;

    @Schema(description = "性别")
    private String sex;

    @Schema(description = "所在班级名称")
    private String clazzName;

    @Schema(description = "所属学校名称")
    private String schoolName;

    @Schema(description = "所属区县名称")
    private String regionName;

    private Integer applyForCategary;
    
    @Schema(description = "申请类别展示")
    private String applyForCategaryShow;

    @Schema(description = "类别情况说明")
    private String categaryDescription;

    @Schema(description = "免考/缓考项目")
    private String items;

    @Schema(description = "免考/缓考期限")
    private String termLimit;

    @Schema(description = "佐证材料")
    private String zuozhengResource;

    @Schema(description = "驳回原因")
    private String rejectReason;

    private Integer countyReviewStatus;
    
    @Schema(description = "区县审核状态展示")
    private String countyReviewStatusShow;

    private Integer cityReviewStatus;
    
    @Schema(description = "市级审核状态展示")
    private String cityReviewStatusShow;

    @Schema(description = "审核时间")
    private LocalDateTime updateTime;

    
	public String getApplyForCategaryShow() {
		if(null != this.applyForCategary) {
			this.applyForCategaryShow = ApplyForCategaryEnum.getByCode(this.applyForCategary);
			this.applyForCategary = null;
		}
		
		return applyForCategaryShow;
	}

	public String getCountyReviewStatusShow() {
		if(null != this.countyReviewStatus) {
			this.countyReviewStatusShow = ReviewStatusEnum.getByCode(this.countyReviewStatus);
			this.countyReviewStatus = null;
		}
		
		return countyReviewStatusShow;
	}

	public String getCityReviewStatusShow() {
		if(null != this.cityReviewStatus) {
			this.cityReviewStatusShow = ReviewStatusEnum.getByCode(this.cityReviewStatus);
			this.cityReviewStatus = null;
		}
		
		return cityReviewStatusShow;
	}

    
}
