package com.shida.region.business.pojo.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.form
 * @ClassName: StudentShiNeiAuditForm
 * @Version 1.0
 * @date 2025/9/4 17:40
 */
@Data
@Accessors(chain = true)
@Schema(name = "StudentShiNeiAuditForm", description = "市内转入审核表单")
public class StudentShiNeiAuditForm {

    @Schema(description = "审核状态")
    @NotNull(message = "审核状态不能为空")
    private Integer status;
    @Schema(description = "审核原因")
    private String reason;
    @Schema(description = "ID")
    @NotNull(message = "ID不能为空")
    private Long id;
    @Schema(description = "审核时间")
    private LocalDateTime createTime;
    @Schema(description = "审核人")
    private String createUser;
    @Schema(description = "审核人ID")
    private Long createUserId;
}
