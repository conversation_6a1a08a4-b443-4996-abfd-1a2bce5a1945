package com.shida.region.business.tenant;

import com.shida.userCenter.api.IFeignRegionService;
import com.shida.userCenter.dto.OrgDto;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 多租户上下文
 */
@Component
public class TenantContextHolder {
    private ThreadLocal<OrgDto> tenantThreadLocal = new ThreadLocal<>();

    @Resource
    private IFeignRegionService feignRegionService;

    @Resource
    private RedisTemplate<String, OrgDto> redisTemplate;


    /**
     * 设置当前租户
     * @param region
     */
    public void setTenant(OrgDto region) {
        tenantThreadLocal.set(region);
    }

    /**
     * 获取当前租户
     * @return
     */
    public OrgDto getTenant() {
        return tenantThreadLocal.get();
    }

    /**
     * 清空当前租户
     */
    public void clear() {
        tenantThreadLocal.remove();
    }

    public OrgDto getRegionByTenantId(Long tenantId) {
        OrgDto region;
        if (redisTemplate.hasKey("X-Tenant-Id:" + tenantId)) {
            region = (OrgDto) redisTemplate.opsForValue().get("X-Tenant-Id:" + tenantId);
        } else {
            region = feignRegionService.getRegionById(tenantId);
            if (Objects.nonNull(region)) {
                redisTemplate.opsForValue().set("X-Tenant-Id:" + tenantId, region, 10, TimeUnit.MINUTES);
            }
        }
        return region;
    }
}
