package com.shida.region.business.controller.base;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.noRepeatSubmit.NoRepeatSubmit;
import com.shida.pojo.form.KeyForm;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.business.pojo.form.DeviceForm;
import com.shida.region.business.pojo.search.DeviceSearch;
import com.shida.region.business.pojo.vo.DeviceInfo;
import com.shida.region.business.service.IDeviceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@Tag(name = "device-设备管理")
@RestController
@RequestMapping("/device/manage")
public class DeviceManageController {

    @Resource
    private IDeviceService deviceService;

    @Operation(summary = "获取设备列表")
    @SaCheckPermission("device:list")
    @PostMapping("/getPageData")
    public PageInfo<DeviceInfo> getPageData(@RequestBody DeviceSearch search) {
        return deviceService.getPageData(search);
    }


    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_ADD)
    @Operation(summary = "新增设备")
    @SaCheckPermission("device:add")
    @PostMapping("/create")
    public String create(@RequestBody @Valid DeviceForm form) {
        return deviceService.create(form);
    }

    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    @Operation(summary = "更新设备")
    @SaCheckPermission("device:edit")
    @PostMapping("/update")
    public String update(@RequestBody @Valid DeviceForm form) {
        return deviceService.updateDevice(form);
    }

    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_DELETE)
    @Operation(summary = "删除设备")
    @SaCheckPermission("device:delete")
    @PostMapping("/delete")
    public boolean delete(@RequestBody @Valid KeyForm<Long> form) {
        return deviceService.removeById(form.getKey());
    }
}
