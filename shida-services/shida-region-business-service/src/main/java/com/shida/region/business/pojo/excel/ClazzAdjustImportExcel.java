package com.shida.region.business.pojo.excel;

import cn.hutool.core.util.StrUtil;
import com.shida.excel.ExcelAlias;
import com.shida.excel.ExcelBatchImportResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.excel
 * @ClassName: ClazzAdjustImportExcel
 * @Version 1.0
 * @date 2025/9/2 14:46
 */
@Data
@Accessors(chain = true)
@Schema(name = "ClazzAdjustImportExcel", description = "班级调动导入信息")
public class ClazzAdjustImportExcel {
    @ExcelAlias(value = "序号",orderNo = 1)
    private Integer rowNum;
    @ExcelAlias(value = "学生姓名")
    private String xm;
    @ExcelAlias(value = "学籍号")
    private String xjh;
    @ExcelAlias(value = "性别")
    private String xb;
    @ExcelAlias(value = "原班级")
    private String originalClazzName;
    @ExcelAlias(value = "调入班级")
    private String newClazzName;
    @ExcelAlias(value = "年级")
    private String gradeName;


    public List<ExcelBatchImportResponse> validateData(Integer rowIndex) {
        List<ExcelBatchImportResponse> result = new ArrayList<>();
        if (StrUtil.isBlank(xm)) {
            result.add(new ExcelBatchImportResponse().setMessage("姓名不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(xjh)) {
            result.add(new ExcelBatchImportResponse().setMessage("学籍号不能为空").setType("error").setRowIndex(rowIndex));
        }
       if(!StrUtil.startWithAny(xjh,"G","g","L","l","J","j")){
            result.add(new ExcelBatchImportResponse().setMessage("学籍号格式错误").setType("error").setRowIndex(rowIndex));
        }
       if (StrUtil.isBlank(xb)) {
            result.add(new ExcelBatchImportResponse().setMessage("性别不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(originalClazzName)) {
            result.add(new ExcelBatchImportResponse().setMessage("原班级不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(newClazzName)) {
            result.add(new ExcelBatchImportResponse().setMessage("调入班级不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(gradeName)) {
            result.add(new ExcelBatchImportResponse().setMessage("年级不能为空").setType("error").setRowIndex(rowIndex));
        }
        return result;
    }
}
