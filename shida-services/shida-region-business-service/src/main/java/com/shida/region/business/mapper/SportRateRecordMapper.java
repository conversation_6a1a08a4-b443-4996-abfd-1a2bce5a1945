package com.shida.region.business.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.shida.region.business.entity.SportRateRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shida.region.business.pojo.search.SportRateSearch;
import com.shida.region.business.pojo.vo.SportRateInfo;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 体育课出勤率 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
public interface SportRateRecordMapper extends BaseMapper<SportRateRecord> {

    IPage<SportRateInfo> getPageData(IPage<SportRateInfo> page, @Param("search") SportRateSearch search);
}
