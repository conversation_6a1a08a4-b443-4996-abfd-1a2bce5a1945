package com.shida.region.business.pojo.search;

import com.shida.pojo.search.Search;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.search
 * @ClassName: ClazzAdjustSearch
 * @Version 1.0
 * @date 2025/9/4 14:17
 */
@Data
@Accessors(chain = true)
@Schema(name = "ClazzAdjustSearch", description = "校内调班搜索条件")
public class ClazzAdjustSearch extends Search {
    @Schema(description = "学校id")
    private Long schoolId;
    @Schema(description = "班级id")
    private Long clazzId;
    @Schema(description = "年级")
    private Integer gradeId;
    @Schema(description = "是否有调班记录")
    private String isClassAdjust;
}
