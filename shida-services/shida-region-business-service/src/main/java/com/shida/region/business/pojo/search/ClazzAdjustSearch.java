package com.shida.region.business.pojo.search;

import com.shida.pojo.search.Search;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;


@Data
@Accessors(chain = true)
@Schema(name = "ClazzSearch", description = "班级搜索参数")
public class ClazzAdjustSearch extends Search {
    @Schema(description = "调入班级名称")
    private String clazzName;

    @Schema(description = "班级ID")
    private Long clazzId;

    @Schema(description = "学生id")
    private List<Long> studentIds;
}
