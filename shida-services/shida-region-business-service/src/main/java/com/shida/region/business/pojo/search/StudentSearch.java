package com.shida.region.business.pojo.search;

import com.shida.pojo.search.Search;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.search
 * @ClassName: StudentSearch
 * @Version 1.0
 * @date 2025/8/18 17:49
 */
@Data
@Accessors(chain = true)
@Schema(name = "StudentSearch", description = "学生搜索条件")
public class StudentSearch extends Search {
    @Schema(description = "学校id")
    private Long schoolId;
    @Schema(description = "班级id")
    private Long clazzId;
    @Schema(description = "学生状态，1在读，2毕业，3休学")
    private Integer studentStatus;
    @Schema(description = "年级")
    private Integer gradeId;
    @Schema(description = "区域id")
    private Long regionId;
    @Schema(description = "学段")
    private Integer period;
    @Schema(description = "学期")
    private Long termId;
    @Schema(description = "是否有调班记录")
    private String isClassAdjust;
}
