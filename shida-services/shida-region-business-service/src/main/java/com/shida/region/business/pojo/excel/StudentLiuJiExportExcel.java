package com.shida.region.business.pojo.excel;

import com.shida.excel.ExcelAlias;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.excel
 * @ClassName: StudentSuspendExportExcel
 * @Version 1.0
 * @date 2025/9/5 16:46
 */
@Data
@Accessors(chain = true)
@Schema(name = "StudentLiuJiExportExcel", description = "导出留级管理")
public class StudentLiuJiExportExcel {
    @ExcelAlias(value = "序号")
    private Integer rowNum;
    @ExcelAlias(value = "申请ID")
    private String adjustId;
    @ExcelAlias(value = "学生姓名")
    private String xm;
    @ExcelAlias(value = "学籍号")
    private String xjh;
    @ExcelAlias(value = "性别")
    private String xb;
    @ExcelAlias(value = "原年级")
    private String oldGradeName;
    @ExcelAlias(value = "原班级")
    private String oldClassName;
    @ExcelAlias(value = "留级年级")
    private String gradeName;
    @ExcelAlias(value = "留级班级")
    private String clazzName;
    @ExcelAlias(value = "申请日期")
    private LocalDate applyDate;
}
