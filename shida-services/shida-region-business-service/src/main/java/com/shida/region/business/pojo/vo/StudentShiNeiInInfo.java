package com.shida.region.business.pojo.vo;

import com.shida.region.business.pojo.form.StudentShiNeiAuditForm;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.vo
 * @ClassName: StudentShiNeiInInfo
 * @Version 1.0
 * @date 2025/9/3 19:52
 */
@Data
@Accessors(chain = true)
@Schema(name = "StudentShiNeiInInfo", description = "市内转入学生信息")
public class StudentShiNeiInInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    @Schema(description = "ID")
    private Long id;
    @Schema(description = "申请ID")
    private Long adjustId;
    @Schema(description = "学生ID")
    private Long studentId;
    @Schema(description = "学生姓名")
    private String studentName;
    @Schema(description = "身份证号")
    private String sfzh;
    @Schema(description = "性别")
    private Integer xb;
    @Schema(description = "学籍号")
    private String xjh;
    @Schema(description = "异动类型 3市外转入")
    private Integer adjustType;
    @Schema(description = "原学校")
    private String outSchoolName;
    @Schema(description = "原年级")
    private String outGradeName;
    @Schema(description = "原学期")
    private Long outTerm;
    @Schema(description = "原学期")
    private String outTermName;
    @Schema(description = "原班级")
    private String outClazzName;
    @Schema(description = "转入学校Id")
    private Long intoSchoolId;
    @Schema(description = "转入学校名称")
    private String intoSchoolName;
    private Long intoPeriod;
    @Schema(description = "转入学段")
    private Long intoPeriodName;
    @Schema(description = "转入年级ID")
    private Long intoGradeId;
    @Schema(description = "转入年级名称")
    private String intoGradeName;
    @Schema(description = "转入班级ID")
    private Long intoClazzId;
    @Schema(description = "转入班级名称")
    private String intoClazzName;
    @Schema(description = "审核状态")
    private Integer status;
    @Schema(description = "操作人ID")
    private Long applyUserId;
    @Schema(description = "操作人用户名")
    private String applyUserName;

    @Schema(description = "转入原因")
    private String inReason;
    @Schema(description = "申请日期")
    private LocalDate applyDate;
    private List<StudentShiNeiAuditForm> auditFormList;
}
