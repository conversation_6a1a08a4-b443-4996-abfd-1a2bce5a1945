package com.shida.region.business.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.shida.entity.BaseEntity;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 学校信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_school")
@Schema(name = "School", description = "学校信息表")
public class School extends BaseEntity<School> {

    private static final long serialVersionUID = 1L;

    @Schema(description = "学校名称")
    private String name;

    @Schema(description = "学校代码")
    private String code;

    @Schema(description = "简介")
    private String description;

    @Schema(description = "区县ID")
    private Long regionId;

    @Schema(description = "学段，1小学，2初中，3高中，4九年一贯制，5十二年一贯制，6初高连读")
    private Integer period;

    @Schema(description = "联系人")
    private String contacts;

    @Schema(description = "联系电话")
    private String telephone;

    @Schema(description = "学校类型,1公办，2民办")
    private Integer schoolType;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
