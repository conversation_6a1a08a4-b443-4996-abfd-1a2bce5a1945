package com.shida.region.business.pojo.search;

import com.shida.pojo.search.Search;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 学生体质健康标准测试结果搜索条件
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
@Accessors(chain = true)
@Schema(name = "StudentTiZhiCeShiSearch", description = "学生体质健康标准测试结果搜索条件")
public class StudentTiZhiCeShiSearch extends Search {

    @Schema(description = "学期ID")
    @NotNull(message = "学期ID不能为空")
    private Long termId;

    @Schema(description = "学校ID")
    private Long schoolId;

    @Schema(description = "班级ID")
    private Long clazzId;

    @Schema(description = "是否残疾")
    private Boolean sfcj;

    @Schema(description = "是否伤病")
    private Boolean sfsb;
}
