package com.shida.region.business.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.shida.entity.BaseEntity;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 学生视力记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("h_vision_record")
@Schema(name = "VisionRecord", description = "学生视力记录表")
public class VisionRecord extends BaseEntity<VisionRecord> {

    private static final long serialVersionUID = 1L;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新者")
    private String updateBy;

    @Schema(description = "学生ID")
    private Long studentId;

    @Schema(description = "学期ID")
    private Long termId;

    @Schema(description = "右眼裸眼视力")
    private String yylysl;

    @Schema(description = "左眼裸眼视力")
    private String zylysl;

    @Schema(description = "右眼球镜")
    private String yyqj;

    @Schema(description = "右眼柱镜")
    private String yyzj;

    @Schema(description = "右眼轴位")
    private String yyzw;

    @Schema(description = "右眼屈光度")
    private String yyqgd;

    @Schema(description = "左眼球镜")
    private String zyqj;

    @Schema(description = "左眼柱镜")
    private String zyzj;

    @Schema(description = "左眼轴位")
    private String zyzw;

    @Schema(description = "左眼屈光度")
    private String zyqgd;

    @Schema(description = "是否为角膜塑形镜")
    private String status;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
