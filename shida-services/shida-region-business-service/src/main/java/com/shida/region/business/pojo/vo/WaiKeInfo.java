package com.shida.region.business.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@Schema(name = "WaiKeInfo", description = "学生体检-外科纪录")
public class WaiKeInfo implements Serializable {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "学生姓名")
    private String studentName;

    @Schema(description = "学籍号")
    private String studentNumber;

    @Schema(description = "性别")
    private String gender;

    @Schema(description = "班级ID")
    private Long clazzId;

    @Schema(description = "所属班级")
    private String clazzName;

    @Schema(description = "学校ID")
    private Long schoolId;

    @Schema(description = "所属学校")
    private String schoolName;


    @Schema(description = "所属学期ID")
    private Long termId;

    @Schema(description = "所属学期")
    private String termName;

    @Schema(description = "所属区县ID")
    private Long regionId;

    @Schema(description = "所属区县")
    private String regionName;

    @Schema(description = "头部")
    private String tb;

    @Schema(description = "颈部")
    private String jb;

    @Schema(description = "脊柱")
    private String jz;

    @Schema(description = "四肢")
    private String sz;

    @Schema(description = "皮肤")
    private String pf;

    @Schema(description = "淋巴结")
    private String lbj;

}
