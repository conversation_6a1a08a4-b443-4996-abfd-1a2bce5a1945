package com.shida.region.business.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.DateTimeException;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.vo
 * @ClassName: ClassAdjustRecordInfo
 * @Version 1.0
 * @date 2025/8/18 18:19
 */
@Data
@Accessors(chain = true)
@Schema(name = "ClassAdjustRecordInfo", description = "学生调班信息")
public class ClassAdjustRecordInfo implements Serializable {
    @Schema(description = "调班id")
    private Long id;
    @Schema(description = "学生ID")
    private Long studentId;
    @Schema(description = "学生姓名")
    private String xm;
    @Schema(description = "年级ID")
    private Long gradeId;
    @Schema(description = "年级名称")
    private String gradeName;
    @Schema(description = "学生性别")
    private Integer xb;
    @Schema(description = "学籍号")
    private String xjh;
    @Schema(description = "原班级ID")
    private Long originalClazz;
    @Schema(description = "原班级名称")
    private String originalClazzName;
    @Schema(description = "班级ID")
    private Long clazzId;
    @Schema(description = "班级名称")
    private String clazzName;
    @Schema(description = "是否校内调班 0否 ; 1是")
    private Integer isClassAdjust;
    @Schema(description = "调班日期")
    private LocalDate adjustDate;
    @Schema(description = "操作人")
    private String operator;
    @Schema(description = "操作时间")
    private LocalDateTime CreateTime;
}
