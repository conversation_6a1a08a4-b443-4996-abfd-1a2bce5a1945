package com.shida.region.business.pojo.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.form
 * @ClassName: StudentShiNeiInForm
 * @Version 1.0
 * @date 2025/9/4 9:16
 */
@Data
@Accessors(chain = true)
@Schema(name = "StudentShiNeiInForm", description = "学生市内转入表单")
public class StudentShiNeiInForm implements Serializable {
    private static final long serialVersionUID = 1L;
    @Schema(description = "ID")
    private Long id;
    @Schema(description = "学籍号")
    @NotBlank(message = "学号不能为空")
    private String xjh;
    @Schema(description = "身份证号")
    @NotBlank(message = "身份证号不能为空")
    private String sfzh;
    @Schema(description = "原学生ID")
    @NotNull(message = "原学生ID不能为空")
    private Long studentId;
    @Schema(description = "原学生姓名")
    @NotBlank(message = "学生姓名不能为空")
    private String studentName;

    @Schema(description = "性别")
    @NotNull(message = "性别不能为空")
    private Integer xb;
    @Schema(description = "学生原区县ID")
    @NotNull(message = "学生原区县ID不能为空")
    private Long oldRegionId;
    @Schema(description = "原班级ID")
    @NotNull(message = "班级ID不能为空")
    private Long clazzId;
    @Schema(description = "原班级名称")
    private String clazzName;
    @Schema(description = "学校名称")
    @NotBlank(message = "学校名称不能为空")
    private String schoolName;
    @Schema(description = "原年级ID")
    @NotNull(message = "年级ID不能为空")
    private Long gradeId;
    @Schema(description = "原年级名称")
    private String gradeName;
    @Schema(description = "原学段")
    @NotNull(message = "学段不能为空")
    private Long period;
    @Schema(description = "民族")
    private Integer mz;

    @Schema(description = "转入时间")
    private LocalDate inBeginDate;

    @Schema(description = "转入原因")
    @NotBlank(message = "转入原因不能为空")
    private String adjustReason;

    @Schema(description = "转入年级ID")
    private Long intoGradeId;

    @Schema(description = "转入班级ID")
    private Long intoClazzId;
}
