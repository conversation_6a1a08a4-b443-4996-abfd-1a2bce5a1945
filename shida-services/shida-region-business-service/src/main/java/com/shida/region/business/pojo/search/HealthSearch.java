package com.shida.region.business.pojo.search;

import com.shida.pojo.search.Search;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

@Data
@Accessors(chain = true)
@Schema(name = "HealthSearch", description = "体检相关搜索条件")
public class HealthSearch extends Search {
    @Schema(description = "学校id")
    private Long schoolId;
    @Schema(description = "班级id")
    private Long clazzId;
    @Schema(description = "年级ID")
    private Long gradeId;
    @Schema(description = "学期ID")
    private Long termId;
}
