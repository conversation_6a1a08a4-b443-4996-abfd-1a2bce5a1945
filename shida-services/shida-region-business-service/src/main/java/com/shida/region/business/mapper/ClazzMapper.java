package com.shida.region.business.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.shida.region.business.entity.Clazz;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shida.region.business.pojo.search.ClazzSearch;
import com.shida.region.business.pojo.vo.ClazzInfo;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 班级信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-03
 */
public interface ClazzMapper extends BaseMapper<Clazz> {

    IPage<ClazzInfo> getPageData(IPage<ClazzInfo> page,@Param("search") ClazzSearch search);
}
