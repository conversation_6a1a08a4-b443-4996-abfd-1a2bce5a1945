package com.shida.region.business.pojo.excel;

import com.shida.excel.ExcelAlias;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.excel
 * @ClassName: TeacherExportExcel
 * @Version 1.0
 * @date 2025/8/14 17:10
 */
@Data
@Accessors(chain = true)
@Schema(name = "TeacherExportExcel", description = "导出的TeacherExportExcel对象")
public class TeacherExportExcel implements Serializable {
    @ExcelAlias(value = "姓名",orderNo = 2)
    private String jsxm;
    @ExcelAlias(value = "性别",orderNo = 5)
    private String jsxb ;
    @ExcelAlias(value = "账号",orderNo = 3)
    private String userName ;
    @ExcelAlias(value = "身份证号",orderNo = 4)
    private String sfzh ;
    @ExcelAlias(value = "手机号码",orderNo = 6)
    private String sjhm ;
    @ExcelAlias(value = "教师类别",orderNo = 7)
    private String jslb;
    @ExcelAlias(value = "角色",orderNo = 8)
    private String roleName;
    @ExcelAlias(value = "所属学校",orderNo = 9)
    private String schoolName;
    @ExcelAlias(value = "序号",orderNo = 1)
    private Integer rowNum;
}
