package com.shida.region.business.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.shida.region.business.entity.Clazz;
import com.shida.region.business.entity.School;
import com.shida.region.business.entity.Teacher;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shida.region.business.entity.TeacherClazz;
import com.shida.region.business.pojo.search.TeacherSearch;
import com.shida.region.business.pojo.vo.TeacherInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
public interface TeacherMapper extends BaseMapper<Teacher> {

    IPage<TeacherInfo> getPageData(IPage<TeacherInfo> page,  @Param("search") TeacherSearch search);

    List<TeacherClazz> getClazzList(@Param("teacherIds")List<Long> teacherIds);

}
