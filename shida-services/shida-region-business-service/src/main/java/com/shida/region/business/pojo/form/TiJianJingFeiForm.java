package com.shida.region.business.pojo.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@Schema(name = "TiJianJingFeiForm", description = "体检经费表单")
public class TiJianJingFeiForm implements Serializable {

    @Schema(description = "学期ID")
    private Long termId;

    @Schema(description = "体检总经费")
    @NotNull(message = "总经费不能为空")
    private BigDecimal zjf;

    @Schema(description = "人均体检经费")
    @NotNull(message = "人均体检经费不能为空")
    private BigDecimal rjjf;

    @Schema(description = "学生人数")
    @NotNull(message = "学生人数不能为空")
    private Integer studentCount;

}
