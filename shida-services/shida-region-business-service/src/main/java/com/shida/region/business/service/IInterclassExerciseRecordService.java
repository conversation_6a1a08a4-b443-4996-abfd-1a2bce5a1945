package com.shida.region.business.service;

import com.shida.excel.ExcelBatchImportResponse;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.business.entity.InterclassExerciseRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.shida.region.business.pojo.search.InterclassExerciseSearch;
import com.shida.region.business.pojo.vo.InterclassExerciseInfo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 课间操出勤率 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
public interface IInterclassExerciseRecordService extends IService<InterclassExerciseRecord> {

    PageInfo<InterclassExerciseInfo> getPageData(@Valid InterclassExerciseSearch search);

    List<ExcelBatchImportResponse> batchImport(MultipartFile file, Long termId);

    void downloadBatchImportTemplate(HttpServletResponse response, String regionId, Long termId);

    boolean deleteData(Long studentId, Long termId);

    void exportInterclassExercise(InterclassExerciseSearch search, HttpServletResponse response);
}
