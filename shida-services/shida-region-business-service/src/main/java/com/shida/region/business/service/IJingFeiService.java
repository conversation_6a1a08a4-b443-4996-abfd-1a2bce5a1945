package com.shida.region.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.business.entity.JingFei;
import com.shida.region.business.pojo.form.TiJianJingFeiForm;
import com.shida.region.business.pojo.search.HealthSearch;
import com.shida.region.business.pojo.vo.JingFeiInfo;

/**
 * <p>
 * 体检经费管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-04
 */
public interface IJingFeiService extends IService<JingFei> {

    /**
     * 列表页查询数据
     *
     * @param search
     * @return
     */
    PageInfo<JingFeiInfo> getPageData(HealthSearch search);


    String updateJingFei(TiJianJingFeiForm form);
}
