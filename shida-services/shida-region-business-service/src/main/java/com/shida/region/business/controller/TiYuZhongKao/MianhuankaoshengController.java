package com.shida.region.business.controller.TiYuZhong<PERSON>ao;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.noRepeatSubmit.NoRepeatSubmit;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.business.pojo.form.MianhuankaoshengForm;
import com.shida.region.business.pojo.search.MianhuankaoshengSearch;
import com.shida.region.business.pojo.vo.MianhuankaoshengVo;
import com.shida.region.business.service.IMianhuankaoshengService;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = "Mianhuankaosheng-免缓考生管理")
@RestController
@RequestMapping("/mhks/manage")
public class MianhuankaoshengController {
    @Autowired
    private IMianhuankaoshengService mianhuankaoshengService;


    @SaCheckPermission(value = "mhks:manage:list")
    @Operation(summary = "查询免考缓考生列表")
    @PostMapping("/getPageData")
    public PageInfo<MianhuankaoshengVo> getPageData(@RequestBody @Valid MianhuankaoshengSearch search) {
        return mianhuankaoshengService.getPageData(search);
    }

    @SaCheckPermission("mhks:manage:add")
    @Operation(summary = "新增免缓考生")
    @PostMapping("/add")
    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_ADD, value = "新增免缓考生")
    public Integer add(@RequestBody @Valid MianhuankaoshengForm mianhuankaoshengForm) {
        return mianhuankaoshengService.add(mianhuankaoshengForm);
    }

}
