package com.shida.region.business.pojo.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.form
 * @ClassName: StudentShiNeiUpdateForm
 * @Version 1.0
 * @date 2025/9/4 18:29
 */
@Data
@Accessors(chain = true)
@Schema(name = "StudentShiNeiUpdateForm", description = "学生市内转入更新表单")
public class StudentShiNeiUpdateForm {
    @Schema(description = "ID")
    @NotNull(message = "ID不能为空")
    private Long id;
    @Schema(description = "转入时间")
    @NotNull(message = "转入时间不能为空")
    private LocalDate inBeginDate;

    @Schema(description = "转入原因")
    @NotBlank(message = "转入原因不能为空")
    private String adjustReason;

    @Schema(description = "转入年级ID")
    @NotNull(message = "转入年级ID不能为空")
    private Long intoGradeId;

    @Schema(description = "转入班级ID")
    @NotNull(message = "转入班级ID不能为空")
    private Long intoClazzId;

}
