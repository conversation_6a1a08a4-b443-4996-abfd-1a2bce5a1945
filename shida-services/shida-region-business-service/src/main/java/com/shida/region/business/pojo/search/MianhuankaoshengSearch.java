package com.shida.region.business.pojo.search;

import com.shida.pojo.search.Search;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "MianhuankaoshengSearch", description = "免考缓考生管理检索参数")
public class MianhuankaoshengSearch extends Search {
    private static final long serialVersionUID = 1L;

    @Schema(description = "区县id")
    private Long regionId;
    
    @Schema(description = "学校id")
    private Long schoolId;
    
    @Schema(description = "年级id")
    private Long gradeId;
    
    @Schema(description = "班级id")
    private Long clazzId;

    @Schema(description = "申请类别")
    private Integer applyForCategary;

    @Schema(description = "免考/缓考项目")
    private String items;

    @Schema(description = "区县审核状态")
    private Integer countyReviewStatus;

    @Schema(description = "市级审核状态")
    private Integer cityReviewStatus;
    
}
