package com.shida.region.business.pojo.excel;

import cn.hutool.core.util.StrUtil;
import com.shida.excel.ExcelAlias;
import com.shida.excel.ExcelBatchImportResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.excel
 * @ClassName: TeacherImportExcel
 * @Version 1.0
 * @date 2025/8/14 15:37
 */
@Data
@Accessors(chain = true)
@Schema(name = "StudentNeiKeImportExcel", description = "学生体检 - 内科数据批量导入")
public class StudentNeiKeImportExcel implements Serializable {
    @ExcelAlias(value = "序号", orderNo = 1)
    private Integer rowNum;
    @ExcelAlias(value = "姓名")
    private String xm;
    @ExcelAlias(value = "学籍号")
    private String xjh;
    @ExcelAlias(value = "性别")
    private String xb;
    @ExcelAlias(value = "班级")
    private String className;
    @ExcelAlias(value = "年级")
    private String gradeName;
    @ExcelAlias(value = "所属学校")
    private String schoolName;
    @ExcelAlias(value = "所属区县")
    private String regionName;
    @ExcelAlias(value = "心")
    private String xz;
    @ExcelAlias(value = "肺")
    private String fei;
    @ExcelAlias(value = "肝")
    private String gz;
    @ExcelAlias(value = "脾")
    private String pz;
    @ExcelAlias(value = "血压")
    private String xy;
    @ExcelAlias(value = "肺活量")
    private String fhl;
    @ExcelAlias(value = "所属学期")
    private String termName;

    public List<ExcelBatchImportResponse> validateData(Integer rowIndex) {
        List<ExcelBatchImportResponse> result = new ArrayList<>();
        if (StrUtil.isBlank(xm)) {
            result.add(new ExcelBatchImportResponse().setMessage("姓名不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(xjh)) {
            result.add(new ExcelBatchImportResponse().setMessage("学籍号不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (!StrUtil.startWithAny(xjh, "G", "g", "L", "l", "J", "j")) {
            result.add(new ExcelBatchImportResponse().setMessage("学籍号格式错误").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(xb)) {
            result.add(new ExcelBatchImportResponse().setMessage("性别不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(className)) {
            result.add(new ExcelBatchImportResponse().setMessage("班级不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(gradeName)) {
            result.add(new ExcelBatchImportResponse().setMessage("年纪不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(schoolName)) {
            result.add(new ExcelBatchImportResponse().setMessage("所属学校不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(regionName)) {
            result.add(new ExcelBatchImportResponse().setMessage("所属区县不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(termName)) {
            result.add(new ExcelBatchImportResponse().setMessage("所属学期不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(xz)) {
            result.add(new ExcelBatchImportResponse().setMessage("心情况不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(fei)) {
            result.add(new ExcelBatchImportResponse().setMessage("肺情况不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(gz)) {
            result.add(new ExcelBatchImportResponse().setMessage("肝情况不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(pz)) {
            result.add(new ExcelBatchImportResponse().setMessage("脾情况不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(xy)) {
            result.add(new ExcelBatchImportResponse().setMessage("血压数据不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(fhl)) {
            result.add(new ExcelBatchImportResponse().setMessage("肺活量数据不能为空").setType("error").setRowIndex(rowIndex));
        }
        return result;
    }
}
