package com.shida.region.business.pojo.search;

import com.shida.pojo.search.Search;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.TreeSet;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.search
 * @ClassName: TeacherSearch
 * @Version 1.0
 * @date 2025/8/12 11:40
 */
@Data
@Accessors(chain = true)
@Schema(name = "TeacherSearch", description = "教师搜索条件")
public class TeacherSearch extends Search {
    @Schema(description = "教师类型，1班主任，2体育教师")
    private TreeSet<String> jslbSet;
    private String jslb;
    @Schema(description = "学段")
    private Integer period;
    @Schema(description = "学校id")
    private Long schoolId;
}
