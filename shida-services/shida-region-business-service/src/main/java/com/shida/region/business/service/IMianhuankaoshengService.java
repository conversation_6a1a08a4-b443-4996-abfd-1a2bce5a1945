package com.shida.region.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.business.entity.Mianhuankaosheng;
import com.shida.region.business.pojo.form.MianhuankaoshengForm;
import com.shida.region.business.pojo.search.MianhuankaoshengSearch;
import com.shida.region.business.pojo.vo.MianhuankaoshengVo;

/**
 * <p>
 * 免考缓考生管理表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-04
 */
public interface IMianhuankaoshengService extends IService<Mianhuankaosheng> {

	/**
	 * 查询-分页列表
	 * 
	 * @param search
	 * @return
	 */
	PageInfo<MianhuankaoshengVo> getPageData(MianhuankaoshengSearch search);

	/**
	 * 新增
	 * 
	 * @param mianhuankaoshengForm
	 * @return
	 */
	Integer add(MianhuankaoshengForm mianhuankaoshengForm);
}
