package com.shida.region.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.shida.region.business.entity.Student;
import com.shida.region.business.pojo.search.StudentSearch;
import com.shida.region.business.pojo.vo.StudentInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 学生信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
public interface StudentMapper extends BaseMapper<Student> {

    IPage<StudentInfo> getPageData(IPage<StudentInfo> page, @Param("search") StudentSearch search);

    StudentInfo getDetailById(Long id);

    List<StudentInfo> getListByStudentNumber(@Param("numberList") List<String> numberList);
    List<StudentInfo> getStudentList(@Param("studentIds") List<Long> studentIds);
    StudentInfo getDetailByCertNo(@Param("certNo") String certNo);
    List<StudentInfo> getStudentAllList(@Param("search") StudentSearch search,@Param("xjhList") List<String> xjhList);
}
