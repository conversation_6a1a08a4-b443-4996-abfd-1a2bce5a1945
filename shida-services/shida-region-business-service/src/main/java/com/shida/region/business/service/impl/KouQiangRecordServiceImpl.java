package com.shida.region.business.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shida.AccountCommonService;
import com.shida.constant.RoleConstant;
import com.shida.enums.RestCode;
import com.shida.excel.ExcelBatchImportResponse;
import com.shida.excel.ExcelHelper;
import com.shida.exceptions.ServiceException;
import com.shida.pojo.vo.AccountInfo;
import com.shida.pojo.vo.PageInfo;
import com.shida.pojo.vo.RoleInfo;
import com.shida.region.business.entity.Clazz;
import com.shida.region.business.entity.KouQiangRecord;
import com.shida.region.business.entity.School;
import com.shida.region.business.mapper.KouQiangRecordMapper;
import com.shida.region.business.mapper.SchoolMapper;
import com.shida.region.business.mapper.StudentMapper;
import com.shida.region.business.pojo.excel.StudentKouQiangExportExcel;
import com.shida.region.business.pojo.excel.StudentKouQiangImportExcel;
import com.shida.region.business.pojo.excel.StudentNeiKeImportExcel;
import com.shida.region.business.pojo.search.HealthSearch;
import com.shida.region.business.pojo.search.StudentSearch;
import com.shida.region.business.pojo.vo.StudentInfo;
import com.shida.region.business.pojo.vo.StudentKouQiangInfo;
import com.shida.region.business.service.IClazzService;
import com.shida.region.business.service.IKouQiangRecordService;
import com.shida.region.business.service.ISchoolService;
import com.shida.region.business.tenant.TenantContextHolder;
import com.shida.userCenter.api.IFeignGradeService;
import com.shida.userCenter.api.IFeignTermService;
import com.shida.userCenter.api.IFeignUserService;
import com.shida.userCenter.dto.GradeDto;
import com.shida.userCenter.dto.OrgDto;
import com.shida.userCenter.dto.TermDto;
import com.shida.userCenter.dto.UserDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <p>
 * 学生口腔检查表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
@Service
public class KouQiangRecordServiceImpl extends ServiceImpl<KouQiangRecordMapper, KouQiangRecord> implements IKouQiangRecordService {
    @Resource
    private IFeignUserService feignUserService;
    @Resource
    private IFeignGradeService gradeService;
    @Resource
    private IClazzService clazzService;
    @Resource
    private IFeignTermService feignTermService;
    @Resource
    private ISchoolService schoolService;
    @Resource
    private StudentMapper studentMapper;
    @Resource
    private SchoolMapper schoolMapper;
    @Resource
    private TenantContextHolder tenantContextHolder;

    @Override
    public PageInfo<StudentKouQiangInfo> getStudentKouQiangPageData(HealthSearch search) {
        IPage<StudentKouQiangInfo> page = new Page<>(search.getPageNumber(), search.getPageSize());
        page = baseMapper.getPageData(page, search);
        List<Long> userIds = page.getRecords().stream().map(StudentKouQiangInfo::getUserId).collect(Collectors.toList());
        Set<Long> gradeIds = page.getRecords().stream().map(StudentKouQiangInfo::getGradeId).collect(Collectors.toSet());
        Set<Long> termIds = page.getRecords().stream().map(StudentKouQiangInfo::getTermId).collect(Collectors.toSet());
        List<GradeDto> gradeList = CollUtil.isNotEmpty(gradeIds) ? gradeService.getGradeList(ListUtil.toList(gradeIds)) : new ArrayList<>();
        List<TermDto> termList = CollUtil.isNotEmpty(gradeIds) ? feignTermService.getTermList(ListUtil.toList(termIds)) : new ArrayList<>();
        List<UserDto> userList = CollUtil.isNotEmpty(userIds) ? feignUserService.getUserDtoList(userIds) : new ArrayList<>();

        page.getRecords().forEach(studentKq -> {
            UserDto userDto = userList.stream().filter(user -> user.getId().equals(studentKq.getUserId())).findFirst().orElse(null);
            if (ObjectUtil.isNotEmpty(userDto)) {
                studentKq.setRegionName(userDto.getRegionName());
                studentKq.setRegionId(userDto.getRegionId());
                GradeDto gradeDto = gradeList.stream().filter(grade -> grade.getId().equals(studentKq.getGradeId())).findFirst().orElse(null);
                TermDto termDto = termList.stream().filter(term -> term.getId().equals(studentKq.getTermId())).findFirst().orElse(null);
                if (ObjectUtil.isNotEmpty(gradeDto)) {
                    studentKq.setGradeName(gradeDto.getName());
                }
                if (ObjectUtil.isNotEmpty(termDto)) {
                    studentKq.setTermName(termDto.getTermName());
                }
            }
        });
        PageInfo<StudentKouQiangInfo> result = PageInfo.fromMybatisPage(page, StudentKouQiangInfo.class);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ExcelBatchImportResponse> importStudentKouQiang(MultipartFile file, Long regoinId) {
        AccountInfo account = AccountCommonService.getCurrentUser();
        List<RoleInfo> roles = account.getRoles();
        if (roles.stream().anyMatch(roleInfo -> roleInfo.getRoleCode().equals(RoleConstant.CITY_ADMIN_CODE))) {//市级管理员不校验区县
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "市级管理员不能导入学生口腔检查");//因为不同区县的学生要导入不同的区县库
        }
        List<ExcelBatchImportResponse> result = new ArrayList<>();
        List<StudentKouQiangImportExcel> excelList = new ArrayList<>();
        try {
            excelList = ExcelHelper.readData(file.getInputStream(), StudentKouQiangImportExcel.class, 0);
        } catch (Exception e) {
            log.error("读取学生口腔检查Excel发生错误，请稍后重试", e);
            result.add(new ExcelBatchImportResponse().setMessage("读取学生口腔检查Excel发生错误，请稍后重试").setRowIndex(1));
        }
        if (CollUtil.isEmpty(excelList)) {
            return result;
        }

        List<String> xjhList = excelList.stream().map(StudentKouQiangImportExcel::getXjh).distinct().collect(Collectors.toList());
        List<StudentInfo> studentInfoList = studentMapper.getStudentAllList(new StudentSearch(),xjhList);
        List<School> schoolList = schoolMapper.selectList(new LambdaQueryWrapper<School>().eq(School::getRegionId, regoinId));
        List<TermDto> termList = feignTermService.getAllTermList();

        List<KouQiangRecord> kouQiangRecordList = new ArrayList<KouQiangRecord>();
        for (int i = 0; i < excelList.size(); i++) {
            KouQiangRecord kouQiangRecord = new KouQiangRecord();
            StudentKouQiangImportExcel studentKouQiangImportExcel = excelList.get(i);
            List<ExcelBatchImportResponse> validateData = studentKouQiangImportExcel.validateData(i + 1);
            if (CollUtil.isNotEmpty(validateData)) {
                result.addAll(validateData);
                return result;
            }
            // 判定Excel中学籍号有无重复
            if (excelList.stream().filter(record -> record.getXjh().equals(studentKouQiangImportExcel.getXjh())).count() > 1) {
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中学籍号【" + studentKouQiangImportExcel.getXjh() + "】存在重复数据").setRowIndex(i + 1));
            }
            if (schoolList.stream().noneMatch(schoolDto -> schoolDto.getName().equals(studentKouQiangImportExcel.getSchoolName()))) {
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中所属学校【" + studentKouQiangImportExcel.getSchoolName() + "】不存在").setRowIndex(i + 1));
            }
            if (termList.stream().noneMatch(termDto -> termDto.getTermName().equals(studentKouQiangImportExcel.getTermName()))) {
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中学期【" + studentKouQiangImportExcel.getTermName() + "】不存在").setRowIndex(i + 1));
            } else {
                TermDto termInfo = termList.stream().filter(term -> term.getTermName().equals(studentKouQiangImportExcel.getTermName())).findFirst().orElse(null);
                if (termInfo.getStatus().equals(2)) {
                    result.add(new ExcelBatchImportResponse().setMessage("Excel文件中学期【" + studentKouQiangImportExcel.getTermName() + "】已归档").setRowIndex(i + 1));
                }
                kouQiangRecord.setTermId(termInfo.getId());
            }
            if (studentInfoList.stream().noneMatch(student -> student.getXjh().equals(studentKouQiangImportExcel.getXjh()))) {
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中学籍号【" + studentKouQiangImportExcel.getXjh() + "】不存在").setRowIndex(i + 1));
            } else {
                StudentInfo studentInfo = studentInfoList.stream().filter(student -> student.getXjh().equals(studentKouQiangImportExcel.getXjh())).findFirst().orElse(null);
                if (!studentInfo.getXm().equals(studentKouQiangImportExcel.getStudentName())) {
                    result.add(new ExcelBatchImportResponse().setMessage("Excel文件中姓名【" + studentKouQiangImportExcel.getStudentName() + "】与数据库中不一致").setRowIndex(i + 1));
                }
                if (!studentInfo.getClazzName().equals(studentKouQiangImportExcel.getClassName())) {
                    result.add(new ExcelBatchImportResponse().setMessage("Excel文件中班级【" + studentKouQiangImportExcel.getClassName() + "】与数据库中不一致").setRowIndex(i + 1));
                }
                if (!studentInfo.getEnrollYear().toString().equals(studentKouQiangImportExcel.getEnrolYear())) {
                    result.add(new ExcelBatchImportResponse().setMessage("Excel文件中入学年份【" + studentKouQiangImportExcel.getEnrolYear() + "】与数据库中不一致").setRowIndex(i + 1));
                }
                if (!studentInfo.getSchoolName().equals(studentKouQiangImportExcel.getSchoolName())) {
                    result.add(new ExcelBatchImportResponse().setMessage("Excel文件中所属学校【" + studentKouQiangImportExcel.getSchoolName() + "】与数据库中不一致").setRowIndex(i + 1));
                }
                kouQiangRecord.setStudentId(studentInfo.getId());
                kouQiangRecord.setSchoolId(studentInfo.getSchoolId());
                kouQiangRecord.setClazzId(studentInfo.getClazzId());
            }
            if (!studentKouQiangImportExcel.getSfqc().equals("是") && !studentKouQiangImportExcel.getSfqc().equals("否")) {
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中是否龋齿【" + studentKouQiangImportExcel.getSfqc() + "】输入不正确").setRowIndex(i + 1));
            } else {
                kouQiangRecord.setSfqc(studentKouQiangImportExcel.getSfqc().equals("是"));
            }
            if (!studentKouQiangImportExcel.getSfyzjb().equals("是") && !studentKouQiangImportExcel.getSfyzjb().equals("否")) {
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中是否牙周疾病【" + studentKouQiangImportExcel.getSfyzjb() + "】输入不正确").setRowIndex(i + 1));
            } else {
                kouQiangRecord.setSfyzjb(studentKouQiangImportExcel.getSfyzjb().equals("是"));
            }
            kouQiangRecord.setCreateTime(LocalDateTime.now());
            kouQiangRecordList.add(kouQiangRecord);
        }
        if (CollUtil.isNotEmpty(result)) {
            return result;
        }

        if (CollUtil.isEmpty(result) && CollUtil.isNotEmpty(kouQiangRecordList)) {
            //每批1000条
            int count = 1000;
            //分批数量
            int runSizes = (int) Math.ceil(kouQiangRecordList.size() / (float) count);
            //循环分批批量插入
            for (int j = 0; j < runSizes; j++) {
                List<KouQiangRecord> newKouQiangRecordlist = null;
                if ((j + 1) == runSizes) {
                    int startIndex = (j * count);
                    int endIndex = kouQiangRecordList.size();
                    newKouQiangRecordlist = kouQiangRecordList.subList(startIndex, endIndex);
                } else {
                    int startIndex = (j * count);
                    int endIndex = (j + 1) * count;
                    newKouQiangRecordlist = kouQiangRecordList.subList(startIndex, endIndex);
                }
                super.saveBatch(newKouQiangRecordlist);
            }

        }
        return result;
    }

    @Override
    public void exportStudentKouQiang(HealthSearch search, HttpServletResponse response) {
        OrgDto region = tenantContextHolder.getTenant();
        search.setPageNumber(1).setPageSize(1000);
        PageInfo<StudentKouQiangInfo> pageInfo;
        ExcelHelper<StudentKouQiangExportExcel> excelHelper = ExcelHelper.create(StudentKouQiangExportExcel.class);
        AtomicInteger index = new AtomicInteger(1);
        do {
            pageInfo = this.getStudentKouQiangPageData(search);
            List<StudentKouQiangExportExcel> excelList;
            List<Clazz> clazzList = clazzService.list(new LambdaQueryWrapper<Clazz>().in(Clazz::getId, pageInfo.getRecords().stream().map(StudentKouQiangInfo::getClazzId).distinct().collect(Collectors.toList())));
            List<School> schoolList = schoolService.list(new LambdaQueryWrapper<School>().in(School::getId, pageInfo.getRecords().stream().map(StudentKouQiangInfo::getSchoolId).distinct().collect(Collectors.toList())));
            List<TermDto> termDtoList = feignTermService.getTermList(pageInfo.getRecords().stream().map(StudentKouQiangInfo::getTermId).distinct().collect(Collectors.toList()));

            excelList = pageInfo.getRecords().stream().map(studentKouQiangInfo -> {
                StudentKouQiangExportExcel studentKouQiangExportExcel = new StudentKouQiangExportExcel();
                studentKouQiangExportExcel.setRowNum(index.getAndIncrement());
                studentKouQiangExportExcel.setStudentName(studentKouQiangInfo.getXm());
                studentKouQiangExportExcel.setStudentNumber(studentKouQiangInfo.getXjh());
                if (ObjectUtil.isNotNull(studentKouQiangInfo.getXb())) {
                    studentKouQiangExportExcel.setGenderStr(studentKouQiangInfo.getXb() == 1 ? "男" : "女");
                }
                studentKouQiangExportExcel.setClazzName(clazzList.stream()
                    .filter(clazz -> clazz.getId().equals(studentKouQiangInfo.getClazzId()))
                    .findFirst()
                    .map(Clazz::getName)
                    .orElse(""));
                studentKouQiangExportExcel.setSchoolName(schoolList.stream().
                    filter(school -> school.getId().equals(studentKouQiangInfo.getSchoolId()))
                    .findFirst()
                    .map(School::getName)
                    .orElse(""));
                studentKouQiangExportExcel.setRegionName(region.getName());
                studentKouQiangExportExcel.setTermName(termDtoList.stream()
                    .filter(termDto -> termDto.getId().equals(studentKouQiangInfo.getTermId()))
                    .findFirst()
                    .map(TermDto::getTermName)
                    .orElse(""));
                if (ObjectUtil.isNotNull(studentKouQiangInfo.getSfqc())) {
                    studentKouQiangExportExcel.setSfyc(studentKouQiangInfo.getSfqc() ? "是" : "否");
                }
                if (ObjectUtil.isNotNull(studentKouQiangInfo.getSfyzjb())) {
                    studentKouQiangExportExcel.setSfyzjb(studentKouQiangInfo.getSfyzjb() ? "是" : "否");
                }
                return studentKouQiangExportExcel;
            }).collect(Collectors.toList());
            excelHelper.writeData(excelList);
            search.setPageNumber(search.getPageNumber() + 1);
        } while (pageInfo.hasNext());
        excelHelper.writeToWebResponse(response, region.getName() + "-学生口腔检查记录表");
    }

}
