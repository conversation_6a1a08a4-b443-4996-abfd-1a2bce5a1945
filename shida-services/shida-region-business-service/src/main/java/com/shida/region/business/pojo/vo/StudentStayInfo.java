package com.shida.region.business.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.form
 * @ClassName: StudentStayInfo
 * @Version 1.0
 * @date 2025/9/5 15:41
 */
@Data
@Accessors(chain = true)
@Schema(name = "StudentStayInfo", description = "留级学生信息")
public class StudentStayInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    @Schema(description = "ID")
    private Long id;
    @Schema(description = "申请ID")
    private Long adjustId;
    @Schema(description = "学生ID")
    private Long studentId;
    @Schema(description = "学生姓名")
    private String xm;
    @Schema(description = "性别")
    private Integer xb;
    @Schema(description = "身份证号")
    private String sfzh;
    @Schema(description = "学籍号")
    private String xjh;
    @Schema(description = "异动类型")
    private String adjustType;
    @Schema(description = "原班级名称")
    private String oldClassName;
    @Schema(description = "新班级ID")
    private Long clazzId;
    @Schema(description = "新班级名称")
    private String clazzName;
    @Schema(description = "原年级名称")
    private String oldGradeName;
    @Schema(description = "新年级ID")
    private Long gradeId;
    @Schema(description = "新年级名称")
    private String gradeName;
    @Schema(description = "申请日期")
    private LocalDate applyDate;
    @Schema(description = "留级原因")
    private String stayReason;
    private Long applyUserId;
    @Schema(description = "申请用户姓名")
    private String applyUserName;
}
