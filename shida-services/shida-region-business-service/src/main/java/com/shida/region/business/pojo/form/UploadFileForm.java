package com.shida.region.business.pojo.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @Author: 老木头
 * @Date: 2022/1/14 8:57
 */
@Data
@Accessors(chain = true)
@Schema(name = "UploadFileForm", description = "文件上传时携带参数")
public class UploadFileForm implements Serializable {
    @Schema(description = "文件类型,TEMP_FILE临时文件，不入库，AVATAR_FILE头像文件，DOC_FILE文档文件，IMAGE_FILE图片文件，" +
            "EDITOR_FILE富文本文件，OTHER_FILE未知文件")
    @NotBlank(message = "文件类型不能为空")
    private String fileType;
    @Schema(description = "存储文件时文件名称（包含扩展名）,如果为空时，存储文件名称为雪花算法的ID")
    private String saveFileName;
}
