package com.shida.region.business.controller.TiYu<PERSON><PERSON><PERSON><PERSON>;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.excel.ExcelBatchImportResponse;
import com.shida.noRepeatSubmit.NoRepeatSubmit;
import com.shida.pojo.vo.PageInfo;

import com.shida.region.business.pojo.search.InterclassExerciseSearch;
import com.shida.region.business.pojo.vo.InterclassExerciseInfo;
import com.shida.region.business.service.IBInterclassExerciseRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

@Tag(name = "interclassExercise-课间操出勤率")
@RestController
@RequestMapping("/interclass-exercise/manage")
public class InterclassExerciseManageController {
    @Resource
    private IBInterclassExerciseRecordService interclassExerciseRecordService;


    @SaCheckPermission(value = "interclassExercise:list")
    @Operation(summary = "获取课间操出勤率列表")
    @PostMapping("/getPageData")
    public PageInfo<InterclassExerciseInfo> getPageData(@RequestBody @Valid InterclassExerciseSearch search) {
        return interclassExerciseRecordService.getPageData(search);
    }

    @SaCheckPermission(value = "interclassExercise:import")
    @Operation(summary = "下载导入模板")
    @PostMapping("/downloadBatchImportTemplate")
    public void downloadBatchImportTemplate(HttpServletResponse response,
                                            @RequestHeader("X-Tenant-Id") String regionId,
                                            @RequestParam @Schema(description = "学期ID") Long termId) {
        interclassExerciseRecordService.downloadBatchImportTemplate(response, regionId, termId);
    }

    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_IMPORT)
    @SaCheckPermission(value = "interclassExercise:import")
    @Operation(summary = "批量导入课间操出勤率信息")
    @PostMapping("/batchImport")
    public List<ExcelBatchImportResponse> batchImport(MultipartFile file, @RequestParam @Schema(description = "学期ID") Long termId) {
        return interclassExerciseRecordService.batchImport(file, termId);
    }


    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_DELETE)
    @SaCheckPermission(value = "interclassExercise:delete")
    @Operation(summary = "删除学生考勤数据")
    @PostMapping("/deleteData")
    public boolean deleteData(@RequestParam @Schema(description = "学生ID") Long studentId, @RequestParam @Schema(description = "学期ID") Long termId) {
        return interclassExerciseRecordService.deleteData(studentId,termId);
    }

    @SaCheckPermission("interclassExercise:export")
    @NoRepeatSubmit
    @Operation(summary = "导出课间操出勤列表")
    @PostMapping("/export")
    @AutoLog(value = "导出课间操出勤列表",operateType = OperateType.OPERATE_EXPORT)
    public void export(@RequestBody InterclassExerciseSearch search, HttpServletResponse response) {
        interclassExerciseRecordService.exportInterclassExercise(search, response);
    }
}
