package com.shida.region.business.service;

import com.shida.excel.ExcelBatchImportResponse;
import com.shida.pojo.vo.KeyValue;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.business.entity.Teacher;
import com.baomidou.mybatisplus.extension.service.IService;
import com.shida.region.business.pojo.form.TeacherForm;
import com.shida.region.business.pojo.search.TeacherSearch;
import com.shida.region.business.pojo.vo.TeacherInfo;
import io.seata.spring.annotation.GlobalTransactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
public interface  ITeacherService extends IService<Teacher> {

    PageInfo<TeacherInfo> getPageData(TeacherSearch search);

    TeacherInfo getDetailById(Long id);

    List<Teacher> getTeacherList(List<Long> teacherIds);

    Integer saveTeacher(TeacherForm teacher);

    Integer updateTeacher(TeacherForm teacher);

    Integer deleteTeacher(Long id);

    List<KeyValue<Long, String>> getTeacherSelectList(Long schoolId);

    List<ExcelBatchImportResponse> importTeacher(MultipartFile file,Long regoinId);

    void exportTeacher(TeacherSearch search, HttpServletResponse response);
}
