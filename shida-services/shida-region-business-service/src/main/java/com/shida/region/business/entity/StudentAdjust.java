package com.shida.region.business.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.shida.entity.BaseEntity;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 学籍异动
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-02
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("b_student_adjust")
@Schema(name = "StudentAdjust", description = "学籍异动")
public class StudentAdjust extends BaseEntity<StudentAdjust> {

    private static final long serialVersionUID = 1L;

    @Schema(description = "异动ID")
    private String adjustId;

    @Schema(description = "异动类型：1休学 2复学，3市内转入，4市内转出，5市外转入，6市外转出，7留级，8跳级")
    private String adjustType;

    @Schema(description = "学籍号")
    private String xjh;

    @Schema(description = "身份证号")
    private String sfzh;
    @Schema(description = "学生姓名")
    private String studentName;

    @Schema(description = "性别")
    private Integer xb;
    @Schema(description = "学生ID")
    private Long studentId;

    @Schema(description = "学校ID")
    private Long schoolId;

    @Schema(description = "原区县ID")
    private Long oldRegionId;

    @Schema(description = "新区县ID")
    private Long newRegionId;

    @Schema(description = "异动预计结束日期")
    private LocalDate adjustEndDate;

    @Schema(description = "异动开始日期")
    private LocalDate adjustBeginDate;

    @Schema(description = "休学原因")
    private String adjustReason;

    @Schema(description = "附件地址")
    private String attachment;

    @Schema(description = "1待审核 2 审核通过 3审核驳回")
    private Integer status;

    @Schema(description = "审核过程")
    private String auditProcess;

    @Schema(description = "审核完成时间")
    private LocalDateTime updateTime;

    @Schema(description = "操作人")
    private Long applyUserId;

    @Schema(description = "转入学校ID")
    private Long intoSchoolId;

    @Schema(description = "转入班级ID")
    private Long intoClazzId;

    @Schema(description = "转入年级ID")
    private Long intoGradeId;

    @Schema(description = "转入城市")
    private String intoCityName;
    @Schema(description = "转入学校")
    private String intoSchoolName;
    @Schema(description = "转出城市")
    private String outCity;

    @Schema(description = "转出学校")
    private String outSchool;

    @Schema(description = "转出年级")
    private String outGrade;

    @Schema(description = "转出班级")
    private String outClazz;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
