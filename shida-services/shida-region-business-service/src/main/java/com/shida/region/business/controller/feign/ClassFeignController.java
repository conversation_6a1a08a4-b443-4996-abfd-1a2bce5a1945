package com.shida.region.business.controller.feign;

import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DataSourceProperty;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.shida.IFeignClassService;
import com.shida.region.business.entity.Clazz;
import com.shida.region.business.service.IClazzService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

@Tag(name = "Feign-班级服务")
@RestController
public class ClassFeignController implements IFeignClassService {
    @Resource
    private DynamicDataSourceProperties dynamicDataSourceProperties;

    @Resource
    private IClazzService clazzService;

    @Operation(summary = "检查年级下是否有班级")
    @Override
    public Boolean checkHasGradeClass(Long gradeId) {
        Map<String, DataSourceProperty> dataSources = dynamicDataSourceProperties.getDatasource();
        boolean result = false;
        for (Map.Entry<String, DataSourceProperty> entry : dataSources.entrySet()) {
            if (!entry.getKey().equals("master")) {
                DynamicDataSourceContextHolder.push(entry.getKey());
                result = clazzService.exists(new LambdaQueryWrapper<Clazz>().eq(Clazz::getGradeId, gradeId));
                DynamicDataSourceContextHolder.clear();
                if (result) {
                    break;
                }
            }
        }

        return result;
    }

    @Operation(summary = "修改年级的入学年份")
    @Override
    public String changeGradeEnrollYear(Long regionId, Long gradeId, Integer year) {
        clazzService.update(new UpdateWrapper<Clazz>().lambda().eq(Clazz::getGradeId, gradeId).set(Clazz::getEnrollYear, year));
        return "success";
    }
}
