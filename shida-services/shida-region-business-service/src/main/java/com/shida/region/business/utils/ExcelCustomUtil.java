package com.shida.region.business.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.IService;
import com.shida.excel.ExcelBatchImportResponse;
import com.shida.exceptions.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/9/4 13:32
 */
@Slf4j
public class ExcelCustomUtil {

    public static void setSizeColumn(Sheet sheet, int size) {
        for (int columnNum = 0; columnNum <= size; columnNum++) {
            int columnWidth = sheet.getColumnWidth(columnNum) / 256;
            for (int rowNum = 0; rowNum <= sheet.getLastRowNum(); rowNum++) {
                Row currentRow;
                //当前行未被使用过
                if (sheet.getRow(rowNum) == null) {
                    currentRow = sheet.createRow(rowNum);
                } else {
                    currentRow = sheet.getRow(rowNum);
                }

                if (currentRow.getCell(columnNum) != null) {
                    Cell currentCell = currentRow.getCell(columnNum);
                    if (currentCell.getCellType() == CellType.STRING) {
                        int length = currentCell.getStringCellValue().getBytes().length;
                        if (columnWidth < length) {
                            columnWidth = length;
                        }
                    }
                }
            }
            sheet.setColumnWidth(columnNum, columnWidth * 256);
        }
    }



    /**
     * 导入读取excel并插入到数据库
     *
     * @param sheetIndex
     * @param file
     * @param service
     * @param headerAlias
     * @param type
     * @return void
     */
    public static <T> List<T> importExcel(int sheetIndex, MultipartFile file, IService service, Map<String, String> headerAlias, Class<T> type) {
        return importExcel(sheetIndex, file, service, headerAlias, type, null, null);
    }


    /**
     * 导入读取excel并插入到数据库
     *
     * @param sheetIndex
     * @param file
     * @param service
     * @param headerAlias
     * @param type
     * @return void
     */
    private static <T> List<T> importExcel(int sheetIndex, MultipartFile file, IService service, Map<String, String> headerAlias, Class<T> type, String field, String filedValue) {
        ExcelReader reader = null;
        List<T> readTotal = new ArrayList<>();
        try {
            reader = ExcelUtil.getReader(file.getInputStream(), sheetIndex);
        } catch (IOException e) {
            e.printStackTrace();
        }
        assert reader != null;
        reader.setIgnoreEmptyRow(true);
        reader.setHeaderAlias(headerAlias);
        int pageNum = 1;
        int pageSize = 1;
        int totalSize = 0;
        int row = 1;
        while (true) {
            try {
                List<T> read = reader.read(0, (pageNum - 1) * pageSize + 1, pageNum * pageSize, type);
                read.forEach(item -> readTotal.add(item));
                if (null != service) {
                    service.saveBatch(read);
                }
                log.info(JSON.toJSONString(read));
                int i = read.size();
                totalSize += i;
                pageNum++;
                if (i < pageSize) {
                    break;
                }
                row++;
            } catch (Exception e) {
                throw new ServiceException("第"+(row + 1)+"行数据有问题,请检查!");
            }
        }
        log.info("总共导入数据={}", totalSize);
        if (readTotal.isEmpty()) {
            throw new ServiceException("导入数据为空!");
        }
        return readTotal;
    }

    /**
     * 去空格
     */
    public static String trim(String str) {
        return (str == null ? "" : str.replaceAll("\\s*", ""));
    }
    /**
     * 去空格
     */
    public static String trimByExcelResult(String str,List<ExcelBatchImportResponse> result,int i,String content) {
        String backStr = (str == null ? "" : str.replaceAll("\\s*", ""));
        if (StrUtil.isEmpty(backStr)) {
            result.add(new ExcelBatchImportResponse().setMessage("Excel文件中【" + content + "】不能为空").setRowIndex(i));
        }
        return backStr;
    }
}
