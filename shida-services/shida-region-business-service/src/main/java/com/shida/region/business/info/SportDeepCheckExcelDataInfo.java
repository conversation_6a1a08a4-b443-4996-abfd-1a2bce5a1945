package com.shida.region.business.info;

import com.shida.region.business.entity.Clazz;
import com.shida.region.business.entity.Student;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/8/25 17:18
 * @description: 体育课出勤率校验结果集
 */
@Data
@Accessors(chain = true)
public class SportDeepCheckExcelDataInfo {
    /**
     * 学生集合 key:学籍号 value:学生信息
     */
    Map<String, Student> studentStutusMap;
    /**
     * 学生集合 key:studentId value:学生信息
     */
    Map<Long, Student> studentIdMap;
    /**
     * 班级列表 key:班级名称 value:班级信息
     */
    Map<String, Clazz> clazzMap;
}
