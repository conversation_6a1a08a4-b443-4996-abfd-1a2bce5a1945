package com.shida.region.business.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.shida.region.business.entity.Device;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shida.region.business.pojo.search.DeviceSearch;
import com.shida.region.business.pojo.vo.DeviceInfo;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
public interface DeviceMapper extends BaseMapper<Device> {

    IPage<DeviceInfo> getPageData(IPage<DeviceInfo> page, @Param("search") DeviceSearch search);
}
