package com.shida.region.business.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.vo
 * @ClassName: StudentKouQiangInfo
 * @Version 1.0
 * @date 2025/9/2
 */
@Data
@Accessors(chain = true)
@Schema(name = "StudentKouQiangInfo", description = "学生口腔检查信息")
public class StudentKouQiangInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    @Schema(description = "学生口腔检查ID")
    private Long id;
    @Schema(description = "学生ID")
    private Long studentId;
    @Schema(description = "学期ID")
    private Long termId;
    @Schema(description = "是否龋齿")
    private Boolean sfqc;
    @Schema(description = "是否牙周疾病")
    private Boolean sfyzjb;

    @Schema(description = "学期名称")
    private String termName;
    @Schema(description = "用户ID")
    private Long userId;
    @Schema(description = "学生姓名")
    private String xm;
    @Schema(description = "学生性别")
    private Integer xb;
    @Schema(description = "班级ID")
    private Long clazzId;
    @Schema(description = "班级名称")
    private String clazzName;
    @Schema(description = "学校ID")
    private Long schoolId;
    @Schema(description = "学校名称")
    private String schoolName;
    @Schema(description = "年级ID")
    private Long gradeId;
    @Schema(description = "年级名称")
    private String gradeName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    @Schema(description = "区县ID")
    private Long regionId;
    @Schema(description = "区县名称")
    private String regionName;
    @Schema(description = "学籍号")
    private String xjh;
    @Schema(description = "身份证号")
    private String sfzh;
    @Schema(description = "学段")
    private Integer period;
    @Schema(description = "学段名称")
    private String periodName;
}
