package com.shida.region.business.service.impl;

import java.time.LocalDateTime;
import java.util.Objects;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shida.AccountCommonService;
import com.shida.enums.RestCode;
import com.shida.enums.ReviewStatusEnum;
import com.shida.exceptions.ServiceException;
import com.shida.pojo.vo.AccountInfo;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.business.entity.Mianhuankaosheng;
import com.shida.region.business.mapper.MianhuankaoshengMapper;
import com.shida.region.business.mapper.MianhuankaoshengRecordMapper;
import com.shida.region.business.mapper.StudentMapper;
import com.shida.region.business.pojo.form.MianhuankaoshengForm;
import com.shida.region.business.pojo.search.MianhuankaoshengSearch;
import com.shida.region.business.pojo.vo.MianhuankaoshengVo;
import com.shida.region.business.pojo.vo.StudentInfo;
import com.shida.region.business.service.IMianhuankaoshengService;

import cn.hutool.core.bean.BeanUtil;

import javax.annotation.Resource;

/**
 * <p>
 * 免考缓考生管理表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-04
 */
@Service
public class MianhuankaoshengServiceImpl extends ServiceImpl<MianhuankaoshengMapper, Mianhuankaosheng>
		implements IMianhuankaoshengService {

	@Resource
	private MianhuankaoshengRecordMapper mianhuankaoshengRecordMapper;
	@Resource
	private StudentMapper studentMapper;

	@Override
	public PageInfo<MianhuankaoshengVo> getPageData(MianhuankaoshengSearch search) {
		IPage<MianhuankaoshengVo> page = new Page<>(search.getPageNumber(), search.getPageSize());
		AccountInfo currentUser = AccountCommonService.getCurrentUser();
		if (currentUser.getCountyAdminFlag()) {
			search.setRegionId(currentUser.getRegionId());
		}
		if (currentUser.getSchoolAdminFlag()) {
			search.setSchoolId(currentUser.getSchoolId());
		}
		page = baseMapper.getPageData(page, search);
		return PageInfo.fromMybatisPage(page);
	}

	@Override
	public Integer add(MianhuankaoshengForm mianhuankaoshengForm) {
		// 验证学生是否存在
		StudentInfo studentInfo = studentMapper.getDetailById(mianhuankaoshengForm.getStudentId());
		if (null == studentInfo || !studentInfo.getXjh().equals(mianhuankaoshengForm.getXjh())
				|| !studentInfo.getSfzh().equals(mianhuankaoshengForm.getSfzh())) {
			throw new ServiceException(RestCode.DATA_ERROR, "学生信息有误");
		}
		QueryWrapper<Mianhuankaosheng> queryWrapper = new QueryWrapper<Mianhuankaosheng>();
		queryWrapper.lambda().eq(Mianhuankaosheng::getXjh, mianhuankaoshengForm.getXjh())
				.eq(Mianhuankaosheng::getTermLimit, mianhuankaoshengForm.getTermLimit());
		Mianhuankaosheng entity = baseMapper.selectOne(queryWrapper);
		if (null != entity) {
			throw new ServiceException(RestCode.DATA_ERROR, "已存在");
		} else {
			entity = new Mianhuankaosheng();
			LocalDateTime date = LocalDateTime.now();
			BeanUtil.copyProperties(mianhuankaoshengForm, entity);
			entity.setCategaryDescription(mianhuankaoshengForm.getCategaryDescription().trim());
			entity.setCreateTime(date);
			entity.setCountyReviewStatus(ReviewStatusEnum.waiting.getCode());
			entity.setCityReviewStatus(ReviewStatusEnum.waiting.getCode());
			return baseMapper.insert(entity);
		}
	}

}
