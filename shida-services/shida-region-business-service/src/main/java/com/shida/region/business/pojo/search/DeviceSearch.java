package com.shida.region.business.pojo.search;

import com.shida.pojo.search.Search;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(name = "DeviceSearch",description = "设备搜索条件")
public class DeviceSearch extends Search {
    @Schema(description = "所属区域ID")
    private Long regionId;
    @Schema(description = "所属学校ID")
    private Long schoolId;
    @Schema(description = "在线状态")
    private Boolean onlineStatus;
    @Schema(description = "运动项目")
    private String sportProjectCode;
}
