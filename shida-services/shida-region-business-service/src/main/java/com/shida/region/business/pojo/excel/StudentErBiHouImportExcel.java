package com.shida.region.business.pojo.excel;

import cn.hutool.core.util.StrUtil;
import com.shida.excel.ExcelAlias;
import com.shida.excel.ExcelBatchImportResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.excel
 * @ClassName: TeacherImportExcel
 * @Version 1.0
 * @date 2025/8/14 15:37
 */
@Data
@Accessors(chain = true)
@Schema(name = "StudentErBiHouImportExcel", description = "学生体检 - 耳鼻喉数据批量导入")
public class StudentErBiHouImportExcel implements Serializable {
    @ExcelAlias(value = "序号", orderNo = 1)
    private Integer rowNum;
    @ExcelAlias(value = "姓名")
    private String xm;
    @ExcelAlias(value = "学籍号")
    private String xjh;
    @ExcelAlias(value = "性别")
    private String gender;
    @ExcelAlias(value = "所属班级")
    private String className;
    @ExcelAlias(value = "年级")
    private String gradeName;
    @ExcelAlias(value = "所属学校")
    private String schoolName;
    @ExcelAlias(value = "所属县区")
    private String regionName;
    @ExcelAlias(value = "听力")
    private String tl;
    @ExcelAlias(value = "外耳道与鼓膜")
    private String wedygm;
    @ExcelAlias(value = "外鼻")
    private String wb;
    @ExcelAlias(value = "嗅觉")
    private String xj;
    @ExcelAlias(value = "扁桃体")
    private String btt;
    @ExcelAlias(value = "所属学期")
    private String termName;

    public List<ExcelBatchImportResponse> validateData(Integer rowIndex) {
        List<ExcelBatchImportResponse> result = new ArrayList<>();
        List<String> contentList = Arrays.asList("正常", "异常");
        if (StrUtil.isBlank(xm)) {
            result.add(new ExcelBatchImportResponse().setMessage("姓名不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(xjh)) {
            result.add(new ExcelBatchImportResponse().setMessage("学籍号不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (!StrUtil.startWithAny(xjh, "G", "g", "L", "l", "J", "j")) {
            result.add(new ExcelBatchImportResponse().setMessage("学籍号格式错误").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(gender)) {
            result.add(new ExcelBatchImportResponse().setMessage("性别不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(className)) {
            result.add(new ExcelBatchImportResponse().setMessage("班级不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(gradeName)) {
            result.add(new ExcelBatchImportResponse().setMessage("年纪不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(schoolName)) {
            result.add(new ExcelBatchImportResponse().setMessage("所属学校不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(regionName)) {
            result.add(new ExcelBatchImportResponse().setMessage("所属区县不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(termName)) {
            result.add(new ExcelBatchImportResponse().setMessage("所属学期不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(tl)) {
            result.add(new ExcelBatchImportResponse().setMessage("听力情况不能为空").setType("error").setRowIndex(rowIndex));
        }
        if(contentList.stream().noneMatch(content -> content.equals(tl))){
            result.add(new ExcelBatchImportResponse().setMessage("听力情况请输入【正常】或【异常】").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(wedygm)) {
            result.add(new ExcelBatchImportResponse().setMessage("外耳道与鼓膜情况不能为空").setType("error").setRowIndex(rowIndex));
        }
        if(contentList.stream().noneMatch(content -> content.equals(wedygm))){
            result.add(new ExcelBatchImportResponse().setMessage("耳道与鼓膜情况请输入【正常】或【异常】").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(wb)) {
            result.add(new ExcelBatchImportResponse().setMessage("外鼻情况不能为空").setType("error").setRowIndex(rowIndex));
        }
        if(contentList.stream().noneMatch(content -> content.equals(wb))){
            result.add(new ExcelBatchImportResponse().setMessage("外鼻情况请输入【正常】或【异常】").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(xj)) {
            result.add(new ExcelBatchImportResponse().setMessage("嗅觉情况不能为空").setType("error").setRowIndex(rowIndex));
        }
        if(contentList.stream().noneMatch(content -> content.equals(xj))){
            result.add(new ExcelBatchImportResponse().setMessage("嗅觉情况请输入【正常】或【异常】").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(btt)) {
            result.add(new ExcelBatchImportResponse().setMessage("扁桃体情况不能为空").setType("error").setRowIndex(rowIndex));
        }
        if(contentList.stream().noneMatch(content -> content.equals(btt))){
            result.add(new ExcelBatchImportResponse().setMessage("扁桃体情况请输入【正常】或【异常】").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(termName)) {
            result.add(new ExcelBatchImportResponse().setMessage("所属学期不能为空").setType("error").setRowIndex(rowIndex));
        }
        return result;
    }
}
