package com.shida.region.business.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.vo
 * @ClassName: StudentSuspendInfo
 * @Version 1.0
 * @date 2025/9/2 9:11
 */
@Data
@Accessors(chain = true)
@Schema(name = "StudentSuspendInfo", description = "学生休学信息")
public class StudentSuspendInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "申请ID")
    private Long adjustId;
    @Schema(description = "学生ID")
    private Long studentId;
    @Schema(description = "学生姓名")
    private String studentName;
    @Schema(description = "身份证号")
    private String sfzh;
    @Schema(description = "班级ID")
    private Long classId;
    @Schema(description = "班级名称")
    private String className;
    @Schema(description = "性别")
    private Integer xb;
    @Schema(description = "年级ID")
    private Long gradeId;
    @Schema(description = "年级名称")
    private String gradeName;
    @Schema(description = "学籍号")
    private String XJH;
    @Schema(description = "休学类型")
    private Integer adjustType;
    @Schema(description = "休学原因")
    private String suspendReason;
    @Schema(description = "休学开始时间")
    private LocalDate suspendBeginDate;
    @Schema(description = "复学时间")
    private LocalDate suspendEndDate;
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
    @Schema(description = "附件")
    private List<String> attachment;
    @Schema(description = "操作人ID")
    private Long applyUserId;
    @Schema(description = "操作人用户名")
    private String applyUserName;
}
