package com.shida.region.business.controller.feign;

import com.shida.IFeignSchoolService;
import com.shida.dto.SchoolDto;
import com.shida.region.business.service.ISchoolService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


@Tag(name = "Feign-学校服务")
@RestController
public class SchoolFeignController implements IFeignSchoolService {
    @Resource
    private ISchoolService schoolService;

    @Operation(summary = "根据区域ID和学校ID获取学校信息")
    @Override
    public SchoolDto getByRegionIdAndSchoolId(Long regionId, Long schoolId) {
        return schoolService.getByRegionIdAndSchoolId(regionId, schoolId);
    }

    @Operation(summary = "根据区域ID获取学校列表")
    @Override
    public List<SchoolDto> getByRegionId(Long regionId) {
        return schoolService.getByRegionId(regionId);
    }

    @Operation(summary = "根据区域ID和学校ID列表获取学校列表")
    @Override
    public List<SchoolDto> getByRegionIdAndSchoolIds(Long regionId, List<Long> schoolIds) {
        return schoolService.getByRegionIdAndSchoolIds(regionId, schoolIds);
    }
}
