package com.shida.region.business.controller.base;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.excel.ExcelBatchImportResponse;
import com.shida.noRepeatSubmit.NoRepeatSubmit;
import com.shida.pojo.form.KeyForm;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.business.pojo.form.StudentForm;
import com.shida.region.business.pojo.search.StudentSearch;
import com.shida.region.business.pojo.vo.StudentInfo;
import com.shida.region.business.service.IFileService;
import com.shida.region.business.service.IStudentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.controller
 * @ClassName: StudentManageController
 * @Version 1.0
 * @date 2025/8/18 17:48
 */
@Tag(name = "student-学生管理")
@RestController
@RequestMapping("/student/manage")
public class StudentManageController {
    @Resource
    private IFileService fileService;
    @Autowired
    private IStudentService studentService;


    @SaCheckPermission("student:list")
    @Operation(summary = "查询学生列表")
    @PostMapping("/getPageData")
    public PageInfo<StudentInfo> list(@RequestBody StudentSearch search) {
        return studentService.getPageData(search);
    }
    @SaCheckPermission("student:add")
    @Operation(summary = "新增学生")
    @PostMapping("/save")
    @NoRepeatSubmit
    @AutoLog(value = "新增学生",operateType = OperateType.OPERATE_ADD)
    public Integer save(@RequestBody StudentForm student) {
        return studentService.saveStudent(student);
    }
    @SaCheckPermission("student:edit")
    @Operation(summary = "更新学生")
    @PostMapping("/edit")
    @NoRepeatSubmit
    @AutoLog(value = "更新学生",operateType = OperateType.OPERATE_EDIT)
    public Integer edit(@RequestBody @NotNull StudentForm student) {
        return studentService.updateStudent(student);
    }
    @SaCheckPermission("student:detail")
    @Operation(summary = "查询学生详情")
    @PostMapping("/detail")
    @AutoLog(value = "查询学生详情",operateType = OperateType.OPERATE_SEARCH)
    public StudentInfo detail(@RequestBody KeyForm<Long> form) {
        return studentService.getDetailById(form.getKey());
    }
    @SaCheckPermission("student:detail")
    @Operation(summary = "查询学生详情(学籍号或身份证号)")
    @PostMapping("/getDetailByCertNo")
    @AutoLog(value = "查询学生详情",operateType = OperateType.OPERATE_SEARCH)
    public StudentInfo getDetailByCertNo(@RequestBody KeyForm<String> form) {
        return studentService.getDetailByCertNo(form.getKey());
    }
    @SaCheckPermission("student:delete")
    @Operation(summary = "删除学生")
    @PostMapping("/delete")
    @NoRepeatSubmit
    @AutoLog(value = "删除学生",operateType = OperateType.OPERATE_DELETE)
    public Integer delete(@RequestBody KeyForm<Long> form) {
        return studentService.deleteStudent(form.getKey());
    }
    //@SaCheckPermission("teacher:export")
    @NoRepeatSubmit
    @Operation(summary = "下载批量导入模板")
    @PostMapping("/downloadBatchImportTemplate")
    @AutoLog(value = "下载批量导入模板",operateType = OperateType.OPERATE_EXPORT)
    public void downloadBatchImportTemplate(HttpServletResponse response, @RequestHeader("X-Tenant-Id") Long regionId) throws IOException {
        fileService.downloadExcelTemplate("student-import-template", "学生信息批量导入模板", response);
    }
    @SaCheckPermission("student:import")
    @NoRepeatSubmit
    @Operation(summary = "导入学生列表")
    @PostMapping("/import")
    public List<ExcelBatchImportResponse> importStudent(@RequestHeader("X-Tenant-Id") Long regionId, @RequestParam("file") MultipartFile file) {
        return studentService.importStudent(file,regionId);
    }
    @SaCheckPermission("student:export")
    @NoRepeatSubmit
    @Operation(summary = "导出学生列表")
    @PostMapping("/export")
    @AutoLog(value = "导出学生列表",operateType = OperateType.OPERATE_EXPORT)
    public void export(@RequestBody StudentSearch search, HttpServletResponse response) {
        studentService.exportStudent(search, response);
    }
}
