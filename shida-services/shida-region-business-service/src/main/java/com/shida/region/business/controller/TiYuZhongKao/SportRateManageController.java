package com.shida.region.business.controller.TiYuZhong<PERSON>ao;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.excel.ExcelBatchImportResponse;
import com.shida.noRepeatSubmit.NoRepeatSubmit;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.business.pojo.search.SportRateSearch;
import com.shida.region.business.pojo.vo.SportRateInfo;
import com.shida.region.business.service.ISportRateRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

@Tag(name = "sportRate-体育课出勤率")
@RestController
@RequestMapping("/sport-rate/manage")
public class SportRateManageController {
    @Resource
    private ISportRateRecordService sportRateRecordService;


    @SaCheckPermission(value = "sportRate:list")
    @Operation(summary = "获取体育课出勤率列表")
    @PostMapping("/getPageData")
    public PageInfo<SportRateInfo> getPageData(@RequestBody @Valid SportRateSearch search) {
        return sportRateRecordService.getPageData(search);
    }

    @SaCheckPermission(value = "sportRate:import")
    @Operation(summary = "下载导入模板")
    @PostMapping("/downloadBatchImportTemplate")
    public void downloadBatchImportTemplate(HttpServletResponse response,
                                            @RequestHeader("X-Tenant-Id") String regionId,
                                            @RequestParam @Schema(description = "学期ID") Long termId) {
        sportRateRecordService.downloadBatchImportTemplate(response, regionId, termId);
    }

    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_IMPORT)
    @SaCheckPermission(value = "sportRate:import")
    @Operation(summary = "批量导入体育课出勤率信息")
    @PostMapping("/batchImport")
    public List<ExcelBatchImportResponse> batchImport(MultipartFile file, @RequestParam @Schema(description = "学期ID") Long termId) {
        return sportRateRecordService.batchImport(file, termId);
    }


    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_DELETE)
    @SaCheckPermission(value = "sportRate:delete")
    @Operation(summary = "删除学生考勤数据")
    @PostMapping("/deleteData")
    public boolean deleteData(@RequestParam @Schema(description = "学生ID") Long studentId, @RequestParam @Schema(description = "学期ID") Long termId) {
        return sportRateRecordService.deleteData(studentId,termId);
    }

    @SaCheckPermission("sportRate:export")
    @NoRepeatSubmit
    @Operation(summary = "导出体育课出勤列表")
    @PostMapping("/export")
    @AutoLog(value = "导出体育课出勤列表",operateType = OperateType.OPERATE_EXPORT)
    public void export(@RequestBody SportRateSearch search, HttpServletResponse response) {
        sportRateRecordService.exportSportRate(search, response);
    }
}
