package com.shida.region.business.service;

import com.shida.pojo.vo.PageInfo;
import com.shida.region.business.entity.Device;
import com.baomidou.mybatisplus.extension.service.IService;
import com.shida.region.business.pojo.form.DeviceForm;
import com.shida.region.business.pojo.search.DeviceSearch;
import com.shida.region.business.pojo.vo.DeviceInfo;

import javax.validation.Valid;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
public interface IDeviceService extends IService<Device> {

    PageInfo<DeviceInfo> getPageData(DeviceSearch search);

    String create(@Valid DeviceForm form);

    String updateDevice(@Valid DeviceForm form);
}
