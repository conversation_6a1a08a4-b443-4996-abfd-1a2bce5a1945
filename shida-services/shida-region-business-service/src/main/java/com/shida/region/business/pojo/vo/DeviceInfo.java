package com.shida.region.business.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@Schema(name = "DeviceInfo", description = "设备信息")
public class DeviceInfo implements Serializable {

    @Schema(description = "设备ID")
    private Long id;

    @Schema(description = "设备ID")
    private String deviceId;

    @Schema(description = "设备名称")
    private String deviceName;

    @Schema(description = "检测项目")
    private String projectName;

    @Schema(description = "所属学校ID")
    private Long schoolId;

    @Schema(description = "所属学校名称")
    private String schoolName;

    @Schema(description = "所属区域ID")
    private Long regionId;

    @Schema(description = "所属区域名称")
    private String regionName;

    @Schema(description = "放置位置")
    private String location;

    @Schema(description = "在线状态")
    private Boolean onlineStatus;

    @Schema(description = "设备描述")
    private String description;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}
