package com.shida.region.business.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.shida.region.business.entity.VisionRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shida.region.business.pojo.search.StudentSearch;
import com.shida.region.business.pojo.search.VisionSearch;
import com.shida.region.business.pojo.vo.StudentInfo;
import com.shida.region.business.pojo.vo.VisionRecordVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 学生视力记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
public interface VisionRecordMapper extends BaseMapper<VisionRecord> {

    IPage<VisionRecordVo> getPageData(IPage<VisionRecordVo> page, @Param("search") VisionSearch search);
    List<VisionRecordVo> getViSionList( @Param("search") VisionSearch search);
    List<VisionRecordVo> getExportStu( @Param("search") VisionSearch search);

}
