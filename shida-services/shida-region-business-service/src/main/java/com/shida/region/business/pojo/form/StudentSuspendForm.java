package com.shida.region.business.pojo.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.form
 * @ClassName: StudentSuspendForm
 * @Version 1.0
 * @date 2025/9/2 11:52
 */
@Data
@Accessors(chain = true)
@Schema(name = "StudentSuspendForm", description = "学生休学表单")
public class StudentSuspendForm  implements Serializable {
    private static final long serialVersionUID = 1L;
    @Schema(description = "ID")
    private Long id;
    @Schema(description = "学籍号")
    @NotNull(message = "学号不能为空")
    private String xjh;

    @Schema(description = "休学原因")
    @NotNull(message = "休学原因不能为空")
    private String suspendReason;
    @Schema(description = "异动类型")
//    @NotNull(message = "异动类型不能为空")
    private String adjustType;
    @Schema(description = "休学开始时间")
    @NotNull(message = "休学开始时间不能为空")
    private LocalDate suspendBeginDate;

    @Schema(description = "附件列表")
    private List<String> attachmentList;
    @Schema(description = "复学时间")
    private LocalDate suspendEndDate;
    @Schema(description = "年级ID")
    private Long gradeId;
    @Schema(description = "班级ID")
    private Long clazzId;
}
