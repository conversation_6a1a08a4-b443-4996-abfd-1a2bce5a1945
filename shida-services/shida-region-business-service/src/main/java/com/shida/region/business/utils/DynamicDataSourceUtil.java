package com.shida.region.business.utils;

import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DataSourceProperty;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;

@Component
public class DynamicDataSourceUtil {
    @Resource
    private DynamicDataSourceProperties dynamicDataSourceProperties;

    private static Map<String, DataSourceProperty> dataSources;

    @PostConstruct
    public void init() {
        dataSources = dynamicDataSourceProperties.getDatasource();
    }

    public static boolean isExist(String dataSourceName) {
        return dataSources.containsKey(dataSourceName) || dataSources.containsKey("region-" + dataSourceName);
    }
}
