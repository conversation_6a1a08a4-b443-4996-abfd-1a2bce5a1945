package com.shida.region.business.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@Schema(name = "JingFeiInfo", description = "学生体检-体检经费")
public class JingFeiInfo implements Serializable {
    @Schema(description = "ID")
    private Long id;
    @Schema(description = "所属学期ID")
    private Long termId;
    @Schema(description = "所属学期")
    private String termName;
    @Schema(description = "操作人")
    private String operator;
    @Schema(description = "操作时间")
    protected LocalDateTime operatorTime;
    @Schema(description = "体检总经费（元）")
    private BigDecimal zjf;
    @Schema(description = "人均体检费用（元）")
    private BigDecimal rjjf;
    @Schema(description = "学生人数")
    private Integer studentCount;
    @Schema(description = "学段")
    private Integer period;
    @Schema(description = "所属区县ID")
    private Long regionId;
    @Schema(description = "所属区县名称")
    private String regionName;
}
