package com.shida.region.business.info;

import com.shida.region.business.entity.Clazz;
import com.shida.region.business.entity.Student;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/8/25 17:18
 * @description: 体育课出勤率校验结果集
 */
@Data
@Accessors(chain = true)
public class SportDeepCheckDataInfo {
    // 学籍号
    private final String xjh;
    // 学生姓名
    private final String studentName;
    // 班级名称
    private final String clazzName;
    // 性别【1.男 2.女】
    private final String gather;
}
