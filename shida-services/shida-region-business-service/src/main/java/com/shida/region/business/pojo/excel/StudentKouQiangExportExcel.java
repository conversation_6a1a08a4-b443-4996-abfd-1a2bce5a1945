package com.shida.region.business.pojo.excel;

import com.shida.excel.ExcelAlias;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.excel
 * @ClassName: StudentKouQiangExportExcel
 * @Version 1.0
 * @date 2025/9/3
 */
@Data
@Accessors(chain = true)
@Schema(name = "StudentKouQiangExportExcel", description = "导出的StudentKouQiangExportExcel对象")
public class StudentKouQiangExportExcel implements Serializable {
    @ExcelAlias(value = "序号",orderNo = 1)
    private Integer rowNum;
    @ExcelAlias(value = "姓名",orderNo = 2)
    private String studentName;
    @ExcelAlias(value = "学籍号",orderNo = 3)
    private String studentNumber ;
    @ExcelAlias(value = "性别",orderNo = 4)
    private String genderStr;
    @ExcelAlias(value = "所属班级",orderNo = 5)
    private String clazzName ;
    @ExcelAlias(value = "所属学校",orderNo = 6)
    private String schoolName ;
    @ExcelAlias(value = "所属县区",orderNo = 7)
    private String regionName ;
    @ExcelAlias(value = "是否龋齿",orderNo = 8)
    private String sfyc ;
    @ExcelAlias(value = "是否牙周疾病",orderNo = 9)
    private String sfyzjb;
    @ExcelAlias(value = "所属学期",orderNo = 10)
    private String termName;
}
