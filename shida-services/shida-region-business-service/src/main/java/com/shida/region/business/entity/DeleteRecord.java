package com.shida.region.business.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.shida.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 数据删除记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_delete_record")
@Schema(name = "DeleteRecord", description = "数据删除记录表")
public class DeleteRecord extends BaseEntity<DeleteRecord> {

    private static final long serialVersionUID = 1L;

    @Schema(description = "对象id")
    private Long entityId;

    @Schema(description = "对象名称")
    private String entityName;

    @Schema(description = "对象标识")
    private String entitySignage;

    @Schema(description = "对象json字符串")
    private String entityJson;

    @Schema(description = "关联父对象ID")
    private Long parentEntityId;

    @Schema(description = "操作账户ID")
    private Long operatorId;

    @Schema(description = "删除原因")
    private String reason;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
