package com.shida.region.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shida.AccountCommonService;
import com.shida.enums.RestCode;
import com.shida.excel.ExcelBatchImportResponse;
import com.shida.excel.ExcelHelper;
import com.shida.exceptions.ServiceException;
import com.shida.pojo.vo.AccountInfo;
import com.shida.pojo.vo.KeyValue;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.business.entity.*;
import com.shida.region.business.mapper.*;
import com.shida.region.business.pojo.excel.ClazzImportExcel;
import com.shida.region.business.pojo.form.ClazzForm;
import com.shida.region.business.pojo.search.ClazzSearch;
import com.shida.region.business.pojo.vo.ClazzInfo;
import com.shida.region.business.service.IClazzService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shida.region.business.service.ITeacherClazzService;
import com.shida.region.business.tenant.TenantContextHolder;
import com.shida.userCenter.api.IFeignGradeService;
import com.shida.userCenter.dto.GradeDto;
import com.shida.userCenter.dto.OrgDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 班级信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-03
 */
@Service
public class ClazzServiceImpl extends ServiceImpl<ClazzMapper, Clazz> implements IClazzService {
    @Resource
    private TenantContextHolder tenantContextHolder;

    @Resource
    private IFeignGradeService feignGradeService;

    @Resource
    private ITeacherClazzService teacherClazzService;

    @Resource
    private StudentMapper studentMapper;

    @Resource
    private TeacherMapper teacherMapper;

    @Resource
    private SchoolMapper schoolMapper;

    @Override
    public PageInfo<ClazzInfo> getPageData(ClazzSearch search) {
        IPage<ClazzInfo> page = new Page<>(search.getPageNumber(), search.getPageSize());

        AccountInfo currentUser = AccountCommonService.getCurrentUser();
        if (Objects.nonNull(currentUser.getSchoolId())) {
            search.setSchoolId(currentUser.getSchoolId());
        }

        page = baseMapper.getPageData(page, search);
        List<Long> gradeIds = page.getRecords().stream().map(ClazzInfo::getGradeId).distinct().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(gradeIds)) {
            List<GradeDto> gradeList = feignGradeService.getGradeList(gradeIds);
            OrgDto orgDto = tenantContextHolder.getTenant();
            page.getRecords().forEach(clazzInfo -> {
                gradeList.stream().filter(gradeDto -> gradeDto.getId().equals(clazzInfo.getGradeId()))
                    .findFirst().ifPresent(gradeDto -> clazzInfo.setGradeName(gradeDto.getName()));
                clazzInfo.setRegionId(orgDto.getId()).setRegionName(orgDto.getName());
            });
        }
        return PageInfo.fromMybatisPage(page);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String create(ClazzForm form) {
        if (super.exists(new LambdaQueryWrapper<Clazz>().eq(Clazz::getCode, form.getCode())
            .eq(Clazz::getSchoolId, form.getSchoolId()))) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "该学校中班级代码已存在");
        }
        List<Integer> periods = CollUtil.newArrayList(1, 2, 3);
        if (!periods.contains(form.getPeriod())) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "学段只能选择：小学、初中、高中");
        }

        School school = schoolMapper.selectById(form.getSchoolId());
        if (school == null) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "该学校不存在");
        } else {
            // 校验学段
            if (school.getPeriod() < 4 && !Objects.equals(form.getPeriod(), school.getPeriod())) {
                throw new ServiceException(RestCode.ARGUMENT_ERROR, "该学校学段与班级学段不一致");
            }
            // 九年一贯制不包含高中
            if (school.getPeriod() == 4 && form.getPeriod() == 3) {
                throw new ServiceException(RestCode.ARGUMENT_ERROR, "该学校学段与班级学段不一致");
            }
            // 初高连读不包含小学
            if (school.getPeriod() == 6 && form.getPeriod() == 1) {
                throw new ServiceException(RestCode.ARGUMENT_ERROR, "该学校学段与班级学段不一致");
            }
        }


        Clazz clazz = BeanUtil.copyProperties(form, Clazz.class);
        GradeDto grade = feignGradeService.getGradeById(form.getGradeId());
        clazz.setEnrollYear(grade.getYear()).setStatus(1);
        clazz.insert();
        // 班主任教师
        TeacherClazz masterClassTeacher = new TeacherClazz();
        masterClassTeacher.setClazzId(clazz.getId())
            .setTeacherId(form.getMasterTeacherId())
            .setType(1);
        masterClassTeacher.insert();
        // 体育教师
        TeacherClazz sportClassTeacher = new TeacherClazz();
        sportClassTeacher.setClazzId(clazz.getId())
            .setTeacherId(form.getSportTeacherId())
            .setType(2);
        sportClassTeacher.insert();
        return "success";
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String updateClazz(ClazzForm form) {
        if (Objects.isNull(form.getId())) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "班级ID不能为空");
        }
        if (super.exists(new LambdaQueryWrapper<Clazz>().eq(Clazz::getCode, form.getCode())
            .eq(Clazz::getSchoolId, form.getSchoolId())
            .ne(Clazz::getId, form.getId()))) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "该学校中班级代码已存在");
        }

        List<Integer> periods = CollUtil.newArrayList(1, 2, 3);
        if (!periods.contains(form.getPeriod())) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "学段只能选择：小学、初中、高中");
        }

        School school = schoolMapper.selectById(form.getSchoolId());
        if (school == null) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "该学校不存在");
        } else {
            // 校验学段
            if (school.getPeriod() < 4 && !Objects.equals(form.getPeriod(), school.getPeriod())) {
                throw new ServiceException(RestCode.ARGUMENT_ERROR, "该学校学段与班级学段不一致");
            }
            // 九年一贯制不包含高中
            if (school.getPeriod() == 4 && form.getPeriod() == 3) {
                throw new ServiceException(RestCode.ARGUMENT_ERROR, "该学校学段与班级学段不一致");
            }
            // 初高连读不包含小学
            if (school.getPeriod() == 6 && form.getPeriod() == 1) {
                throw new ServiceException(RestCode.ARGUMENT_ERROR, "该学校学段与班级学段不一致");
            }
        }

        Clazz clazz = BeanUtil.copyProperties(form, Clazz.class);
        GradeDto grade = feignGradeService.getGradeById(form.getGradeId());
        clazz.setEnrollYear(grade.getYear()).setStatus(1);
        clazz.updateById();

        teacherClazzService.remove(new LambdaQueryWrapper<TeacherClazz>().eq(TeacherClazz::getClazzId, clazz.getId()));

        // 班主任教师
        TeacherClazz masterClassTeacher = new TeacherClazz();
        masterClassTeacher.setClazzId(clazz.getId())
            .setTeacherId(form.getMasterTeacherId())
            .setType(1);
        masterClassTeacher.insert();
        // 体育教师
        TeacherClazz sportClassTeacher = new TeacherClazz();
        sportClassTeacher.setClazzId(clazz.getId())
            .setTeacherId(form.getSportTeacherId())
            .setType(2);
        sportClassTeacher.insert();
        return "success";
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String deleteByClazzId(Long id) {
        if (studentMapper.exists(new LambdaQueryWrapper<Student>().eq(Student::getClazzId, id))) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "该班级已经存在学生数据，不可删除，请知悉！");
        }
        if (teacherClazzService.exists(new LambdaQueryWrapper<TeacherClazz>().eq(TeacherClazz::getClazzId, id))) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "该班级已经存在教师数据，不可删除，请知悉！");
        }
        super.removeById(id);
        return "success";
    }

    @Override
    public List<KeyValue<Long, String>> getClazzSelectList(Long schoolId, Long gradeId,Long period) {
        LambdaQueryWrapper<Clazz> queryWrapper = new LambdaQueryWrapper<Clazz>()
            .eq(Clazz::getSchoolId, schoolId)
            .eq(Objects.nonNull(gradeId), Clazz::getGradeId, gradeId)
            .eq(Objects.nonNull(period), Clazz::getPeriod, period)
            .eq(Clazz::getStatus, 1);
        List<Clazz> clazzList = super.list(queryWrapper);
        return clazzList.stream().map(clazz -> new KeyValue<>(clazz.getId(), clazz.getName())).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String changeStatus(Long id, Integer status) {
        Clazz clazz = super.getById(id);
        if (clazz == null) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "班级不存在");
        }
        if (clazz.getStatus().equals(2)) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "班级已毕业，不可再修改状态。");
        }
        clazz.setStatus(status);
        clazz.updateById();

        if (status == 2) {
            studentMapper.update(new LambdaUpdateWrapper<Student>().eq(Student::getClazzId, clazz.getId()).set(Student::getStatus, 2));
        }
        return "success";
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<ExcelBatchImportResponse> batchImport(MultipartFile file, Long schoolId) {
        List<ClazzImportExcel> excelList = new ArrayList<>();
        List<ExcelBatchImportResponse> result = new ArrayList<>();
        try {
            excelList = ExcelHelper.readData(file.getInputStream(), ClazzImportExcel.class);
        } catch (Exception e) {
            log.error("读取Excel发生错误，请稍后重试", e);
            result.add(new ExcelBatchImportResponse().setMessage("读取Excel发生错误，请稍后重试").setRowIndex(1));
        }
        if (CollUtil.isNotEmpty(excelList)) {
            return result;
        }
        List<String> codeList = excelList.stream().map(ClazzImportExcel::getCode).distinct().collect(Collectors.toList());
        List<String> masterTeacherNameList = excelList.stream().map(ClazzImportExcel::getMasterTeacherName).distinct().collect(Collectors.toList());
        List<String> sportTeacherNameList = excelList.stream().map(ClazzImportExcel::getSportTeacherName).distinct().collect(Collectors.toList());
        sportTeacherNameList.addAll(masterTeacherNameList);
        List<String> allTeachers = sportTeacherNameList.stream().distinct().collect(Collectors.toList());
        List<String> gradeNameList = excelList.stream().map(ClazzImportExcel::getGradeName).distinct().collect(Collectors.toList());

        List<Clazz> clazzList = CollUtil.isNotEmpty(codeList) ? super.list(new LambdaQueryWrapper<Clazz>().eq(Clazz::getSchoolId, schoolId).in(Clazz::getCode, codeList)) : new ArrayList<>();
        List<Teacher> teacherList = CollUtil.isNotEmpty(masterTeacherNameList) ? teacherMapper.selectList(new LambdaQueryWrapper<Teacher>()
            .eq(Teacher::getSchoolId, schoolId).in(Teacher::getJsxm, allTeachers)) : new ArrayList<>();
        List<GradeDto> gradeList = CollUtil.isNotEmpty(gradeNameList) ? feignGradeService.getGradeListByName(gradeNameList) : new ArrayList<>();

        School school = schoolMapper.selectById(schoolId);
        List<String> periods = new ArrayList<>();
        if (school.getPeriod() < 4) {
            periods.add("小学");
        } else if (school.getPeriod() == 4) {
            periods.add("小学");
            periods.add("初中");
        } else if (school.getPeriod() == 5) {
            periods.add("小学");
            periods.add("初中");
            periods.add("高中");
        } else if (school.getPeriod() == 6) {
            periods.add("初中");
            periods.add("高中");
        }

        List<Clazz> addClazzList = new ArrayList<>();
        List<TeacherClazz> teacherClazzList = new ArrayList<>();

        for (int i = 0; i < excelList.size(); i++) {
            ClazzImportExcel excel = excelList.get(i);
            List<ExcelBatchImportResponse> responses = excel.validateData(i + 1);
            if (CollUtil.isNotEmpty(responses)) {
                result.addAll(responses);
            } else {
                if (excelList.stream().filter(clazz -> clazz.getCode().equals(excel.getCode())).count() > 1) {
                    result.add(new ExcelBatchImportResponse().setMessage("Excel文件中班级代码【" + excel.getCode() + "】存在重复数据").setRowIndex(i + 1));
                }
                if (CollUtil.isNotEmpty(clazzList)) {
                    if (clazzList.stream().anyMatch(clazz -> clazz.getCode().equals(excel.getCode()))) {
                        result.add(new ExcelBatchImportResponse().setMessage("该学校班级信息库中已存在代码为【" + excel.getCode() + "】的班级信息").setRowIndex(i + 1));
                    }
                }
                if (teacherList.stream().noneMatch(teacher -> teacher.getJsxm().equals(excel.getMasterTeacherName()) && teacher.getSjhm().equals(excel.getMasterTeacherMobile()))) {
                    result.add(new ExcelBatchImportResponse().setMessage("该学校教师信息库中不存在【" + excel.getMasterTeacherName() + "】的班主任信息").setRowIndex(i + 1));
                }
                if (teacherList.stream().noneMatch(teacher -> teacher.getJsxm().equals(excel.getSportTeacherName()) && teacher.getSjhm().equals(excel.getSportTeacherMobile()))) {
                    result.add(new ExcelBatchImportResponse().setMessage("该学校教师信息库中不存在【" + excel.getSportTeacherName() + "】的体育老师信息").setRowIndex(i + 1));
                }
                if (gradeList.stream().noneMatch(grade -> grade.getName().equals(excel.getGradeName()))) {
                    result.add(new ExcelBatchImportResponse().setMessage("平台信息库中不存在【" + excel.getGradeName() + "】的年级信息").setRowIndex(i + 1));
                }
                if (!periods.contains(excel.getPeriod())) {
                    result.add(new ExcelBatchImportResponse().setMessage("该学校学段信息中不存在【" + excel.getPeriod() + "】的学段信息").setRowIndex(i + 1));
                }
                if (CollUtil.isEmpty(result)) {
                    GradeDto grade = gradeList.stream().filter(g -> g.getName().equals(excel.getGradeName())).findFirst().orElse(new GradeDto());
                    Clazz clazz = new Clazz();
                    clazz.setName(excel.getName())
                        .setCode(excel.getCode())
                        .setGradeId(grade.getId())
                        .setPeriod(excel.getPeriod().equals("小学") ? 1 : excel.getPeriod().equals("初中") ? 2 : 3)
                        .setEnrollYear(grade.getYear())
                        .setSchoolId(schoolId)
                        .setStatus(1);
                    addClazzList.add(clazz);
                    Teacher masterTeacher = teacherList.stream().filter(t -> t.getJsxm().equals(excel.getMasterTeacherName()) && t.getSjhm()
                        .equals(excel.getMasterTeacherMobile())).findFirst().orElse(null);
                    Teacher sportTeacher = teacherList.stream().filter(t -> t.getJsxm().equals(excel.getSportTeacherName()) && t.getSjhm()
                        .equals(excel.getSportTeacherMobile())).findFirst().orElse(null);
                    if (masterTeacher != null) {
                        TeacherClazz teacherClazz = new TeacherClazz();
                        teacherClazz.setTeacherId(masterTeacher.getId())
                            .setClazzId(clazz.getId())
                            .setType(1);
                        teacherClazzList.add(teacherClazz);
                    }
                    if (sportTeacher != null) {
                        TeacherClazz teacherClazz = new TeacherClazz();
                        teacherClazz.setTeacherId(sportTeacher.getId())
                            .setClazzId(clazz.getId())
                            .setType(2);
                        teacherClazzList.add(teacherClazz);
                    }
                }
            }
        }

        if (CollUtil.isEmpty(result) && CollUtil.isNotEmpty(addClazzList)) {
            super.saveBatch(addClazzList);
            teacherClazzService.saveBatch(teacherClazzList);
        }

        return result;
    }
    @Override
    public List<ClazzInfo> getClazzList(List<Long> schoolId, List<Long> gradeId) {
        List<Clazz> clazzList = baseMapper.selectList(new LambdaQueryWrapper<Clazz>()
            .in(Clazz::getSchoolId, schoolId)
            .in(Clazz::getGradeId, gradeId));
        Set<Long> schoolIds = clazzList.stream().map(Clazz::getSchoolId).collect(Collectors.toSet());
        List<School> schools = schoolMapper.selectList(new LambdaQueryWrapper<School>().in(School::getId, schoolIds));
        List<Long> gradeIds = clazzList.stream().map(Clazz::getGradeId).distinct().collect(Collectors.toList());
        List<GradeDto> gradeList = feignGradeService.getGradeList(gradeIds);
        List<ClazzInfo> clazzInfoList = new ArrayList<>();
        clazzList.forEach(clazz -> {
           ClazzInfo clazzInfo = new ClazzInfo();
           BeanUtil.copyProperties(clazz,clazzInfo);
           clazzInfo.setSchoolName(schools.stream().filter(school -> school.getId().equals(clazz.getSchoolId())).findFirst().orElse(new School()).getName());
           clazzInfo.setGradeName(gradeList.stream().filter(grade -> grade.getId().equals(clazz.getGradeId())).findFirst().orElse(new GradeDto()).getName());
           clazzInfoList.add(clazzInfo);
        });
        return clazzInfoList;
    }
}
