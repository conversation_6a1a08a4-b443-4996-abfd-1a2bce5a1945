package com.shida.region.business.pojo.search;

import com.shida.pojo.search.Search;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

@Data
@Accessors(chain = true)
@Schema(name = "SportRateSearch", description = "体育课出勤率搜索条件")
public class SportRateSearch extends Search {

    @Schema(description = "学期ID")
    @NotNull(message = "学期ID不能为空")
    private Long termId;

    @Schema(description = "学校ID")
    private Long schoolId;

    @Schema(description = "班级ID")
    private Long clazzId;

    @Schema(description = "是否残疾")
    private Boolean sfcj;

    @Schema(description = "是否伤病")
    private Boolean sfsb;

}
