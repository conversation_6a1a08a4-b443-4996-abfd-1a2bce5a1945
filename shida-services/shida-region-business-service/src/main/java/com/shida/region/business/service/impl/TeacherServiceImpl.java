package com.shida.region.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shida.AccountCommonService;
import com.shida.constant.RoleConstant;
import com.shida.dto.SchoolDto;
import com.shida.enums.RestCode;
import com.shida.enums.TeacherCategary;
import com.shida.excel.ExcelBatchImportResponse;
import com.shida.excel.ExcelHelper;
import com.shida.exceptions.ServiceException;
import com.shida.pojo.vo.AccountInfo;
import com.shida.pojo.vo.PageInfo;
import com.shida.pojo.vo.RoleInfo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.shida.pojo.vo.KeyValue;
import com.shida.region.business.entity.Teacher;
import com.shida.region.business.entity.TeacherClazz;
import com.shida.region.business.mapper.TeacherClazzMapper;
import com.shida.region.business.mapper.TeacherMapper;
import com.shida.region.business.pojo.excel.TeacherExportExcel;
import com.shida.region.business.pojo.excel.TeacherImportExcel;
import com.shida.region.business.pojo.form.TeacherForm;
import com.shida.region.business.pojo.search.TeacherSearch;
import com.shida.region.business.pojo.vo.SchoolInfo;
import com.shida.region.business.pojo.vo.TeacherInfo;
import com.shida.region.business.service.IDeleteRecordService;
import com.shida.region.business.service.ISchoolService;
import com.shida.region.business.service.ITeacherService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shida.region.business.tenant.TenantContextHolder;
import com.shida.userCenter.api.IFeignRoleService;
import com.shida.userCenter.api.IFeignUserService;
import com.shida.userCenter.dto.OrgDto;
import com.shida.userCenter.dto.RoleDto;
import com.shida.userCenter.dto.UserDto;
import io.seata.spring.annotation.GlobalTransactional;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
@Service
public class TeacherServiceImpl extends ServiceImpl<TeacherMapper, Teacher> implements ITeacherService {
    @Resource
    private IFeignUserService feignUserService;
    @Resource
    private TeacherClazzMapper teacherClazzMapper;
    @Resource
    private IFeignRoleService feignRoleService;
    @Resource
    private ISchoolService schoolService;
    @Resource
    private TenantContextHolder tenantContextHolder;
    @Resource
    private IDeleteRecordService deleteRecordService;

    @Override
    public List<KeyValue<Long, String>> getTeacherSelectList(Long schoolId) {
        List<Teacher> list = super.list(new QueryWrapper<Teacher>().lambda().eq(Teacher::getSchoolId, schoolId)
            .select(Teacher::getId, Teacher::getJsxm, Teacher::getSjhm));
        return CollUtil.isNotEmpty(list) ? list.stream().map(teacher ->
            new KeyValue<>(teacher.getId(), teacher.getJsxm(), teacher.getSjhm())).collect(Collectors.toList()) : Collections.emptyList();
    }

    @Override
    public PageInfo<TeacherInfo> getPageData(TeacherSearch search) {
        IPage<TeacherInfo> page = new Page<>(search.getPageNumber(), search.getPageSize());
        if (ObjectUtil.isNotNull(search.getJslbSet())) {
            search.setJslb(String.join(",", search.getJslbSet()));
        }
        AccountInfo currentUser = AccountCommonService.getCurrentUser();
        if (Objects.nonNull(currentUser.getSchoolId()) && ObjectUtil.isNull(search.getSchoolId())) {
            search.setSchoolId(currentUser.getSchoolId());
        }
        page = baseMapper.getPageData(page, search);
        List<Long> userIds = page.getRecords().stream().map(TeacherInfo::getUserId).collect(Collectors.toList());
        List<UserDto> userList = CollUtil.isNotEmpty(userIds) ? feignUserService.getUserDtoList(userIds) : new ArrayList<>();
        page.getRecords().forEach(teacher -> {
            UserDto userDto = userList.stream().filter(user -> user.getId().equals(teacher.getUserId())).findFirst().orElse(null);
            if (ObjectUtil.isNotEmpty(userDto)) {
                if (teacher.getJslb() != null) {
                    teacher.setJslbName(Arrays.stream(teacher.getJslb().split(",")).map(jslb -> TeacherCategary.getByCode(jslb)).collect(Collectors.joining(",")));
                }
                teacher.setStatus(userDto.getStatus());
                teacher.setDefaultPassword(userDto.getDefaultPassword());
                teacher.setUserName(userDto.getUsername());
                teacher.setSjhm(userDto.getMobile());
                String roleName = userDto.getRoles().stream().map(RoleInfo::getRoleName).collect(Collectors.joining(","));
                teacher.setRoleName(roleName);
                teacher.setRoleList(userDto.getRoles());
                teacher.setRegionName(userDto.getRegionName());
                teacher.setRegionId(userDto.getRegionId());
                teacher.setDefaultPasswordFlag(userDto.getDefaultPasswordFlag());
            }
        });
//        List<Long> teacherIds = page.getRecords().stream().map(TeacherInfo::getId).collect(Collectors.toList());
//        if (ObjectUtil.isNotNull(teacherIds)) {
//            List<TeacherClazz> clazzList = baseMapper.getClazzList(teacherIds);
//            page.getRecords().forEach(teacher -> {
//                List<TeacherClazz> teacherClazzList = clazzList.stream().filter(clazz -> clazz.getTeacherId().equals(teacher.getId())).collect(Collectors.toList());
//                teacher.setTeacherClazzList(teacherClazzList);
//            });
//        }
//        List<RoleDto> roleDtoList = feignUserService.getUserRoleDtoList(userIds);
//        page.getRecords().forEach(teacher -> {
//            List<RoleDto> collect = roleDtoList.stream().filter(roleDto -> roleDto.getUserId().equals(teacher.getUserId())).collect(Collectors.toList());
//            teacher.setRoleList(collect);
//        });
        PageInfo<TeacherInfo> result = PageInfo.fromMybatisPage(page, TeacherInfo.class);
        return result;
    }

    @Override
    public TeacherInfo getDetailById(Long id) {
        Teacher teacher = baseMapper.selectById(id);

        TeacherInfo teacherInfo = new TeacherInfo();
        BeanUtil.copyProperties(teacher, teacherInfo);
        if (ObjectUtil.isNotEmpty(teacher)) {
            Long userId = teacher.getUserId();
            Long schoolId = teacher.getSchoolId();
            SchoolInfo schoolInfo = schoolService.getDetailById(schoolId);
            if (ObjectUtil.isNotEmpty(schoolInfo)) {
                teacherInfo.setSchoolName(schoolInfo.getName());
            }
            UserDto userDto = feignUserService.getUserDto(userId);
            teacherInfo.setStatus(userDto.getStatus());
            teacherInfo.setDefaultPassword(userDto.getDefaultPassword());
            teacherInfo.setUserName(userDto.getUsername());
            teacherInfo.setSjhm(userDto.getMobile());
            teacherInfo.setRoleList(userDto.getRoles());
            teacherInfo.setRoleCode(userDto.getRoles().stream().map(RoleInfo::getRoleCode).collect(Collectors.toList()));
            teacherInfo.setRegionName(userDto.getRegionName());
            teacherInfo.setRegionId(userDto.getRegionId());
            teacherInfo.setSchoolName(schoolInfo.getName());
            if (teacherInfo.getJslb() != null) {
                teacherInfo.setJslbName(Arrays.stream(teacher.getJslb().split(",")).map(jslb -> TeacherCategary.getByCode(jslb)).collect(Collectors.joining(",")));
                teacherInfo.setJslbList(Arrays.asList(teacher.getJslb().split(",")));
            }
        }
//        List<TeacherClazz> clazzList = baseMapper.getClazzList(CollUtil.newArrayList(teacher.getId()));
//        teacherInfo.setTeacherClazzList(clazzList);
        return teacherInfo;
    }

    @Override
    public List<Teacher> getTeacherList(List<Long> teacherIds) {
        return baseMapper.selectList(new LambdaQueryWrapper<Teacher>().in(Teacher::getId, teacherIds));
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Integer saveTeacher(TeacherForm teacher) {
        if (super.count(new LambdaQueryWrapper<Teacher>().eq(Teacher::getSfzh, teacher.getSfzh())) > 0) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "身份证号已存在");
        }
        if (!IdcardUtil.isValidCard(teacher.getSfzh())) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "身份证号格式错误");
        }
        AccountInfo account = AccountCommonService.getCurrentUser();
        Long schoolId = null;
        Long regionId = null;
        if (account.getCityAdminFlag() || account.getCountyAdminFlag()) {
            schoolId = teacher.getSchoolId();
            regionId = teacher.getRegionId();
        } else {
            schoolId = account.getSchoolId();
            regionId = account.getRegionId();
        }
        teacher.setSchoolId(schoolId);
        teacher.setRegionId(regionId);
        Teacher teacherEntity = new Teacher();
        BeanUtil.copyProperties(teacher, teacherEntity);
        UserDto userDto = getUserDto(teacher, teacherEntity);
        userDto = feignUserService.createUser(userDto);
        if (ObjectUtil.isNotEmpty(userDto.getId())) {
            teacherEntity.setUserId(userDto.getId());
            teacherEntity.setJslb(String.join(",", teacher.getJslbList()));
            return baseMapper.insert(teacherEntity);
//            List<Long> teacherClazzList = teacher.getClazzIds();
//            if (CollUtil.isNotEmpty(teacherClazzList)) {
//                teacherClazzList.forEach(clazzId -> {
//                    TeacherClazz teacherClazz = new TeacherClazz();
//                    teacherClazz.setTeacherId(teacherEntity.getId());
//                    teacherClazz.setClazzId(clazzId);
//                    teacherClazzMapper.insert(teacherClazz);
//                });
//            }
        }
        return 0;
    }

    private static UserDto getUserDto(TeacherForm teacher, Teacher teacherEntity) {
        teacherEntity.setSchoolId(teacher.getSchoolId());
        UserDto userDto = new UserDto();
        userDto.setId(teacher.getUserId());
        userDto.setUsername(teacher.getUserName());
        userDto.setNickname(teacher.getJsxm());
        userDto.setMobile(teacher.getSjhm());
        userDto.setDefaultPasswordFlag(true);
        userDto.setAdminFlag(false);
        userDto.setStatus(1);
        userDto.setRegionId(teacher.getRegionId());
        userDto.setSchoolId(teacher.getSchoolId());
        ArrayList<RoleInfo> roleInfos = new ArrayList<>();
        teacher.getRoleCode().forEach(roleCod -> {
            roleInfos.add(new RoleInfo().setRoleCode(roleCod));
        });
        userDto.setRoles(roleInfos);
        return userDto;
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Integer updateTeacher(TeacherForm teacher) {
        if (ObjectUtil.isEmpty(teacher.getId())) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "学生ID不能为空");
        }
        if (super.count(new LambdaQueryWrapper<Teacher>().eq(Teacher::getSfzh, teacher.getSfzh()).ne(Teacher::getId, teacher.getId())) > 0) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "身份证号已存在");
        }
        if (!IdcardUtil.isValidCard(teacher.getSfzh())) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "身份证号格式错误");
        }
        AccountInfo account = AccountCommonService.getCurrentUser();
        Long schoolId = null;
        Long regionId = null;
        if (account.getCityAdminFlag() || account.getCountyAdminFlag()) {
            schoolId = teacher.getSchoolId();
            regionId = teacher.getRegionId();
        } else {
            schoolId = account.getSchoolId();
            regionId = account.getRegionId();
        }
        teacher.setSchoolId(schoolId);
        teacher.setRegionId(regionId);
        Teacher teacherEntity = baseMapper.selectById(teacher.getId());
        if (teacherEntity == null) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "教师信息不存在，不能更新");
        }
        BeanUtil.copyProperties(teacher, teacherEntity);
        teacherEntity.setJslb(String.join(",", teacher.getJslbList()));
        teacherEntity.setSchoolId(account.getSchoolId());
        baseMapper.updateById(teacherEntity);
        UserDto userDto = getUserDto(teacher, teacherEntity);
        feignUserService.updateUser(userDto);
        return 1;
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Integer deleteTeacher(Long id) {
        if (ObjectUtil.isNull(id)) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "id不能为空");
        }
        Teacher teacher = baseMapper.selectById(id);
        if (teacher != null) {
            if (teacher.getUserId() == null) {
                throw new ServiceException(RestCode.ARGUMENT_ERROR, "教师不存在");
            }
            baseMapper.deleteById(id);
            deleteRecordService.createRecord(id, Teacher.class, teacher, teacher.getSfzh());
            teacherClazzMapper.delete(new LambdaQueryWrapper<TeacherClazz>().eq(TeacherClazz::getTeacherId, id));
            feignUserService.deleteUser(teacher.getUserId());
            return 1;
        }
        return 0;
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public List<ExcelBatchImportResponse> importTeacher(MultipartFile file, Long regoinId) {
        AccountInfo account = AccountCommonService.getCurrentUser();
        List<RoleInfo> roles = account.getRoles();
        if (roles.stream().anyMatch(roleInfo -> roleInfo.getRoleCode().equals(RoleConstant.CITY_ADMIN_CODE))) {//市级管理员不校验区县
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "市级管理员不能导入教师");//因为不同区县的学生要导入不同的区县库
        }
        List<SchoolDto> schoolDtoList = schoolService.getByRegionId(regoinId);
        List<ExcelBatchImportResponse> result = new ArrayList<>();
        List<TeacherImportExcel> excelList = new ArrayList<>();
        try {
            excelList = ExcelHelper.readData(file.getInputStream(), TeacherImportExcel.class, 0);
        } catch (Exception e) {
            log.error("读取Excel发生错误，请稍后重试", e);
            result.add(new ExcelBatchImportResponse().setMessage("读取Excel发生错误，请稍后重试").setRowIndex(1));
        }
        if (CollUtil.isEmpty(excelList)) {
            return result;
        }
        List<Teacher> teacherList = baseMapper.selectList(new LambdaQueryWrapper<Teacher>().select(Teacher::getSfzh));
        List<UserDto> userDtoList = feignUserService.getListByUsernames(excelList.stream().map(TeacherImportExcel::getUserName).distinct().collect(Collectors.toList()));
        for (int i = 0; i < excelList.size(); i++) {
            TeacherImportExcel teacherImportExcel = excelList.get(i);
            List<ExcelBatchImportResponse> validateData = teacherImportExcel.validateData(i + 1);
            if (CollUtil.isNotEmpty(validateData)) {
                result.addAll(validateData);
                return result;
            }
            // 判定身份证号有无重复
            if (excelList.stream().filter(teacher -> teacher.getSfzh().equals(teacherImportExcel.getSfzh())).count() > 1) {
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中身份证号【" + teacherImportExcel.getSfzh() + "】存在重复数据").setRowIndex(i + 1));
            }
            // 判定用户账号有无重复
            if (excelList.stream().filter(teacher -> teacher.getUserName().equals(teacherImportExcel.getUserName())).count() > 1) {
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中账号【" + teacherImportExcel.getUserName() + "】存在重复数据").setRowIndex(i + 1));
            }
            if (userDtoList.stream().anyMatch(userDto -> userDto.getUsername().equals(teacherImportExcel.getUserName()))) {
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中账号【" + teacherImportExcel.getUserName() + "】已被注册").setRowIndex(i + 1));
            }
            if (teacherList.stream().anyMatch(teacher -> teacher.getSfzh().equals(teacherImportExcel.getSfzh()))) {
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中身份证号【" + teacherImportExcel.getSfzh() + "】已被注册").setRowIndex(i + 1));
            }
            if (schoolDtoList.stream().noneMatch(schoolDto -> schoolDto.getName().equals(teacherImportExcel.getShcoolName()))) {
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中所属学校【" + teacherImportExcel.getShcoolName() + "】不存在").setRowIndex(i + 1));
            }
        }
        if (CollUtil.isNotEmpty(result)) {
            return result;
        }
        List<RoleDto> roleSelectList = feignRoleService.getRoleSelectList(RoleConstant.TEACHER_CODE);
        List<UserDto> addUserDtoList = new ArrayList<>();
        List<Teacher> addTeacherList = new ArrayList<>();
        if (CollUtil.isEmpty(result)) {
            for (int j = 0; j < excelList.size(); j++) {
                TeacherImportExcel teacherImportExcel = excelList.get(j);
                Long schoolId = schoolDtoList.stream().filter(schoolDto -> schoolDto.getName().equals(teacherImportExcel.getShcoolName())).findFirst().orElse(null).getId();
                List<RoleDto> roleDtoList = new ArrayList<>();
                UserDto u = new UserDto()
                    .setUsername(teacherImportExcel.getUserName())
                    .setNickname(teacherImportExcel.getUserName())
                    .setMobile(teacherImportExcel.getSjhm())
                    .setDefaultPasswordFlag(true)
                    .setAdminFlag(false)
                    .setSchoolId(schoolId)
                    .setStatus(1)
                    .setRegionId(regoinId)
                    .setIdCard(teacherImportExcel.getSfzh());
                Arrays.stream(teacherImportExcel.getRoleName().split("，|,|；|;|-"))
                    .forEach(roleName -> {
                        RoleDto roleDto = roleSelectList.stream().filter(item -> item.getName().equals(roleName)).findFirst().orElse(null);
                        if (ObjectUtil.isNotEmpty(roleDto)) {
                            roleDtoList.add(roleDto);
                        }
                    });
                if (roleDtoList.size() > 0) {
                    u.setRoles(roleDtoList.stream().map(item -> new RoleInfo().setRoleCode(item.getCode())).collect(Collectors.toList()));
                }
                addUserDtoList.add(u);
            }
            List<UserDto> createUserDtos = feignUserService.batchCreateUser(addUserDtoList);
            if (CollUtil.isNotEmpty(createUserDtos)) {
                excelList.forEach(teacherImportExcel -> {
                    UserDto createUserDto = createUserDtos.stream().filter(item -> item.getUsername().equals(teacherImportExcel.getUserName())).findFirst().orElse(null);
                    if (ObjectUtil.isNotEmpty(createUserDto)) {
                        Long userId = createUserDto.getId();
                        Teacher teacher = new Teacher();
                        teacher.setSchoolId(createUserDto.getSchoolId());
                        teacher.setSjhm(teacherImportExcel.getSjhm());
                        teacher.setJsxm(teacherImportExcel.getJsxm());
                        teacher.setSfzh(teacherImportExcel.getSfzh());
                        teacher.setJsxb(ObjectUtil.equals(teacherImportExcel.getJsxb(), "男") ? 1 : 2);
                        teacher.setUserId(userId);
                        teacher.setJslb(Arrays.stream(teacherImportExcel.getJslb().split("，|,|；|;|-")).map(jslb -> TeacherCategary.getByName(jslb)).sorted().collect(Collectors.joining(",")));
                        addTeacherList.add(teacher);
                    }
                });
            }
        }
        if (CollUtil.isNotEmpty(addTeacherList)) {
            super.saveBatch(addTeacherList);
        }
        return result;
    }

    @Override
    public void exportTeacher(TeacherSearch search, HttpServletResponse response) {
        OrgDto region = tenantContextHolder.getTenant();
        search.setPageNumber(1).setPageSize(1000);
        PageInfo<TeacherInfo> pageInfo;
        ExcelHelper<TeacherExportExcel> excelHelper = ExcelHelper.create(TeacherExportExcel.class);
        AtomicInteger index = new AtomicInteger(1);
        do {
            pageInfo = this.getPageData(search);
            List<TeacherExportExcel> excelList;
            List<Long> userIds = pageInfo.getRecords().stream().map(TeacherInfo::getUserId).collect(Collectors.toList());
            List<UserDto> userList = CollUtil.isNotEmpty(userIds) ? feignUserService.getUserDtoList(userIds) : new ArrayList<>();
            pageInfo.getRecords().forEach(teacher -> {
                UserDto userDto = userList.stream().filter(user -> user.getId().equals(teacher.getUserId())).findFirst().orElse(null);
                if (ObjectUtil.isNotEmpty(userDto)) {
                    teacher.setStatus(userDto.getStatus());
                    teacher.setDefaultPassword(userDto.getDefaultPassword());
                    teacher.setUserName(userDto.getUsername());
                    String roleName = userDto.getRoles().stream().map(RoleInfo::getRoleName).collect(Collectors.joining(","));
                    teacher.setRoleName(roleName);
                    teacher.setRoleList(userDto.getRoles());
                }
            });
            excelList = pageInfo.getRecords().stream().map(teacherInfo -> {
                TeacherExportExcel teacherExportExcel = new TeacherExportExcel();
                if (ObjectUtil.isNotNull(teacherInfo.getJsxb())) {
                    teacherExportExcel.setJsxb(teacherInfo.getJsxb() == 1 ? "男" : "女");
                }
                teacherExportExcel.setJsxm(teacherInfo.getJsxm());
                teacherExportExcel.setSfzh(teacherInfo.getSfzh());
                teacherExportExcel.setSjhm(teacherInfo.getSjhm());
                teacherExportExcel.setUserName(teacherInfo.getUserName());
                if (teacherInfo.getJslb() != null) {
                    teacherExportExcel.setJslb(Arrays.stream(teacherInfo.getJslb().split(",")).map(jslb -> TeacherCategary.getByCode(jslb)).collect(Collectors.joining(",")));
                }
                teacherExportExcel.setSchoolName(schoolService.getById(teacherInfo.getSchoolId()).getName());
                teacherExportExcel.setJslb(teacherInfo.getRoleName());
                teacherExportExcel.setRoleName(teacherInfo.getRoleName());
                teacherExportExcel.setRowNum(index.getAndIncrement());
                return teacherExportExcel;
            }).collect(Collectors.toList());
            excelHelper.writeData(excelList);
            search.setPageNumber(search.getPageNumber() + 1);
        } while (pageInfo.hasNext());
        excelHelper.writeToWebResponse(response, region.getName() + "-教师信息");
    }
}
