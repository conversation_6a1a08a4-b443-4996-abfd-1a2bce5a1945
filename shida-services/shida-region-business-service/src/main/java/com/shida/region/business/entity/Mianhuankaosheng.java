package com.shida.region.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shida.entity.BaseEntity;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 免考缓考生管理表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-04
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("b_mianhuankaosheng")
@Schema(name = "Mianhuankaosheng", description = "免考缓考生管理表")
public class Mianhuankaosheng extends BaseEntity<Mianhuankaosheng> {

    private static final long serialVersionUID = 1L;

    @Schema(description = "学生id")
    private Long studentId;

    @Schema(description = "学生姓名")
    private String studentName;

    @Schema(description = "学籍号")
    private String xjh;
    
    @Schema(description = "身份证号")
    private String sfzh;

    @Schema(description = "性别")
    private String sex;

    @Schema(description = "年级id")
    private Long gradeId;
    
    @Schema(description = "班级id")
    private Long clazzId;
    
    @Schema(description = "所在班级名称")
    private String clazzName;

    @Schema(description = "学校id")
    private Long schoolId;
    
    @Schema(description = "所属学校名称")
    private String schoolName;

    @Schema(description = "区县id")
    private Long regionId;
    
    @Schema(description = "所属区县名称")
    private String regionName;

    @Schema(description = "申请类别")
    private Integer applyForCategary;

    @Schema(description = "类别情况说明")
    private String categaryDescription;

    @Schema(description = "免考/缓考项目")
    private String items;

    @Schema(description = "免考/缓考期限")
    private String termLimit;

    @Schema(description = "佐证材料")
    private String zuozhengResource;

    @Schema(description = "驳回原因")
    private String rejectReason;

    @Schema(description = "区县审核状态")
    private Integer countyReviewStatus;

    @Schema(description = "区县审核人用户id")
    private Long countyReviewerUserId;

    @Schema(description = "区县审核人")
    private String countyReviewer;

    @Schema(description = "区县审核时间")
    private LocalDateTime countyReviewTime;

    @Schema(description = "市级审核状态")
    private Integer cityReviewStatus;

    @Schema(description = "市级审核人用户id")
    private Long cityReviewerUserId;

    @Schema(description = "市级审核人")
    private String cityReviewer;

    @Schema(description = "市级审核时间")
    private LocalDateTime cityReviewTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
