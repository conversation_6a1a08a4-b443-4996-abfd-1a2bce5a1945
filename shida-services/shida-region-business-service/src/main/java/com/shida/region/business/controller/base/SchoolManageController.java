package com.shida.region.business.controller.base;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.shida.AccountCommonService;
import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.constant.RoleConstant;
import com.shida.enums.RestCode;
import com.shida.excel.ExcelBatchImportResponse;
import com.shida.exceptions.ServiceException;
import com.shida.noRepeatSubmit.NoRepeatSubmit;
import com.shida.pojo.form.KeyForm;
import com.shida.pojo.vo.AccountInfo;
import com.shida.pojo.vo.PageInfo;
import com.shida.pojo.vo.RoleInfo;
import com.shida.region.business.pojo.form.SchoolForm;
import com.shida.region.business.pojo.search.SchoolSearch;
import com.shida.region.business.pojo.vo.SchoolInfo;
import com.shida.region.business.service.ISchoolService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

@Tag(name = "school-学校管理")
@RestController
@RequestMapping("/school/manage")
public class SchoolManageController {

    @Resource
    private ISchoolService schoolService;

    @SaCheckPermission("school:list")
    @Operation(summary = "获取学校列表")
    @PostMapping("/getPageData")
    public PageInfo<SchoolInfo> getPageData(@RequestBody SchoolSearch search) {
        return schoolService.getPageData(search);
    }

    @SaCheckPermission("school:edit")
    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    @Operation(summary = "更新学校信息")
    @PostMapping("/update")
    public String update(@RequestBody @Valid SchoolForm form) {
        return schoolService.updateSchool(form);
    }


    @Operation(summary = "获取学校详情")
    @PostMapping("/getDetail")
    public SchoolInfo getDetail(@RequestBody KeyForm<Long> form) {
        return schoolService.getDetailById(form.getKey());
    }


    @NoRepeatSubmit
    @SaCheckPermission("school:add")
    @AutoLog(operateType = OperateType.OPERATE_ADD)
    @Operation(summary = "创建学校")
    @PostMapping("/create")
    public String create(@RequestBody @Valid SchoolForm form) {
        return schoolService.createSchool(form);
    }


    @NoRepeatSubmit
    @SaCheckPermission("school:add")
    @AutoLog(operateType = OperateType.OPERATE_IMPORT)
    @Operation(summary = "批量导入学校信息")
    @PostMapping("/batchImport")
    public List<ExcelBatchImportResponse> batchImport(@RequestBody MultipartFile file, @RequestHeader("X-Tenant-Id") Long regionId) {
        AccountInfo currentUser = AccountCommonService.getCurrentUser();
        List<RoleInfo> roles = currentUser.getRoles();
        if (roles.stream().anyMatch(role -> role.getRoleCode().equals(RoleConstant.CITY_ADMIN_CODE)
                || (role.getSysRoleCode() != null && role.getSysRoleCode().equals(RoleConstant.CITY_ADMIN_CODE)))
                || roles.stream().anyMatch(role -> role.getRoleCode().equals(RoleConstant.COUNTY_ADMIN_CODE)
                || (role.getSysRoleCode() != null && role.getSysRoleCode().equals(RoleConstant.COUNTY_ADMIN_CODE)))
        ) {
            return schoolService.batchImport(file, regionId);
        } else {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "对不起，只有学校级账号才可以进行此项操作");
        }
    }

    @NoRepeatSubmit
    @SaCheckPermission("school:export:account")
    @Operation(summary = "导出学校管理员账户信息")
    @AutoLog(operateType = OperateType.OPERATE_EXPORT)
    @PostMapping("/export/admin/account")
    public void exportAdminCount(HttpServletResponse response,
                                 @RequestHeader("X-Tenant-Id") Long regionId,
                                 @RequestBody SchoolSearch search) {
        schoolService.exportAdminCount(response, regionId, search);
    }
}
