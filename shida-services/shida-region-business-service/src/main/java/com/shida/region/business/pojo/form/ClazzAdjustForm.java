package com.shida.region.business.pojo.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;


@Data
@Accessors(chain = true)
@Schema(name = "ClazzAdjustForm", description = "校内调班请求参数")
public class ClazzAdjustForm implements Serializable {
    @Schema(description = "调入班级名称")
    private String clazzName;

    @Schema(description = "班级ID")
    private Long clazzId;

    @Schema(description = "学校Id")
    private Long schoolId;

    @Schema(description = "学生id")
    @NotEmpty(message = "学生ID不能为空")
    @Size(min = 1, message = "至少选择一个学生")
    private List<Long> studentIds;
}
