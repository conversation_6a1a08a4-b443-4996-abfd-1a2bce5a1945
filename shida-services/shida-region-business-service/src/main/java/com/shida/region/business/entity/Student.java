package com.shida.region.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shida.entity.BaseEntity;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 学生信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_student")
@Schema(name = "Student", description = "学生信息表")
public class Student extends BaseEntity<Student> {

    private static final long serialVersionUID = 1L;

    @Schema(description = "班级ID")
    private Long clazzId;

    @Schema(description = "学校ID")
    private Long schoolId;

    @Schema(description = "账户ID")
    private Long userId;

    @Schema(description = "学生状态，1在读，2毕业，3休学")
    private Integer status;

    @Schema(description = "姓名")
    private String xm;

    @Schema(description = "学籍号")
    private String xjh;

    @Schema(description = "身份证号")
    private String sfzh;

    @Schema(description = "性别，1男，2女")
    private Integer xb;

    @Schema(description = "手机号码")
    private String sjhm;

    @Schema(description = "家庭住址")
    private String jtzz;

    @Schema(description = "学生头像")
    private String xstx;

    @Schema(description = "民族")
    private Integer mz;

    @Schema(description = "是否校内调班 0否;1是")
    private Integer isClazzAdjust;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
