package com.shida.region.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shida.enums.RestCode;
import com.shida.exceptions.ServiceException;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.business.entity.Device;
import com.shida.region.business.mapper.DeviceMapper;
import com.shida.region.business.pojo.form.DeviceForm;
import com.shida.region.business.pojo.search.DeviceSearch;
import com.shida.region.business.pojo.vo.DeviceInfo;
import com.shida.region.business.service.IDeviceService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shida.region.business.tenant.TenantContextHolder;
import com.shida.userCenter.dto.OrgDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
@Service
public class DeviceServiceImpl extends ServiceImpl<DeviceMapper, Device> implements IDeviceService {

    @Resource
    private TenantContextHolder tenantContextHolder;

    @Override
    public PageInfo<DeviceInfo> getPageData(DeviceSearch search) {
        IPage<DeviceInfo> page = new Page<>(search.getPageNumber(), search.getPageSize());
        page = baseMapper.getPageData(page, search);
        OrgDto region = tenantContextHolder.getTenant();
        page.getRecords().forEach(deviceInfo -> {
            deviceInfo.setRegionName(region.getName());
            deviceInfo.setRegionId(region.getId());
        });
        return PageInfo.fromMybatisPage(page);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String create(DeviceForm form) {
        if (super.exists(new LambdaQueryWrapper<Device>().eq(Device::getDeviceId, form.getDeviceId()))) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "设备ID已存在");
        }
        Device device = BeanUtil.copyProperties(form, Device.class);
        device.setOnlineStatus(false);
        device.insert();
        return "success";
    }

    @Override
    public String updateDevice(DeviceForm form) {
        if (Objects.isNull(form.getId())) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "设备ID不能为空");
        }
        if (super.exists(new LambdaQueryWrapper<Device>().ne(Device::getId, form.getId())
                .eq(Device::getDeviceId, form.getDeviceId()))) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "设备ID已存在");
        }
        Device device = BeanUtil.copyProperties(form, Device.class);
        device.updateById();
        return "success";
    }

}
