package com.shida.region.business.pojo.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.form
 * @ClassName: StudentShiWaiOutForm
 * @Version 1.0
 * @date 2025/9/3 14:37
 */
@Data
@Accessors(chain = true)
@Schema(name = "StudentShiWaiOutForm", description = "学生市外转出表单")
public class StudentShiWaiOutForm implements Serializable {
    private static final long serialVersionUID = 1L;
    @Schema(description = "ID")
    private Long id;
    @Schema(description = "学籍号")
    @NotBlank(message = "学号不能为空")
    private String xjh;
    @Schema(description = "转出原因")
    @NotBlank(message = "转出原因不能为空")
    private String adjustReason;
    @Schema(description = "转出时间")
    @NotNull(message = "转出时间不能为空")
    private LocalDate outBeginDate;
    @Schema(description = "附件列表")
    private List<String> attachmentList;
    @Schema(description = "转入城市")
    @NotBlank(message = "转入城市不能为空")
    private String inCityName;
    @Schema(description = "转入学校")
    @NotBlank(message = "转入学校不能为空")
    private String inSchoolName;
    @Schema(description = "转出学期ID")
    @NotNull(message = "转出学期不能为空")
    private Long outTerm;

}
