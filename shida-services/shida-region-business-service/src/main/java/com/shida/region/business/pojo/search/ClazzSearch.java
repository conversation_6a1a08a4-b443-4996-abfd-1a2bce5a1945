package com.shida.region.business.pojo.search;

import com.shida.pojo.search.Search;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
@Schema(name = "ClazzSearch", description = "班级搜索参数")
public class ClazzSearch extends Search {
    @Schema(description = "学段，1小学，2初中，3高中，4九年一贯制，5十二年一贯制，6初高连读")
    private Integer schoolPeriod;

    @Schema(description = "年级ID")
    private Long gradeId;

    @Schema(description = "学校ID")
    private Long schoolId;
}
