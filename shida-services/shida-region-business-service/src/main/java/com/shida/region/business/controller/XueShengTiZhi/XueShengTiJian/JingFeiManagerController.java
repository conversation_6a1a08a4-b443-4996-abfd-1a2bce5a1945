package com.shida.region.business.controller.XueShengTiZhi.XueShengTi<PERSON>ian;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.noRepeatSubmit.NoRepeatSubmit;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.business.pojo.form.TiJianJingFeiForm;
import com.shida.region.business.pojo.search.HealthSearch;
import com.shida.region.business.pojo.vo.JingFeiInfo;
import com.shida.region.business.service.IJingFeiService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;


@Tag(name = "tijian-经费管理")
@RestController
@RequestMapping("/jingFei/manage")
public class JingFeiManagerController {

    @Resource
    private IJingFeiService jingFeiService;

    @Operation(summary = "获取学校经费列表")
    @SaCheckPermission("jingFei:list")
    @PostMapping("/getJingFeiPageData")
    public PageInfo<JingFeiInfo> getJingFeiPageData(@RequestBody HealthSearch search) {
        return jingFeiService.getPageData(search);
    }

    @SaCheckPermission("jingFei:edit")
    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    @Operation(summary = "更新学校对应学期的经费信息")
    @PostMapping("/update")
    public String update(@RequestBody @Valid TiJianJingFeiForm form) {
        return jingFeiService.updateJingFei(form);
    }


}
