package com.shida.region.business.service;

import com.shida.excel.ExcelBatchImportResponse;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.business.entity.KouQiangRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.shida.region.business.pojo.search.HealthSearch;
import com.shida.region.business.pojo.vo.StudentKouQiangInfo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * 学生口腔检查表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
public interface IKouQiangRecordService extends IService<KouQiangRecord> {
    PageInfo<StudentKouQiangInfo> getStudentKouQiangPageData(HealthSearch search);
    List<ExcelBatchImportResponse> importStudentKouQiang(MultipartFile file, Long regoinId);
    void exportStudentKouQiang(HealthSearch search, HttpServletResponse response);
}
