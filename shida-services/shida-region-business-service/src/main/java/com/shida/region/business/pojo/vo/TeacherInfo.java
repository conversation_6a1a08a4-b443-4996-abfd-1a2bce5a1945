package com.shida.region.business.pojo.vo;

import cn.hutool.core.util.IdcardUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.shida.pojo.vo.RoleInfo;
import com.shida.region.business.entity.TeacherClazz;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.vo
 * @ClassName: TeacherInfo
 * @Version 1.0
 * @date 2025/8/12 11:40
 */
@Data
@Accessors(chain = true)
@Schema(name = "TeacherInfo", description = "教师信息")
public class TeacherInfo implements Serializable {
    @Schema(description = "教师ID")
    private Long id;

    @Schema(description = "学校ID")
    private Long schoolId;

    @Schema(description = "账户ID")
    private Long userId;

    @Schema(description = "教师姓名")
    private String jsxm;

    @Schema(description = "教师类型，1班主任，2体育教师")
    private String jslb;
    @Schema(description = "教师类型名称")
    private String jslbName;
    private List<String> jslbList;
    @Schema(description = "身份证号")
    private String sfzh;

    @Schema(description = "头像")
    private String jstx;
    @Schema(description = "默认密码")
    private String defaultPassword;
    @Schema(description = "是否默认密码")
    private Boolean defaultPasswordFlag;
    @Schema(description = "学校名称")
    private String schoolName;
    @Schema(description = "用户账号")
    private String userName;
    @Schema(description = "用户状态")
    private Integer status;
    @Schema(description = "手机号码")
    private String sjhm;

    @Schema(description = "教师性别，1男，2女")
    private Integer jsxb;

    @Schema(description = "教师状态，1正常，2停用")
    private Integer jszt;

    @Schema(description = "教师班级关联关系列表")
    private List<TeacherClazz> teacherClazzList;

    @Schema(description = "角色列表")
    private List<RoleInfo> roleList;
    @Schema(description = "角色名称")
    private String roleName;
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    @Schema(description = "角色代码")
    private List<String> roleCode;
    @Schema(description = "区县名称")
    private String regionName;
    @Schema(description = "区县ID")
    private Long regionId;

//    public void setSfzh(String sfzh) {
//        this.sfzh = IdcardUtil.hide(sfzh, 10, 14);
//    }

}
