package com.shida.region.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.shida.region.business.entity.WaiKeRecord;
import com.shida.region.business.pojo.search.HealthSearch;
import com.shida.region.business.pojo.vo.NeiKeInfo;
import com.shida.region.business.pojo.vo.WaiKeInfo;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 学生外科纪录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-02
 */
public interface WaiKeRecordMapper extends BaseMapper<WaiKeRecord> {

    /**
     * 列表页查询
     *
     * @param page
     * @param search
     * @return
     */
    IPage<WaiKeInfo> getPageData(IPage<WaiKeInfo> page, @Param("search") HealthSearch search);
}
