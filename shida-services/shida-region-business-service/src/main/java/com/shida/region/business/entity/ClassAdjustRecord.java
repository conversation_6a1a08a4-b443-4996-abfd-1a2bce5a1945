package com.shida.region.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shida.entity.BaseEntity;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 校内调班管理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-02
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("b_clazz_adjust_record")
@Schema(name = "ClassAdjustRecord", description = "校内调班管理")
public class ClassAdjustRecord extends BaseEntity<ClassAdjustRecord> {

    private static final long serialVersionUID = 1L;

    @Schema(description = "学生id")
    private Long studentId;

    @Schema(description = "原班级ID")
    private Long originalClazz;

    @Schema(description = "原班级名称")
    private String originalClazzName;

    @Schema(description = "调入班级ID")
    private Long newClazz;

    @Schema(description = "调入班级名称")
    private String newClazzName;

    @Schema(description = "年级")
    private String gradeName;

    @Schema(description = "调班日期")
    private LocalDate adjustDate;

    @Schema(description = "操作人")
    private String operator;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
