package com.shida.region.business.config.aop;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.shida.constant.RedisKeyConstant;
import com.shida.enums.RestCode;
import com.shida.exceptions.ServiceException;
import com.shida.pojo.vo.AccountInfo;
import com.shida.region.business.tenant.TenantContextHolder;
import com.shida.region.business.utils.DynamicDataSourceUtil;
import com.shida.userCenter.dto.OrgDto;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

@Slf4j
@Aspect
@Component
public class TenantAspect {

    @Resource
    private TenantContextHolder tenantContextHolder;


    @Pointcut("execution(* com.shida..*Controller.*(..))")
    public void controllerPointCut() {
    }

    @Around("controllerPointCut())")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        AccountInfo account = null;
        if (StpUtil.isLogin()) {
            account = (AccountInfo) StpUtil.getSession().get(RedisKeyConstant.CURRENT_USER_SESSION);
        }
        String tenantId = null;
        if (account != null && !account.getCityAdminFlag()) {
            tenantId = account.getRegionId() + "";
        }
        if (tenantId == null) {
            HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
            tenantId = request.getHeader("X-Tenant-Id");
        }
        if (StrUtil.isNotBlank(tenantId)) {
            OrgDto region = tenantContextHolder.getRegionByTenantId(Long.valueOf(tenantId));
            if (Objects.isNull(region)) {
                throw new ServiceException(RestCode.ARGUMENT_ERROR, "未找到该租户信息，请稍后重试");
            }

            if (DynamicDataSourceUtil.isExist("region-" + tenantId)) {
                try {
                    // 切换租户数据源
                    DynamicDataSourceContextHolder.push("region-" + tenantId);
                    log.debug("成功切换到数据源：region-{}", tenantId);
                    // 加入ThreadLocal
                    tenantContextHolder.setTenant(region);
                    return point.proceed();
                } finally {
                    if (StrUtil.isNotBlank(tenantId) && DynamicDataSourceUtil.isExist("region-" + tenantId)) {
                        DynamicDataSourceContextHolder.clear();
                    }
                    tenantContextHolder.clear();
                }
            } else {
                throw new ServiceException(RestCode.ARGUMENT_ERROR, "未找到该租户数据源，请稍后重试");
            }
        }
        // 无租户，则直接查询master库
        else {
//            return point.proceed();
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "未找到租户信息，请勿违法访问！");
        }
    }
}
