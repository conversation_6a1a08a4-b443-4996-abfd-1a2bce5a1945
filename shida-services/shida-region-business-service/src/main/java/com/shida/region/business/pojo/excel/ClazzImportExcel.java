package com.shida.region.business.pojo.excel;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.shida.constant.SchoolPeriodConstant;
import com.shida.excel.ExcelAlias;
import com.shida.excel.ExcelBatchImportResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@Accessors(chain = true)
@Schema(name = "ClazzImportExcel", description = "班级导入信息")
public class ClazzImportExcel implements Serializable {

    @ExcelAlias(value = "班级名称")
    private String name;

    @ExcelAlias(value = "班级代码")
    private String code;

    @ExcelAlias(value = "年级")
    private String gradeName;

    @ExcelAlias(value = "学段")
    private String period;

    @ExcelAlias(value = "所属学校")
    private String schoolName;

    @ExcelAlias(value = "班主任")
    private String masterTeacherName;

    @ExcelAlias(value = "班主任手机号码")
    private String masterTeacherMobile;

    @ExcelAlias(value = "体育老师")
    private String sportTeacherName;

    @ExcelAlias(value = "体育老师手机号码")
    private String sportTeacherMobile;


    /**
     * 验证数据
     *
     * @return
     */
    public List<ExcelBatchImportResponse> validateData(Integer rowIndex) {
        List<ExcelBatchImportResponse> result = new ArrayList<>();
        if (StrUtil.isBlank(name)) {
            result.add(new ExcelBatchImportResponse().setMessage("班级名称不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(code)) {
            result.add(new ExcelBatchImportResponse().setMessage("班级代码不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(gradeName)) {
            result.add(new ExcelBatchImportResponse().setMessage("年级不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(period)) {
            result.add(new ExcelBatchImportResponse().setMessage("学段不能为空").setType("error").setRowIndex(rowIndex));
        } else if (!SchoolPeriodConstant.CLAZZ_PERIOD_LIST.contains(period)) {
            result.add(new ExcelBatchImportResponse().setMessage("学段只能选择小学，初中，高中").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(schoolName)) {
            result.add(new ExcelBatchImportResponse().setMessage("所属学校不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(masterTeacherName)) {
            result.add(new ExcelBatchImportResponse().setMessage("班主任不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(sportTeacherName)) {
            result.add(new ExcelBatchImportResponse().setMessage("体育老师不能为空").setType("error").setRowIndex(rowIndex));
        }
        return result;
    }
}
