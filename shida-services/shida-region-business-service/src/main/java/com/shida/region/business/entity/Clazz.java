package com.shida.region.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shida.entity.BaseEntity;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 班级信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-03
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_clazz")
@Schema(name = "Clazz", description = "班级信息表")
public class Clazz extends BaseEntity<Clazz> {

    private static final long serialVersionUID = 1L;

    @Schema(description = "班级名称")
    private String name;

    @Schema(description = "班级代码")
    private String code;

    @Schema(description = "年级ID")
    private Long gradeId;

    @Schema(description = "学段")
    private Integer period;

    @Schema(description = "入学年份")
    private Integer enrollYear;

    @Schema(description = "学校ID")
    private Long schoolId;

    @Schema(description = "状态，0停用，1正常，2毕业")
    private Integer status;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
