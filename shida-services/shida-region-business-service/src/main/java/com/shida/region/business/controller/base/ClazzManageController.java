package com.shida.region.business.controller.base;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.shida.AccountCommonService;
import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.constant.RoleConstant;
import com.shida.enums.RestCode;
import com.shida.excel.ExcelBatchImportResponse;
import com.shida.exceptions.ServiceException;
import com.shida.noRepeatSubmit.NoRepeatSubmit;
import com.shida.pojo.form.KeyForm;
import com.shida.pojo.vo.AccountInfo;
import com.shida.pojo.vo.PageInfo;
import com.shida.pojo.vo.RoleInfo;
import com.shida.region.business.pojo.form.ClazzForm;
import com.shida.region.business.pojo.search.ClazzSearch;
import com.shida.region.business.pojo.vo.ClazzInfo;
import com.shida.region.business.service.IClazzService;
import com.shida.region.business.service.IFileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

@Tag(name = "clazz-班级管理")
@RestController
@RequestMapping("/clazz/manage")
public class ClazzManageController {
    @Resource
    private IClazzService clazzService;
    @Resource
    private IFileService fileService;

    @Operation(summary = "获取班级列表")
    @SaCheckPermission("clazz:list")
    @PostMapping("/getPageData")
    public PageInfo<ClazzInfo> getPageData(@RequestBody ClazzSearch search) {
        return clazzService.getPageData(search);
    }


    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_ADD)
    @Operation(summary = "创建班级")
    @SaCheckPermission("clazz:add")
    @PostMapping("/create")
    public String create(@RequestBody @Valid ClazzForm form) {
        return clazzService.create(form);
    }


    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    @Operation(summary = "更新班级")
    @SaCheckPermission("clazz:edit")
    @PostMapping("/update")
    public String update(@RequestBody @Valid ClazzForm form) {
        return clazzService.updateClazz(form);
    }


    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_DELETE)
    @Operation(summary = "删除班级")
    @SaCheckPermission("clazz:delete")
    @PostMapping("/delete")
    public String delete(@RequestBody @Valid KeyForm<Long> form) {
        return clazzService.deleteByClazzId(form.getKey());
    }

    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    @Operation(summary = "更改班级状态")
    @SaCheckPermission("clazz:change:status")
    @PostMapping("/changeStatus/{status}")
    public String changeStatus(@RequestBody @Valid KeyForm<Long> form,
                               @PathVariable @Schema(description = "状态，0停用，1启用，2毕业") Integer status) {
        return clazzService.changeStatus(form.getKey(), status);
    }


    @NoRepeatSubmit
    @Operation(summary = "下载批量导入模板")
    @PostMapping("/downloadBatchImportTemplate")
    public void downloadBatchImportTemplate(HttpServletResponse response) throws IOException {
        fileService.downloadExcelTemplate("clazz-import-template", "班级信息批量导入模板", response);
    }

    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_IMPORT)
    @Operation(summary = "批量导入班级信息")
    @SaCheckPermission("clazz:add")
    @PostMapping("/batchImport")
    public List<ExcelBatchImportResponse> batchImport(@RequestBody MultipartFile file) {
        AccountInfo currentUser = AccountCommonService.getCurrentUser();
        List<RoleInfo> roles = currentUser.getRoles();
        if (roles.stream().anyMatch(role -> role.getRoleCode().equals(RoleConstant.SCHOOL_ADMIN_CODE)
                || (role.getSysRoleCode() != null && role.getSysRoleCode().equals(RoleConstant.SUPER_ADMIN_CODE)))) {
            return clazzService.batchImport(file, currentUser.getSchoolId());
        } else {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "对不起，只有学校级账号才可以进行此项操作");
        }
    }

}
