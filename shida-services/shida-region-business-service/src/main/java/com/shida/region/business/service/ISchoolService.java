package com.shida.region.business.service;

import com.shida.dto.SchoolDto;
import com.shida.excel.ExcelBatchImportResponse;
import com.shida.pojo.vo.KeyValue;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.business.entity.School;
import com.baomidou.mybatisplus.extension.service.IService;
import com.shida.region.business.pojo.form.SchoolForm;
import com.shida.region.business.pojo.search.SchoolSearch;
import com.shida.region.business.pojo.vo.SchoolInfo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * 学校信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
public interface ISchoolService extends IService<School> {

    PageInfo<SchoolInfo> getPageData(SchoolSearch search);

    String updateSchool(SchoolForm form);

    SchoolDto getByRegionIdAndSchoolId(Long regionId, Long schoolId);

    List<SchoolDto> getByRegionId(Long regionId);

    List<SchoolDto> getByRegionIdAndSchoolIds(Long regionId, List<Long> schoolIds);

    SchoolInfo getDetailById(Long schoolId);

    String createSchool(SchoolForm form);

    List<KeyValue<Long, String>> getSchoolSelectList(Long regionId);

    List<ExcelBatchImportResponse> batchImport(MultipartFile file, Long regionId);

    void exportAdminCount(HttpServletResponse response, Long regionId, SchoolSearch search);


    List<School> getSchoolBySchoolNames(List<String> schoolNames);
}
