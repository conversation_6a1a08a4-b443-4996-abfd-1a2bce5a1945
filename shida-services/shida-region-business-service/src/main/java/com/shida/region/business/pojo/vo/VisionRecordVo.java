package com.shida.region.business.pojo.vo;

import com.baomidou.mybatisplus.annotation.*;
import com.shida.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 学生视力记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(name = "VisionRecordVo", description = "学生视力记录表")
public class VisionRecordVo implements Serializable {

    private static final long serialVersionUID = 1L;
    @Schema(description = "主键")
    protected Long id;
    @Schema(description = "创建时间")
    protected LocalDateTime createTime;
    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新者")
    private String updateBy;

    @Schema(description = "学生ID")
    private Long studentId;

    @Schema(description = "学期ID")
    private Long termId;

    @Schema(description = "右眼裸眼视力")
    private String yylysl;

    @Schema(description = "左眼裸眼视力")
    private String zylysl;

    @Schema(description = "右眼球镜")
    private String yyqj;

    @Schema(description = "右眼柱镜")
    private String yyzj;

    @Schema(description = "右眼轴位")
    private String yyzw;

    @Schema(description = "右眼屈光度")
    private String yyqgd;

    @Schema(description = "左眼球镜")
    private String zyqj;

    @Schema(description = "左眼柱镜")
    private String zyzj;

    @Schema(description = "左眼轴位")
    private String zyzw;

    @Schema(description = "左眼屈光度")
    private String zyqgd;

    @Schema(description = "是否为角膜塑形镜")
    private String status;
    @Schema(description = "学生姓名")
    private String studentName;
    @Schema(description = "学籍号")
    private String cardNum;
    @Schema(description = "身份证号")
    private String card;
    @Schema(description = "性别")
    private String sex;
    @Schema(description = "班级名称")
    private String className;
    @Schema(description = "学校名称")
    private String schoolName;
    @Schema(description = "区县名称")
    private String countyName;

    @Schema(description = "右眼裸眼视力对比")
    private String termCompareByYylysl;
    @Schema(description = "左眼裸眼视力对比")
    private String termCompareByZylysl;

}
