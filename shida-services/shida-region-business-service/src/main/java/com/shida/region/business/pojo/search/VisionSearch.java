package com.shida.region.business.pojo.search;

import com.shida.pojo.search.Search;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.TreeSet;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.search
 * @ClassName: TeacherSearch
 * @Version 1.0
 * @date 2025/8/12 11:40
 */
@Data
@Accessors(chain = true)
@Schema(name = "VisoionSearch", description = "视力条件")
public class VisionSearch extends Search {

    @Schema(description = "学校id")
    private Long schoolId;
    @Schema(description = "班级id")
    private Long clazzId;
    @Schema(description = "学生状态，1在读，2毕业，3休学")
    private Integer studentStatus;
    @Schema(description = "年级")
    private Integer gradeId;
    @Schema(description = "区域id")
    private Long regionId;
    @Schema(description = "学段")
    private Integer period;
    @Schema(description = "学期id")
    private Long termId;
}
