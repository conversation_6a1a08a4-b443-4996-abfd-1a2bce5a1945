package com.shida.region.business.pojo.excel;

import com.shida.excel.ExcelAlias;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.excel
 * @ClassName: StudentExportExcel
 * @Version 1.0
 * @date 2025/8/19 16:46
 */
@Data
@Accessors(chain = true)
@Schema(name = "StudentExportExcel", description = "导出的学生对象")
public class StudentExportExcel {
    @ExcelAlias(value = "序号",orderNo = 1)
    private Integer rowNum;
    @ExcelAlias(value = "姓名",orderNo = 2)
    private String xm;
    @ExcelAlias(value = "学籍号",orderNo = 3)
    private String xjh;
    @ExcelAlias(value = "身份证号",orderNo = 4)
    private String sfzh;
    @ExcelAlias(value = "性别",orderNo = 5)
    private String xb;
    @ExcelAlias(value = "学段",orderNo = 6)
    private String period;
    @ExcelAlias(value = "班级",orderNo = 7)
    private String className;
    @ExcelAlias(value = "手机号",orderNo = 8)
    private String sjhm;
    @ExcelAlias(value = "所属学校",orderNo = 9)
    private String schoolName;
    @ExcelAlias(value = "所属区县",orderNo = 10)
    private String regionName;
    @ExcelAlias(value = "家庭住址",orderNo = 11)
    private String jtzz;
}
