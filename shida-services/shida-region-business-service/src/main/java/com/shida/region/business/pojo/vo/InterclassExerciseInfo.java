package com.shida.region.business.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
@Schema(name = "InterclassExerciseInfo", description = "课间操出勤率")
public class InterclassExerciseInfo implements Serializable {
    @Schema(description = "学生ID")
    private Long studentId;
    @Schema(description = "学生名称")
    private String studentName;
    @Schema(description = "学籍号")
    private String xjh;
    @Schema(description = "性别")
    private String xb;
    @Schema(description = "班级ID")
    private Long clazzId;
    @Schema(description = "班级名称")
    private String clazzName;
    @Schema(description = "学校ID")
    private Long schoolId;
    @Schema(description = "学校名称")
    private String schoolName;
    @Schema(description = "区县ID")
    private Long regionId;
    @Schema(description = "区县名称")
    private String regionName;
    @Schema(description = "是否残疾免试")
    private Boolean sfcj;
    @Schema(description = "是否伤病免试")
    private Boolean sfsb;
    @Schema(description = "学期ID")
    private Long termId;
    @Schema(description = "学期名称")
    private String termName;
    @Schema(description = "学期总课时")
    private Integer xqzks;
    @Schema(description = "月度数据")
    private List<MonthInterclassExerciseInfo> monthInterclassExerciseInfoList;
    @Schema(description = "缺勤数")
    private String qqs;
    @Schema(description = "出勤率")
    private String cql;
    @Schema(description = "出勤得分")
    private BigDecimal cqScore;
}

