package com.shida.region.business.controller.base;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.excel.ExcelBatchImportResponse;
import com.shida.noRepeatSubmit.NoRepeatSubmit;
import com.shida.pojo.form.KeyForm;
import com.shida.pojo.vo.KeyValue;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.business.pojo.form.*;
import com.shida.region.business.pojo.search.ClazzAdjustSearch;
import com.shida.region.business.pojo.search.StudentAdjustSearch;
import com.shida.region.business.pojo.vo.*;
import com.shida.region.business.service.IClassAdjustRecordService;
import com.shida.region.business.service.IFileService;
import com.shida.region.business.service.IStudentAdjustService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @description 学籍异动
 * @PackageName: com.shida.region.business.controller
 * @ClassName: StudentAdjustController
 * @Version 1.0
 * @date 2025/9/2 8:45
 */
@Tag(name = "student-学籍异动")
@RestController
@RequestMapping("/student/adjust")
public class StudentAdjustController {
    @Resource
    private IStudentAdjustService studentAdjustService;
    @Resource
    private IFileService fileService;
    @Resource
    private IClassAdjustRecordService classAdjustRecordService;

    @SaCheckPermission("student:suspend:list")
    @Operation(summary = "查询休学列表")
    @PostMapping("/getSuspendPageData")
    public PageInfo<StudentSuspendInfo> list(@RequestBody StudentAdjustSearch search) {
        return studentAdjustService.getSuspendPageData(search);
    }

    @SaCheckPermission("student:suspend:export")
    @NoRepeatSubmit
    @Operation(summary = "导出-休学复学")
    @PostMapping("/exportSuspend")
    @AutoLog(value = "导出-休学复学", operateType = OperateType.OPERATE_EXPORT)
    public void suspendExport(@RequestBody StudentAdjustSearch search, HttpServletResponse response) {
        studentAdjustService.exportSuspend(search, response);
    }


    @SaCheckPermission("student:suspend:add")
    @Operation(summary = "新增休学")
    @PostMapping("/saveSuspend")
    @NoRepeatSubmit
    @AutoLog(value = "新增休学", operateType = OperateType.OPERATE_ADD)
    public Integer saveSuspend(@RequestBody @Validated StudentSuspendForm form) {
        return studentAdjustService.saveStudentSuspend(form);
    }

    @SaCheckPermission("student:suspend:update")
    @Operation(summary = "复学")
    @PostMapping("/updateSuspend")
    @NoRepeatSubmit
    @AutoLog(value = "复学", operateType = OperateType.OPERATE_EDIT)
    public Integer updateSuspend(@RequestBody @Validated StudentSuspendForm form) {
        return studentAdjustService.updateStudentSuspend(form);
    }

    @SaCheckPermission("student:shiWaiOut:list")
    @Operation(summary = "查询市外转出列表")
    @PostMapping("/getShiWaiOutPageData")
    @AutoLog(value = "查询市外转出列表", operateType = OperateType.OPERATE_SEARCH)
    public PageInfo<StudentShiWaiOutInfo> getShiWaiOutPageData(@RequestBody StudentAdjustSearch search) {
        return studentAdjustService.getShiWaiOutPageData(search);
    }

    @SaCheckPermission("student:shiWaiOut:export")
    @NoRepeatSubmit
    @Operation(summary = "导出-市外转入转出")
    @PostMapping("/exportShiWai")
    @AutoLog(value = "导出-市外转入转出", operateType = OperateType.OPERATE_EXPORT)
    public void shiWaiExport(@RequestBody StudentAdjustSearch search, HttpServletResponse response) {
        studentAdjustService.exportShiWai(search, response);
    }

    @SaCheckPermission("student:shiWaiOut:add")
    @Operation(summary = "市外转出")
    @PostMapping("/saveShiWaiOut")
    @NoRepeatSubmit
    @AutoLog(value = "市外转出", operateType = OperateType.OPERATE_ADD)
    public Integer saveShiWaiOut(@RequestBody @Validated StudentShiWaiOutForm form) {
        return studentAdjustService.saveShiWaiOut(form);
    }

    @SaCheckPermission("student:shiWaiIn:list")
    @Operation(summary = "查询市外转入列表")
    @PostMapping("/getShiWaiInPageData")
    @AutoLog(value = "查询市外转出列表", operateType = OperateType.OPERATE_SEARCH)
    public PageInfo<StudentShiWaiInInfo> getShiWaiInPageData(@RequestBody StudentAdjustSearch search) {
        return studentAdjustService.getShiWaiInPageData(search);
    }

    @SaCheckPermission("student:shiWaiIn:add")
    @Operation(summary = "市外转入")
    @PostMapping("/saveShiWaiIn")
    @NoRepeatSubmit
    @AutoLog(value = "市外转入", operateType = OperateType.OPERATE_ADD)
    public Integer saveShiWaiIn(@RequestBody @Validated StudentShiWaiInForm form) {
        return studentAdjustService.saveShiWaiIn(form);
    }

    @SaCheckPermission("student:shiNeiIn:list")
    @Operation(summary = "查询市内转入列表")
    @PostMapping("/getShiNeiInPageData")
    @AutoLog(value = "查询市内转入列表",operateType = OperateType.OPERATE_SEARCH)
    public PageInfo<StudentShiNeiInInfo> getShiNeiInPageData(@RequestBody StudentAdjustSearch search) {
        return studentAdjustService.getShiNeiInPageData(search);
    }

    @SaCheckPermission("student:shiNeiOut:list")
    @Operation(summary = "查询市内转出列表")
    @PostMapping("/getShiNeiOutPageData")
    @AutoLog(value = "查询市内转出列表", operateType = OperateType.OPERATE_SEARCH)
    public PageInfo<StudentShiNeiOutInfo> getShiNeiOutPageData(@RequestBody StudentAdjustSearch search) {
        return studentAdjustService.getShiNeiOutPageData(search);
    }

    @SaCheckPermission("student:shiNeiOut:export")
    @NoRepeatSubmit
    @Operation(summary = "导出-市内转入转出")
    @PostMapping("/exportShiNei")
    @AutoLog(value = "导出-市内转入转出", operateType = OperateType.OPERATE_EXPORT)
    public void shiNeiExport(@RequestBody StudentAdjustSearch search, HttpServletResponse response) {
        studentAdjustService.exportShiNei(search, response);
    }

    @SaCheckPermission("student:shiNeiIn:add")
    @Operation(summary = "市内转入")
    @PostMapping("/saveShiNeiIn")
    @NoRepeatSubmit
    @AutoLog(value = "市内转入", operateType = OperateType.OPERATE_ADD)
    public Integer saveShiNeiIn(@RequestBody @Validated StudentShiNeiInForm form) {
        return studentAdjustService.saveShiNeiIn(form);
    }
    @SaCheckPermission("student:shiNeiIn:audit")
    @Operation(summary = "市内转入审核")
    @PostMapping("/auditShiNeiIn")
    @NoRepeatSubmit
    @AutoLog(value = "市内转入审核",operateType = OperateType.OPERATE_EDIT)
    public Integer auditShiNeiOut(@RequestBody @Validated StudentShiNeiAuditForm form) {
        return studentAdjustService.auditShiNei(form);
    }

    @SaCheckPermission("student:shiNei:process")
    @Operation(summary = "市内转入审核记录")
    @PostMapping("/getShiNeiProcess")
    @NoRepeatSubmit
    @AutoLog(value = "市内转入审核记录",operateType = OperateType.OPERATE_SEARCH)
    public List<StudentShiNeiAuditForm> getShiNeiProcess(@RequestBody @Valid KeyForm<Long> form) {
        return studentAdjustService.getShiNeiProcess(form.getKey());
    }
    @SaCheckPermission("student:shiNeiIn:update")
    @Operation(summary = "市内转入修改")
    @PostMapping("/updateShiNeiIn")
    @NoRepeatSubmit
    @AutoLog(value = "市内转入修改",operateType = OperateType.OPERATE_EDIT)
    public Integer updateShiNeiIn(@RequestBody @Validated StudentShiNeiUpdateForm form) {
        return studentAdjustService.updateShiNeiIn(form);
    }
    @SaCheckPermission("student:shiNeiIn:delete")
    @Operation(summary = "市内转入撤销")
    @PostMapping("/deleteShiNeiIn")
    @NoRepeatSubmit
    @AutoLog(value = "市内转入撤销",operateType = OperateType.OPERATE_DELETE)
    public Integer deleteShiNeiIn(@RequestBody @Valid KeyForm<Long> form) {
        return studentAdjustService.deleteShiNeiIn(form.getKey());
    }

    @SaCheckPermission("student:stay:list")
    @Operation(summary = "查询留级跳级列表")
    @PostMapping("/getStayPageData")
    public PageInfo<StudentStayInfo> getStayPageData(@RequestBody StudentAdjustSearch search) {
        return studentAdjustService.getStayPageData(search);
    }


    @SaCheckPermission("student:stayOut:export")
    @NoRepeatSubmit
    @Operation(summary = "导出-跳级留级")
    @PostMapping("/exportStay")
    @AutoLog(value = "导出-跳级留级", operateType = OperateType.OPERATE_EXPORT)
    public void stayExport(@RequestBody StudentAdjustSearch search, HttpServletResponse response) {
        studentAdjustService.exportStay(search, response);
    }

    @SaCheckPermission("student:stay:add")
    @Operation(summary = "留级跳级")
    @PostMapping("/saveStay")
    @NoRepeatSubmit
    @AutoLog(value = "留级跳级", operateType = OperateType.OPERATE_ADD)
    public Integer saveStay(@RequestBody @Validated StudentStayForm form) {
        return studentAdjustService.saveStay(form);
    }
    @SaCheckPermission("class-adjust:list")
    @Operation(summary = "查询校内调班管理列表")
    @PostMapping("/getPageData")
    @AutoLog(value = "查询校内调班管理列表", operateType = OperateType.OPERATE_SEARCH)
    public PageInfo<ClassAdjustRecordInfo> list(@RequestBody ClazzAdjustSearch search) {
        return classAdjustRecordService.getPageData(search);
    }

    @SaCheckPermission("class-adjust:list")
    @Operation(summary = "查询调班记录")
    @PostMapping("/getAdjustList")
    @AutoLog(value = "根据XX查询调班记录", operateType = OperateType.OPERATE_SEARCH)
    public List<ClassAdjustRecordInfo> list(@RequestBody @Valid KeyForm<Long> form) {
        return classAdjustRecordService.getListByStudentId(form.getKey());
    }

    @SaCheckPermission("class-adjust:import")
    @NoRepeatSubmit
    @Operation(summary = "导入-校内调班列表")
    @PostMapping("/import")
    @AutoLog(value = "导入-校内调班列表", operateType = OperateType.OPERATE_IMPORT)
    public List<ExcelBatchImportResponse> importClassAdjustRecord(@RequestHeader("X-Tenant-Id") Long regionId, @RequestParam("file") MultipartFile file) {
        return classAdjustRecordService.importClassAdjustRecord(file, regionId);
    }

    @SaCheckPermission("class-adjust:download")
    @NoRepeatSubmit
    @Operation(summary = "下载校内调班批量导入模板")
    @PostMapping("/downloadTemplate")
    @AutoLog(value = "下载校内调班批量导入模板", operateType = OperateType.OPERATE_EXPORT)
    public void downloadBatchImportTemplate(HttpServletResponse response, @RequestHeader("X-Tenant-Id") Long regionId) throws IOException {
        fileService.downloadExcelTemplate("clazz_adjust_record_import", "批量导入调班记录模板", response);
    }

    @SaCheckPermission("class-adjust:adjust")
    @Operation(summary = "校内调班/批量调班")
    @PostMapping("/adjust")
    @AutoLog(value = "校内调班", operateType = OperateType.OPERATE_ADD)
    public boolean adjust(@RequestBody ClazzAdjustForm search) {
        return classAdjustRecordService.adjustClazz(search);
    }

    @SaCheckPermission("class-adjust:list")
    @Operation(summary = "查询调班班级")
    @PostMapping("/getClazzAdjustList")
    @AutoLog(value = "查询调班班级", operateType = OperateType.OPERATE_SEARCH)
    public List<KeyValue<Long, String>> getClazzAdjustList(@RequestBody ClazzAdjustForm search) {
        return classAdjustRecordService.getClazzList(search);
    }


}
