package com.shida.region.business.controller.base;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.excel.ExcelBatchImportResponse;
import com.shida.noRepeatSubmit.NoRepeatSubmit;
import com.shida.pojo.form.KeyForm;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.business.pojo.form.StudentSuspendForm;
import com.shida.region.business.pojo.search.ClazzAdjustSearch;
import com.shida.region.business.pojo.search.StudentAdjustSearch;
import com.shida.region.business.pojo.search.StudentSearch;
import com.shida.region.business.pojo.vo.ClassAdjustRecordInfo;
import com.shida.region.business.pojo.vo.StudentShiWaiOutInfo;
import com.shida.region.business.pojo.vo.StudentSuspendInfo;
import com.shida.region.business.service.IClassAdjustRecordService;
import com.shida.region.business.service.IFileService;
import com.shida.region.business.service.IStudentAdjustService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @description 学籍异动
 * @PackageName: com.shida.region.business.controller
 * @ClassName: StudentAdjustController
 * @Version 1.0
 * @date 2025/9/2 8:45
 */
@Tag(name = "student-学籍异动")
@RestController
@RequestMapping("/student/adjust")
public class StudentAdjustController {
    @Autowired
    private IStudentAdjustService studentAdjustService;
    @Resource
    private IFileService fileService;
    @Resource
    private IClassAdjustRecordService iClassAdjustRecordService;
    @SaCheckPermission("student:suspend:list")
    @Operation(summary = "查询休学列表")
    @PostMapping("/getSuspendPageData")
    public PageInfo<StudentSuspendInfo> list(@RequestBody StudentAdjustSearch search) {
        return studentAdjustService.getSuspendPageData(search);
    }
    @SaCheckPermission("student:suspend:add")
    @Operation(summary = "新增休学")
    @PostMapping("/saveSuspend")
    @NoRepeatSubmit
    @AutoLog(value = "新增休学",operateType = OperateType.OPERATE_ADD)
    public Integer saveSuspend(@RequestBody StudentSuspendForm form) {
        return studentAdjustService.saveStudentSuspend(form);
    }

    @SaCheckPermission("student:suspend:update")
    @Operation(summary = "复学")
    @PostMapping("/updateSuspend")
    @NoRepeatSubmit
    @AutoLog(value = "复学",operateType = OperateType.OPERATE_ADD)
    public Integer updateSuspend(@RequestBody StudentSuspendForm form) {
        return studentAdjustService.updateStudentSuspend(form);
    }
    @SaCheckPermission("student:shiWaiOut:list")
    @Operation(summary = "查询市外转出列表")
    @PostMapping("/getShiWaiOutPageData")
    public PageInfo<StudentShiWaiOutInfo> getShiWaiOutPageData(@RequestBody StudentAdjustSearch search) {
        return studentAdjustService.getShiWaiOutPageData(search);
    }

    //    @SaCheckPermission("class-adjust:list")
    @Operation(summary = "查询校内调班管理列表")
    @PostMapping("/getPageData")
    public PageInfo<ClassAdjustRecordInfo> list(@RequestBody StudentSearch search) {
        return iClassAdjustRecordService.getPageData(search);
    }

    //    @SaCheckPermission("class-adjust:list")
    @Operation(summary = "查询调班记录")
    @PostMapping("/getAdjustList")
    public List<ClassAdjustRecordInfo> list(@RequestBody KeyForm<Long> key) {
        return iClassAdjustRecordService.getList(key);
    }

    @SaCheckPermission("class-adjust:import")
    @NoRepeatSubmit
    @Operation(summary = "导入校内调班列表")
    @PostMapping("/import")
    public List<ExcelBatchImportResponse> importClassAdjustRecord(@RequestHeader("X-Tenant-Id") Long regionId, @RequestParam("file") MultipartFile file) {
        return iClassAdjustRecordService.importClassAdjustRecord(file,regionId);
    }

    @NoRepeatSubmit
    @Operation(summary = "下载校内调班批量导入模板")
    @PostMapping("/downloadTemplate")
    @AutoLog(value = "下载校内调班批量导入模板",operateType = OperateType.OPERATE_EXPORT)
    public void downloadBatchImportTemplate(HttpServletResponse response, @RequestHeader("X-Tenant-Id") Long regionId) throws IOException {
        fileService.downloadExcelTemplate("clazz_adjust_record_import", "批量导入调班记录模板", response);
    }

    @SaCheckPermission("class-adjust:adjust")
    @Operation(summary = "校内调班/批量调班")
    @PostMapping("/adjust")
    @AutoLog(value = "校内调班",operateType = OperateType.OPERATE_ADD)
    public void adjust(@RequestBody ClazzAdjustSearch search) {
        iClassAdjustRecordService.adjustClazz(search);
    }



}
