package com.shida.region.business.pojo.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
@Accessors(chain = true)
@Schema(name = "ClazzForm", description = "班级信息")
public class ClazzForm implements Serializable {

    @Schema(description = "ID，编辑时必填")
    private Long id;

    @Schema(description = "班级名称")
    @NotBlank(message = "班级名称不能为空")
    @Size(max = 32, message = "班级名称不能超过32个字符")
    private String name;

    @Schema(description = "班级代码")
    @NotBlank(message = "班级代码不能为空")
    @Size(max = 32, message = "班级代码不能超过32个字符")
    private String code;

    @Schema(description = "年级ID")
    @NotNull(message = "年级ID不能为空")
    private Long gradeId;

    @Schema(description = "学校ID")
    @NotNull(message = "学校ID不能为空")
    private Long schoolId;

    @Schema(description = "学段，1小学，2初中，3高中")
    @NotNull(message = "学段不能为空")
    private Integer period;

    @Schema(description = "班主任教师ID")
    @NotNull(message = "班主任教师ID不能为空")
    private Long masterTeacherId;

    @Schema(description = "体育老师ID")
    @NotNull(message = "体育老师ID不能为空")
    private Long sportTeacherId;
}
