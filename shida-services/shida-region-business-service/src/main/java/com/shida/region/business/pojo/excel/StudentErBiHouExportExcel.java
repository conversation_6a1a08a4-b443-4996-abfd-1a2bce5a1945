package com.shida.region.business.pojo.excel;

import com.shida.excel.ExcelAlias;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.excel
 * @ClassName: StudentErBiHouExportExcel
 * @Version 1.0
 * @date 2025/8/14 17:10
 */
@Data
@Accessors(chain = true)
@Schema(name = "StudentErBiHouExportExcel", description = "导出的耳鼻喉列表")
public class StudentErBiHouExportExcel implements Serializable {
    @ExcelAlias(value = "序号")
    private Integer rowNum;
    @ExcelAlias(value = "姓名")
    private String studentName;
    @ExcelAlias(value = "学籍号")
    private String studentNumber;
    @ExcelAlias(value = "性别")
    private String genderStr;
    @ExcelAlias(value = "所属班级")
    private String clazzName;
    @ExcelAlias(value = "所属学校")
    private String schoolName;
    @ExcelAlias(value = "所属县区")
    private String regionName;
    @ExcelAlias(value = "听力")
    private String tl;
    @ExcelAlias(value = "外耳道与鼓膜")
    private String wedygm;
    @ExcelAlias(value = "外鼻")
    private String wb;
    @ExcelAlias(value = "嗅觉")
    private String xj;
    @ExcelAlias(value = "扁桃体")
    private String btt;
    @ExcelAlias(value = "所属学期")
    private String termName;


}
