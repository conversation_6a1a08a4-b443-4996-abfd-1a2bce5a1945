package com.shida.region.business.pojo.excel;

import cn.hutool.core.util.StrUtil;
import com.shida.excel.ExcelAlias;
import com.shida.excel.ExcelBatchImportResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.excel
 * @ClassName: StudentKouQiangImportExcel
 * @Version 1.0
 * @date 2025/9/3
 */
@Data
@Accessors(chain = true)
@Schema(name = "StudentKouQiangImportExcel", description = "学生口腔检查导入信息")
public class StudentKouQiangImportExcel implements Serializable {
    @ExcelAlias(value = "姓名")
    private String studentName;
    @ExcelAlias(value =  "学籍号")
    private String xjh ;
    @ExcelAlias(value =  "班级")
    private String className ;
    @ExcelAlias(value =  "入学年份")
    private String enrolYear;
    @ExcelAlias(value = "所属学校")
    private String schoolName;
    @ExcelAlias(value = "学期")
    private String termName;
    @ExcelAlias(value = "是否龋齿")
    private String sfqc;
    @ExcelAlias(value = "是否牙周疾病")
    private String sfyzjb;

    public List<ExcelBatchImportResponse> validateData(Integer rowIndex) {
        List<ExcelBatchImportResponse> result = new ArrayList<>();
        if (StrUtil.isBlank(studentName)) {
            result.add(new ExcelBatchImportResponse().setMessage("姓名不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(xjh)) {
            result.add(new ExcelBatchImportResponse().setMessage("学籍号").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(className)) {
            result.add(new ExcelBatchImportResponse().setMessage("班级不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(enrolYear)) {
            result.add(new ExcelBatchImportResponse().setMessage("入学年份不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(schoolName)) {
            result.add(new ExcelBatchImportResponse().setMessage("所属学校不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(termName)) {
            result.add(new ExcelBatchImportResponse().setMessage("学期不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(sfqc)) {
            result.add(new ExcelBatchImportResponse().setMessage("是否龋齿不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(sfyzjb)) {
            result.add(new ExcelBatchImportResponse().setMessage("是否牙周疾病不能为空").setType("error").setRowIndex(rowIndex));
        }
        return result;
    }
}
