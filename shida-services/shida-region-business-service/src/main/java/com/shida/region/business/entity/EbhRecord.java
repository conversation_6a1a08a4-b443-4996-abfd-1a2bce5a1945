package com.shida.region.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shida.entity.BaseEntity;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 学生耳鼻喉纪录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-02
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("h_ebh_record")
@Schema(name = "HEbhRecord", description = "学生耳鼻喉纪录表")
public class EbhRecord extends BaseEntity<EbhRecord> {

    private static final long serialVersionUID = 1L;

    @Schema(description = "学生ID")
    private Long studentId;

    @Schema(description = "班级ID")
    private Long clazzId;

    @Schema(description = "学校ID")
    private Long schoolId;

    @Schema(description = "学期ID")
    private Long termId;

    @Schema(description = "听力")
    private String tl;

    @Schema(description = "外耳道与骨膜")
    private String wedygm;

    @Schema(description = "外鼻")
    private String wb;

    @Schema(description = "嗅觉")
    private String xj;

    @Schema(description = "扁桃体")
    private String btt;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
