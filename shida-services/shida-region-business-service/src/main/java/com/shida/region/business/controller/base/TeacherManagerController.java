package com.shida.region.business.controller.base;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.excel.ExcelBatchImportResponse;
import com.shida.noRepeatSubmit.NoRepeatSubmit;
import com.shida.pojo.form.KeyForm;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.business.pojo.form.TeacherForm;
import com.shida.region.business.pojo.search.TeacherSearch;
import com.shida.region.business.pojo.vo.TeacherInfo;
import com.shida.region.business.service.IFileService;
import com.shida.region.business.service.ITeacherService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.controller
 * @ClassName: TeacherManagerController
 * @Version 1.0
 * @date 2025/8/12 11:38
 */
@Tag(name = "teacher-教师管理")
@RestController
@RequestMapping("/teacher/manage")
public class TeacherManagerController {
    @Resource
    private IFileService fileService;
    @Autowired
    private ITeacherService teacherService;

    @SaCheckPermission("teacher:list")
    @Operation(summary = "获取教师列表")
    @PostMapping("/getPageData")
    public PageInfo<TeacherInfo> getPageData(@RequestBody TeacherSearch search) {
        return teacherService.getPageData(search);
    }
    @Operation(summary = "获取教师详情")
    @SaCheckPermission("teacher:detail")
    @PostMapping("/getDetail")
    public TeacherInfo getDetail(@RequestBody KeyForm<Long> form) {
        return teacherService.getDetailById(form.getKey());
    }
    @SaCheckPermission("teacher:add")
    @Operation(summary = "新增教师")
    @PostMapping("/add")
    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_ADD,value = "新增教师")
    public Integer add(@RequestBody @NotNull TeacherForm teacherInfo) {
        return teacherService.saveTeacher(teacherInfo);
    }
    @SaCheckPermission("teacher:edit")
    @Operation(summary = "编辑教师")
    @PostMapping("/edit")
    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_EDIT,value = "编辑教师")
    public Integer edit(@RequestBody @NotNull TeacherForm teacherInfo) {
        return teacherService.updateTeacher(teacherInfo);
    }
    @SaCheckPermission("teacher:delete")
    @Operation(summary = "删除教师")
    @PostMapping("/delete")
    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_DELETE,value = "删除教师")
    public Integer delete(@RequestBody @NotNull KeyForm<Long> form) {
        return teacherService.deleteTeacher(form.getKey());
    }

    //@SaCheckPermission("teacher:export")
    @NoRepeatSubmit
    @Operation(summary = "下载批量导入模板")
    @PostMapping("/downloadBatchImportTemplate")
    public void downloadBatchImportTemplate(HttpServletResponse response,@RequestHeader("X-Tenant-Id") Long regionId) throws IOException {
        fileService.downloadExcelTemplate("teacher-import-template", "教师信息批量导入模板", response);
    }
    /**
     * 导入教师
     * @param file
     * @return
     */
    @SaCheckPermission("teacher:import")
    @Operation(summary = "导入教师")
    @PostMapping("/import")
    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_IMPORT,value = "导入教师")
    public List<ExcelBatchImportResponse> importTeacher(@RequestBody MultipartFile file, @RequestHeader("X-Tenant-Id") Long regionId) {
        return teacherService.importTeacher(file,regionId);
    }
    /**
     * 导出教师
     * @return
     */
    @SaCheckPermission("teacher:export")
    @Operation(summary = "导出教师")
    @PostMapping("/export")
    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_EXPORT,value = "导出教师")
    public void exportTeacher(@RequestBody TeacherSearch search, HttpServletResponse response) {
        teacherService.exportTeacher(search,response);
    }
}
