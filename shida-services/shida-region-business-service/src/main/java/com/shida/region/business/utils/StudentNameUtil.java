package com.shida.region.business.utils;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.utils
 * @ClassName: StudentNameUtil
 * @Version 1.0
 * @date 2025/8/19 11:39
 */
@Component
public class StudentNameUtil {
    @Resource
    RedisTemplate redisTemplate;
    @Resource
    RedissonClient redissonClient;

    private final String LOCK_KEY = "studentName:lock:";
    private final String STUDENT_NAME_KEY = "studentName:seq:";
    private final Integer MAX_STUDENT_NAME = 999999;

    private final String STUDENT_ADJUST_NUM_KEY = "studentAdjustNum:seq:";
    /**
     * 获取学生账号
     * @param prefix 前缀
     * @param num 获取数量
     * @return 姓名
     */
    public List<String> getStudentName(String prefix, Integer num) {
        List<String> list = new java.util.ArrayList<>();
        RLock lock = redissonClient.getLock(LOCK_KEY);
        Integer seq = 1;
        int year = LocalDateTime.now().getYear();
        try {
            if(lock.tryLock(10, TimeUnit.SECONDS)){
                Map<String,Integer> StudentKey = (Map<String, Integer>) redisTemplate.opsForValue().get(STUDENT_NAME_KEY);
                if(StudentKey == null){
                    StudentKey = new java.util.HashMap<>();
                    StudentKey.put("year",year);
                    StudentKey.put("lastNum",num);
                    redisTemplate.opsForValue().set(STUDENT_NAME_KEY,StudentKey);
                }else {
                    seq = StudentKey.get("lastNum");
                    Integer redisYear = StudentKey.get("year");
                    if(redisYear != year){
                        //新的一年重置序号和年份
                        StudentKey.put("year",year);
                        StudentKey.put("lastNum",num);
                        redisTemplate.opsForValue().set(STUDENT_NAME_KEY,StudentKey);
                    }else {
                        seq = StudentKey.get("lastNum");
                        if((seq + num)> MAX_STUDENT_NAME){
                           throw new RuntimeException("学生账号已达上限");
                        }
                        StudentKey.put("lastNum",seq + num);
                        redisTemplate.opsForValue().set(STUDENT_NAME_KEY,StudentKey);
                    }
                }
                for (int i = 0; i < num; i++) {
                    String seqStr = String.valueOf(seq);
                    while (seqStr.length()<6){
                        seqStr = "0" + seqStr;
                    }
                    list.add(prefix + year + seqStr);
                    seq++;
                }
                return list;
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }finally {
            lock.unlock();
        }
        return null;
    }
    /**
     * 获取学生异动申请号 .申请ID生成规则：
     *
     *    18位定长数字，前两位：系统标识，10=学籍异动
     *
     *                             第3-8位：区县ID 6 位，
     *
     *                             第9-12位：年份
     *
     *                             第13-18位：顺序号（该区县内从000001）
     * @param prefix 前缀
     * @param regionId 区县
     * @return 姓名
     */
    public String getStudentAdjustNum(String prefix, Long regionId) {
        Long seq = redisTemplate.opsForValue().increment(STUDENT_ADJUST_NUM_KEY);
        return prefix + regionId +LocalDateTime.now().getYear() + String.format("%06d", seq);
    }

}
