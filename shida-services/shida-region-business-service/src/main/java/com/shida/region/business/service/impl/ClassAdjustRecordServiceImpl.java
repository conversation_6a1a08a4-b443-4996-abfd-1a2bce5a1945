package com.shida.region.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shida.AccountCommonService;
import com.shida.constant.RoleConstant;
import com.shida.enums.RestCode;
import com.shida.excel.ExcelBatchImportResponse;
import com.shida.excel.ExcelHelper;
import com.shida.exceptions.ServiceException;
import com.shida.pojo.vo.AccountInfo;
import com.shida.pojo.form.KeyForm;
import com.shida.pojo.vo.PageInfo;
import com.shida.pojo.vo.RoleInfo;
import com.shida.region.business.entity.ClassAdjustRecord;
import com.shida.region.business.entity.Clazz;
import com.shida.region.business.entity.Student;
import com.shida.region.business.mapper.ClassAdjustRecordMapper;
import com.shida.region.business.mapper.ClazzMapper;
import com.shida.region.business.mapper.StudentMapper;
import com.shida.region.business.pojo.excel.ClazzAdjustImportExcel;
import com.shida.region.business.pojo.search.ClazzAdjustSearch;
import com.shida.region.business.pojo.search.StudentSearch;
import com.shida.region.business.pojo.vo.ClazzInfo;
import com.shida.region.business.pojo.vo.ClassAdjustRecordInfo;
import com.shida.region.business.service.IClassAdjustRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shida.region.business.service.IClazzService;
import com.shida.region.business.service.ISchoolService;
import com.shida.userCenter.api.IFeignGradeService;
import com.shida.userCenter.api.IFeignUserService;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 校内调班管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-02
 */
@Service
public class ClassAdjustRecordServiceImpl extends ServiceImpl<ClassAdjustRecordMapper, ClassAdjustRecord> implements IClassAdjustRecordService {

    @Resource
    private StudentMapper studentMapper;
    @Resource
    private ClazzMapper clazzMapper;
    @Override
    public PageInfo<ClassAdjustRecordInfo> getPageData(StudentSearch search) {
        IPage<ClassAdjustRecordInfo> page = new Page<>(search.getPageNumber(), search.getPageSize());
        page = baseMapper.getPageData(page, search);
        return PageInfo.fromMybatisPage(page, ClassAdjustRecordInfo.class);
    }

    @Override
    public List<ExcelBatchImportResponse> importClassAdjustRecord(MultipartFile file, Long regionId) {
        AccountInfo account = AccountCommonService.getCurrentUser();
        List<RoleInfo> roles = account.getRoles();
        if(roles.stream().anyMatch(roleInfo -> roleInfo.getRoleCode().equals(RoleConstant.CITY_ADMIN_CODE))){//市级管理员不校验区县
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "市级管理员不能导入学生");//因为不同区县的学生要导入不同的区县库
        }
        if (StrUtil.isBlank(account.getSchoolName())) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "当前登录人学校不存在");
        }
        List<ExcelBatchImportResponse> result = new ArrayList<>();
        List<ClazzAdjustImportExcel> excelList = new ArrayList<>();
        try {
            excelList = ExcelHelper.readData(file.getInputStream(), ClazzAdjustImportExcel.class, 0);
        } catch (Exception e) {
            log.error("读取Excel发生错误，请稍后重试", e);
            result.add(new ExcelBatchImportResponse().setMessage("读取Excel发生错误，请稍后重试").setRowIndex(1));
        }
        if (CollUtil.isEmpty(excelList)) {
            return result;
        }
        List<Student> studentList = studentMapper.selectList(new LambdaQueryWrapper<Student>().select(Student::getId,Student::getXjh));
        //获取登录的学校下的所有班级
        List<Clazz> clazzInfoList = clazzMapper.selectList(new LambdaQueryWrapper<Clazz>().select().eq(Clazz::getSchoolId ,account.getSchoolId()));
        for (int i = 0; i < excelList.size(); i++) {
            ClazzAdjustImportExcel classAdjustImportExcel = excelList.get(i);
            List<ExcelBatchImportResponse> validateData = classAdjustImportExcel.validateData(i + 1);
            if (CollUtil.isNotEmpty(validateData)) {
                result.addAll(validateData);
                return result;
            }
            boolean originalClazzExists = clazzInfoList.stream().noneMatch(clazzInfo -> clazzInfo.getName().equals(classAdjustImportExcel.getOriginalClazzName().trim()) && String.valueOf(clazzInfo.getGradeId()).equals(classAdjustImportExcel.getGradeName().trim().substring(0,4)));
            boolean newClazzExists = clazzInfoList.stream().noneMatch(clazzInfo -> clazzInfo.getName().equals(classAdjustImportExcel.getNewClazzName().trim()) && String.valueOf(clazzInfo.getGradeId()).equals(classAdjustImportExcel.getGradeName().trim().substring(0,4)));

            // 判定原班级是否存在
            if(originalClazzExists){
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中原班级【" + classAdjustImportExcel.getOriginalClazzName() + "】不存在").setRowIndex(i + 1));
            }
            // 判定调入班级是否存在
            if(newClazzExists){
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中调入班级【" + classAdjustImportExcel.getNewClazzName() + "】不存在").setRowIndex(i + 1));
            }
            // 判定学号有无重复
            if (excelList.stream().filter(student -> student.getXjh().equals(classAdjustImportExcel.getXjh())).count() > 1) {
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中学籍号【" + classAdjustImportExcel.getXjh() + "】存在重复数据").setRowIndex(i + 1));
            }
            //判断学生是否存在
            if(studentList.stream().noneMatch(student -> student.getXjh().equals(classAdjustImportExcel.getXjh()))){
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中学籍号【" + classAdjustImportExcel.getXjh() + "】该学生不存在").setRowIndex(i + 1));
            }
        }
        if(CollUtil.isNotEmpty(result)){
            return result;
        }
        //批量插入数据
        List<ClassAdjustRecord> addClassAdjustRecordList = new ArrayList<>();
        if (CollUtil.isEmpty(result)) {
                excelList.forEach(classAdjustImportExcel -> {
                        ClassAdjustRecord classAdjustRecord = new ClassAdjustRecord();
                        classAdjustRecord.setOperator(account.getUsername());
                        classAdjustRecord.setCreateTime(LocalDateTime.now());
                        classAdjustRecord.setAdjustDate(LocalDate.now());
                        classAdjustRecord.setStudentId(studentList.stream().filter(student -> student.getXjh().equals(classAdjustImportExcel.getXjh())).findFirst().get().getId());
                        classAdjustRecord.setOriginalClazz(clazzInfoList.stream().filter(clazzInfo -> clazzInfo.getName().equals(classAdjustImportExcel.getOriginalClazzName())).findFirst().get().getId());
                        classAdjustRecord.setOriginalClazzName(classAdjustImportExcel.getOriginalClazzName());
                        classAdjustRecord.setNewClazz(clazzInfoList.stream().filter(clazzInfo -> clazzInfo.getName().equals(classAdjustImportExcel.getNewClazzName())).findFirst().get().getId());
                        classAdjustRecord.setNewClazzName(classAdjustImportExcel.getNewClazzName());
                        classAdjustRecord.setGradeName(classAdjustImportExcel.getGradeName());
                        addClassAdjustRecordList.add(classAdjustRecord);
                });
        }
        if (CollUtil.isNotEmpty(addClassAdjustRecordList)) {
            //批量插入调班数据
            super.saveBatch( addClassAdjustRecordList);
            //有调班的学生id
            List<Long> studentIds = addClassAdjustRecordList.stream().map(ClassAdjustRecord::getStudentId).collect(Collectors.toList());
            //批量修改学生是否调班0否1是
            studentMapper.update(new Student().setIsClazzAdjust(1), new LambdaUpdateWrapper<Student>().in(Student::getId, studentIds));
        }
        return result;
    }

    @Override
    public List<ClassAdjustRecordInfo> getList(KeyForm<Long> key) {
        List<ClassAdjustRecord> list = baseMapper.selectList(new LambdaQueryWrapper<ClassAdjustRecord>()
            .eq(ClassAdjustRecord::getStudentId, key.getKey())
            .orderByDesc(ClassAdjustRecord::getCreateTime)
            .orderByDesc(ClassAdjustRecord::getId)
        );
        List<ClassAdjustRecordInfo> ClassAdjustRecordList = new ArrayList<>();
        for (ClassAdjustRecord record : list){
            ClassAdjustRecordInfo info = new ClassAdjustRecordInfo();
            info.setId(record.getId());
            info.setStudentId(record.getStudentId());
            info.setOriginalClazz(record.getOriginalClazz());
            info.setOriginalClazzName(record.getOriginalClazzName());
            info.setClazzId(record.getNewClazz());
            info.setClazzName(record.getNewClazzName());
            info.setAdjustDate(record.getAdjustDate());
            info.setOperator(record.getOperator());
            info.setCreateTime(record.getCreateTime());
            ClassAdjustRecordList.add(info);
        }
        return ClassAdjustRecordList;
    }

    @Override
    public List<ClazzInfo> getClazzList(ClazzAdjustSearch search){
        List<Long> gradeIdList = baseMapper.getClazzList(search);
        // 使用HashSet判断是否唯一值
        Set<Long> uniqueGradeIds = gradeIdList.stream()
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        if (uniqueGradeIds.size() > 1) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "所选学生不属于同一年级，无法进行调班操作");
        }
        if (uniqueGradeIds.isEmpty()) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "未查询到相关年级信息");
        }
        // 其他属性设置
        List<ClazzInfo> clazzInfos = clazzMapper.selectList(new LambdaQueryWrapper<Clazz>()
                .eq(Clazz::getGradeId, uniqueGradeIds.iterator().next())
                .eq(Clazz::getStatus, 1))
            .stream()
            .map(clazz -> {
                ClazzInfo info = new ClazzInfo();
                info.setId(clazz.getId());
                info.setName(clazz.getName());
                // 其他属性设置
                return info;
            })
            .collect(Collectors.toList());
        return clazzInfos;
    }

    @Override
    public boolean adjustClazz(ClazzAdjustSearch search) {
        AccountInfo account = AccountCommonService.getCurrentUser();
        if (StrUtil.isBlank(account.getSchoolName())) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "当前登录人学校不存在");
        }
        if (search.getStudentIds().size()==0) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "未选择学生");
        }

        // 查询学生及其原班级信息
        List<Student> studentList = studentMapper.selectList(new LambdaQueryWrapper<Student>()
            .in(Student::getId, search.getStudentIds())
        );

        // 创建学生ID到学生对象的映射，方便查找
        Map<Long, Student> studentMap = studentList.stream()
            .collect(Collectors.toMap(Student::getId, student -> student));

        // 查询原班级信息
        Set<Long> originalClazzIds = studentList.stream()
            .map(Student::getClazzId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        List<Clazz> originalClazzList = clazzMapper.selectList(new LambdaQueryWrapper<Clazz>()
            .in(Clazz::getId, originalClazzIds)
        );

        // 创建班级ID到班级对象的映射
        Map<Long, Clazz> clazzMap = originalClazzList.stream()
            .collect(Collectors.toMap(Clazz::getId, clazz -> clazz));

        // 创建调班记录列表
        List<ClassAdjustRecord> recordList = search.getStudentIds().stream()
            .map(studentId -> {
                Student student = studentMap.get(studentId);
                ClassAdjustRecord record = new ClassAdjustRecord();
                record.setStudentId(studentId);

                // 设置原班级信息
                if (student != null && student.getClazzId() != null) {
                    record.setOriginalClazz(student.getClazzId());
                    Clazz originalClazz = clazzMap.get(student.getClazzId());
                    if (originalClazz != null) {
                        record.setOriginalClazzName(originalClazz.getName());
                        record.setGradeName(String.valueOf(originalClazz.getGradeId())+'级');
                    }
                }

                // 设置新班级信息
                record.setNewClazz(search.getClazzId());
                record.setNewClazzName(search.getClazzName());
                record.setOperator(account.getUsername());
                record.setAdjustDate(LocalDate.now());
                record.setCreateTime(LocalDateTime.now());
                return record;
            })
            .collect(Collectors.toList());

        // 批量插入调班记录
        super.saveBatch(recordList);

        // 修改学生表中的是否调班状态
        int updateCount = studentMapper.update(
            new Student().setIsClazzAdjust(1).setClazzId(search.getClazzId()),
            new LambdaUpdateWrapper<Student>().in(Student::getId, search.getStudentIds())
        );
        return updateCount > 0;
    }


}
