package com.shida.region.business.pojo.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.form
 * @ClassName: StudentStayForm
 * @Version 1.0
 * @date 2025/9/5 16:16
 */
@Data
@Accessors(chain = true)
@Schema(name = "StudentStayForm", description = "学生留级跳级表单")
public class StudentStayForm {
    @Schema(name = "xjh", description = "学籍号")
    @NotBlank(message = "学籍号不能为空")
    private String xjh;
    @Schema(name = "intoGradeId", description = "转入年级id")
    @NotNull(message = "转入年级不能为空")
    private Long intoGradeId;
    @Schema(name = "intoClazzId", description = "转入班级id")
    @NotNull(message = "转入班级不能为空")
    private Long intoClazzId;
    @Schema(name = "adjustReason", description = "申请原因")
    private String adjustReason;
    @Schema(name = "adjustType", description = "异动类型")
    @NotBlank(message = "异动类型不能为空")
    private String adjustType;
    @Schema(name = "adjustDate", description = "申请时间")
    @NotNull(message = "申请时间不能为空")
    private LocalDate adjustDate;
    @Schema(name = "attachmentList", description = "附件列表")
    private List<String> attachmentList;
}
