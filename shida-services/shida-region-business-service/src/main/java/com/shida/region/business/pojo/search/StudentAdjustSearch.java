package com.shida.region.business.pojo.search;

import com.shida.pojo.search.Search;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.search
 * @ClassName: StudentAdjustSearch
 * @Version 1.0
 * @date 2025/9/2 9:23
 */
@Data
@Accessors(chain = true)
@Schema(name = "StudentAdjustSearch", description = "学籍异动搜索条件")
public class StudentAdjustSearch extends Search {
    @Schema(description = "学校ID")
    private Long schoolId;
    @Schema(description = "学生姓名")
    private String studentName;
    @Schema(description = "学籍号")
    private String xjh;
    @Schema(description = "异动类型")
    private Integer adjustType;
    @Schema(description = "申请日期")
    private LocalDate suspendBeginDate;
}
