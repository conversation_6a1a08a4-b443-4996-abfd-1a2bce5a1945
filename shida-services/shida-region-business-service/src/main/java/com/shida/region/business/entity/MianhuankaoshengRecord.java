package com.shida.region.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shida.entity.BaseEntity;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 免考缓考生审核变更记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-04
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("b_mianhuankaosheng_record")
@Schema(name = "MianhuankaoshengRecord", description = "免考缓考生审核变更记录表")
public class MianhuankaoshengRecord extends BaseEntity<MianhuankaoshengRecord> {

    private static final long serialVersionUID = 1L;

    @Schema(description = "免考缓考生管理id")
    private Long mianhuankaoId;

    @Schema(description = "学生id")
    private Long studentId;

    @Schema(description = "审核人用户id")
    private Long reviewerUserId;

    @Schema(description = "审核人")
    private String reviewer;

    @Schema(description = "审核人角色")
    private String roleCode;

    @Schema(description = "审核状态")
    private Integer reviewStatus;

    @Schema(description = "审核时间")
    private LocalDateTime reviewTime;

    @Schema(description = "驳回原因")
    private String rejectReason;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
