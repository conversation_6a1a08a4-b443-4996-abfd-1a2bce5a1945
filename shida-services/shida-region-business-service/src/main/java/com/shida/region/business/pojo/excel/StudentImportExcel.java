package com.shida.region.business.pojo.excel;

import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.StrUtil;
import com.shida.constant.SchoolPeriodConstant;
import com.shida.excel.ExcelAlias;
import com.shida.excel.ExcelBatchImportResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.excel
 * @ClassName: StudentImportExcel
 * @Version 1.0
 * @date 2025/8/19 16:46
 */
@Data
@Accessors(chain = true)
@Schema(name = "StudentImportExcel", description = "学生导入信息")
public class StudentImportExcel {
    @ExcelAlias(value = "序号",orderNo = 1)
    private Integer rowNum;
    @ExcelAlias(value = "姓名")
    private String xm;
    @ExcelAlias(value = "学籍号")
    private String xjh;
    @ExcelAlias(value = "身份证号")
    private String sfzh;
    @ExcelAlias(value = "性别")
    private String xb;
    @ExcelAlias(value = "学段")
    private String period;
    @ExcelAlias(value = "年级")
    private String gradeName;
    @ExcelAlias(value = "班级")
    private String className;
    @ExcelAlias(value = "手机号")
    private String sjhm;
    @ExcelAlias(value = "所属学校")
    private String schoolName;
    @ExcelAlias(value = "所属区县")
    private String regionName;
    @ExcelAlias(value = "家庭住址")
    private String jtzz;

    public List<ExcelBatchImportResponse> validateData(Integer rowIndex) {
        List<ExcelBatchImportResponse> result = new ArrayList<>();
        if (StrUtil.isBlank(xm)) {
            result.add(new ExcelBatchImportResponse().setMessage("姓名不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(xjh)) {
            result.add(new ExcelBatchImportResponse().setMessage("学籍号不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(sfzh)) {
            result.add(new ExcelBatchImportResponse().setMessage("身份证号不能为空").setType("error").setRowIndex(rowIndex));
        }
        if(!IdcardUtil.isValidCard(sfzh)){
            result.add(new ExcelBatchImportResponse().setMessage("身份证号格式错误").setType("error").setRowIndex(rowIndex));
        }
        if(!StrUtil.startWithAny(xjh,"G","L","J")){
            result.add(new ExcelBatchImportResponse().setMessage("学籍号格式错误").setType("error").setRowIndex(rowIndex));
        }
        if(StrUtil.startWithAny(xjh,"G") && !StrUtil.contains(xjh,sfzh)){
            result.add(new ExcelBatchImportResponse().setMessage("学籍号与身份证号不匹配").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(xb)) {
            result.add(new ExcelBatchImportResponse().setMessage("性别不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(period)) {
            result.add(new ExcelBatchImportResponse().setMessage("学段不能为空").setType("error").setRowIndex(rowIndex));
        } else if (!SchoolPeriodConstant.SCHOOL_PERIOD_LIST.contains(period)) {
            result.add(new ExcelBatchImportResponse().setMessage("学段只能选择小学，初中，高中，九年一贯制，十二年一贯制，初高连读").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(className)) {
            result.add(new ExcelBatchImportResponse().setMessage("班级不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(sjhm)) {
            result.add(new ExcelBatchImportResponse().setMessage("手机号不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(gradeName)) {
            result.add(new ExcelBatchImportResponse().setMessage("年级不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(schoolName)) {
            result.add(new ExcelBatchImportResponse().setMessage("所属学校不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(regionName)) {
            result.add(new ExcelBatchImportResponse().setMessage("所属区县不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(jtzz)) {
            result.add(new ExcelBatchImportResponse().setMessage("家庭住址不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (!IdcardUtil.isValidCard(sfzh)) {
            result.add(new ExcelBatchImportResponse().setMessage("身份证号格式错误").setType("error").setRowIndex(rowIndex));
        }
        return result;
    }
}
