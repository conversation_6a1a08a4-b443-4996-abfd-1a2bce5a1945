package com.shida.region.business.pojo.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.form
 * @ClassName: StudentShiWaiInForm
 * @Version 1.0
 * @date 2025/9/3 15:48
 */
@Data
@Accessors(chain = true)
@Schema(name = "StudentShiWaiInForm", description = "学生市外转入表单")
public class StudentShiWaiInForm  implements Serializable {
    private static final long serialVersionUID = 1L;
    @Schema(description = "学籍号")
    @NotBlank(message = "学号不能为空")
    private String xjh;
    @Schema(description = "身份证号")
    @NotBlank(message = "身份证号不能为空")
    private String sfzh;

    @Schema(description = "学生姓名")
    @NotBlank(message = "学生姓名不能为空")
    private String studentName;

    @Schema(description = "性别")
    @NotNull(message = "性别不能为空")
    private Integer xb;
    @Schema(description = "手机号")
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3456789]\\d{9}$", message = "手机号格式错误")
    private String sjhm;
    @Schema(description = "班级ID")
    @NotNull(message = "班级ID不能为空")
    private Long clazzId;
    @Schema(description = "班级名称")
    private String clazzName;
    @Schema(description = "学校名称")
    private String schoolName;
    @Schema(description = "年级ID")
    @NotNull(message = "年级ID不能为空")
    private Long gradeId;
    @Schema(description = "年级名称")
    private String gradeName;
    @Schema(description = "学段")
    @NotNull(message = "学段不能为空")
    private Long period;
    @Schema(description = "民族")
    private Integer mz;
    @Schema(description = "家庭住址")
    @NotBlank(message = "家庭住址不能为空")
    private String jtzz;
    @Schema(description = "转入时间")
    private LocalDate inBeginDate;
    @Schema(description = "转出城市")
    private String outCityName;
    @Schema(description = "转出学期")
    @NotNull(message = "转出学期不能为空")
    private Long outTerm;
    @Schema(description = "转出学校")
    private String outSchoolName;
    @Schema(description = "转出年级")
    private String outGradeName;
    @Schema(description = "转出学期")
    private String outPeriodName;
    @Schema(description = "转出班级")
    private String outClazzName;
    @Schema(description = "转入原因")
    @NotBlank(message = "转入原因不能为空")
    private String adjustReason;
}
