package com.shida.region.business.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 市外转出
 * @PackageName: com.shida.region.business.pojo.vo
 * @ClassName: StudentShiWaiOutInfo
 * @Version 1.0
 * @date 2025/9/2 18:55
 */
@Data
@Accessors(chain = true)
@Schema(name = "StudentShiWaiOutInfo", description = "市外转出学生信息")
public class StudentShiWaiOutInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    @Schema(description = "ID")
    private Long id;
    @Schema(description = "申请ID")
    private Long adjustId;
    @Schema(description = "学生ID")
    private Long studentId;
    @Schema(description = "学生姓名")
    private String studentName;
    @Schema(description = "身份证号")
    private String sfzh;
    @Schema(description = "班级ID")
    private Long classId;
    @Schema(description = "班级名称")
    private String className;
    @Schema(description = "性别")
    private Integer xb;
    @Schema(description = "年级ID")
    private Long gradeId;
    @Schema(description = "年级名称")
    private String gradeName;
    @Schema(description = "学籍号")
    private String XJH;
    @Schema(description = "异动类型 6市外转出")
    private Integer adjustType;
    @Schema(description = "转入城市")
    private String intoCityName;
    @Schema(description = "转入学校")
    private String intoSchoolName;
    @Schema(description = "操作人ID")
    private Long applyUserId;
    @Schema(description = "操作人用户名")
    private String applyUserName;
    @Schema(description = "申请时间")
    private LocalDate applyDate;
    @Schema(description = "市外转出原因")
    private String outReason;
}
