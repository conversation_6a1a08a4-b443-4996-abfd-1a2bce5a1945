package com.shida.region.business.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shida.AccountCommonService;
import com.shida.constant.RoleConstant;
import com.shida.enums.RestCode;
import com.shida.excel.ExcelBatchImportResponse;
import com.shida.excel.ExcelHelper;
import com.shida.exceptions.ServiceException;
import com.shida.pojo.vo.AccountInfo;
import com.shida.pojo.vo.PageInfo;
import com.shida.pojo.vo.RoleInfo;
import com.shida.region.business.entity.Clazz;
import com.shida.region.business.entity.NeiKeRecord;
import com.shida.region.business.entity.School;
import com.shida.region.business.entity.WaiKeRecord;
import com.shida.region.business.mapper.StudentMapper;
import com.shida.region.business.mapper.WaiKeRecordMapper;
import com.shida.region.business.pojo.excel.StudentNeiKeExportExcel;
import com.shida.region.business.pojo.excel.StudentNeiKeImportExcel;
import com.shida.region.business.pojo.excel.StudentWaiKeExportExcel;
import com.shida.region.business.pojo.excel.StudentWaiKeImportExcel;
import com.shida.region.business.pojo.search.HealthSearch;
import com.shida.region.business.pojo.vo.ClazzInfo;
import com.shida.region.business.pojo.vo.NeiKeInfo;
import com.shida.region.business.pojo.vo.StudentInfo;
import com.shida.region.business.pojo.vo.WaiKeInfo;
import com.shida.region.business.service.IClazzService;
import com.shida.region.business.service.ISchoolService;
import com.shida.region.business.service.IWaiKeRecordService;
import com.shida.userCenter.api.IFeignGradeService;
import com.shida.userCenter.api.IFeignRegionService;
import com.shida.userCenter.api.IFeignTermService;
import com.shida.userCenter.dto.GradeDto;
import com.shida.userCenter.dto.OrgDto;
import com.shida.userCenter.dto.TermDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <p>
 * 学生外科纪录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-02
 */
@Service
public class WaiKeRecordServiceImpl extends ServiceImpl<WaiKeRecordMapper, WaiKeRecord> implements IWaiKeRecordService {

    @Resource
    private IFeignTermService feignTermService;
    @Autowired
    private IClazzService clazzService;
    @Resource
    private IFeignGradeService gradeService;
    @Resource
    private StudentMapper studentMapper;
    @Resource
    private ISchoolService schoolService;
    @Resource
    private IFeignRegionService feignRegionService;

    @Override
    public PageInfo<WaiKeInfo> getPageData(HealthSearch search) {
        IPage<WaiKeInfo> page = new Page<>(search.getPageNumber(), search.getPageSize());
        AccountInfo currentUser = AccountCommonService.getCurrentUser();
        assert currentUser != null;
        if (Objects.nonNull(currentUser.getSchoolId())) {
            search.setSchoolId(currentUser.getSchoolId());
        }
        page = baseMapper.getPageData(page, search);
        if (!page.getRecords().isEmpty()) {
            TermDto termDto = feignTermService.getById(search.getTermId());
            List<Long> regionIds = page.getRecords().stream().map(WaiKeInfo::getRegionId).distinct().collect(Collectors.toList());
            List<OrgDto> regionList = feignRegionService.getRegionListByIds(regionIds);
            page.getRecords().forEach(dataIfo -> {
                dataIfo.setTermName(termDto.getTermName());
                regionList.stream().filter(region -> region.getId().equals(dataIfo.getRegionId())).findFirst().ifPresent(region -> {
                    dataIfo.setRegionName(region.getName());
                });
            });
        }
        return PageInfo.fromMybatisPage(page);
    }

    @Override
    public List<ExcelBatchImportResponse> importStudent(MultipartFile file) {
        AccountInfo account = AccountCommonService.getCurrentUser();
        assert account != null;
        List<RoleInfo> roles = account.getRoles();
        String regionName = account.getRegionName();//区县名字
        if (roles.stream().anyMatch(roleInfo -> !roleInfo.getRoleCode().equals(RoleConstant.SCHOOL_ADMIN_CODE))) {//只有学校管理员可以导入
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "当前登录用户权限不足");
        }
        if (StrUtil.isBlank(account.getSchoolName())) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "当前登录人学校不存在");
        }
        List<ExcelBatchImportResponse> result = new ArrayList<>();
        List<StudentWaiKeImportExcel> excelList = new ArrayList<>();
        try {
            excelList = ExcelHelper.readData(file.getInputStream(), StudentWaiKeImportExcel.class, 0);
        } catch (Exception e) {
            log.error("读取Excel发生错误，请稍后重试", e);
            result.add(new ExcelBatchImportResponse().setMessage("读取Excel发生错误，请稍后重试").setRowIndex(1));
        }
        if (CollUtil.isEmpty(excelList)) {
            return result;
        }

        List<String> collect = excelList.stream().map(StudentWaiKeImportExcel::getXjh).distinct().collect(Collectors.toList());

        List<StudentInfo> studentList = studentMapper.getListByStudentNumber(collect);
        List<GradeDto> gradeDtoList = gradeService.getGradeListByName(excelList.stream().map(StudentWaiKeImportExcel::getGradeName).distinct().collect(Collectors.toList()));
        List<ClazzInfo> clazzInfoList = clazzService.getClazzList(Collections.singletonList(account.getSchoolId()), gradeDtoList.stream().map(GradeDto::getId).collect(Collectors.toList()));
        List<TermDto> termList = feignTermService.getTermListByName(excelList.stream().map(StudentWaiKeImportExcel::getTermName).distinct().collect(Collectors.toList()));
        for (int i = 0; i < excelList.size(); i++) {
            StudentWaiKeImportExcel studentImportExcel = excelList.get(i);
            List<ExcelBatchImportResponse> validateData = studentImportExcel.validateData(i + 1);
            if (CollUtil.isNotEmpty(validateData)) {
                result.addAll(validateData);
                return result;
            }
            // 判定班级是否存在
            if (clazzInfoList.stream().noneMatch(clazzInfo -> clazzInfo.getName().equals(studentImportExcel.getClassName()))) {
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中班级【" + studentImportExcel.getClassName() + "】不存在").setRowIndex(i + 1));
            }
            // 判定学期是否存在
            if (termList.stream().noneMatch(term -> term.getTermName().equals(studentImportExcel.getTermName()))) {
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中学期【" + studentImportExcel.getXjh() + "】不存在").setRowIndex(i + 1));
            }
            // 判定学籍号是否存在
            if (studentList.stream().noneMatch(student -> student.getXjh().equals(studentImportExcel.getXjh()))) {
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中学籍号【" + studentImportExcel.getXjh() + "】不存在").setRowIndex(i + 1));
            }
            // 判定学号有无重复
            if (excelList.stream().filter(student -> student.getXjh().equals(studentImportExcel.getXjh())).count() > 1) {
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中学籍号【" + studentImportExcel.getXjh() + "】存在重复数据").setRowIndex(i + 1));
            }
            if (!account.getSchoolName().equals(studentImportExcel.getSchoolName())) {
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中所属学校【" + studentImportExcel.getSchoolName() + "】与当前登录学校不一致").setRowIndex(i + 1));
            }
            if (!regionName.equals(studentImportExcel.getRegionName())) {
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中所属区县【" + studentImportExcel.getRegionName() + "】与当前登录区县不一致").setRowIndex(i + 1));
            }
            if (clazzInfoList.stream().noneMatch(clazzInfo -> clazzInfo.getSchoolName().equals(studentImportExcel.getSchoolName()))) {
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中班级【" + studentImportExcel.getClassName() + "】所属学校【" + studentImportExcel.getSchoolName() + "】不一致").setRowIndex(i + 1));
            }
            if (clazzInfoList.stream().noneMatch(clazzInfo -> clazzInfo.getGradeName().equals(studentImportExcel.getGradeName()))) {
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中班级【" + studentImportExcel.getClassName() + "】所属年级【" + studentImportExcel.getGradeName() + "】不一致").setRowIndex(i + 1));
            }
            boolean consistent = false;
            for (ClazzInfo clazzInfo : clazzInfoList) {
                if (clazzInfo.getSchoolName().equals(studentImportExcel.getSchoolName()) && clazzInfo.getGradeName().equals(studentImportExcel.getGradeName())) {
                    consistent = true;
                    break;
                }
            }
            if (!consistent) {
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中班级【" + studentImportExcel.getClassName() + "】所属年级【" + studentImportExcel.getGradeName() + "】不一致").setRowIndex(i + 1));
            }
        }
        if (CollUtil.isNotEmpty(result)) {
            return result;
        }
        List<WaiKeRecord> addStudentList = new ArrayList<>();
        if (CollUtil.isEmpty(result)) {
            for (StudentWaiKeImportExcel studentImportExcel : excelList) {
                WaiKeRecord dbInsert = new WaiKeRecord();
                Optional<StudentInfo> findStudent = studentList.stream().filter(studentInfo -> studentInfo.getXjh().equals(studentImportExcel.getXjh())).findFirst();
                findStudent.ifPresent(studentInfo -> dbInsert.setStudentId(studentInfo.getId()));
                Optional<ClazzInfo> findClazz = clazzInfoList.stream().filter(clazzInfo -> clazzInfo.getName().equals(studentImportExcel.getClassName())).findFirst();
                findClazz.ifPresent(clazzInfo -> dbInsert.setClazzId(clazzInfo.getId()));
                Optional<TermDto> findTerm = termList.stream().filter(termDto -> termDto.getTermName().equals(studentImportExcel.getTermName())).findFirst();
                findTerm.ifPresent(termDto -> dbInsert.setTermId(termDto.getId()));
                dbInsert.setSchoolId(account.getSchoolId());
                dbInsert.setTb(studentImportExcel.getTb());
                dbInsert.setJb(studentImportExcel.getJb());
                dbInsert.setXb(studentImportExcel.getXb());
                dbInsert.setLbj(studentImportExcel.getLbj());
                dbInsert.setJz(studentImportExcel.getJz());
                dbInsert.setSz(studentImportExcel.getSz());
                dbInsert.setPf(studentImportExcel.getPf());
                addStudentList.add(dbInsert);
            }
        }
        if (CollUtil.isNotEmpty(addStudentList)) {
            super.saveBatch(addStudentList);
        }
        return result;
    }

    @Override
    public void exportStudentWaiKe(HealthSearch search, HttpServletResponse response) {
        search.setPageNumber(1).setPageSize(1000);
        PageInfo<WaiKeInfo> pageInfo;
        ExcelHelper<StudentWaiKeExportExcel> excelHelper = ExcelHelper.create(StudentWaiKeExportExcel.class);
        AtomicInteger index = new AtomicInteger(1);
        do {
            pageInfo = this.getPageData(search);
            List<Clazz> clazzList = clazzService.list(new LambdaQueryWrapper<Clazz>().in(Clazz::getId, pageInfo.getRecords().stream().map(WaiKeInfo::getClazzId).distinct().collect(Collectors.toList())));
            List<School> schoolList = schoolService.list(new LambdaQueryWrapper<School>().in(School::getId, pageInfo.getRecords().stream().map(WaiKeInfo::getSchoolId).distinct().collect(Collectors.toList())));
            List<TermDto> termDtoList = feignTermService.getTermList(pageInfo.getRecords().stream().map(WaiKeInfo::getTermId).distinct().collect(Collectors.toList()));
            List<OrgDto> regionList = feignRegionService.getRegionListByIds(pageInfo.getRecords().stream().map(WaiKeInfo::getRegionId).distinct().collect(Collectors.toList()));
            List<StudentWaiKeExportExcel> excelList;
            excelList = pageInfo.getRecords().stream().map(dataInfo -> {
                StudentWaiKeExportExcel exportInfo = new StudentWaiKeExportExcel();
                exportInfo.setRowNum(index.getAndIncrement());
                if (ObjectUtil.isNotNull(dataInfo.getGender())) {
                    exportInfo.setGenderStr(dataInfo.getGender().equals("1") ? "男" : "女");
                }
                exportInfo.setStudentName(dataInfo.getStudentName());
                exportInfo.setStudentNumber(dataInfo.getStudentNumber());
                clazzList.stream().filter(clazz -> clazz.getId().equals(dataInfo.getClazzId())).findFirst().ifPresent(clazz -> exportInfo.setClazzName(clazz.getName()));
                schoolList.stream().filter(school -> school.getId().equals(dataInfo.getSchoolId())).findFirst().ifPresent(school -> exportInfo.setSchoolName(school.getName()));
                termDtoList.stream().filter(termDto -> termDto.getId().equals(dataInfo.getTermId())).findFirst().ifPresent(termDto -> exportInfo.setTermName(termDto.getTermName()));
                regionList.stream().filter(region -> region.getId().equals(dataInfo.getRegionId())).findFirst().ifPresent(region -> exportInfo.setRegionName(region.getName()));
                exportInfo.setTb(dataInfo.getTb());
                exportInfo.setJb(dataInfo.getJb());
                exportInfo.setXb(dataInfo.getJz());
                exportInfo.setJz(dataInfo.getLbj());
                exportInfo.setSz(dataInfo.getSz());
                exportInfo.setPf(dataInfo.getPf());
                exportInfo.setLbj(dataInfo.getLbj());
                return exportInfo;
            }).collect(Collectors.toList());
            excelHelper.writeData(excelList);
            search.setPageNumber(search.getPageNumber() + 1);
        } while (pageInfo.hasNext());
        excelHelper.writeToWebResponse(response, "学生外科纪录表");
    }
}
