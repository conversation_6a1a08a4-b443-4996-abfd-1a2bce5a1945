package com.shida.region.business.service;

import com.shida.pojo.form.KeyForm;
import com.shida.excel.ExcelBatchImportResponse;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.business.entity.ClassAdjustRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.shida.region.business.pojo.search.ClazzAdjustSearch;
import com.shida.region.business.pojo.search.StudentSearch;
import com.shida.region.business.pojo.vo.ClassAdjustRecordInfo;
import com.shida.region.business.pojo.vo.ClazzInfo;
import com.shida.region.business.pojo.vo.StudentInfo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

import java.util.List;

/**
 * <p>
 * 校内调班管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-02
 */
public interface IClassAdjustRecordService extends IService<ClassAdjustRecord> {
    List<ExcelBatchImportResponse> importClassAdjustRecord(MultipartFile file, Long regionId);

    PageInfo<ClassAdjustRecordInfo> getPageData(StudentSearch search);

    List<ClassAdjustRecordInfo> getList(KeyForm<Long> key);

    List<ClazzInfo> getClazzList(ClazzAdjustSearch search);

    boolean adjustClazz(ClazzAdjustSearch search);
}
