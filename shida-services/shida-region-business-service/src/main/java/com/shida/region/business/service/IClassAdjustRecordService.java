package com.shida.region.business.service;

import com.shida.excel.ExcelBatchImportResponse;
import com.shida.pojo.vo.KeyValue;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.business.entity.ClassAdjustRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.shida.region.business.pojo.form.ClazzAdjustForm;
import com.shida.region.business.pojo.search.ClazzAdjustSearch;
import com.shida.region.business.pojo.search.StudentSearch;
import com.shida.region.business.pojo.vo.ClassAdjustRecordInfo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 校内调班管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-02
 */
public interface IClassAdjustRecordService extends IService<ClassAdjustRecord> {
    List<ExcelBatchImportResponse> importClassAdjustRecord(MultipartFile file, Long regionId);

    PageInfo<ClassAdjustRecordInfo> getPageData(ClazzAdjustSearch search);

    List<ClassAdjustRecordInfo> getListByStudentId(Long studentId);

    List<KeyValue<Long, String>> getClazzList(ClazzAdjustForm search);

    boolean adjustClazz(ClazzAdjustForm search);
}
