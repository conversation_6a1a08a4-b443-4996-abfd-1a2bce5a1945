package com.shida.region.business.pojo.excel;

import com.shida.excel.ExcelAlias;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.excel
 * @ClassName: StudentSuspendExportExcel
 * @Version 1.0
 * @date 2025/9/5 16:46
 */
@Data
@Accessors(chain = true)
@Schema(name = "StudentSuspendExportExcel", description = "导出的学生休学信息")
public class StudentSuspendExportExcel {
    @ExcelAlias(value = "序号",orderNo = 1)
    private Integer rowNum;
    @ExcelAlias(value = "申请ID",orderNo = 2)
    private String adjustId;
    @ExcelAlias(value = "学生姓名",orderNo = 3)
    private String studentName;
    @ExcelAlias(value = "学籍号",orderNo = 4)
    private String xjh;
    @ExcelAlias(value = "身份证号",orderNo = 5)
    private String sfzh;
    @ExcelAlias(value = "性别",orderNo = 6)
    private String xb;
    @ExcelAlias(value = "年级名称",orderNo = 7)
    private String gradeName;
    @ExcelAlias(value = "当前班级",orderNo = 8)
    private String className;
    @ExcelAlias(value = "休学类型",orderNo = 9)
    private String adjustType;
    @ExcelAlias(value = "休学开始时间",orderNo = 10)
    private LocalDate suspendBeginDate;
    @ExcelAlias(value = "复学时间",orderNo = 11)
    private LocalDate suspendEndDate;
}
