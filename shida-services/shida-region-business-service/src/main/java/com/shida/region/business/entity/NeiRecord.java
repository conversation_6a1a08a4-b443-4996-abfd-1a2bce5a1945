package com.shida.region.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shida.entity.BaseEntity;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 学生内科纪录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-02
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("h_nei_record")
@Schema(name = "HNeiRecord", description = "学生内科纪录表")
public class NeiRecord extends BaseEntity<NeiRecord> {

    private static final long serialVersionUID = 1L;

    @Schema(description = "学生ID")
    private Long studentId;

    @Schema(description = "班级ID")
    private Long clazzId;

    @Schema(description = "学校ID")
    private Long schoolId;

    @Schema(description = "学期ID")
    private Long termId;

    @Schema(description = "心脏")
    private String xz;

    @Schema(description = "肝脏")
    private String gz;

    @Schema(description = "脾脏")
    private String pz;

    @Schema(description = "肺布")
    private String fb;

    @Schema(description = "血压")
    private String xy;

    @Schema(description = "肺活量")
    private String fhl;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
