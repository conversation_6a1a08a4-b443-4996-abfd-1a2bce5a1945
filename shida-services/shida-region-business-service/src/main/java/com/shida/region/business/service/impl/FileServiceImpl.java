package com.shida.region.business.service.impl;

import cn.hutool.core.io.FileUtil;
import com.shida.AccountCommonService;
import com.shida.enums.RestCode;
import com.shida.exceptions.ServiceException;
import com.shida.pojo.vo.AccountInfo;
import com.shida.properties.UploadFileProperties;
import com.shida.properties.UploadUrlProperties;
import com.shida.region.business.pojo.form.UploadFileForm;
import com.shida.pojo.vo.FileInfoLog;
import com.shida.region.business.service.IFileService;
import com.shida.region.business.tenant.TenantContextHolder;
import com.shida.service.impl.MongodbServiceImpl;
import com.shida.userCenter.dto.OrgDto;
import com.shida.utils.IdUtil;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.time.LocalDateTime;

@Service
public class FileServiceImpl extends MongodbServiceImpl<Long, FileInfoLog> implements IFileService {
    @Resource
    private UploadFileProperties uploadFileProperties;
    @Resource
    private UploadUrlProperties uploadUrlProperties;
    @Resource
    private TenantContextHolder tenantContextHolder;

    @Override
    public FileInfoLog uploadFile(MultipartFile file, UploadFileForm form, String domain) {
        try {
            if ("EDITOR_FILE".equals(form.getFileType())) {
                return saveEditorFile(file);
            }
            return null;
        } catch (Exception e) {
            throw new ServiceException("文件上传失败");
        }
    }

    @Override
    public String deleteById(Long fileId) {
        return "";
    }

    @Override
    public String deleteByUrl(String url) {
        return "";
    }

    @Override
    public void downloadExcelTemplate(String templateName, String downloadFileName, HttpServletResponse response) {
        ClassPathResource classPathResource;
        if (templateName.contains(".xlsx")) {
            classPathResource = new ClassPathResource("template/excel/" + templateName);
        } else {
            classPathResource = new ClassPathResource("template/excel/" + templateName + ".xlsx");
        }
        if (!classPathResource.exists()) {
            throw new ServiceException(RestCode.EXCEL_TEMPLATE_ERROR);
        } else {
            InputStream inputStream = null;
            ServletOutputStream outputStream = null;
            try {
                inputStream = classPathResource.getInputStream();
                response.setContentType("application/vnd.ms-excel");
                String fileName;
                if (downloadFileName.contains(".xlsx")) {
                    fileName = URLEncoder.encode(downloadFileName, "UTF-8");
                } else {
                    fileName = URLEncoder.encode(downloadFileName + ".xlsx", "utf-8");
                }
                response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
                response.setHeader("Access-Control-Expose-Headers", "FileName");
                response.setHeader("FileName", fileName);
                outputStream = response.getOutputStream();
                byte[] buff = new byte[10240];
                int length;
                while ((length = inputStream.read(buff)) != -1) {
                    outputStream.write(buff, 0, length);
                }
            } catch (Exception e) {
                throw new ServiceException(RestCode.EXCEL_TEMPLATE_ERROR);
            } finally {
                try {
                    if (inputStream != null) {
                        inputStream.close();
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
                try {
                    if (outputStream != null) {
                        outputStream.flush();
                        outputStream.close();
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }


    private FileInfoLog saveEditorFile(MultipartFile file) throws IOException {
        AccountInfo currentUser = AccountCommonService.getCurrentUser();
        if (currentUser == null) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "请先登录");
        }
        String fileName = IdUtil.fastSimpleUUID() + "." + FileUtil.extName(file.getOriginalFilename());
        OrgDto region = tenantContextHolder.getTenant();
        String filepath = uploadFileProperties.getEditorPath() + region.getCode() + "/";
        if (!FileUtil.exist(filepath)) {
            FileUtil.mkdir(filepath);
        }
        filepath = filepath + fileName;
        FileUtil.writeFromStream(file.getInputStream(), filepath);
        FileInfoLog fileInfoLog = new FileInfoLog()
                .setId(IdUtil.getSnowflakeNextId())
                .setFilePath(filepath)
                .setName(file.getOriginalFilename())
                .setServerFileName(fileName)
                .setUrl(uploadUrlProperties.getEditor() + region.getCode() + "/" + fileName)
                .setCreateTime(LocalDateTime.now())
                .setUsername(currentUser.getUsername())
                .setDeptCode(region.getCode())
                .setUserId(currentUser.getId());
        super.save(fileInfoLog);
        fileInfoLog.setUsername(null).setUserId(null).setDeptCode(null).setFilePath(null);
        return fileInfoLog;
    }
}
