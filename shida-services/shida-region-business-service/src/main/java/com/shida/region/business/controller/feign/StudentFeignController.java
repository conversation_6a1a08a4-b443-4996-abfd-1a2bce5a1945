package com.shida.region.business.controller.feign;

import cn.hutool.core.bean.BeanUtil;
import com.shida.IFeignStudentService;
import com.shida.dto.StudentAdjustDto;
import com.shida.dto.StudentAuditDto;
import com.shida.dto.StudentDto;
import com.shida.region.business.pojo.form.StudentShiNeiOutForm;
import com.shida.region.business.service.IStudentAdjustService;
import com.shida.region.business.service.IStudentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.controller.feign
 * @ClassName: StudentFeignController
 * @Version 1.0
 * @date 2025/9/3 18:03
 */
@Tag(name = "Feign-学生服务")
@RestController
public class StudentFeignController implements IFeignStudentService {

    @Resource
    IStudentService studentService;

    @Resource
    IStudentAdjustService studentAdjustService;

    @Operation(summary = "通过学籍号获取学生信息")
    @Override
    public StudentDto getStudentInfoByXjh(String xjh, Long regionId) {
        return studentService.getStudentInfoByXjh(xjh);
    }

    @Operation(summary = "保存学生异动")
    @Override
    public Integer saveStudentAdjust(StudentAdjustDto studentAdjustDto, Long regionId) {
        StudentShiNeiOutForm studentShiNeiOutForm = new StudentShiNeiOutForm();
        BeanUtil.copyProperties(studentAdjustDto,studentShiNeiOutForm);
        return studentAdjustService.saveShiNeiOut(studentShiNeiOutForm);
    }
    @Override
    public Integer auditStudentAdjust(StudentAuditDto studentAuditDto, Long regionId) {
        return studentAdjustService.auditShiNeiIn(studentAuditDto);
    }
    @Override
    public Integer updateShiNeiOut(StudentAdjustDto studentAdjustDto, Long regionId) {
        StudentShiNeiOutForm studentShiNeiOutForm = new StudentShiNeiOutForm();
        BeanUtil.copyProperties(studentAdjustDto,studentShiNeiOutForm);
        return studentAdjustService.updateShiNeiOut(studentShiNeiOutForm);
    }

    @Override
    public Integer deleteStudent(String xjh, Long regionId) {
        return studentAdjustService.deleteShiNeiOut(xjh,regionId);
    }
}
