package com.shida.region.business.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.shida.region.business.entity.StudentAdjust;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shida.region.business.pojo.search.StudentAdjustSearch;
import com.shida.region.business.pojo.search.StudentSearch;
import com.shida.region.business.pojo.vo.StudentInfo;
import com.shida.region.business.pojo.vo.StudentShiWaiOutInfo;
import com.shida.region.business.pojo.vo.StudentSuspendInfo;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 学籍异动 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-02
 */
public interface StudentAdjustMapper extends BaseMapper<StudentAdjust> {
    IPage<StudentSuspendInfo> getSuspendPageData(IPage<StudentSuspendInfo> page, @Param("search") StudentAdjustSearch search);

    IPage<StudentShiWaiOutInfo> getShiWaiOutPageData(IPage<StudentShiWaiOutInfo> page,@Param("search") StudentAdjustSearch search);
}
