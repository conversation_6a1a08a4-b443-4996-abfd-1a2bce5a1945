package com.shida.region.business.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shida.AccountCommonService;
import com.shida.entity.BaseEntity;
import com.shida.enums.RestCode;
import com.shida.enums.TermStatudEnum;
import com.shida.excel.ExcelBatchImportResponse;
import com.shida.exceptions.ServiceException;
import com.shida.pojo.vo.AccountInfo;
import com.shida.pojo.vo.KeyValue;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.business.entity.Clazz;
import com.shida.region.business.entity.SportRateRecord;
import com.shida.region.business.entity.Student;
import com.shida.region.business.info.SportDeepCheckDataInfo;
import com.shida.region.business.info.SportDeepCheckExcelDataInfo;
import com.shida.region.business.mapper.SportRateRecordMapper;
import com.shida.region.business.pojo.excel.SportRateRecordImportExcel;
import com.shida.region.business.pojo.search.SportRateSearch;
import com.shida.region.business.pojo.vo.MonthSportRateInfo;
import com.shida.region.business.pojo.vo.SportRateInfo;
import com.shida.region.business.service.IClazzService;
import com.shida.region.business.service.ISportRateRecordService;
import com.shida.region.business.service.IStudentService;
import com.shida.region.business.tenant.TenantContextHolder;
import com.shida.userCenter.api.IFeignTermService;
import com.shida.userCenter.dto.OrgDto;
import com.shida.userCenter.dto.TermDto;
import com.shida.utils.FormulaUtils;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <p>
 * 体育课出勤率 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@Service
@Slf4j
public class SportRateRecordServiceImpl extends ServiceImpl<SportRateRecordMapper, SportRateRecord> implements ISportRateRecordService {

    @Resource
    private IFeignTermService feignTermService;
    @Resource
    private IClazzService clazzService;
    @Resource
    private IStudentService studentService;
    @Resource
    private TenantContextHolder tenantContextHolder;

    @Override
    public PageInfo<SportRateInfo> getPageData(SportRateSearch search) {
        IPage<SportRateInfo> page = new Page<>(search.getPageNumber(), search.getPageSize());
        page = baseMapper.getPageData(page, search);

        List<Long> studentIds = page.getRecords().stream().map(SportRateInfo::getStudentId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(studentIds)) {
            List<SportRateRecord> records = super.list(new LambdaQueryWrapper<SportRateRecord>()
                .eq(SportRateRecord::getTermId, search.getTermId())
                .in(SportRateRecord::getStudentId, studentIds));
            Map<String, List<SportRateRecord>> recordMap = records.stream().collect(Collectors.groupingBy(SportRateRecord::getYfmc));
            TermDto termDto = feignTermService.getById(search.getTermId());

            OrgDto orgDto = tenantContextHolder.getTenant();
            for (SportRateInfo record : page.getRecords()) {
                record.setTermName(termDto.getTermName());
                record.setRegionId(orgDto.getId()).setRegionName(orgDto.getName());
                record.setMonthSportRateInfoList(new ArrayList<>());
                // 总缺勤数
                int totalQqcs = 0;
                for (String month : termDto.getMonths().split(",")) {
                    List<SportRateRecord> sportRateRecords = recordMap.get(month);
                    SportRateRecord sportRateRecord = sportRateRecords.stream().filter(r -> r.getStudentId().equals(record.getStudentId()))
                        .findFirst().orElse(null);
                    if (sportRateRecord != null) {
                        MonthSportRateInfo monthSportRateInfo = new MonthSportRateInfo();
                        monthSportRateInfo.setId(sportRateRecord.getId())
                            .setStudentId(sportRateRecord.getStudentId())
                            .setMonth(month)
                            .setBjcs(sportRateRecord.getBjcs())
                            .setSjcs(sportRateRecord.getSjcs())
                            .setQqcs(sportRateRecord.getQqcs());

                        // 存在值计算月份缺勤数加入到总缺勤数
                        log.debug("计算月份缺勤数：{}，病假次数：{}，事假次数：{}，旷课次数：{}，总缺勤数：{}", month, sportRateRecord.getBjcs(), sportRateRecord.getSjcs(), sportRateRecord.getQqcs(), totalQqcs);
                        totalQqcs += FormulaUtils.getQqcs(sportRateRecord.getBjcs(), sportRateRecord.getSjcs(), sportRateRecord.getQqcs());
                        record.getMonthSportRateInfoList().add(monthSportRateInfo);
                    } else {
                        record.getMonthSportRateInfoList().add(
                            new MonthSportRateInfo()
                                .setId(null)
                                .setStudentId(record.getStudentId())
                                .setMonth(month)
                                .setBjcs(0)
                                .setSjcs(0)
                                .setQqcs(0));
                    }
                }
                // 如果学生伤病/残疾 出勤率显示'--',缺勤天数'--'
                if (record.getSfcj() || record.getSfsb()) {
                    record.setQqs("--");
                    record.setCql("--");
                    // 残疾学生不能参加的，过程性考核成绩按满分记入
                    if (record.getSfcj()) {
                        record.setCqScore(FormulaUtils.PHYSICAL_EDUCATION_SCORE_MIDDLE);
                    }
                    // 伤病学生不能参加的，按该项满分的60%记入
                    if (record.getSfsb()) {
                        record.setCqScore(FormulaUtils.PHYSICAL_EDUCATION_SCORE_SICK);
                    }
                }else {
                    // 计算出勤率，缺勤天数，出勤得分
                    record.setQqs(String.valueOf(totalQqcs));
                    BigDecimal cql = FormulaUtils.getCql(record.getXqzks(), totalQqcs);
                    // 转换为百分比
                    cql = cql.multiply(new BigDecimal(100));
                    record.setCql(String.valueOf(cql));
                    record.setCqScore(FormulaUtils.getPhysicalEducationScore(cql));
                }
            }
        }

        return PageInfo.fromMybatisPage(page);
    }


    @Override
    public void downloadBatchImportTemplate(HttpServletResponse response, String regionId, Long termId) {
        TermDto termDto = feignTermService.getById(termId);
        ExcelWriter excelWriter = ExcelUtil.getBigWriter();
        // 构建 Excel 表头
        List<KeyValue<String, String>> headerList = buildHeaderList(termDto);

        JSONObject jsonObject = new JSONObject();
        for (KeyValue<String, String> header : headerList) {
            excelWriter.addHeaderAlias(header.getKey(), header.getValue());
            jsonObject.set(header.getKey(), "");
        }
        excelWriter.setOnlyAlias(true);
        JSONArray jsonArray = new JSONArray();
        jsonArray.add(jsonObject);
        jsonArray.add(jsonObject);
        jsonArray.add(jsonObject);
        jsonArray.add(jsonObject);
        jsonArray.add(jsonObject);
        excelWriter.write(jsonArray);
        // 合并单元格
        mergeCells(excelWriter, headerList);

        // 导出excel
        exportToResponse(response, excelWriter, termDto.getTermName() + "导入模板.xlsx");
    }

    @Override
    public boolean deleteData(Long studentId, Long termId) {
        // 获取学期+校验学期合法性
        getValidTermById(termId);
        return super.lambdaUpdate().eq(SportRateRecord::getStudentId, studentId).remove();
    }

    @Override
    public void exportSportRate(SportRateSearch search, HttpServletResponse response) {
        // 获取学期信息
        TermDto termDto = feignTermService.getById(search.getTermId());
        // 构建 Excel 表头
        List<KeyValue<String, String>> headerList = buildHeaderList(termDto);
        // 获取数据
        PageInfo<SportRateInfo> pageData = getPageData(search);
        List<SportRateInfo> dataRecords = pageData.getRecords();
        // 将业务数据转换为 JSONArray（适配 ExcelWriter 的写入格式）
        JSONArray jsonArray = convertToJsonArray(dataRecords);
        // 初始化 ExcelWriter 并设置表头别名
        ExcelWriter excelWriter = ExcelUtil.getBigWriter();
        // 设置数值起始行
        excelWriter.setCurrentRow(2);
        // 写入数据
        excelWriter.write(jsonArray);
        // 合并单元格（与模板逻辑一致的合并规则）
        mergeCells(excelWriter, headerList);
        // 导出 Excel 到前端
        exportToResponse(response, excelWriter, termDto.getTermName() + "数据导出.xlsx");
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public List<ExcelBatchImportResponse> batchImport(MultipartFile file, Long termId) {
        AccountInfo accountInfo = AccountCommonService.getCurrentUser();

        // 拿到学校
        String schoolName = accountInfo.getSchoolName();
        if (StrUtil.isBlank(schoolName)) {
            log.error("该用户获取学校姓名异常：{}", accountInfo.getUsername());
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "导入失败，体育课出勤率请使用学校或体育老师账号导入");
        }
        // 获取合法的学期信息
        TermDto term = getValidTermById(termId);
        // 读取模板相关信息
        List<List<Object>> readResult = null;
        try {
            ExcelReader excelReader = ExcelUtil.getReader(file.getInputStream());
            readResult = excelReader.read();
        } catch (IOException exception) {
            log.error("读取模板数据发生错误，请稍后重试", exception);
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "读取模板数据发生错误");
        }
        if (CollUtil.isEmpty(readResult)) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "模板数据为空，请下载最新模板");
        }

        // 校验模板标题是否符合标准
        checkExcelTemplate(readResult.get(0), term);

        // 结果集（去标题后结果集）
        List<List<Object>> resultData = readResult.subList(2, readResult.size());
        if (ObjectUtil.isEmpty(resultData)) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "模板内容数据为空，请填写数据");
        }

        // 基础数据检查
        List<ExcelBatchImportResponse> errors = baseCheckExcelData(resultData, accountInfo, term);

        // 深度校验数据的准确性以及合法性（进行数据库层面的校验）
        SportDeepCheckExcelDataInfo deepCheckResultInfo = deepCheckExcelData(resultData, errors, accountInfo);
        // 数据库中学生对象
        Map<String, Student> studentStutusMap = deepCheckResultInfo.getStudentStutusMap();
        // 数据库中的ID对象
        Map<Long, Student> studentIdMap = deepCheckResultInfo.getStudentIdMap();
        // 数据库中班级对象
        Map<String, Clazz> clazzMap = deepCheckResultInfo.getClazzMap();

        if (CollUtil.isNotEmpty(errors)) {
            return errors;
        }

        // 转换为BO对象让数据库存储
        List<SportRateRecord> sportRateRecordList = convertSportRateRecord(resultData, studentStutusMap, clazzMap, term, accountInfo);
        // 先删除原数据
        List<Long> studentIds = studentIdMap.values().stream().map(BaseEntity::getId).collect(Collectors.toList());
        super.lambdaUpdate().in(SportRateRecord::getStudentId, studentIds).remove();
        // 导入新数据
        super.saveBatch(sportRateRecordList);

        return null;
    }

    /**
     * 获取合法学期
     *
     * @param termId 学期ID
     * @return 合法学期
     */
    private TermDto getValidTermById(Long termId) {
        TermDto term = feignTermService.getById(termId);
        if (ObjectUtil.isEmpty(term)) {
            log.error("未找到该学期信息，{}", termId);
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "非法学期");
        }
        Integer status = term.getStatus();
        if (TermStatudEnum.ARCHIVE.getCode().equals(status.toString())) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "该学期已经归档，无法进行操作");
        } else if (TermStatudEnum.DISABLED.getCode().equals(status.toString())) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "该学期已经禁用，无法进行操作");
        }
        return term;
    }

    /**
     * 校验模板数据是否符合标准
     *
     * @param titleData 模板标题
     * @param term      学期信息
     */
    private void checkExcelTemplate(List<Object> titleData, TermDto term) {
        int defaultTitleSize = 11;
        log.info("开始校验长度，模板长度计算公式：默认11列标题 + 月份 * 3 = 总列数");
        int titleSize = titleData.size();
        String months = term.getMonths();
        if (StrUtil.isBlank(months)) {
            log.error("学期【{}】未配置月份", term.getTermName());
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "导入失败，学期【" + term.getTermName() + "】未配置月份");
        }
        String[] monthArray = months.split(",");
        int monthTitleSize = monthArray.length * 3 + defaultTitleSize;

        log.info("开始比较下载模板长度与用户上传模板长度是否一致，用户上传模板长度：【{}】，模板默认长度：【{}】", titleSize, monthTitleSize);
        if (titleSize != monthTitleSize) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "导入失败，模板列长度与模板不一致，请下载最新模板");
        }
        // 校验月份
        List<Object> monthList = titleData.subList(defaultTitleSize, titleData.size());
        if (ObjectUtil.isEmpty(monthList)) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "导入失败，上传的模板未配置月份");
        }
        // 剩下长度模运算取余，不能被3整除，证明模板有问题
        if (monthList.size() % 3 != 0) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "导入失败，模板月份列长度与模板不一致，请下载最新模板");
        }
        log.info("开始校验模板标题字段是否与模板一致");
        // 默认模板标题
        List<String> titleList = new ArrayList<>(Arrays.asList("序号", "姓名", "学籍号", "性别", "所属班级", "所属学校", "所属区县", "是否残疾免试", "是否伤病免试", "所属学期", "学期总课时"));
        // 添加上默认月份
        for (String month : monthArray) {
            String monthTitle = month + "月份";
            // 这里需要的三条的原因是因为模板是3条合并而来，也可以参考模板去读
            titleList.add(monthTitle);
            titleList.add(monthTitle);
            titleList.add(monthTitle);
        }
        log.info("默认模板标题：【{}】", titleList);
        log.info("用户上传模板标题：【{}】", titleData);
        // 校验标题
        for (int i = 0; i < titleData.size(); i++) {
            Object o = titleData.get(i);
            if (ObjectUtil.isEmpty(o)) {
                log.error("模板标题为空，列：【{}】,请检查模板标题是否一致", i + 1);
                throw new ServiceException(RestCode.ARGUMENT_ERROR, "导入失败，模板第" + (i + 1) + "列标题为空");
            }
            String title = o.toString();
            if (!titleList.get(i).equals(title)) {
                throw new ServiceException(RestCode.ARGUMENT_ERROR, "导入失败，列标题：【" + title + "】，与模板不一致，模板标题：【" + titleList.get(i) + "】，请更新列名或下载最新模板");
            }
        }
    }

    /**
     * 对上传的数据进行检查
     *
     * @param data 数据
     * @return 错误信息
     */
    private List<ExcelBatchImportResponse> baseCheckExcelData(List<List<Object>> data, AccountInfo accountInfo, TermDto termDto) {
        // 拿到学校ID
        String schoolName = accountInfo.getSchoolName();
        // 地区ID
        String regionName = accountInfo.getRegionName();
        if (null == regionName) {
            log.error("该用户获取区县ID异常：{}", accountInfo.getUsername());
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "导入失败，获取区县ID失败，请联系管理员");
        }
        // 学期ID
        String termName = termDto.getTermName();
        if (StrUtil.isBlank(termName)) {
            log.error("该用户获取学期名称异常：{}", accountInfo.getUsername());
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "导入失败，获取学期名称失败，请联系管理员");
        }
        // 错误信息
        List<ExcelBatchImportResponse> errorList = new ArrayList<>();

        // 进行基础信息检查，排空
        for (int i = 0; i < data.size(); i++) {
            // 这里 i + 3的原因是因为： i为下标，序号本身需要+1，excel行数前两行是标题，所以需要加3，方便用户快速定位问题数据
            int errorIndex = i + 3;
            // 姓名
            Object name = data.get(i).get(1);
            if (ObjectUtil.isEmpty(name)) {
                errorList.add(new ExcelBatchImportResponse().setMessage("第" + (errorIndex) + "行姓名为空").setRowIndex(errorIndex));
            }
            // 学籍号
            Object xjh = data.get(i).get(2);
            if (ObjectUtil.isEmpty(xjh)) {
                errorList.add(new ExcelBatchImportResponse().setMessage("第" + (errorIndex) + "行学籍号为空").setRowIndex(errorIndex));
            }
            // 性别
            Object gender = data.get(i).get(3);
            if (ObjectUtil.isEmpty(gender)) {
                errorList.add(new ExcelBatchImportResponse().setMessage("第" + (errorIndex) + "行性别为空").setRowIndex(errorIndex));
            } else if (!"男".equals(gender.toString()) && !"女".equals(gender.toString())) {
                errorList.add(new ExcelBatchImportResponse().setMessage("第" + (errorIndex) + "行性别填写错误，请填写【男】或【女】").setRowIndex(errorIndex));
            }
            // 班级
            Object clazz = data.get(i).get(4);
            if (ObjectUtil.isEmpty(clazz)) {
                errorList.add(new ExcelBatchImportResponse().setMessage("第" + (errorIndex) + "行班级为空").setRowIndex(errorIndex));
            }
            // 学校
            Object school = data.get(i).get(5);
            if (ObjectUtil.isEmpty(school)) {
                errorList.add(new ExcelBatchImportResponse().setMessage("第" + (errorIndex) + "行学校为空").setRowIndex(errorIndex));
            } else if (!school.toString().equals(schoolName)) {
                errorList.add(new ExcelBatchImportResponse().setMessage("第" + (errorIndex) + "行学校名称不一致，请检查").setRowIndex(errorIndex));
            }
            // 区县
            Object county = data.get(i).get(6);
            if (ObjectUtil.isEmpty(county)) {
                errorList.add(new ExcelBatchImportResponse().setMessage("第" + (errorIndex) + "行区县为空").setRowIndex(errorIndex));
            } else if (!county.toString().equals(regionName)) {
                errorList.add(new ExcelBatchImportResponse().setMessage("第" + (errorIndex) + "行区县名称不一致，请检查").setRowIndex(errorIndex));
            }
            // 是否残疾免试
            Object sfcj = data.get(i).get(7);
            if (ObjectUtil.isEmpty(sfcj)) {
                errorList.add(new ExcelBatchImportResponse().setMessage("第" + (errorIndex) + "行是否残疾免试为空").setRowIndex(errorIndex));
            } else if (!"是".equals(sfcj.toString()) && !"否".equals(sfcj.toString())) {
                errorList.add(new ExcelBatchImportResponse().setMessage("第" + (errorIndex) + "行是否残疾免试填写错误，请填写【是】或【否】").setRowIndex(errorIndex));
            }
            // 是否伤病免试
            Object sfsb = data.get(i).get(8);
            if (ObjectUtil.isEmpty(sfsb)) {
                errorList.add(new ExcelBatchImportResponse().setMessage("第" + (errorIndex) + "行是否伤病免试为空").setRowIndex(errorIndex));
            } else if (!"是".equals(sfsb.toString()) && !"否".equals(sfsb.toString())) {
                errorList.add(new ExcelBatchImportResponse().setMessage("第" + (errorIndex) + "行是否伤病免试填写错误，请填写【是】或【否】").setRowIndex(errorIndex));
            }
            // 学期
            Object term = data.get(i).get(9);
            if (ObjectUtil.isEmpty(term)) {
                errorList.add(new ExcelBatchImportResponse().setMessage("第" + (errorIndex) + "行学期为空").setRowIndex(errorIndex));
            } else if (!term.toString().equals(termName)) {
                errorList.add(new ExcelBatchImportResponse().setMessage("第" + (errorIndex) + "行学期名称不一致，请检查").setRowIndex(errorIndex));
            }
            // 学期总课时
            Object xqzks = data.get(i).get(10);
            if (ObjectUtil.isEmpty(xqzks)) {
                errorList.add(new ExcelBatchImportResponse().setMessage("第" + (errorIndex) + "行学期总课时为空").setRowIndex(errorIndex));
            } else if (!NumberUtil.isNumber(xqzks.toString())) {
                errorList.add(new ExcelBatchImportResponse().setMessage("第" + (errorIndex) + "行学期总课时填写错误，请填写数字").setRowIndex(errorIndex));
            }
        }
        return errorList;
    }

    /**
     * 深度校验数据是否存在
     *
     * @param resultData 待校验的数据
     * @param errors     错误信息
     */
    private SportDeepCheckExcelDataInfo deepCheckExcelData(List<List<Object>> resultData, List<ExcelBatchImportResponse> errors, AccountInfo accountInfo) {
        List<SportDeepCheckDataInfo> importDataList = resultData.stream()
            .map(item -> new SportDeepCheckDataInfo(
                item.get(2).toString(),  // 导入的学籍号
                item.get(1).toString(),  // 导入的学生姓名
                item.get(4).toString(),  // 导入的班级名称
                item.get(3).toString()   // 导入的性别
            ))
            .collect(Collectors.toList());

        // 提取单独的列表（用于查询和重复校验）
        List<String> studentXjhs = importDataList.stream()
            .map(SportDeepCheckDataInfo::getXjh)
            .collect(Collectors.toList());
        List<String> clazzNames = importDataList.stream()
            .map(SportDeepCheckDataInfo::getClazzName)
            .collect(Collectors.toList());

        // 校验学籍号重复（标记所有重复行，而非仅第一行）
        Map<String, List<Integer>> xjhDuplicateRows = new HashMap<>();
        for (int i = 0; i < studentXjhs.size(); i++) {
            String xjh = studentXjhs.get(i);
            xjhDuplicateRows.computeIfAbsent(xjh, k -> new ArrayList<>()).add(i);
        }
        xjhDuplicateRows.values().stream()
            // 筛选重复的学籍号
            .filter(rows -> rows.size() > 1)
            .forEach(rows -> {
                String xjh = studentXjhs.get(rows.get(0));
                rows.forEach(rowIdx -> {
                    int errorIndex = rowIdx + 3;  // 行号从3开始（Excel表头+标题行）
                    errors.add(new ExcelBatchImportResponse()
                        .setMessage("学籍号【" + xjh + "】存在重复（与第" + (rows.get(0) + 3) + "行重复）")
                        .setRowIndex(errorIndex));
                });
            });

        Long schoolId = accountInfo.getSchoolId();
        // 查询学生（仅当前学校）
        List<Student> studentList = studentService.lambdaQuery()
            .eq(Student::getSchoolId, schoolId)
            .in(Student::getXjh, studentXjhs)
            .list();
        if (CollectionUtil.isEmpty(studentList)) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "未查询到任何学生信息，请检查学籍号是否属于当前学校");
        }
        Map<String, Student> xjhToStudentMap = studentList.stream()
            // 处理极端情况：同校同学籍号（理论上不应存在）
            .collect(Collectors.toMap(Student::getXjh, s -> s, (s1, s2) -> s1));
        Map<Long, Student> studentIdMap = studentList.stream()
            // 处理极端情况：理论上ID不可重复
            .collect(Collectors.toMap(Student::getId, s -> s, (s1, s2) -> s1));

        // 查询班级（仅当前学校）
        List<Clazz> clazzList = clazzService.lambdaQuery()
            .eq(Clazz::getSchoolId, schoolId)
            .in(Clazz::getName, clazzNames)
            .list();
        if (CollectionUtil.isEmpty(clazzList)) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "未查询到任何班级信息，请检查班级名称是否属于当前学校");
        }
        Map<String, Clazz> clazzNameToClazzMap = clazzList.stream()
            .collect(Collectors.toMap(Clazz::getName, c -> c, (c1, c2) -> c1));

        // 逐行校验
        for (int i = 0; i < importDataList.size(); i++) {
            int errorIndex = i + 3;
            SportDeepCheckDataInfo data = importDataList.get(i);
            String xjh = data.getXjh();
            String importName = data.getStudentName();
            String importClazzName = data.getClazzName();
            String gather = data.getGather();

            // 校验学生信息
            Student student = xjhToStudentMap.get(xjh);
            if (ObjectUtil.isEmpty(student)) {
                errors.add(new ExcelBatchImportResponse()
                    .setMessage("学籍号【" + xjh + "】在本校本学期无匹配学生")
                    .setRowIndex(errorIndex));
            } else if (!importName.equals(student.getXm())) {
                errors.add(new ExcelBatchImportResponse()
                    .setMessage("学籍号【" + xjh + "】姓名不匹配")
                    .setRowIndex(errorIndex));
                // 详细提示：（导入：" + importName + "，系统：" + student.getXm() + "） 暂时隐藏，有需要再次添加上
            }
            // 校验班级信息
            Clazz clazz = clazzNameToClazzMap.get(importClazzName);
            if (ObjectUtil.isEmpty(clazz)) {
                errors.add(new ExcelBatchImportResponse()
                    .setMessage("班级【" + importClazzName + "】在本校本学期不存在")
                    .setRowIndex(errorIndex));
                // 双重保险（查询已限定学校，理论上不会触发）
            } else if (!schoolId.equals(clazz.getSchoolId())) {
                errors.add(new ExcelBatchImportResponse()
                    .setMessage("班级【" + importClazzName + "】不属于当前学校（学校ID不匹配）")
                    .setRowIndex(errorIndex));
            }  // 校验班级ID是否一致
            else if (!student.getClazzId().equals(clazz.getId())) {
                errors.add(new ExcelBatchImportResponse()
                    .setMessage("班级【" + importClazzName + "】填写错误，该生不属于该班级")
                    .setRowIndex(errorIndex));
            }
            // 校验性别是否正确
            String gatherLabel = student.getXb() == 1 ? "男" : "女";
            if (!gather.equals(gatherLabel)) {
                errors.add(new ExcelBatchImportResponse().setMessage("性别填写错误").setRowIndex(errorIndex));
            }
        }

        //返回结果（提供映射供后续使用）
        return new SportDeepCheckExcelDataInfo().setClazzMap(clazzNameToClazzMap).setStudentStutusMap(xjhToStudentMap).setStudentIdMap(studentIdMap);
    }

    /**
     * 转换为BO对象
     *
     * @param resultData 待转换的数据
     * @param studentMap 学生列表
     * @param clazzMap   班级列表
     * @return 转换后的BO对象
     */
    private List<SportRateRecord> convertSportRateRecord(List<List<Object>> resultData, Map<String, Student> studentMap, Map<String, Clazz> clazzMap, TermDto term, AccountInfo accountInfo) {
        List<SportRateRecord> sportRateRecordList = new ArrayList<>();
        for (List<Object> resultDatum : resultData) {
            SportRateRecordImportExcel item = SportRateRecordImportExcel.readBuilder(resultDatum, term);
            // 获取月份后，通过月份生成对应的考勤表数据
            Map<String, SportRateRecordImportExcel.SportRateRecordMonthAttendance> monthAttendances = item.getMonthAttendances();

            monthAttendances.forEach((month, monthAttendance) -> {
                SportRateRecord sportRateRecord = new SportRateRecord();
                sportRateRecord.setStudentId(studentMap.get(item.getXjh()).getId());
                sportRateRecord.setClazzId(clazzMap.get(item.getClassName()).getId());
                sportRateRecord.setSchoolId(accountInfo.getSchoolId());
                sportRateRecord.setTermId(term.getId());
                sportRateRecord.setSfsb("是".equals(item.getSfsb()));
                sportRateRecord.setSfcj("是".equals(item.getSfcj()));
                sportRateRecord.setXqzks(Integer.parseInt(item.getXqzks()));
                sportRateRecord.setYfmc(month);
                sportRateRecord.setBjcs(monthAttendance.getSickLeaveCount());
                sportRateRecord.setSjcs(monthAttendance.getPersonalLeaveCount());
                sportRateRecord.setQqcs(monthAttendance.getAbsentCount());
                sportRateRecordList.add(sportRateRecord);
            });
        }
        return sportRateRecordList;
    }

    /**
     * 构建 Excel 表头（与模板逻辑一致）
     */
    private List<KeyValue<String, String>> buildHeaderList(TermDto termDto) {
        List<KeyValue<String, String>> headerList = new ArrayList<>();
        headerList.add(new KeyValue<>("index", "序号"));
        headerList.add(new KeyValue<>("xm", "姓名"));
        headerList.add(new KeyValue<>("xjh", "学籍号"));
        headerList.add(new KeyValue<>("xb", "性别"));
        headerList.add(new KeyValue<>("clazzName", "所属班级"));
        headerList.add(new KeyValue<>("schoolName", "所属学校"));
        headerList.add(new KeyValue<>("regionName", "所属区县"));
        headerList.add(new KeyValue<>("sfcj", "是否残疾免试"));
        headerList.add(new KeyValue<>("sfsb", "是否伤病免试"));
        headerList.add(new KeyValue<>("termName", "所属学期"));
        headerList.add(new KeyValue<>("xqzks", "学期总课时"));

        // 动态添加月份列（病假、事假、缺勤次数）
        for (String month : termDto.getMonths().split(",")) {
            headerList.add(new KeyValue<>(month + "_bjcs", "病假次数", month));
            headerList.add(new KeyValue<>(month + "_sjcs", "事假次数", month));
            headerList.add(new KeyValue<>(month + "_qqcs", "缺勤次数", month));
        }
        return headerList;
    }

    /**
     * 将业务数据转换为 JSONArray（适配 ExcelWriter 写入）
     */
    private JSONArray convertToJsonArray(List<SportRateInfo> sportRateInfos) {
        JSONArray jsonArray = new JSONArray();
        for (int i = 0; i < sportRateInfos.size(); i++) {
            JSONObject json = new JSONObject();
            SportRateInfo data = sportRateInfos.get(i);
            // 固定字段赋值
            json.set("index", i + 1);
            json.set("xm", data.getStudentName());
            json.set("xjh", data.getXjh());
            json.set("xb", "1".equals(data.getXb()) ? "男" : "女");
            json.set("clazzName", data.getClazzName());
            json.set("schoolName", data.getSchoolName());
            json.set("regionName", data.getRegionName());
            json.set("sfcj", data.getSfcj() ? "是" : "否");
            json.set("sfsb", data.getSfsb() ? "是" : "否");
            json.set("termName", data.getTermName());
            json.set("xqzks", data.getXqzks());

            // 动态月份字段赋值
            List<MonthSportRateInfo> monthSportRateInfoList = data.getMonthSportRateInfoList();
            for (MonthSportRateInfo month : monthSportRateInfoList) {
                json.set(month.getMonth() + "_bjcs", month.getBjcs());
                json.set(month.getMonth() + "_sjcs", month.getSjcs());
                json.set(month.getMonth() + "_qqcs", month.getQqcs());
            }
            jsonArray.add(json);
        }
        return jsonArray;
    }

    /**
     * 合并单元格（与模板逻辑一致）
     */
    private void mergeCells(ExcelWriter excelWriter, List<KeyValue<String, String>> headerList) {
        AtomicInteger colIndex = new AtomicInteger(0);
        for (KeyValue<String, String> header : headerList) {
            int col = colIndex.getAndIncrement();
            // 非动态月份列（extra 为 null）
            if (Objects.isNull(header.getExtra())) {
                excelWriter.merge(0, 1, col, col, header.getValue(), true);
            }
        }
        for (int i = 0; i < headerList.size(); ) {
            // 动态月份列（有 extra 标识月份）
            if (Objects.nonNull(headerList.get(i).getExtra())) {
                String month = headerList.get(i).getExtra();
                // 合并第一行：月份标题（跨 3 列）
                excelWriter.merge(0, 0, i, i + 2, month + "月份", true);
                // 第二行设置列名（病假、事假、缺勤）
                excelWriter.setCurrentRow(1);
                for (int j = 0; j < 3; j++) {
                    Cell cell = excelWriter.getOrCreateCell(i + j, 1);
                    cell.setCellValue(headerList.get(i + j).getValue());
                    cell.setCellStyle(excelWriter.getHeadCellStyle());
                }
                // 跳过已处理的 3 列
                i += 3;
            } else {
                // 非动态列，继续下一个
                i++;
            }
        }
    }


    /**
     * 导出 Excel 到前端响应
     */
    private void exportToResponse(HttpServletResponse response, ExcelWriter excelWriter, String fileName) {
        ServletOutputStream out = null;
        try {
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            fileName = java.net.URLEncoder.encode(fileName, "utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            out = response.getOutputStream();
            excelWriter.flush(out, true);
        } catch (Exception e) {
            throw new ServiceException(RestCode.EXCEL_REPORT_ERROR);
        } finally {
            excelWriter.close();
            IoUtil.close(out);
        }
    }
}
