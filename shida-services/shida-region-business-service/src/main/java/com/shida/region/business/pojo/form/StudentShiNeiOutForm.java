package com.shida.region.business.pojo.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.form
 * @ClassName: StudentShiNeiOutForm
 * @Version 1.0
 * @date 2025/9/4 10:04
 */
@Data
@Accessors(chain = true)
@Schema(name = "StudentShiNeiOutForm", description = "学生市内转出表单")
public class StudentShiNeiOutForm  implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "学生ID")
    private Long studentId;

    @Schema(description = "原区县名称")
    private String outRegionName;

    @Schema(description = "原区县ID")
    @NotNull(message = "原区县ID不能为空")
    private Long outRegionId;

    @Schema(description = "转入学校ID")
    private Long intoSchoolId;
    @Schema(description = "转入学校名称")
    private String intoSchoolName;
    @Schema(description = "转入年级ID")
    @NotNull(message = "转入年级ID不能为空")
    private Long intoGradeId;
    @Schema(description = "转入年级名称")
    private String intoGradeName;
    @Schema(description = "转入班级ID")
    @NotNull(message = "转入班级ID不能为空")
    private Long intoClazzId;
    @Schema(description = "转入班级名称")
    private String intoClazzName;
    @Schema(description = "转出原因")
    @NotBlank(message = "转出原因不能为空")
    private String outReason;
    @Schema(description = "申请日期")
    @NotNull(message = "申请日期不能为空")
    private LocalDate applyDate;
}
