package com.shida.region.business.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.shida.region.business.entity.KouQiangRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shida.region.business.pojo.search.HealthSearch;
import com.shida.region.business.pojo.vo.StudentKouQiangInfo;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 学生口腔检查表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
public interface KouQiangRecordMapper extends BaseMapper<KouQiangRecord> {
    IPage<StudentKouQiangInfo> getPageData(IPage<StudentKouQiangInfo> page, @Param("search") HealthSearch search);
}
