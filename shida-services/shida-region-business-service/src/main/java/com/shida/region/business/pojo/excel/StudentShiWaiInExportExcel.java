package com.shida.region.business.pojo.excel;

import com.shida.excel.ExcelAlias;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.excel
 * @ClassName: StudentSuspendExportExcel
 * @Version 1.0
 * @date 2025/9/5 16:46
 */
@Data
@Accessors(chain = true)
@Schema(name = "StudentShiWaiOutExportExcel", description = "导出的市外转入信息")
public class StudentShiWaiInExportExcel {
    @ExcelAlias(value = "序号")
    private Integer rowNum;
    @ExcelAlias(value = "申请ID")
    private String adjustId;
    @ExcelAlias(value = "学生姓名")
    private String studentName;
    @ExcelAlias(value = "身份证号")
    private String sfzh;
    @ExcelAlias(value = "学籍号")
    private String xjh;
    @ExcelAlias(value = "性别")
    private String xb;
    @ExcelAlias(value = "转出城市")
    private String outCityName;
    @ExcelAlias(value = "转出学校")
    private String outSchoolName;
    @ExcelAlias(value = "转出年级")
    private String outGradeName;
    @ExcelAlias(value = "转出学期")
    private String outTermName;
    @ExcelAlias(value = "转入城市")
    private String intoCityName;
    @ExcelAlias(value = "转入学校")
    private String intoSchoolName;
    @ExcelAlias(value = "转入年级")
    private String inGradeName;
    @ExcelAlias(value = "转入学期")
    private String inTermName;
    @ExcelAlias(value = "申请日期")
    private String applyDate;
}
