package com.shida.region.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shida.entity.BaseEntity;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_device")
@Schema(name = "Device", description = "")
public class Device extends BaseEntity<Device> {

    private static final long serialVersionUID = 1L;

    @Schema(description = "设备ID")
    private String deviceId;

    @Schema(description = "设备名称")
    private String deviceName;

    @Schema(description = "检测项目")
    private String projectName;

    @Schema(description = "所属学校ID")
    private Long schoolId;

    @Schema(description = "放置位置")
    private String location;

    @Schema(description = "在线状态")
    private Boolean onlineStatus;

    @Schema(description = "设备描述")
    private String description;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
