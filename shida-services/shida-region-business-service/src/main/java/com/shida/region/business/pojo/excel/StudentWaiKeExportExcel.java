package com.shida.region.business.pojo.excel;

import com.shida.excel.ExcelAlias;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.excel
 * @ClassName: StudentNeiKeExportExcel
 * @Version 1.0
 * @date 2025/8/14 17:10
 */
@Data
@Accessors(chain = true)
@Schema(name = "StudentWaiKeExportExcel", description = "学生外科情况导出")
public class StudentWaiKeExportExcel implements Serializable {
    @ExcelAlias(value = "序号")
    private Integer rowNum;
    @ExcelAlias(value = "姓名")
    private String studentName;
    @ExcelAlias(value = "学籍号")
    private String studentNumber;
    @ExcelAlias(value = "性别")
    private String genderStr;
    @ExcelAlias(value = "所属班级")
    private String clazzName;
    @ExcelAlias(value = "所属学校")
    private String schoolName;
    @ExcelAlias(value = "所属县区")
    private String regionName;
    @ExcelAlias(value = "头部")
    private String tb;
    @ExcelAlias(value = "颈部")
    private String jb;
    @ExcelAlias(value = "胸部")
    private String xb;
    @ExcelAlias(value = "脊柱")
    private String jz;
    @ExcelAlias(value = "四肢")
    private String sz;
    @ExcelAlias(value = "皮肤")
    private String pf;
    @ExcelAlias(value = "淋巴结")
    private String lbj;
    @ExcelAlias(value = "所属学期")
    private String termName;
}
