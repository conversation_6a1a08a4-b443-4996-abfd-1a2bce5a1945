package com.shida.region.business.pojo.excel;

import cn.hutool.core.util.StrUtil;
import com.shida.excel.ExcelAlias;
import com.shida.excel.ExcelBatchImportResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.region.business.pojo.excel
 * @ClassName: TeacherImportExcel
 * @Version 1.0
 * @date 2025/8/14 15:37
 */
@Data
@Accessors(chain = true)
@Schema(name = "StudentWaiKeImportExcel", description = "学生体检 - 外科数据批量导入")
public class StudentWaiKeImportExcel implements Serializable {
    @ExcelAlias(value = "序号", orderNo = 1)
    private Integer rowNum;
    @ExcelAlias(value = "姓名")
    private String xm;
    @ExcelAlias(value = "学籍号")
    private String xjh;
    @ExcelAlias(value = "性别")
    private String gender;
    @ExcelAlias(value = "所属班级")
    private String className;
    @ExcelAlias(value = "年级")
    private String gradeName;
    @ExcelAlias(value = "所属学校")
    private String schoolName;
    @ExcelAlias(value = "所属县区")
    private String regionName;
    @ExcelAlias(value = "头部")
    private String tb;
    @ExcelAlias(value = "颈部")
    private String jb;
    @ExcelAlias(value = "胸部")
    private String xb;
    @ExcelAlias(value = "脊柱")
    private String jz;
    @ExcelAlias(value = "四肢")
    private String sz;
    @ExcelAlias(value = "皮肤")
    private String pf;
    @ExcelAlias(value = "淋巴结")
    private String lbj;
    @ExcelAlias(value = "所属学期")
    private String termName;

    public List<ExcelBatchImportResponse> validateData(Integer rowIndex) {
        List<ExcelBatchImportResponse> result = new ArrayList<>();
        List<String> contentList = Arrays.asList("正常", "异常");
        if (StrUtil.isBlank(xm)) {
            result.add(new ExcelBatchImportResponse().setMessage("姓名不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(xjh)) {
            result.add(new ExcelBatchImportResponse().setMessage("学籍号不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (!StrUtil.startWithAny(xjh, "G", "g", "L", "l", "J", "j")) {
            result.add(new ExcelBatchImportResponse().setMessage("学籍号格式错误").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(xb)) {
            result.add(new ExcelBatchImportResponse().setMessage("性别不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(className)) {
            result.add(new ExcelBatchImportResponse().setMessage("班级不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(gradeName)) {
            result.add(new ExcelBatchImportResponse().setMessage("年纪不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(schoolName)) {
            result.add(new ExcelBatchImportResponse().setMessage("所属学校不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(regionName)) {
            result.add(new ExcelBatchImportResponse().setMessage("所属区县不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(termName)) {
            result.add(new ExcelBatchImportResponse().setMessage("所属学期不能为空").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(tb)) {
            result.add(new ExcelBatchImportResponse().setMessage("头部情况不能为空").setType("error").setRowIndex(rowIndex));
        }
        if(contentList.stream().noneMatch(content -> content.equals(tb))){
            result.add(new ExcelBatchImportResponse().setMessage("头部情况请输入【正常】或【异常】").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(jb)) {
            result.add(new ExcelBatchImportResponse().setMessage("颈部情况不能为空").setType("error").setRowIndex(rowIndex));
        }
        if(contentList.stream().noneMatch(content -> content.equals(jb))){
            result.add(new ExcelBatchImportResponse().setMessage("颈部情况请输入【正常】或【异常】").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(xb)) {
            result.add(new ExcelBatchImportResponse().setMessage("胸部情况不能为空").setType("error").setRowIndex(rowIndex));
        }
        if(contentList.stream().noneMatch(content -> content.equals(xb))){
            result.add(new ExcelBatchImportResponse().setMessage("胸部情况请输入【正常】或【异常】").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(jz)) {
            result.add(new ExcelBatchImportResponse().setMessage("脊柱情况不能为空").setType("error").setRowIndex(rowIndex));
        }
        if(contentList.stream().noneMatch(content -> content.equals(jz))){
            result.add(new ExcelBatchImportResponse().setMessage("脊柱情况请输入【正常】或【异常】").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(sz)) {
            result.add(new ExcelBatchImportResponse().setMessage("四肢情况不能为空").setType("error").setRowIndex(rowIndex));
        }
        if(contentList.stream().noneMatch(content -> content.equals(sz))){
            result.add(new ExcelBatchImportResponse().setMessage("四肢情况请输入【正常】或【异常】").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(pf)) {
            result.add(new ExcelBatchImportResponse().setMessage("皮肤情况不能为空").setType("error").setRowIndex(rowIndex));
        }
        if(contentList.stream().noneMatch(content -> content.equals(pf))){
            result.add(new ExcelBatchImportResponse().setMessage("皮肤情况请输入【正常】或【异常】").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(lbj)) {
            result.add(new ExcelBatchImportResponse().setMessage("淋巴结情况不能为空").setType("error").setRowIndex(rowIndex));
        }
        if(contentList.stream().noneMatch(content -> content.equals(lbj))){
            result.add(new ExcelBatchImportResponse().setMessage("淋巴结情况请输入【正常】或【异常】").setType("error").setRowIndex(rowIndex));
        }
        if (StrUtil.isBlank(termName)) {
            result.add(new ExcelBatchImportResponse().setMessage("所属学期不能为空").setType("error").setRowIndex(rowIndex));
        }
        return result;
    }
}
