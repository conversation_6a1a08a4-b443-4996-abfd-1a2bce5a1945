package com.shida.region.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shida.entity.BaseEntity;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 体育课出勤率
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("b_sport_rate_record")
@Schema(name = "SportRateRecord", description = "体育课出勤率")
public class SportRateRecord extends BaseEntity<SportRateRecord> {

    private static final long serialVersionUID = 1L;

    @Schema(description = "学生ID")
    private Long studentId;

    @Schema(description = "班级ID")
    private Long clazzId;

    @Schema(description = "学校ID")
    private Long schoolId;

    @Schema(description = "学期ID")
    private Long termId;

    @Schema(description = "是否伤病")
    private Boolean sfsb;

    @Schema(description = "是否残疾")
    private Boolean sfcj;

    @Schema(description = "学期总课时")
    private Integer xqzks;

    @Schema(description = "月份名称")
    private String yfmc;

    @Schema(description = "病假次数")
    private Integer bjcs;

    @Schema(description = "事假次数")
    private Integer sjcs;

    @Schema(description = "旷课次数")
    private Integer qqcs;



    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
