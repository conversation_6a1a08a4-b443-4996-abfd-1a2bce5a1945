package com.shida.region.business.pojo.vo;

import com.shida.userCenter.dto.UserDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
@Schema(name = "SchoolInfo", description = "学校信息")
public class SchoolInfo implements Serializable {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "学校名称")
    private String name;

    @Schema(description = "学校代码")
    private String code;

    @Schema(description = "简介")
    private String description;

    @Schema(description = "区县ID")
    private Long regionId;

    @Schema(description = "学段，1小学，2初中，3高中，4九年一贯制")
    private Integer period;

    @Schema(description = "学校类型,1公办，2民办，3公民同办")
    private Integer schoolType;

    @Schema(description = "联系人")
    private String contacts;

    @Schema(description = "联系电话")
    private String telephone;

    @Schema(description = "部门名称")
    private String regionName;

    @Schema(description = "部门code")
    private String regionCode;

    @Schema(description = "管理员")
    List<UserDto> masterList;

}
