package com.shida.region.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shida.entity.BaseEntity;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_teacher")
@Schema(name = "Teacher", description = "")
public class Teacher extends BaseEntity<Teacher> {

    private static final long serialVersionUID = 1L;

    @Schema(description = "学校ID")
    private Long schoolId;

    @Schema(description = "账户ID")
    private Long userId;

    @Schema(description = "教师姓名")
    private String jsxm;

    @Schema(description = "教师性别，1-男，2-女")
    private Integer jsxb;

    @Schema(description = "教师类型，1班主任，2体育教师")
    private String jslb;

    @Schema(description = "身份证号")
    private String sfzh;

    @Schema(description = "头像")
    private String jstx;

    @Schema(description = "手机号码")
    private String sjhm;


    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
