package com.shida.region.business.config.dataSource;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.druid.pool.DruidDataSource;
import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DataSourceProperty;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import io.seata.rm.datasource.DataSourceProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.annotation.Order;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.sql.*;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Order(-**********)
@Configuration
public class DynamicDataSourceConfig {
    @Resource
    private DynamicDataSourceProperties dynamicDataSourceProperties;

    @Resource
    private DefaultDataSourceCreator dataSourceCreator;


    @Bean
    @Primary
    public DataSource dataSource() throws Exception {
        DynamicDataSourceProperties properties = dynamicDataSourceProperties;
        Map<String, DataSourceProperty> dataSources = properties.getDatasource();
        DynamicRoutingDataSource dynamicRoutingDataSource = new DynamicRoutingDataSource();
        Connection connection = null;
        for (Map.Entry<String, DataSourceProperty> entry : dataSources.entrySet()) {
            if (entry.getKey().equals("master")) {
                dynamicRoutingDataSource.addDataSource(entry.getKey(), dataSourceCreator.createDataSource(entry.getValue()));
                connection = DriverManager.getConnection(entry.getValue().getUrl(), entry.getValue().getUsername(), entry.getValue().getPassword());
            }
        }
        if (connection == null) {
            throw new RuntimeException("未获取到主数据库（master）链接，请先配置主数据库链接");
        }

        for (Map.Entry<String, DataSourceProperty> entry : dataSources.entrySet()) {
            if (!entry.getKey().equals("master")) {
                try {
                    // 创建Statement对象来执行SQL语 句
                    Statement stmt = connection.createStatement();
                    String regex = "/([^/?]+)\\?";
                    java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(regex);
                    java.util.regex.Matcher matcher = pattern.matcher(entry.getValue().getUrl());
                    String databaseName = null;
                    if (matcher.find()) {
                        databaseName = matcher.group(1);
                    }
                    if (StrUtil.isBlank(databaseName)) {
                        throw new RuntimeException(StrUtil.format("未获取到数据源{}的数据库名称，请检查数据库链接URL是否正确", entry.getKey()));
                    }
                    String sql = StrUtil.format("CREATE DATABASE IF NOT EXISTS `{}` CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;", databaseName);
                    stmt.executeUpdate(sql);
                    sql = StrUtil.format("USE `{}`;", databaseName);
                    stmt.executeUpdate(sql);
                    String initSql = ResourceUtil.readStr("classpath:/sql/init_database.sql", CharsetUtil.CHARSET_UTF_8);
                    initSql = initSql.replaceAll("Dynamic;", "DYNAMIC;");
                    String[] tableSqls = initSql.split("DYNAMIC;");
                    for (String ts : tableSqls) {
                        if (StrUtil.isNotBlank(ts.trim())) {
                            String tableSql = "";
                            try {
                                tableSql = StrUtil.removeAllLineBreaks(ts);
                                tableSql = tableSql.replaceAll("utf8mb4_0900_ai_ci", "utf8mb4_general_ci");
                                if (!tableSql.startsWith("CREATE TABLE IF NOT EXISTS ")) {
                                    tableSql = tableSql.replaceAll("CREATE TABLE ", "CREATE TABLE IF NOT EXISTS ");
                                }
                                tableSql = tableSql.trim() + "DYNAMIC;";
                                log.info("执行SQL：{}", tableSql);
                                stmt.executeUpdate(tableSql);
                            } catch (Exception e) {
                                log.error("初始化数据库表失败，错误信息：{}", tableSql);
                                throw e;
                            }
                        }
                    }

                    String updateSql = ResourceUtil.readStr("classpath:/sql/update_database.sql", CharsetUtil.CHARSET_UTF_8);
                    // 按行分割
                    String[] lines = updateSql.split("\n");
                    for (String sqlLine : lines) {
                        if (sqlLine.startsWith("--")) {
                            continue;
                        }
                        // 执行sql
                        if (StrUtil.isNotBlank(sqlLine)) {
                            log.info("执行SQL：{}", sqlLine);
                            stmt.executeUpdate(sqlLine);
                        }
                    }

                    // 关闭连接
                    stmt.close();
//                    DataSourceProperty dataSourceProperty = entry.getValue();
//                    dataSourceProperty.getHikari().setConnectionTestQuery("SELECT 1");
//                    dataSourceProperty.getHikari().setValidationTimeout(5000L);
                    dynamicRoutingDataSource.addDataSource(entry.getKey(), dataSourceCreator.createDataSource(entry.getValue()));
                } catch (Exception e) {
                    throw e;
                }
            }
        }
        connection.close();


        DynamicRoutingDataSource dataSource = new DynamicRoutingDataSource();
        dataSource.setPrimary(properties.getPrimary());
        dataSource.setStrict(properties.getStrict());
        dataSource.setStrategy(properties.getStrategy());
        dataSource.setP6spy(properties.getP6spy());
        dataSource.setSeata(properties.getSeata());
        // 创建代理后的数据源Map
        properties.getDatasource().forEach((k, v) -> {
            // 数据库为mysql0数据库，帮我优化Druid配置,应对高并发的情况，并关闭数据统计监控
            DruidDataSource druidDataSource = new DruidDataSource();
            druidDataSource.setUrl(v.getUrl());
            druidDataSource.setUsername(v.getUsername());
            druidDataSource.setPassword(v.getPassword());
            druidDataSource.setDriverClassName(v.getDriverClassName());
            // 优化Druid 数据源
            druidDataSource.setInitialSize(8);
            // 最小空闲连接数
            druidDataSource.setMinIdle(4);
            // 最大连接数
            druidDataSource.setMaxActive(20);
            // 获取连接等待超时的时间
            druidDataSource.setMaxWait(60000);
            // 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            druidDataSource.setTimeBetweenEvictionRunsMillis(60000);
            // 配置一个连接在池中最小生存的时间，单位是毫秒
            druidDataSource.setMinEvictableIdleTimeMillis(600000);
            // 配置间隔多久才进行一次检测，检测连接是否 Alive，单位是毫秒
            druidDataSource.setMaxEvictableIdleTimeMillis(1800000);
            // 验证连接有效性的SQL
            druidDataSource.setValidationQuery("SELECT 1");
            // 建议配置为true，不影响性能，并且保证安全性。申请连接的时候检测，
            // 如果空闲时间大于timeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效
            druidDataSource.setTestWhileIdle(true);
            // 申请连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
            druidDataSource.setTestOnBorrow(false);
            // 归还连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
            druidDataSource.setTestOnReturn(false);
            // 是否开启
            druidDataSource.setEnable(true);

            dataSource.addDataSource(k, new DataSourceProxy(druidDataSource));
        });

        return new DataSourceProxy(dataSource);
    }
}
