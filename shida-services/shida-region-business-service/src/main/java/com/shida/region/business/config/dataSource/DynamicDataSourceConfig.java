package com.shida.region.business.config.dataSource;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.support.http.StatViewServlet;
import com.alibaba.druid.support.http.WebStatFilter;
import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DataSourceProperty;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.shida.config.AppConfigure;
import io.seata.rm.datasource.DataSourceProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.ConfigurationPropertiesBinding;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.annotation.Order;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.sql.*;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Order(-2147483647)
@Configuration
public class DynamicDataSourceConfig {
    @Resource
    private DynamicDataSourceProperties dynamicDataSourceProperties;

    @Resource
    private DefaultDataSourceCreator dataSourceCreator;

    @Value("${spring.profiles.active:dev}")
    private String active;

    @Bean
    @Primary
    public DataSource dataSource() throws Exception {
        DynamicDataSourceProperties properties = dynamicDataSourceProperties;
        Map<String, DataSourceProperty> dataSources = properties.getDatasource();
        DynamicRoutingDataSource dynamicRoutingDataSource = new DynamicRoutingDataSource();
        Connection connection = null;
        for (Map.Entry<String, DataSourceProperty> entry : dataSources.entrySet()) {
            if (entry.getKey().equals("master")) {
                dynamicRoutingDataSource.addDataSource(entry.getKey(), dataSourceCreator.createDataSource(entry.getValue()));
                connection = DriverManager.getConnection(entry.getValue().getUrl(), entry.getValue().getUsername(), entry.getValue().getPassword());
            }
        }
        if (connection == null) {
            throw new RuntimeException("未获取到主数据库（master）链接，请先配置主数据库链接");
        }

        for (Map.Entry<String, DataSourceProperty> entry : dataSources.entrySet()) {
            if (!entry.getKey().equals("master")) {
                try {
                    // 创建Statement对象来执行SQL语 句
                    Statement stmt = connection.createStatement();
                    String regex = "/([^/?]+)\\?";
                    java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(regex);
                    java.util.regex.Matcher matcher = pattern.matcher(entry.getValue().getUrl());
                    String databaseName = null;
                    if (matcher.find()) {
                        databaseName = matcher.group(1);
                    }
                    if (StrUtil.isBlank(databaseName)) {
                        throw new RuntimeException(StrUtil.format("未获取到数据源{}的数据库名称，请检查数据库链接URL是否正确", entry.getKey()));
                    }
                    String sql = StrUtil.format("CREATE DATABASE IF NOT EXISTS `{}` CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;", databaseName);
                    stmt.executeUpdate(sql);
                    sql = StrUtil.format("USE `{}`;", databaseName);
                    stmt.executeUpdate(sql);
                    String initSql = ResourceUtil.readStr("classpath:/sql/init_database.sql", CharsetUtil.CHARSET_UTF_8);
                    initSql = initSql.replaceAll("Dynamic;", "DYNAMIC;");
                    String[] tableSqls = initSql.split("DYNAMIC;");
                    for (String ts : tableSqls) {
                        if (StrUtil.isNotBlank(ts.trim())) {
                            String tableSql = "";
                            try {
                                tableSql = StrUtil.removeAllLineBreaks(ts);
                                tableSql = tableSql.replaceAll("utf8mb4_0900_ai_ci", "utf8mb4_general_ci");
                                if (!tableSql.startsWith("CREATE TABLE IF NOT EXISTS ")) {
                                    tableSql = tableSql.replaceAll("CREATE TABLE ", "CREATE TABLE IF NOT EXISTS ");
                                }
                                tableSql = tableSql.trim() + "DYNAMIC;";
                                log.info("执行SQL：{}", tableSql);
                                stmt.executeUpdate(tableSql);
                            } catch (Exception e) {
                                log.error("初始化数据库表失败，错误信息：{}", tableSql);
                                throw e;
                            }
                        }
                    }

                    String updateSql = ResourceUtil.readStr("classpath:/sql/update_database.sql", CharsetUtil.CHARSET_UTF_8);
                    // 按行分割
                    String[] lines = updateSql.split("\n");
                    for (String sqlLine : lines) {
                        if (sqlLine.startsWith("--")) {
                            continue;
                        }
                        // 执行sql
                        if (StrUtil.isNotBlank(sqlLine)) {
                            log.info("执行SQL：{}", sqlLine);
                            stmt.executeUpdate(sqlLine);
                        }
                    }

                    // 关闭连接
                    stmt.close();
//                    DataSourceProperty dataSourceProperty = entry.getValue();
//                    dataSourceProperty.getHikari().setConnectionTestQuery("SELECT 1");
//                    dataSourceProperty.getHikari().setValidationTimeout(5000L);
                    dynamicRoutingDataSource.addDataSource(entry.getKey(), dataSourceCreator.createDataSource(entry.getValue()));
                } catch (Exception e) {
                    throw e;
                }
            }
        }
        connection.close();


        DynamicRoutingDataSource dataSource = new DynamicRoutingDataSource();
        dataSource.setPrimary(properties.getPrimary());
        dataSource.setStrict(properties.getStrict());
        dataSource.setStrategy(properties.getStrategy());
        dataSource.setP6spy(properties.getP6spy());
        dataSource.setSeata(properties.getSeata());
        // 创建代理后的数据源Map
        properties.getDatasource().forEach((k, v) -> {
            // 数据库为mysql0数据库，帮我优化Druid配置,应对高并发的情况，并关闭数据统计监控
            DruidDataSource druidDataSource = new DruidDataSource();
            druidDataSource.setUrl(v.getUrl());
            druidDataSource.setUsername(v.getUsername());
            druidDataSource.setPassword(v.getPassword());
            druidDataSource.setDriverClassName(v.getDriverClassName());
            if (active.startsWith("dev")) {
                // 优化Druid 数据源
                druidDataSource.setInitialSize(2);
                // 最小空闲连接数
                druidDataSource.setMinIdle(2);
                // 最大连接数
                druidDataSource.setMaxActive(20);
            } else {
                // 优化Druid 数据源
                druidDataSource.setInitialSize(8);
                // 最小空闲连接数
                druidDataSource.setMinIdle(8);
                // 最大连接数
                druidDataSource.setMaxActive(200);
            }
            // 获取连接时最大等待时间，单位毫秒。配置了max-wait之后，
            // 缺省启用公平锁，并发效率会有所下降，但可以有效避免获取连接超时。如果需要可以通过配置useUnfairLock属性为true使用非公平锁。
            druidDataSource.setMaxWait(60000);
            // 连接有效性检测配置 (防止使用已失效的连接，非常重要),
            // 申请连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能，但保证了安全性。通常可以设置为false，依靠空闲检测来保证
            druidDataSource.setTestOnBorrow(false);
            // 归还连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
            druidDataSource.setTestOnReturn(false);
            // 建议设置为true，不影响性能，并且保证安全性。
            // 申请连接的时候，如果空闲时间大于timeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效
            druidDataSource.setTestWhileIdle(true);
            // 验证连接有效性的SQL
            druidDataSource.setValidationQuery("SELECT 1");
            // # 有两个含义：
            // 1) Destroy线程会检测连接的间隔时间
            // 2) testWhileIdle的判断依据，详细看testWhileIdle属性的说明。单位毫秒。
            druidDataSource.setTimeBetweenEvictionRunsMillis(60000);
            // 一个连接在池中最小生存的时间，单位是毫秒。连接保持空闲而不被驱逐的最小时间。
            druidDataSource.setMinEvictableIdleTimeMillis(300000);
            // 配置间隔多久才进行一次检测，检测连接是否 Alive，单位是毫秒
            druidDataSource.setMaxEvictableIdleTimeMillis(300000);
            // 是否移除泄露的连接/超过时间限制的连接
            druidDataSource.setRemoveAbandoned(true);
            // 泄露连接可以被移除的超时时间，单位秒。值应大于业务中最长运行时间。
            druidDataSource.setRemoveAbandonedTimeout(180);
            // 移除泄露连接时是否输出日志，可以看到泄露连接的堆栈信息，有助于调试。
            druidDataSource.setLogAbandoned(true);
            // 是否开启
            druidDataSource.setEnable(true);

            dataSource.addDataSource(k, new DataSourceProxy(druidDataSource));
        });

        return new DataSourceProxy(dataSource);
    }

    /**
     * 配置Druid监控界面的Servlet
     * 访问地址：http://localhost:你的端口号/druid/index.html
     */
    @Bean
    @ConditionalOnProperty(name = "spring.profiles.active", havingValue = "dev")
    public ServletRegistrationBean<StatViewServlet> statViewServlet() {
        ServletRegistrationBean<StatViewServlet> registrationBean =
            new ServletRegistrationBean<>(new StatViewServlet(), "/druid/*");

        // 设置初始化参数
        Map<String, String> initParams = new HashMap<>();

        // 监控页面登录用户名
        initParams.put("loginUsername", "shida_admin");
        // 监控页面登录密码
        initParams.put("loginPassword", "shida@2025");
        // 是否允许重置数据（生产环境建议设置为false）
        initParams.put("resetEnable", "false");
        // 允许访问的IP地址（为空表示所有IP都可以访问）
        initParams.put("allow", "");
        // 拒绝访问的IP地址
        // initParams.put("deny", "*************");

        registrationBean.setInitParameters(initParams);
        return registrationBean;
    }

    /**
     * 配置Web监控的Filter
     * 用于采集web-jdbc关联监控的数据
     */
    @Bean
    @ConditionalOnProperty(name = "spring.profiles.active", havingValue = "dev")
    public FilterRegistrationBean<WebStatFilter> webStatFilter() {
        FilterRegistrationBean<WebStatFilter> registrationBean =
            new FilterRegistrationBean<>(new WebStatFilter());

        // 设置初始化参数
        Map<String, String> initParams = new HashMap<>();
        // 排除一些不必要的url，比如静态资源、druid监控页面本身
        initParams.put("exclusions", "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*");
        // 是否开启session统计
        initParams.put("sessionStatEnable", "true");
        // 配置profileEnable能够监控单个url调用的sql列表
        initParams.put("profileEnable", "true");

        registrationBean.setInitParameters(initParams);
        // 添加过滤规则，所有请求都进行监控
        registrationBean.addUrlPatterns("/*");

        return registrationBean;
    }
}
