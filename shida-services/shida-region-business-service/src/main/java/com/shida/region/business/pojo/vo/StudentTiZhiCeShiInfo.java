package com.shida.region.business.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 学生体质健康标准测试信息VO
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
@Accessors(chain = true)
@Schema(name = "StudentTiZhiCeShiInfo", description = "学生体质健康标准测试信息")
public class StudentTiZhiCeShiInfo implements Serializable {

    @Schema(description = "学生ID")
    private Long studentId;

    @Schema(description = "学生姓名")
    private String studentName;

    @Schema(description = "学籍号")
    private String xjh;

    @Schema(description = "性别")
    private Integer xb;

    @Schema(description = "班级ID")
    private Long clazzId;

    @Schema(description = "班级名称")
    private String clazzName;

    @Schema(description = "学校ID")
    private Long schoolId;

    @Schema(description = "学校名称")
    private String schoolName;

    @Schema(description = "区域名称")
    private String regionName;

    @Schema(description = "学期ID")
    private Long termId;

    @Schema(description = "学期名称")
    private String termName;

    @Schema(description = "是否伤病")
    private Boolean sfsb;

    @Schema(description = "是否残疾")
    private Boolean sfcj;

    @Schema(description = "身高(cm)")
    private BigDecimal sg;

    @Schema(description = "体重(kg)")
    private BigDecimal tz;

    @Schema(description = "体重指数BMI")
    private BigDecimal bmi;

    @Schema(description = "BMI成绩")
    private String bmiScore;

    @Schema(description = "BMI分数")
    private BigDecimal bmiPoints;

    @Schema(description = "体育项目测试结果")
    private Map<String, ProjectTestResult> projectResults;

    @Schema(description = "总分")
    private BigDecimal totalScore;

    @Schema(description = "等级")
    private String grade;

    /**
     * 项目测试结果内部类
     */
    @Data
    @Accessors(chain = true)
    @Schema(name = "ProjectTestResult", description = "项目测试结果")
    public static class ProjectTestResult {
        @Schema(description = "项目代码")
        private String projectCode;

        @Schema(description = "项目名称")
        private String projectName;

        @Schema(description = "测试成绩")
        private String result;

        @Schema(description = "得分")
        private BigDecimal score;

        @Schema(description = "等级")
        private String grade;
    }
}
