package com.shida.region.business.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shida.AccountCommonService;
import com.shida.constant.RoleConstant;
import com.shida.enums.RestCode;
import com.shida.excel.ExcelBatchImportResponse;
import com.shida.excel.ExcelHelper;
import com.shida.exceptions.ServiceException;
import com.shida.pojo.vo.AccountInfo;
import com.shida.pojo.vo.PageInfo;
import com.shida.pojo.vo.RoleInfo;
import com.shida.region.business.entity.Clazz;
import com.shida.region.business.entity.JieHeBingRecord;
import com.shida.region.business.entity.School;
import com.shida.region.business.mapper.JieHeBingRecordMapper;
import com.shida.region.business.mapper.SchoolMapper;
import com.shida.region.business.mapper.StudentMapper;
import com.shida.region.business.pojo.excel.StudentJieHeBingExportExcel;
import com.shida.region.business.pojo.excel.StudentJieHeBingImportExcel;
import com.shida.region.business.pojo.excel.StudentKouQiangImportExcel;
import com.shida.region.business.pojo.search.HealthSearch;
import com.shida.region.business.pojo.search.StudentSearch;
import com.shida.region.business.pojo.vo.StudentInfo;
import com.shida.region.business.pojo.vo.StudentJieHeBingInfo;
import com.shida.region.business.service.IClazzService;
import com.shida.region.business.service.IJieHeBingRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shida.region.business.service.ISchoolService;
import com.shida.region.business.tenant.TenantContextHolder;
import com.shida.userCenter.api.IFeignGradeService;
import com.shida.userCenter.api.IFeignTermService;
import com.shida.userCenter.api.IFeignUserService;
import com.shida.userCenter.dto.GradeDto;
import com.shida.userCenter.dto.OrgDto;
import com.shida.userCenter.dto.TermDto;
import com.shida.userCenter.dto.UserDto;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <p>
 * 学生结核病检查表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-04
 */
@Service
public class JieHeBingRecordServiceImpl extends ServiceImpl<JieHeBingRecordMapper, JieHeBingRecord> implements IJieHeBingRecordService {
    @Resource
    private IFeignUserService feignUserService;
    @Resource
    private IFeignGradeService gradeService;
    @Resource
    private IClazzService clazzService;
    @Resource
    private IFeignTermService feignTermService;
    @Resource
    private ISchoolService schoolService;
    @Resource
    private StudentMapper studentMapper;
    @Resource
    private SchoolMapper schoolMapper;
    @Resource
    private TenantContextHolder tenantContextHolder;

    @Override
    public PageInfo<StudentJieHeBingInfo> getStudentJieHeBingPageData(HealthSearch search) {
        IPage<StudentJieHeBingInfo> page = new Page<>(search.getPageNumber(), search.getPageSize());
        page = baseMapper.getPageData(page, search);
        List<Long> userIds = page.getRecords().stream().map(StudentJieHeBingInfo::getUserId).collect(Collectors.toList());
        Set<Long> gradeIds = page.getRecords().stream().map(StudentJieHeBingInfo::getGradeId).collect(Collectors.toSet());
        Set<Long> termIds = page.getRecords().stream().map(StudentJieHeBingInfo::getTermId).collect(Collectors.toSet());
        List<GradeDto> gradeList = CollUtil.isNotEmpty(gradeIds) ? gradeService.getGradeList(ListUtil.toList(gradeIds)) : new ArrayList<>();
        List<TermDto> termList = CollUtil.isNotEmpty(gradeIds) ? feignTermService.getTermList(ListUtil.toList(termIds)) : new ArrayList<>();
        List<UserDto> userList = CollUtil.isNotEmpty(userIds) ? feignUserService.getUserDtoList(userIds) : new ArrayList<>();

        page.getRecords().forEach(studentJhb -> {
            UserDto userDto = userList.stream().filter(user -> user.getId().equals(studentJhb.getUserId())).findFirst().orElse(null);
            if (ObjectUtil.isNotEmpty(userDto)) {
                studentJhb.setRegionName(userDto.getRegionName());
                studentJhb.setRegionId(userDto.getRegionId());
                GradeDto gradeDto = gradeList.stream().filter(grade -> grade.getId().equals(studentJhb.getGradeId())).findFirst().orElse(null);
                TermDto termDto = termList.stream().filter(term -> term.getId().equals(studentJhb.getTermId())).findFirst().orElse(null);
                if (ObjectUtil.isNotEmpty(gradeDto)) {
                    studentJhb.setGradeName(gradeDto.getName());
                }
                if (ObjectUtil.isNotEmpty(termDto)) {
                    studentJhb.setTermName(termDto.getTermName());
                }
            }
        });
        PageInfo<StudentJieHeBingInfo> result = PageInfo.fromMybatisPage(page, StudentJieHeBingInfo.class);
        return result;
    }

    @Override
    public List<ExcelBatchImportResponse> importStudentJieHeBing(MultipartFile file, Long regoinId) {
        AccountInfo account = AccountCommonService.getCurrentUser();
        List<RoleInfo> roles = account.getRoles();
        if (roles.stream().anyMatch(roleInfo -> roleInfo.getRoleCode().equals(RoleConstant.CITY_ADMIN_CODE))) {//市级管理员不校验区县
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "市级管理员不能导入学生结核病检查");//因为不同区县的学生要导入不同的区县库
        }
        List<ExcelBatchImportResponse> result = new ArrayList<>();
        List<StudentJieHeBingImportExcel> excelList = new ArrayList<>();
        try {
            excelList = ExcelHelper.readData(file.getInputStream(), StudentJieHeBingImportExcel.class, 0);
        } catch (Exception e) {
            log.error("读取学生结核病检查Excel发生错误，请稍后重试", e);
            result.add(new ExcelBatchImportResponse().setMessage("读取学生结核病检查Excel发生错误，请稍后重试").setRowIndex(1));
        }
        if (CollUtil.isEmpty(excelList)) {
            return result;
        }

        List<String> xjhList = excelList.stream().map(StudentJieHeBingImportExcel::getXjh).distinct().collect(Collectors.toList());
        List<StudentInfo> studentInfoList = studentMapper.getStudentAllList(new StudentSearch(),xjhList);
        List<School> schoolList = schoolMapper.selectList(new LambdaQueryWrapper<School>().eq(School::getRegionId, regoinId));
        List<TermDto> termList = feignTermService.getAllTermList();

        List<JieHeBingRecord> jieHeBingRecordList = new ArrayList<JieHeBingRecord>();
        for (int i = 0; i < excelList.size(); i++) {
            JieHeBingRecord jieHeBingRecord = new JieHeBingRecord();
            StudentJieHeBingImportExcel studentJieHeBingImportExcel = excelList.get(i);
            List<ExcelBatchImportResponse> validateData = studentJieHeBingImportExcel.validateData(i + 1);
            if (CollUtil.isNotEmpty(validateData)) {
                result.addAll(validateData);
                return result;
            }
            // 判定Excel学籍号有无重复
            if (excelList.stream().filter(record -> record.getXjh().equals(studentJieHeBingImportExcel.getXjh())).count() > 1) {
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中学籍号【" + studentJieHeBingImportExcel.getXjh() + "】存在重复数据").setRowIndex(i + 1));
            }
            if (schoolList.stream().noneMatch(schoolDto -> schoolDto.getName().equals(studentJieHeBingImportExcel.getSchoolName()))) {
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中所属学校【" + studentJieHeBingImportExcel.getSchoolName() + "】不存在").setRowIndex(i + 1));
            }
            if (termList.stream().noneMatch(termDto -> termDto.getTermName().equals(studentJieHeBingImportExcel.getTermName()))) {
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中学期【" + studentJieHeBingImportExcel.getTermName() + "】不存在").setRowIndex(i + 1));
            } else {
                TermDto termInfo = termList.stream().filter(term -> term.getTermName().equals(studentJieHeBingImportExcel.getTermName())).findFirst().orElse(null);
                if (termInfo.getStatus().equals(2)) {
                    result.add(new ExcelBatchImportResponse().setMessage("Excel文件中学期【" + studentJieHeBingImportExcel.getTermName() + "】已归档").setRowIndex(i + 1));
                }
                jieHeBingRecord.setTermId(termInfo.getId());
            }
            if (studentInfoList.stream().noneMatch(student -> student.getXjh().equals(studentJieHeBingImportExcel.getXjh()))) {
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中学籍号【" + studentJieHeBingImportExcel.getXjh() + "】不存在").setRowIndex(i + 1));
            } else {
                StudentInfo studentInfo = studentInfoList.stream().filter(student -> student.getXjh().equals(studentJieHeBingImportExcel.getXjh())).findFirst().orElse(null);
                if (!studentInfo.getXm().equals(studentJieHeBingImportExcel.getStudentName())) {
                    result.add(new ExcelBatchImportResponse().setMessage("Excel文件中姓名【" + studentJieHeBingImportExcel.getStudentName() + "】与数据库中不一致").setRowIndex(i + 1));
                }
                if (!studentInfo.getClazzName().equals(studentJieHeBingImportExcel.getClassName())) {
                    result.add(new ExcelBatchImportResponse().setMessage("Excel文件中班级【" + studentJieHeBingImportExcel.getClassName() + "】与数据库中不一致").setRowIndex(i + 1));
                }
                if (!studentInfo.getEnrollYear().toString().equals(studentJieHeBingImportExcel.getEnrolYear())) {
                    result.add(new ExcelBatchImportResponse().setMessage("Excel文件中入学年份【" + studentJieHeBingImportExcel.getEnrolYear() + "】与数据库中不一致").setRowIndex(i + 1));
                }
                if (!studentInfo.getSchoolName().equals(studentJieHeBingImportExcel.getSchoolName())) {
                    result.add(new ExcelBatchImportResponse().setMessage("Excel文件中所属学校【" + studentJieHeBingImportExcel.getSchoolName() + "】与数据库中不一致").setRowIndex(i + 1));
                }
                jieHeBingRecord.setStudentId(studentInfo.getId());
                jieHeBingRecord.setSchoolId(studentInfo.getSchoolId());
                jieHeBingRecord.setClazzId(studentInfo.getClazzId());
            }
            if (!studentJieHeBingImportExcel.getSfjc().equals("是") && !studentJieHeBingImportExcel.getSfjc().equals("否")) {
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中是否检查【" + studentJieHeBingImportExcel.getSfjc() + "】输入不正确").setRowIndex(i + 1));
            } else {
                jieHeBingRecord.setSfjc(studentJieHeBingImportExcel.getSfjc().equals("是"));
            }
            if (!studentJieHeBingImportExcel.getSffb().equals("是") && !studentJieHeBingImportExcel.getSffb().equals("否")) {
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中是否发病【" + studentJieHeBingImportExcel.getSffb() + "】输入不正确").setRowIndex(i + 1));
            } else {
                jieHeBingRecord.setSffb(studentJieHeBingImportExcel.getSffb().equals("是"));
            }
            jieHeBingRecord.setCreateTime(LocalDateTime.now());
            jieHeBingRecordList.add(jieHeBingRecord);
        }
        if (CollUtil.isNotEmpty(result)) {
            return result;
        }

        if (CollUtil.isEmpty(result) && CollUtil.isNotEmpty(jieHeBingRecordList)) {
            //每批1000条
            int count = 1000;
            //分批数量
            int runSizes = (int) Math.ceil(jieHeBingRecordList.size() / (float) count);
            //循环分批批量插入
            for (int j = 0; j < runSizes; j++) {
                List<JieHeBingRecord> newJieHeBingRecordRecordlist = null;
                if ((j + 1) == runSizes) {
                    int startIndex = (j * count);
                    int endIndex = jieHeBingRecordList.size();
                    newJieHeBingRecordRecordlist = jieHeBingRecordList.subList(startIndex, endIndex);
                } else {
                    int startIndex = (j * count);
                    int endIndex = (j + 1) * count;
                    newJieHeBingRecordRecordlist = jieHeBingRecordList.subList(startIndex, endIndex);
                }
                super.saveBatch(newJieHeBingRecordRecordlist);
            }

        }
        return result;
    }

    @Override
    public void exportStudentJieHeBing(HealthSearch search, HttpServletResponse response) {
        OrgDto region = tenantContextHolder.getTenant();
        search.setPageNumber(1).setPageSize(1000);
        PageInfo<StudentJieHeBingInfo> pageInfo;
        ExcelHelper<StudentJieHeBingExportExcel> excelHelper = ExcelHelper.create(StudentJieHeBingExportExcel.class);
        AtomicInteger index = new AtomicInteger(1);
        do {
            pageInfo = this.getStudentJieHeBingPageData(search);
            List<StudentJieHeBingExportExcel> excelList;
            List<Clazz> clazzList = clazzService.list(new LambdaQueryWrapper<Clazz>().in(Clazz::getId, pageInfo.getRecords().stream().map(StudentJieHeBingInfo::getClazzId).distinct().collect(Collectors.toList())));
            List<School> schoolList = schoolService.list(new LambdaQueryWrapper<School>().in(School::getId, pageInfo.getRecords().stream().map(StudentJieHeBingInfo::getSchoolId).distinct().collect(Collectors.toList())));
            List<TermDto> termDtoList = feignTermService.getTermList(pageInfo.getRecords().stream().map(StudentJieHeBingInfo::getTermId).distinct().collect(Collectors.toList()));

            excelList = pageInfo.getRecords().stream().map(studentJieHeBingInfo -> {
                StudentJieHeBingExportExcel studentJieHeBingExportExcel = new StudentJieHeBingExportExcel();
                studentJieHeBingExportExcel.setRowNum(index.getAndIncrement());
                studentJieHeBingExportExcel.setStudentName(studentJieHeBingInfo.getXm());
                studentJieHeBingExportExcel.setStudentNumber(studentJieHeBingInfo.getXjh());
                if (ObjectUtil.isNotNull(studentJieHeBingInfo.getXb())) {
                    studentJieHeBingExportExcel.setGenderStr(studentJieHeBingInfo.getXb() == 1 ? "男" : "女");
                }
                studentJieHeBingExportExcel.setClazzName(clazzList.stream()
                    .filter(clazz -> clazz.getId().equals(studentJieHeBingInfo.getClazzId()))
                    .findFirst()
                    .map(Clazz::getName)
                    .orElse(""));
                studentJieHeBingExportExcel.setSchoolName(schoolList.stream().
                    filter(school -> school.getId().equals(studentJieHeBingInfo.getSchoolId()))
                    .findFirst()
                    .map(School::getName)
                    .orElse(""));
                studentJieHeBingExportExcel.setRegionName(region.getName());
                studentJieHeBingExportExcel.setTermName(termDtoList.stream()
                    .filter(termDto -> termDto.getId().equals(studentJieHeBingInfo.getTermId()))
                    .findFirst()
                    .map(TermDto::getTermName)
                    .orElse(""));
                if (ObjectUtil.isNotNull(studentJieHeBingInfo.getSfjc())) {
                    studentJieHeBingExportExcel.setSfjc(studentJieHeBingInfo.getSfjc() ? "是" : "否");
                }
                if (ObjectUtil.isNotNull(studentJieHeBingInfo.getSffb())) {
                    studentJieHeBingExportExcel.setSffb(studentJieHeBingInfo.getSffb() ? "是" : "否");
                }
                return studentJieHeBingExportExcel;
            }).collect(Collectors.toList());
            excelHelper.writeData(excelList);
            search.setPageNumber(search.getPageNumber() + 1);
        } while (pageInfo.hasNext());
        excelHelper.writeToWebResponse(response, region.getName() + "-学生结核病检查记录表");
    }
}
