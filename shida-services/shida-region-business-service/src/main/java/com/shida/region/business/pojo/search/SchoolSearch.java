package com.shida.region.business.pojo.search;

import com.shida.pojo.search.Search;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(name = "SchoolSearch", description = "学校搜索条件")
public class SchoolSearch  extends Search {
    @Schema(description = "区县id")
    private Long regionId;

    @Schema(description = "学校类型,1公办，2民办")
    private Integer schoolType;

    @Schema(description = "学段，1小学，2初中，3高中，4九年一贯制，5十二年一贯制，6初高连读")
    private Integer schoolPeriod;

    @Schema(description = "学校id")
    private Long schoolId;
}
