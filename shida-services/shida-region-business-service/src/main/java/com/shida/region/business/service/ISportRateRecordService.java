package com.shida.region.business.service;

import com.shida.excel.ExcelBatchImportResponse;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.business.entity.SportRateRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.shida.region.business.pojo.search.SportRateSearch;
import com.shida.region.business.pojo.vo.SportRateInfo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 体育课出勤率 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
public interface ISportRateRecordService extends IService<SportRateRecord> {

    PageInfo<SportRateInfo> getPageData(@Valid SportRateSearch search);

    List<ExcelBatchImportResponse> batchImport(MultipartFile file, Long termId);

    void downloadBatchImportTemplate(HttpServletResponse response, String regionId, Long termId);

    boolean deleteData(Long studentId, Long termId);

    void exportSportRate(SportRateSearch search, HttpServletResponse response);
}
