package com.shida.region.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shida.AccountCommonService;
import com.shida.constant.RoleConstant;
import com.shida.dto.SchoolDto;
import com.shida.enums.RestCode;
import com.shida.excel.ExcelBatchImportResponse;
import com.shida.excel.ExcelHelper;
import com.shida.exceptions.ServiceException;
import com.shida.pojo.vo.AccountInfo;
import com.shida.pojo.vo.KeyValue;
import com.shida.pojo.vo.PageInfo;
import com.shida.pojo.vo.RoleInfo;
import com.shida.region.business.entity.School;
import com.shida.region.business.entity.Student;
import com.shida.region.business.mapper.StudentMapper;
import com.shida.region.business.pojo.excel.StudentExportExcel;
import com.shida.region.business.pojo.excel.StudentImportExcel;
import com.shida.region.business.pojo.form.StudentForm;
import com.shida.region.business.pojo.search.StudentSearch;
import com.shida.region.business.pojo.vo.ClazzInfo;
import com.shida.region.business.pojo.vo.StudentInfo;
import com.shida.region.business.service.IClazzService;
import com.shida.region.business.service.IDeleteRecordService;
import com.shida.region.business.service.IStudentService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shida.region.business.tenant.TenantContextHolder;
import com.shida.userCenter.api.IFeignGradeService;
import com.shida.userCenter.api.IFeignUserService;
import com.shida.userCenter.api.IFeignUserStudentService;
import com.shida.userCenter.dto.GradeDto;
import com.shida.userCenter.dto.OrgDto;
import com.shida.userCenter.dto.UserDto;
import com.shida.region.business.utils.StudentNameUtil;
import com.shida.userCenter.dto.UserStudentDto;
import io.seata.spring.annotation.GlobalTransactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import com.shida.constant.SchoolPeriodConstant;
/**
 * <p>
 * 学生信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
@Service
public class StudentServiceImpl extends ServiceImpl<StudentMapper, Student> implements IStudentService {

    @Resource
    private IFeignUserService feignUserService;
    @Resource
    private TenantContextHolder tenantContextHolder;
    @Resource
    private StudentNameUtil studentNameUtil;
    @Resource
    private SchoolServiceImpl schoolService;
    @Resource
    private IFeignGradeService gradeService;
    //学籍号前缀
    private final String XS = "XS";
    @Autowired
    private IClazzService clazzService;
    @Resource
    private IDeleteRecordService deleteRecordService;
    @Resource
    private IFeignUserStudentService feignUserStudentService;

    @Override
    public PageInfo<StudentInfo> getPageData(StudentSearch search) {
        IPage<StudentInfo> page = new Page<>(search.getPageNumber(), search.getPageSize());
        page = baseMapper.getPageData(page, search);
        List<Long> userIds = page.getRecords().stream().map(StudentInfo::getUserId).collect(Collectors.toList());
        Set<Long> gradeIds = page.getRecords().stream().map(StudentInfo::getGradeId).collect(Collectors.toSet());
        List<GradeDto> gradeList = CollUtil.isNotEmpty(gradeIds) ? gradeService.getGradeList(ListUtil.toList(gradeIds)) : new ArrayList<>();
        List<UserDto> userList = CollUtil.isNotEmpty(userIds) ? feignUserService.getUserDtoList(userIds) : new ArrayList<>();
        page.getRecords().forEach(student -> {
            UserDto userDto = userList.stream().filter(user -> user.getId().equals(student.getUserId())).findFirst().orElse(null);
            if(ObjectUtil.isNotEmpty(userDto)){
                student.setDefaultPassword(userDto.getDefaultPassword());
                student.setUserName(userDto.getUsername());
                student.setSjhm(userDto.getMobile());
                String roleName = userDto.getRoles().stream().map(RoleInfo::getRoleName).collect(Collectors.joining(","));
                student.setRoleName(roleName);
                student.setRoleList(userDto.getRoles());
                student.setRegionName(userDto.getRegionName());
                student.setRegionId(userDto.getRegionId());
                student.setStatus(userDto.getStatus());
                student.setDefaultPasswordFlag(userDto.getDefaultPasswordFlag());
                GradeDto gradeDto = gradeList.stream().filter(grade -> grade.getId().equals(student.getGradeId())).findFirst().orElse(null);
                if(ObjectUtil.isNotEmpty(gradeDto)){
                    student.setGradeName(gradeDto.getName());
                }
            }
        });
        PageInfo<StudentInfo> result = PageInfo.fromMybatisPage(page, StudentInfo.class);
        return result;
    }

    @Override
    public List<StudentInfo> getList(List<Long> studentIds) {
        return baseMapper.getStudentList(studentIds);
    }

    //新增学生 产品确定这个功能只有学校管理员及一下级别可以用 所以schoolId regionId 理论都取accout里面的值就可以
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Integer saveStudent(StudentForm student) {
        Boolean has = feignUserStudentService.checkHasByCertNumber(student.getSfzh());
        if (has) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "身份证号已存在");
        }
        if (!IdcardUtil.isValidCard(student.getSfzh())) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "身份证号格式错误");
        }
        if(!StrUtil.startWithAny(student.getXjh(),"G","L","J")){
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "学籍号格式错误");
        }
        if(StrUtil.startWithAny(student.getXjh(),"G") && !StrUtil.contains(student.getXjh(),student.getSfzh())){
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "学籍号与身份证号不匹配");
        }
        AccountInfo account = AccountCommonService.getCurrentUser();
        Long schoolId = null;
        Long regionId = null;
        if (account.getCityAdminFlag() || account.getCountyAdminFlag()) {
            schoolId = student.getSchoolId();
            regionId = student.getRegionId();
        }else {
            schoolId = account.getSchoolId();
            regionId = account.getRegionId();
        }
        if (ObjectUtil.isNull(schoolId)) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "学校ID不存在");
        }
        Student studentEntity = new Student();
        BeanUtil.copyProperties(student, studentEntity);
        studentEntity.setSchoolId(schoolId);
        studentEntity.setStatus(1);
        List<String> studentNameList = studentNameUtil.getStudentName(XS, 1);
        if(CollUtil.isEmpty(studentNameList)){
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "生成学生账号失败");
        }

        UserDto userDto = getUserDto(student,schoolId,regionId);
        userDto.setUsername(studentNameList.get(0));
        UserDto user = feignUserService.createUser(userDto);
        if(user.getId() == null){
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "创建用户失败");
        }
        UserStudentDto userStudentDto = new UserStudentDto().setUserId(user.getId()).setCertNumber(student.getSfzh()).setRegionId(regionId).setSchoolId(schoolId);
        feignUserStudentService.addUserStudent(userStudentDto);
        studentEntity.setUserId(user.getId());
        return baseMapper.insert(studentEntity);
    }

    private static UserDto getUserDto(StudentForm student,Long schoolId,Long regionId) {
        UserDto userDto = new UserDto();
        userDto.setSchoolId(schoolId);
        userDto.setMobile(student.getSjhm());
        userDto.setDefaultPasswordFlag(true);
        userDto.setAdminFlag(false);
        userDto.setStatus(1);
        userDto.setNickname(student.getXm());
        userDto.setRegionId(regionId);
        ArrayList<RoleInfo> roleInfos = new ArrayList<>();
        roleInfos.add(new RoleInfo().setRoleCode(RoleConstant.STUDENT_CODE));
        userDto.setRoles(roleInfos);
        return userDto;
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Integer updateStudent(StudentForm form) {
        if(ObjectUtil.isEmpty(form.getId())){
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "学生ID不能为空");
        }
        if (!IdcardUtil.isValidCard(form.getSfzh())) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "身份证号格式错误");
        }
        if(!StrUtil.startWithAny(form.getXjh(),"G","L","J")){
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "学籍号格式错误");
        }
        if(StrUtil.startWithAny(form.getXjh(),"G") && !StrUtil.contains(form.getXjh(),form.getSfzh())){
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "学籍号与身份证号不匹配");
        }
        StudentInfo studentEntity = baseMapper.getDetailById(form.getId());
        Boolean has = false;
        String oldSfzh = studentEntity.getSfzh();
        if(!form.getSfzh().equals(oldSfzh)){
            has = feignUserStudentService.checkHasByCertNumber(form.getSfzh());
            if (has) {
                throw new ServiceException(RestCode.ARGUMENT_ERROR, "身份证号已存在");
            }
        }
        AccountInfo account = AccountCommonService.getCurrentUser();
        Long schoolId = null;
        Long regionId = null;
        if (account.getCityAdminFlag() || account.getCountyAdminFlag()) {
            schoolId = form.getSchoolId();
            regionId = form.getRegionId();
        }else {
            schoolId = account.getSchoolId();
            regionId = account.getRegionId();
        }
        if(ObjectUtil.isEmpty(studentEntity)){
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "学生不存在");
        }
        UserDto userDto = getUserDto(form,schoolId,regionId);
        userDto.setUsername(form.getUserName());
        userDto.setId(studentEntity.getUserId());
        feignUserService.updateUser(userDto);
        if(!form.getSfzh().equals(oldSfzh)){
            UserStudentDto userStudentDto = new UserStudentDto().setUserId(studentEntity.getUserId()).setCertNumber(form.getSfzh()).setRegionId(regionId).setSchoolId(schoolId);
            feignUserStudentService.deleteUserStudentByCertNumber(oldSfzh);
            feignUserStudentService.addUserStudent(userStudentDto);
        }
        Student student = new Student();
        BeanUtil.copyProperties(form, student);
        student.setId(form.getId());
        return baseMapper.updateById(student);
    }
    @Override
    public Integer updateStatus(Long id, Integer status) {
        if(ObjectUtil.isEmpty(id)){
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "学生ID不能为空");
        }
        Student student = baseMapper.selectById(id);
        if(ObjectUtil.isEmpty(student)){
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "学生不存在");
        }
        student.setStatus(status);
        return baseMapper.updateById(student);
    }

    @Override
    public StudentInfo getDetailById(Long id) {
        if(ObjectUtil.isEmpty(id)){
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "学生ID不能为空");
        }
        StudentInfo studentInfo = baseMapper.getDetailById(id);
        if(ObjectUtil.isEmpty(studentInfo)){
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "学生不存在");
        }
        UserDto userDto = feignUserService.getUserDto(studentInfo.getUserId());
        if(ObjectUtil.isEmpty(userDto)){
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "用户不存在");
        }
        studentInfo.setUserName(userDto.getUsername());
        studentInfo.setStatus(userDto.getStatus());
        studentInfo.setDefaultPasswordFlag(userDto.getDefaultPasswordFlag());
        studentInfo.setDefaultPassword(userDto.getDefaultPassword());
        studentInfo.setNickname(userDto.getNickname());
        studentInfo.setRegionName(userDto.getRegionName());
        studentInfo.setRegionId(userDto.getRegionId());
        GradeDto grade = gradeService.getGradeById(studentInfo.getGradeId());
        if(ObjectUtil.isNotNull(grade)){
            studentInfo.setGradeName(grade.getName());
        }
        return studentInfo;
    }

    @Override
    public StudentInfo getDetailByCertNo(String xjhOrSfzh) {
        StudentInfo studentInfo = baseMapper.getDetailByCertNo(xjhOrSfzh);
        if(studentInfo == null){
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "学籍号/身份证号不存在");
        }
        return studentInfo;
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Integer deleteStudent(Long id) {
        if(ObjectUtil.isEmpty(id)){
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "学生ID不能为空");
        }
        Student student = baseMapper.selectById(id);
        if(ObjectUtil.isEmpty(student)){
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "学生不存在");
        }
        baseMapper.deleteById(id);
        deleteRecordService.createRecord(id,Student.class, student,student.getXjh());
        feignUserService.deleteUser(student.getUserId());
        feignUserStudentService.deleteUserStudentByCertNumber(student.getSfzh());
        return 1;
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public List<ExcelBatchImportResponse> importStudent(MultipartFile file, Long regoinId) {
        AccountInfo account = AccountCommonService.getCurrentUser();
        List<RoleInfo> roles = account.getRoles();
        String regionName = account.getRegionName();//区县名字
        if(roles.stream().anyMatch(roleInfo -> roleInfo.getRoleCode().equals(RoleConstant.CITY_ADMIN_CODE))){//市级管理员不校验区县
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "市级管理员不能导入学生");//因为不同区县的学生要导入不同的区县库
        }
        if (StrUtil.isBlank(account.getSchoolName())) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "当前登录人学校不存在");
        }
        List<SchoolDto> schoolDtoList = schoolService.getByRegionId(regoinId);
        List<ExcelBatchImportResponse> result = new ArrayList<>();
        List<StudentImportExcel> excelList = new ArrayList<>();
        try {
            excelList = ExcelHelper.readData(file.getInputStream(), StudentImportExcel.class, 0);
        } catch (Exception e) {
            log.error("读取Excel发生错误，请稍后重试", e);
            result.add(new ExcelBatchImportResponse().setMessage("读取Excel发生错误，请稍后重试").setRowIndex(1));
        }
        if (CollUtil.isEmpty(excelList)) {
            return result;
        }
        List<KeyValue<String, Boolean>> hasCertNumbersByCertNumbers = feignUserStudentService.getHasCertNumbersByCertNumbers(excelList.stream().map(StudentImportExcel::getSfzh).collect(Collectors.toList()));
        List<Student> studentList = baseMapper.selectList(new LambdaQueryWrapper<Student>().select(Student::getSfzh,Student::getXjh));
        List<GradeDto> gradeDtoList = gradeService.getGradeListByName(excelList.stream().map(StudentImportExcel::getGradeName).distinct().collect(Collectors.toList()));
        List<School> schoolNameList = schoolService.getSchoolBySchoolNames(excelList.stream().map(StudentImportExcel::getSchoolName).distinct().collect(Collectors.toList()));
        List<ClazzInfo> clazzInfoList = clazzService.getClazzList(schoolNameList.stream().map(School::getId).collect(Collectors.toList()), gradeDtoList.stream().map(GradeDto::getId).collect(Collectors.toList()));
        for (int i = 0; i < excelList.size(); i++) {
            StudentImportExcel studentImportExcel = excelList.get(i);
            List<ExcelBatchImportResponse> validateData = studentImportExcel.validateData(i + 1);
            if (CollUtil.isNotEmpty(validateData)) {
                result.addAll(validateData);
                return result;
            }
            // 判定班级是否存在
            if(clazzInfoList.stream().noneMatch(clazzInfo -> clazzInfo.getName().equals(studentImportExcel.getClassName()))){
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中班级【" + studentImportExcel.getClassName() + "】不存在").setRowIndex(i + 1));
            }
            // 判定身份证号有无重复
            if (excelList.stream().filter(student -> student.getSfzh().equals(studentImportExcel.getSfzh())).count() > 1) {
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中身份证号【" + studentImportExcel.getSfzh() + "】存在重复数据").setRowIndex(i + 1));
            }

            if(hasCertNumbersByCertNumbers.stream().anyMatch(student -> student.getKey().equals(studentImportExcel.getSfzh()))){
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中身份证号【" + studentImportExcel.getSfzh() + "】已被注册").setRowIndex(i + 1));
            }
            // 判定学号有无重复
            if (excelList.stream().filter(student -> student.getXjh().equals(studentImportExcel.getXjh())).count() > 1) {
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中学籍号【" + studentImportExcel.getXjh() + "】存在重复数据").setRowIndex(i + 1));
            }
            if(studentList.stream().anyMatch(student -> student.getXjh().equals(studentImportExcel.getXjh()))){
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中学籍号【" + studentImportExcel.getXjh() + "】已被注册").setRowIndex(i + 1));
            }
//            if(schoolDtoList.stream().noneMatch(schoolDto -> schoolDto.getName().equals(studentImportExcel.getSchoolName()))){
//                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中所属学校【" + studentImportExcel.getSchoolName() + "】不存在").setRowIndex(i + 1));
//            }
            if(account.getSchoolName().equals(studentImportExcel.getSchoolName())){
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中所属学校【" + studentImportExcel.getSchoolName() + "】与当前登录学校不一致").setRowIndex(i + 1));
            }
            if(!regionName.equals(studentImportExcel.getRegionName())){
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中所属区县【" + studentImportExcel.getRegionName() + "】与当前登录区县不一致").setRowIndex(i + 1));
            }
            // 判定手机号有无重复
            if (excelList.stream().filter(student -> student.getSjhm().equals(studentImportExcel.getSjhm())).count() > 1) {
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中手机号【" + studentImportExcel.getSjhm() + "】存在重复数据").setRowIndex(i + 1));
            }
            if(!clazzInfoList.stream().anyMatch(clazzInfo -> clazzInfo.getSchoolName().equals(studentImportExcel.getSchoolName()))){
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中班级【" + studentImportExcel.getClassName() + "】所属学校【" + studentImportExcel.getSchoolName() + "】不一致").setRowIndex(i + 1));
            }
            if(!clazzInfoList.stream().anyMatch(clazzInfo -> clazzInfo.getGradeName().equals(studentImportExcel.getGradeName()))){
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中班级【" + studentImportExcel.getClassName() + "】所属年级【" + studentImportExcel.getGradeName() + "】不一致").setRowIndex(i + 1));
            }
            Boolean consistent = false;
            for (int k = 0; k < clazzInfoList.size(); k++) {
                if(clazzInfoList.get(k).getSchoolName().equals(studentImportExcel.getSchoolName()) && clazzInfoList.get(k).getGradeName().equals(studentImportExcel.getGradeName())){
                    consistent = true;
                }
            }
            if (!consistent) {
                result.add(new ExcelBatchImportResponse().setMessage("Excel文件中班级【" + studentImportExcel.getClassName() + "】所属年级【" + studentImportExcel.getGradeName() + "】不一致").setRowIndex(i + 1));
            }
        }
        if(CollUtil.isNotEmpty(result)){
            return result;
        }
        List<UserDto> addUserDtoList = new ArrayList<>();
        List<UserStudentDto> addUserStudentDtoList = new ArrayList<>();
        List<Student> addStudentList = new ArrayList<>();
        if (CollUtil.isEmpty(result)) {
            List<String> studentNameList = studentNameUtil.getStudentName(XS, excelList.size());
            if(CollUtil.isEmpty(studentNameList)){
                throw new ServiceException(RestCode.ARGUMENT_ERROR, "学生账号生成失败");
            }
            for (int j = 0; j < excelList.size(); j++) {
                StudentImportExcel studentImportExcel = excelList.get(j);
                Long schoolId = schoolDtoList.stream().filter(schoolDto -> schoolDto.getName().equals(studentImportExcel.getSchoolName())).findFirst().orElse(null).getId();
                UserDto u = new UserDto()
                    .setUsername(studentNameList.get(j))
                    .setNickname(studentImportExcel.getXm())
                    .setMobile(studentImportExcel.getSjhm())
                    .setDefaultPasswordFlag(true)
                    .setAdminFlag(false)
                    .setSchoolId(schoolId)
                    .setStatus(1)
                    .setRegionId(regoinId)
                    .setIdCard(studentImportExcel.getSfzh())
                    .setRoles(Collections.singletonList(new RoleInfo().setRoleCode(RoleConstant.STUDENT_CODE)));
                addUserDtoList.add(u);

            }
            List<UserDto> createUserDtos = feignUserService.batchCreateUser(addUserDtoList);
            if (CollUtil.isNotEmpty(createUserDtos)) {
                excelList.forEach(studentImportExcel -> {
                    UserDto createUserDto = createUserDtos.stream().filter(item -> item.getIdCard().equals(studentImportExcel.getSfzh())).findFirst().orElse(null);
                    if(ObjectUtil.isNotEmpty(createUserDto)){
                        Long userId = createUserDto.getId();
                        Student student = new Student();
                        student.setSchoolId(createUserDto.getSchoolId());
                        student.setSjhm(studentImportExcel.getSjhm());
                        student.setXm(studentImportExcel.getXm());
                        student.setSfzh(studentImportExcel.getSfzh());
                        student.setXb(ObjectUtil.equals(studentImportExcel.getXb(),"男")?1:2);
                        student.setXjh(studentImportExcel.getXjh());
                        student.setStatus(1);
                        student.setJtzz(studentImportExcel.getJtzz());
                        ClazzInfo clazz = clazzInfoList.stream().filter(clazzInfo -> clazzInfo.getGradeName().equals(studentImportExcel.getGradeName()) && clazzInfo.getSchoolName().equals(studentImportExcel.getSchoolName()) && clazzInfo.getName().equals(studentImportExcel.getClassName())).findFirst().get();
                        if (ObjectUtil.isNotNull(clazz)) {
                            student.setClazzId(clazz.getId());
                        }
                        student.setUserId(userId);
                        addStudentList.add(student);
                        UserStudentDto userStudentDto = new UserStudentDto();
                        userStudentDto.setUserId(createUserDto.getId());
                        userStudentDto.setCertNumber(studentImportExcel.getSfzh());
                        userStudentDto.setRegionId(regoinId);
                        userStudentDto.setSchoolId(createUserDto.getSchoolId());
                        addUserStudentDtoList.add(userStudentDto);
                    }else {
                        result.add(new ExcelBatchImportResponse().setMessage("学生账号生成失败").setRowIndex(studentImportExcel.getRowNum()));
                    }
                });
            }
        }
        if (CollUtil.isNotEmpty(addStudentList)) {
            feignUserStudentService.batchAddUserStudent(addUserStudentDtoList);
            super.saveBatch(addStudentList);
        }
        return result;
    }

    @Override
    public void exportStudent(StudentSearch search, HttpServletResponse response) {
        OrgDto region = tenantContextHolder.getTenant();
        search.setPageNumber(1).setPageSize(1000);
        PageInfo<StudentInfo> pageInfo;
        ExcelHelper<StudentExportExcel> excelHelper = ExcelHelper.create(StudentExportExcel.class);
        AtomicInteger index = new AtomicInteger(1);
        do {
            pageInfo = this.getPageData( search);
            List<StudentExportExcel> excelList;
            List<Long> userIds = pageInfo.getRecords().stream().map(StudentInfo::getUserId).collect(Collectors.toList());
            List<UserDto> userList = CollUtil.isNotEmpty(userIds) ? feignUserService.getUserDtoList(userIds) : new ArrayList<>();
            pageInfo.getRecords().forEach(student -> {
                UserDto userDto = userList.stream().filter(user -> user.getId().equals(student.getUserId())).findFirst().orElse(null);
                if(ObjectUtil.isNotEmpty(userDto)){
                    student.setStatus(userDto.getStatus());
                    student.setDefaultPassword(userDto.getDefaultPassword());
                    student.setUserName(userDto.getUsername());
                    String roleName = userDto.getRoles().stream().map(RoleInfo::getRoleName).collect(Collectors.joining(","));
                    student.setRoleName(roleName);
                    student.setRoleList(userDto.getRoles());
                }
            });
            excelList = pageInfo.getRecords().stream().map(studentInfo -> {
                StudentExportExcel studentExportExcel = new StudentExportExcel();
                if(ObjectUtil.isNotNull(studentInfo.getXb())){
                    studentExportExcel.setXb(studentInfo.getXb() == 1 ? "男" : "女");
                }
                studentExportExcel.setXm(studentInfo.getXm());
                studentExportExcel.setXjh(studentInfo.getXjh());
                studentExportExcel.setSfzh(studentInfo.getSfzh());
                studentExportExcel.setSjhm(studentInfo.getSjhm());
                studentExportExcel.setRowNum(index.getAndIncrement());
                studentExportExcel.setPeriod(SchoolPeriodConstant.SCHOOL_PERIOD_MAP_REVERSE.get(studentInfo.getPeriod()));
                studentExportExcel.setClassName(studentInfo.getClazzName());
                studentExportExcel.setSchoolName(studentInfo.getSchoolName());
                studentExportExcel.setRegionName(region.getName());
                studentExportExcel.setJtzz(studentInfo.getJtzz());
                return studentExportExcel;
            }).collect(Collectors.toList());
            excelHelper.writeData(excelList);
            search.setPageNumber(search.getPageNumber() + 1);
        }while (pageInfo.hasNext());
        excelHelper.writeToWebResponse( response,region.getName() + "-学生信息");
    }
}
