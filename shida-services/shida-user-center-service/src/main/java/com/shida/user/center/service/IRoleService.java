package com.shida.user.center.service;

import com.shida.pojo.search.Search;
import com.shida.pojo.vo.KeyValue;
import com.shida.pojo.vo.PageInfo;
import com.shida.pojo.vo.RoleInfo;
import com.shida.user.center.entity.Role;
import com.baomidou.mybatisplus.extension.service.IService;
import com.shida.user.center.pojo.form.RoleForm;
import com.shida.userCenter.dto.RoleDto;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 角色信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface IRoleService extends IService<Role> {

    Role findByCode(String code);

    PageInfo<RoleInfo> getPageData(Search search);

    List<RoleInfo> getUserRolesByUserId(Long userId);

    String changeStatus(Long roleId, Integer status);

    String create(RoleForm form);

    String updateRole(RoleForm form);

    List<KeyValue<String, String>> getRoleSelectList(String roleCode);

    List<RoleDto> getRolestList(String roleCode);
}
