package com.shida.user.center.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.noRepeatSubmit.NoRepeatSubmit;
import com.shida.pojo.form.KeyForm;
import com.shida.pojo.search.Search;
import com.shida.pojo.vo.PageInfo;
import com.shida.user.center.pojo.form.GradeForm;
import com.shida.user.center.pojo.vo.GradeInfo;
import com.shida.user.center.service.IGradeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.sql.DataSource;
import javax.validation.Valid;

@Tag(name = "grade-年级管理")
@RestController
@RequestMapping("/grade/manage")
public class GradeManageController {
    @Resource
    private IGradeService gradeService;

    @Operation(summary = "获取年级列表")
    @SaCheckPermission("grade:list")
    @PostMapping("/getPageData")
    public PageInfo<GradeInfo> getPageData(@RequestBody Search search) {
        return gradeService.getPageData(search);
    }


    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_ADD)
    @Operation(summary = "创建年级")
    @SaCheckPermission("grade:add")
    @PostMapping("/create")
    public String create(@RequestBody GradeForm form) {
        return gradeService.create(form);
    }

    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_ADD)
    @Operation(summary = "更新年级")
    @SaCheckPermission("grade:edit")
    @PostMapping("/update")
    public String update(@RequestBody GradeForm form) {
        return gradeService.updateGrade(form);
    }


    @NoRepeatSubmit
    @SaCheckPermission("grade:delete")
    @AutoLog(operateType = OperateType.OPERATE_DELETE)
    @Operation(summary = "删除年级")
    @PostMapping("/delete")
    public String delete(@RequestBody @Valid KeyForm<Long> form) {
        return gradeService.deleteById(form.getKey());
    }

}
