package com.shida.user.center.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.shida.pojo.search.Search;
import com.shida.pojo.vo.KeyValue;
import com.shida.pojo.vo.PageInfo;
import com.shida.user.center.entity.Org;
import com.shida.user.center.pojo.form.OrgForm;
import com.shida.user.center.pojo.vo.OrgInfo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * 机构信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface IOrgService extends IService<Org> {

    Org findByCode(String code);

    List<KeyValue<String, String>> getRegionSelectList();

    List<KeyValue<String, String>> getSchoolSelectList(Long regionId);

    PageInfo<OrgInfo> getRegionList(Search search);

    String updateOrgInfo(OrgForm orgForm);

    void exportAccount(Long regionId, HttpServletResponse response);

    void exportAllRegionAccount(Search search, HttpServletResponse response);
}
