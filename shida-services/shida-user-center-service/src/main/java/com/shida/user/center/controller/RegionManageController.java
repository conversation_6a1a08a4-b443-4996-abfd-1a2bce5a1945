package com.shida.user.center.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.http.HttpResource;
import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.noRepeatSubmit.NoRepeatSubmit;
import com.shida.pojo.form.KeyForm;
import com.shida.pojo.search.Search;
import com.shida.pojo.vo.PageInfo;
import com.shida.user.center.pojo.form.OrgForm;
import com.shida.user.center.pojo.vo.OrgInfo;
import com.shida.user.center.service.IOrgService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

@Tag(name = "region-区县管理接口")
@RestController
@RequestMapping("/region/manage")
public class RegionManageController {
    @Resource
    private IOrgService orgService;

    @SaCheckPermission("region:list")
    @Operation(summary = "获取区县列表")
    @PostMapping("/getRegionList")
    public PageInfo<OrgInfo> getPageData(@RequestBody Search search) {
        return orgService.getRegionList(search);
    }

    @SaCheckPermission("region:edit")
    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    @Operation(summary = "更新区县信息")
    @PostMapping("/update")
    public String update(@RequestBody @Valid OrgForm orgForm) {
        return orgService.updateOrgInfo(orgForm);
    }


    @NoRepeatSubmit
    @SaCheckPermission("region:export:account")
    @AutoLog(operateType = OperateType.OPERATE_EXPORT)
    @Operation(summary = "导出区县管理员信息")
    @PostMapping("/export/account")
    public void exportAccount(@RequestBody @Valid KeyForm<Long> keyForm, HttpServletResponse response) {
        orgService.exportAccount(keyForm.getKey(), response);
    }

    @NoRepeatSubmit
    @SaCheckPermission("region:export:account")
    @AutoLog(operateType = OperateType.OPERATE_EXPORT)
    @Operation(summary = "导出所有区县管理员信息")
    @PostMapping("/export/all/account")
    public void exportAllAccount(@RequestBody Search search, HttpServletResponse response) {
        orgService.exportAllRegionAccount(search, response);
    }
}
