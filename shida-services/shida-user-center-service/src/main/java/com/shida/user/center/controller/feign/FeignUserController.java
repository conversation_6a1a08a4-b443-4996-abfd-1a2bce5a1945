package com.shida.user.center.controller.feign;

import cn.dev33.satoken.secure.SaSecureUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.shida.constant.RoleConstant;
import com.shida.enums.RestCode;
import com.shida.exceptions.ServiceException;
import com.shida.pojo.vo.KeyValue;
import com.shida.pojo.vo.PermissionInfo;
import com.shida.pojo.vo.RoleInfo;
import com.shida.user.center.entity.*;
import com.shida.user.center.pojo.form.UserForm;
import com.shida.user.center.service.*;
import com.shida.userCenter.api.IFeignUserService;
import com.shida.userCenter.dto.*;
import com.shida.utils.IdUtil;
import io.seata.spring.annotation.GlobalTransactional;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Tag(name = "Feign-用户服务")
@RestController
public class FeignUserController implements IFeignUserService {
    @Resource
    private IUserService userService;
    @Resource
    private IUserRoleService userRoleService;
    @Resource
    private IUserOrgService userOrgService;
    @Resource
    private IRoleService roleService;


    @Operation(summary = "根据用户ID获取用户信息")
    @Override
    public UserDto getUserDto(Long userId) {
        return userService.findUserDtoByUserId(userId);
    }

    @Operation(summary = "根据用户名获取用户信息")
    @Override
    public UserDto getUserDto(String username) {
        return userService.findUserDtoByUsername(username);
    }

    @Operation(summary = "根据用户ID列表获取用户信息列表")
    @Override
    public List<UserDto> getUserDtoList(List<Long> userIds) {
        return userService.findUserDtoList(userIds);
    }

    @Operation(summary = "根据用户名列表获取用户信息列表")
    @Override
    public List<UserDto> getListByUsernames(List<String> usernameList) {
        return userService.findListByUsernames(usernameList);
    }

    @Operation(summary = "修改用户密码")
    @Override
    public String changeUserPassword(ChangeUserPasswordDto dto) {
        return userService.changeUserPassword(dto);
    }

    @Operation(summary = "获取学校管理员列表")
    @Override
    public List<UserDto> getMasterUserListBySchoolId(Long regionId, Long schoolId) {
        return userService.getMasterUserListBySchoolId(regionId, schoolId);
    }

    @Operation(summary = "获取学校IDS管理员列表")
    @Override
    public List<UserDto> getMasterUserListBySchoolIds(Long regionId, List<Long> schoolIds) {
        return userService.getMasterUserListBySchoolIds(regionId, schoolIds);
    }

    @Operation(summary = "修改字段值")
    @Override
    public String changeFieldValue(Long userId, KeyValue<String, ?> form) {
        User user = userService.getById(userId);
        if (user == null) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "用户不存在");
        }
        if (form.getKey().equals("nickname")) {
            user.setNickname(form.getValue().toString());
        }
        if (form.getKey().equals("mobile")) {
            user.setMobile(form.getValue().toString());
        }
        if (form.getKey().equals("email")) {
            user.setEmail(form.getValue().toString());
        }
        userService.updateById(user);
        return "ok";
    }

    @Operation(summary = "批量创建用户")
    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public List<UserDto> batchCreateUser(List<UserDto> userList) {
        if (userList.isEmpty()) {
            return userList;
        }
        List<User> users = new ArrayList<>();
        List<UserRole> userRoles = new ArrayList<>();
        List<UserOrg> userOrgs = new ArrayList<>();
        for (UserDto userDto : userList) {
            if (StrUtil.isBlank(userDto.getUsername())) {
                throw new ServiceException(RestCode.ARGUMENT_ERROR, "用户名不能为空");
            }
            User user = new User();
            String password = RandomUtil.randomString(8);
            String salt = RandomUtil.randomString(32);
            user.setUsername(userDto.getUsername())
                .setNickname(userDto.getNickname())
                .setEmail(userDto.getEmail())
                .setMobile(userDto.getMobile())
                .setSalt(salt)
                .setPassword(SaSecureUtil.md5BySalt(password, salt))
                .setDefaultPassword(password)
                .setDefaultPasswordFlag(true)
                .setExpireTime(null)
                .setStatus(1);
            user.setId(IdUtil.getSnowflakeNextId());
            userDto.setId(user.getId());
            users.add(user);
            if (CollUtil.isEmpty(userDto.getRoles())) {
                throw new ServiceException(RestCode.ARGUMENT_ERROR, "用户角色不能为空");
            }
            for (RoleInfo role : userDto.getRoles()) {
                if (StrUtil.isBlank(role.getRoleCode())) {
                    throw new ServiceException(RestCode.ARGUMENT_ERROR, "角色代码不能为空");
                }
                Role r = roleService.findByCode(role.getRoleCode());
                if (r == null) {
                    throw new ServiceException(RestCode.ARGUMENT_ERROR, "角色不存在");
                }
                userRoles.add(new UserRole().setUserId(user.getId()).setRoleId(r.getId()));
            }
            if (Objects.isNull(userDto.getRegionId())) {
                throw new ServiceException(RestCode.ARGUMENT_ERROR, "区划不能为空");
            }
            if (Objects.isNull(userDto.getAdminFlag())) {
                throw new ServiceException(RestCode.ARGUMENT_ERROR, "管理员标识不能为空");
            }
            UserOrg userOrg = new UserOrg();
            userOrg.setUserId(user.getId())
                .setOrgId(userDto.getRegionId())
                .setSchoolId(userDto.getSchoolId())
                .setMasterFlag(userDto.getAdminFlag());
            userOrgs.add(userOrg);
        }
        userService.saveBatch(users);
        userRoleService.saveBatch(userRoles);
        userOrgService.saveBatch(userOrgs);
        return userList;
    }

    /**
     * 新增用户
     *
     * @param userDto
     * @return
     */
    @Operation(summary = "新增用户，返回用户ID")
    @Override
    @Transactional
    public UserDto createUser(UserDto userDto) {
        return userService.createUser(userDto);
    }

    /**
     * 获取用户角色列表
     *
     * @param userIds
     * @return
     */
    @Operation(summary = "根据用户ID列表获取用户角色列表")
    public List<RoleDto> getUserRoleDtoList(List<Long> userIds) {
        return userService.getUserRoleDtoList(userIds);
    }

    /**
     * 更新用户信息
     *
     * @param userDto
     * @return
     */
    @Operation(summary = "更新用户信息")
    public String updateUser(UserDto userDto) {
        UserForm userForm = new UserForm();
        BeanUtil.copyProperties(userDto, userForm);
        userForm.setRoleCodes(userDto.getRoles().stream().map(RoleInfo::getRoleCode).collect(Collectors.toList()));
        return userService.updateUserInfo(userForm);
    }

    /**
     * 删除用户
     *
     * @param userId
     * @return
     */
    @Operation(summary = "删除用户")
    public String deleteUser(Long userId) {
        return userService.deleteUser(userId);
    }
}
