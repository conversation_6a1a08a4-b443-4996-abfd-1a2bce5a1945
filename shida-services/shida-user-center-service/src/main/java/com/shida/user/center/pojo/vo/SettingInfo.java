package com.shida.user.center.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@Schema(name = "SettingInfo", description = "设置项信息")
public abstract class SettingInfo implements Serializable {
    @Schema(description = "设置ID")
    private Long id;

    @Schema(description = "设置名称")
    private String name;

    @Schema(description = "设置代码")
    private String code;

    @Schema(description = "设置内容，json")
    private Object content;

    @Schema(description = "排序序号")
    private Integer orderNumber;
}
