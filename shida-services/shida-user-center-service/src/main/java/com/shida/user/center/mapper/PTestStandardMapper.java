package com.shida.user.center.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.shida.user.center.entity.PTestStandard;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shida.user.center.pojo.search.TestStandardSearch;
import com.shida.user.center.pojo.vo.TestStandardInfo;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 通用体质健康测试评分标准表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-02
 */
public interface PTestStandardMapper extends BaseMapper<PTestStandard> {

    /**
     * 分页查询体质健康测试评分标准
     * @param page 分页参数
     * @param search 搜索条件
     * @return 分页结果
     */
    IPage<TestStandardInfo> getPageData(IPage<TestStandardInfo> page, @Param("search") TestStandardSearch search);
}
