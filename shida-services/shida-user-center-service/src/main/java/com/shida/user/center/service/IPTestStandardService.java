package com.shida.user.center.service;

import com.shida.pojo.vo.PageInfo;
import com.shida.user.center.entity.PTestStandard;
import com.baomidou.mybatisplus.extension.service.IService;
import com.shida.user.center.pojo.search.TestStandardSearch;
import com.shida.user.center.pojo.vo.TestStandardInfo;

import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * 通用体质健康测试评分标准表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-02
 */
public interface IPTestStandardService extends IService<PTestStandard> {

    /**
     * 分页查询体质健康测试评分标准
     * @param search 搜索条件
     * @return 分页结果
     */
    PageInfo<TestStandardInfo> getPageData(TestStandardSearch search);

    /**
     * 更新体质健康测试评分标准
     * @param testStandardInfo 更新信息
     * @return 更新是否成功
     */
    boolean updateTestStandard(TestStandardInfo testStandardInfo);

    /**
     * 批量导入体质健康测试评分标准
     * @param file 导入文件
     * @param code 数据类型代码
     */
    void importTestStandard(MultipartFile file, String code);

    /**
     * 下载导入模板
     * @param code 数据类型代码
     * @param response HTTP响应
     */
    void downloadTemplate(String code, HttpServletResponse response);
}
