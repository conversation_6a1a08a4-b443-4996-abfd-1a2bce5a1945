package com.shida.user.center.init;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * 数据库初始化脚本
 * 所有数据库新增或编辑字段需要在这里写
 */
@Slf4j
@Component
@Order(0)
public class A000DatabaseInitializer implements ApplicationRunner {

    @Resource
    RedissonClient redissonClient;
    @Resource
    private DataSourceProperties dataSourceProperties;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        RLock lock = redissonClient.getLock("lockers:A000DatabaseInitializer");
        if (lock.tryLock()) {
            try {
                initDatabase();
                initUpdateDatabase();
            } finally {
                lock.unlock();
            }
        }
    }


    private void initDatabase() throws SQLException {
        Connection connection = DriverManager.getConnection(dataSourceProperties.getUrl(), dataSourceProperties.getUsername(), dataSourceProperties.getPassword());
        Statement stmt = connection.createStatement();
        String initSql = ResourceUtil.readStr("classpath:/sql/init_database.sql", CharsetUtil.CHARSET_UTF_8);
        initSql = initSql.replaceAll("Dynamic;", "DYNAMIC;");
        String[] tableSqls = initSql.split("DYNAMIC;");
        try {
            for (String ts : tableSqls) {
                if (StrUtil.isNotBlank(ts.trim())) {
                    String tableSql = "";
                    try {
                        tableSql = StrUtil.removeAllLineBreaks(ts);
                        tableSql = tableSql.replaceAll("utf8mb4_0900_ai_ci", "utf8mb4_general_ci");
                        if (!tableSql.startsWith("CREATE TABLE IF NOT EXISTS ")) {
                            tableSql = tableSql.replaceAll("CREATE TABLE ", "CREATE TABLE IF NOT EXISTS ");
                        }
                        tableSql = tableSql.trim() + "DYNAMIC;";
                        log.info("执行SQL：{}", tableSql);
                        stmt.executeUpdate(tableSql);
                    } catch (Exception e) {
                        e.printStackTrace();
                        throw e;
                    }
                }
            }
        } finally {
            stmt.close();
        }
    }

    private void initUpdateDatabase() throws SQLException {
        Connection connection = DriverManager.getConnection(dataSourceProperties.getUrl(), dataSourceProperties.getUsername(), dataSourceProperties.getPassword());
        Statement stmt = connection.createStatement();
        String updateSql = ResourceUtil.readStr("classpath:/sql/update_database.sql", CharsetUtil.CHARSET_UTF_8);
        // 按行分割
        String[] lines = updateSql.split("\n");
        for (String sqlLine : lines) {
            try {
                if (sqlLine.startsWith("--")) {
                    continue;
                }
                // 执行sql
                if (StrUtil.isNotBlank(sqlLine)) {
                    log.info("执行SQL：{}", sqlLine);
                    stmt.executeUpdate(sqlLine);
                }
            } catch (Exception e) {
            }
        }
        // 关闭连接
        stmt.close();
    }
}
