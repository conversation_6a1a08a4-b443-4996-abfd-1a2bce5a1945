package com.shida.user.center.pojo.vo;

import com.shida.pojo.vo.KeyValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.lang.reflect.Array;
import java.util.List;

@Data
@Accessors(chain = true)
@Schema(name = "QuanZhongZhanBiSettingInfo", description = "指标权重设置项信息")
public class QuanZhongZhanBiSettingInfo extends SettingInfo {
    @Schema(description = "权重项列表")
    private List<KeyValue<String, String>> content;
}
