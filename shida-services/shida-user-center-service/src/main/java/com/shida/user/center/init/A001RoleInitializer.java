package com.shida.user.center.init;

import com.shida.constant.RoleConstant;
import com.shida.user.center.entity.Role;
import com.shida.user.center.service.IRoleService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Order(1)
@Component
public class A001RoleInitializer implements ApplicationRunner {
    @Resource
    private IRoleService roleService;
    @Resource
    RedissonClient redissonClient;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        RLock lock = redissonClient.getLock("lockers:A001RoleInitializer");
        if (lock.tryLock()) {
            try {
                Role role = roleService.findByCode(RoleConstant.SUPER_ADMIN_CODE);
                if (role == null) {
                    role = new Role();
                    role.setCode(RoleConstant.SUPER_ADMIN_CODE);
                    role.setName("超级管理员");
                    role.setAdminFlag(true);
                    role.setStatus(1);
                    role.setSysRoleFlag(true);
                    role.setId(1L);
                    roleService.save(role);
                }
                role = roleService.findByCode(RoleConstant.CITY_ADMIN_CODE);
                if (role == null) {
                    role = new Role();
                    role.setCode(RoleConstant.CITY_ADMIN_CODE);
                    role.setName("市级管理员");
                    role.setAdminFlag(true);
                    role.setStatus(1);
                    role.setSysRoleFlag(true);
                    role.setId(2L);
                    roleService.save(role);
                }
                role = roleService.findByCode(RoleConstant.COUNTY_ADMIN_CODE);
                if (role == null) {
                    role = new Role();
                    role.setCode(RoleConstant.COUNTY_ADMIN_CODE);
                    role.setName("县级管理员");
                    role.setAdminFlag(true);
                    role.setStatus(1);
                    role.setSysRoleFlag(true);
                    role.setId(3L);
                    roleService.save(role);
                }
                role = roleService.findByCode(RoleConstant.SCHOOL_ADMIN_CODE);
                if (role == null) {
                    role = new Role();
                    role.setCode(RoleConstant.SCHOOL_ADMIN_CODE);
                    role.setName("学校管理员");
                    role.setAdminFlag(true);
                    role.setStatus(1);
                    role.setSysRoleFlag(true);
                    role.setId(4L);
                    roleService.save(role);
                }
                role = roleService.findByCode(RoleConstant.TEACHER_CODE);
                if (role == null) {
                    role = new Role();
                    role.setCode(RoleConstant.TEACHER_CODE);
                    role.setName("教师");
                    role.setAdminFlag(false);
                    role.setStatus(1);
                    role.setSysRoleFlag(true);
                    role.setId(5L);
                    roleService.save(role);
                }
                role = roleService.findByCode(RoleConstant.PARENT_CODE);
                if (role == null) {
                    role = new Role();
                    role.setCode(RoleConstant.PARENT_CODE);
                    role.setName("家长");
                    role.setAdminFlag(false);
                    role.setStatus(1);
                    role.setSysRoleFlag(true);
                    role.setId(6L);
                    roleService.save(role);
                }
                role = roleService.findByCode(RoleConstant.STUDENT_CODE);
                if (role == null) {
                    role = new Role();
                    role.setCode(RoleConstant.STUDENT_CODE);
                    role.setName("学生");
                    role.setAdminFlag(false);
                    role.setStatus(1);
                    role.setSysRoleFlag(true);
                    role.setId(7L);
                    roleService.save(role);
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                lock.unlock();
            }
        }
    }
}
