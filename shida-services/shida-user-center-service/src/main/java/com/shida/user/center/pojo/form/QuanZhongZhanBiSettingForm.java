package com.shida.user.center.pojo.form;

import com.shida.pojo.vo.KeyValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@Accessors(chain = true)
@Schema(name = "QuanZhongZhanBiSettingForm", description = "指标权重设置项信息表单")
public class QuanZhongZhanBiSettingForm extends SettingForm {
    @Schema(description = "设置名称")
    @NotBlank(message = "设置名称不能为空")
    @Size(max = 50, message = "设置名称长度不能超过50")
    private String name;

    @Schema(description = "设置代码")
    @NotBlank(message = "设置代码不能为空")
    @Size(max = 32, message = "设置代码长度不能超过32")
    private String code;

    @Schema(description = "权重项列表")
    @NotEmpty(message = "权重项列表不能为空")
    private List<KeyValue<String, String>> content;
}
