package com.shida.user.center.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shida.entity.BaseEntity;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 角色信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_role")
@Schema(name = "Role", description = "角色信息")
public class Role extends BaseEntity<Role> {

    private static final long serialVersionUID = 1L;

    @Schema(description = "角色代码")
    private String code;

    @Schema(description = "角色名称")
    private String name;

    @Schema(description = "是否是管理员角色")
    private Boolean adminFlag;

    @Schema(description = "是否启用，0禁用，1启用")
    private Integer status;

    @Schema(description = "是否是内置角色")
    private Boolean sysRoleFlag;

    @Schema(description = "自定义角色所属内置角色代码")
    private String sysRoleCode;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
