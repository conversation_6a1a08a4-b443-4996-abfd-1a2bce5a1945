package com.shida.user.center.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.shida.enums.RestCode;
import com.shida.exceptions.ServiceException;
import com.shida.user.center.entity.Setting;
import com.shida.user.center.mapper.SettingMapper;
import com.shida.user.center.service.ISettingService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 设置项信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-02
 */
@Service
public class SettingServiceImpl extends ServiceImpl<SettingMapper, Setting> implements ISettingService {

    @Override
    public <T> T getSettingByCode(String code, Class<T> classType) {
        Setting setting = super.getOne(new QueryWrapper<Setting>().lambda().eq(Setting::getCode, code));
        if (setting != null) {
            return BeanUtil.copyProperties(setting, classType);
        }
        return null;
    }

    @Override
    public String saveOrUpdateSetting(String code, String name, Object content, Integer orderNumber) {
        Setting setting = super.getOne(new QueryWrapper<Setting>().lambda().eq(Setting::getCode, code));
        if (setting == null) {
            setting = new Setting();
            setting.setCode(code);
            setting.setName(name);
            setting.setOrderNumber(orderNumber);
            setting.setContent(JSONUtil.toJsonStr(content));
            super.save(setting);
        } else {
            setting.setCode(code).setName(name).setContent(JSONUtil.toJsonStr(content)).setOrderNumber(orderNumber);
            super.updateById(setting);
        }
        return "success";
    }
}
