package com.shida.user.center.controller;


import cn.dev33.satoken.annotation.SaCheckPermission;
import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.noRepeatSubmit.NoRepeatSubmit;
import com.shida.pojo.form.KeyForm;
import com.shida.pojo.vo.PageInfo;
import com.shida.user.center.pojo.form.TermForm;
import com.shida.user.center.pojo.search.TermSearch;
import com.shida.user.center.pojo.vo.TermInfo;
import com.shida.user.center.service.ITermService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

@Tag(name = "term-学期管理")
@RestController
@RequestMapping("/term/manage")
public class TermManageController {
    @Resource
    private ITermService termService;

    @SaCheckPermission("term:list")
    @Operation(summary = "获取学期分页数据")
    @PostMapping("/getPageData")
    public PageInfo<TermInfo> getPageData(@RequestBody TermSearch search) {
        return termService.getPageData(search);
    }

    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_ADD)
    @SaCheckPermission("term:add")
    @Operation(summary = "添加学期")
    @PostMapping("/create")
    public String create(@RequestBody @Valid TermForm form) {
        return termService.create(form);
    }

    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    @SaCheckPermission("term:edit")
    @Operation(summary = "修改学期")
    @PostMapping("/update")
    public String update(@RequestBody @Valid TermForm form) {
        return termService.updateTerm(form);
    }

    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    @SaCheckPermission("term:change:status")
    @Operation(summary = "修改学期状态")
    @PostMapping("/changeStatus/{status}")
    public String changeStatus(@RequestBody @Valid KeyForm<Long> form,
                               @PathVariable @Schema(description = "状态，0禁用，1启用，2归档") Integer status) {
        return termService.changeStatus(form.getKey(), status);
    }


    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    @SaCheckPermission("term:guidang")
    @Operation(summary = "归档")
    @PostMapping("/guiDang")
    public String guiDang(@RequestBody @Valid KeyForm<Long> form) {
        return termService.changeStatus(form.getKey(), 2);
    }

}
