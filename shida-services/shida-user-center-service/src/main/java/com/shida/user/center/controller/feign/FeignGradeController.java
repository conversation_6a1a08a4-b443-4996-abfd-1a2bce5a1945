package com.shida.user.center.controller.feign;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.shida.user.center.entity.Grade;
import com.shida.user.center.service.IGradeService;
import com.shida.userCenter.api.IFeignGradeService;
import com.shida.userCenter.dto.GradeDto;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Tag(name = "Feign-年级服务")
@RestController
public class FeignGradeController implements IFeignGradeService {

    @Resource
    private IGradeService gradeService;

    @Override
    public GradeDto getGradeById(Long gradeId) {
        return BeanUtil.copyProperties(gradeService.getById(gradeId), GradeDto.class);
    }

    @Override
    public List<GradeDto> getGradeList(List<Long> gradeIds) {
        return BeanUtil.copyToList(gradeService.listByIds(gradeIds), GradeDto.class);
    }

    @Override
    public List<GradeDto> getGradeListByName(List<String> gradeNameList) {
        if (CollUtil.isEmpty(gradeNameList)) {
            return Collections.emptyList();
        }
        List<Grade> gradeList = gradeService.list(new LambdaQueryWrapper<Grade>().in(Grade::getName, gradeNameList));
        return BeanUtil.copyToList(gradeList, GradeDto.class);
    }
}
