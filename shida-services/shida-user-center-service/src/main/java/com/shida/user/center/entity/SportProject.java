package com.shida.user.center.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shida.entity.BaseEntity;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 体育项目信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_sport_project")
@Schema(name = "SportProject", description = "体育项目信息表")
public class SportProject extends BaseEntity<SportProject> {

    private static final long serialVersionUID = 1L;

    @Schema(description = "项目名称")
    private String name;

    @Schema(description = "项目代码")
    private String code;

    @Schema(description = "描述")
    private String description;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
