package com.shida.user.center.pojo.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDate;

@Data
@Accessors(chain = true)
@Schema(name = "TermForm", description = "学期表单")
public class TermForm implements Serializable {
    @Schema(description = "ID,修改时必填")
    private Long id;

    @Schema(description = "学年名称")
    @NotBlank(message = "请输入学年名称")
    @Size(max = 10, message = "学年名称长度不能超过10")
    private String yearName;

    @Schema(description = "学期名称")
    @NotBlank(message = "请输入学期名称")
    @Size(max = 32, message = "学期名称长度不能超过32")
    private String termName;

    @Schema(description = "开始日期")
    @NotNull(message = "请选择开始日期")
    private LocalDate startDate;

    @Schema(description = "结束日期")
    @NotNull(message = "请选择结束日期")
    private LocalDate stopDate;

}
