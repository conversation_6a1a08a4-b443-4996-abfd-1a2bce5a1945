package com.shida.user.center.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shida.entity.BaseEntity;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_grade")
@Schema(name = "Grade", description = "Grade")
public class Grade extends BaseEntity<Grade> {

    private static final long serialVersionUID = 1L;

    @Schema(description = "年份")
    private Integer year;

    @Schema(description = "名称")
    private String name;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
