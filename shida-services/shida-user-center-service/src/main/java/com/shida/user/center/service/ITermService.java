package com.shida.user.center.service;

import com.shida.pojo.vo.KeyValue;
import com.shida.pojo.vo.PageInfo;
import com.shida.user.center.entity.Term;
import com.baomidou.mybatisplus.extension.service.IService;
import com.shida.user.center.pojo.form.TermForm;
import com.shida.user.center.pojo.search.TermSearch;
import com.shida.user.center.pojo.vo.TermInfo;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
public interface ITermService extends IService<Term> {

    PageInfo<TermInfo> getPageData(TermSearch search);

    String create(TermForm form);

    String updateTerm(TermForm form);

    String changeStatus(Long id, Integer status);

    List<KeyValue<String, String>> getTermSelectList();
}
