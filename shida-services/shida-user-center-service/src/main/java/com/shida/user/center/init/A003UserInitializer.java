package com.shida.user.center.init;

import cn.dev33.satoken.secure.SaSecureUtil;
import cn.hutool.core.util.RandomUtil;
import com.shida.constant.RoleConstant;
import com.shida.user.center.entity.Role;
import com.shida.user.center.entity.User;
import com.shida.user.center.entity.UserOrg;
import com.shida.user.center.entity.UserRole;
import com.shida.user.center.service.*;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


@Component
public class A003UserInitializer implements ApplicationRunner {
    @Resource
    private IUserService userService;
    @Resource
    private IRoleService roleService;
    @Resource
    private IOrgService orgService;
    @Resource
    private IUserRoleService userRoleService;
    @Resource
    private IUserOrgService userOrgService;
    @Resource
    RedissonClient redissonClient;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        RLock lock = redissonClient.getLock("lockers:A003UserInitializer");
        if (lock.tryLock()) {
            try {

                User user = userService.findByUsername("super_admin");
                if (user == null) {
                    Role role = roleService.findByCode(RoleConstant.SUPER_ADMIN_CODE);
                    user = new User();
                    user.setUsername("super_admin");
                    user.setNickname("超级管理员");
                    user.setSalt(RandomUtil.randomString(32));
                    user.setPassword(SaSecureUtil.md5BySalt("a12345678*", user.getSalt()));
                    user.setDefaultPassword("a12345678*");
                    user.setDefaultPasswordFlag(true);
                    user.setStatus(1);
                    user.setId(1001L);
                    userService.save(user);
                    userRoleService.save(new UserRole().setUserId(user.getId()).setRoleId(role.getId()));
                    userOrgService.save(new UserOrg().setUserId(user.getId()).setOrgId(orgService.findByCode("130100").getId()));
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                lock.unlock();
            }
        }
    }
}
