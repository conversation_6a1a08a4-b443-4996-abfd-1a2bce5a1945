package com.shida.user.center.controller.feign;

import cn.hutool.core.bean.BeanUtil;
import com.shida.pojo.vo.RoleInfo;
import com.shida.user.center.entity.Role;
import com.shida.user.center.service.IRoleService;
import com.shida.userCenter.api.IFeignRoleService;
import com.shida.userCenter.dto.RoleDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Tag(name = "Feign-角色服务")
@RestController
public class FeignRoleController implements IFeignRoleService {
    @Resource
    private IRoleService roleService;

    @Operation(summary = "根据角色代码获取角色信息")
    @Override
    public RoleDto getByCode(String code) {
        return BeanUtil.copyProperties(roleService.findByCode(code), RoleDto.class);
    }

    @Operation(summary = "获取角色下拉列表")
    @Override
    public List<RoleDto> getRoleSelectList(String roleCode) {
        return roleService.getRolestList(roleCode);
    }
}
