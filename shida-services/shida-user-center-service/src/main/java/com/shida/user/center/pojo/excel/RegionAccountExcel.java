package com.shida.user.center.pojo.excel;

import com.shida.excel.ExcelAlias;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@Schema(name = "RegionAccountExcel", description = "导出的RegionExcel对象")
public class RegionAccountExcel implements Serializable {
    /**
     * 账户ID
     */
    private Long id;

    @ExcelAlias("序号")
    private Integer index;

    @ExcelAlias("账户名")
    private String username;

    @ExcelAlias("默认密码")
    private String defaultPassword;

    @ExcelAlias("真实姓名")
    private String nickname;

    @ExcelAlias("手机号")
    private String mobile;

    @ExcelAlias("电子邮箱")
    private String email;

    @ExcelAlias("角色")
    private String roleName;

    @ExcelAlias("区县名称")
    private String regionName;

    @ExcelAlias("区县编码")
    private String regionCode;

}
