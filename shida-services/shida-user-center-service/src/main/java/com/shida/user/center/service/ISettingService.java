package com.shida.user.center.service;

import com.shida.user.center.entity.Setting;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 设置项信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-02
 */
public interface ISettingService extends IService<Setting> {

    <T> T getSettingByCode(String code, Class<T> classType);

    String saveOrUpdateSetting(String code, String name, Object content,Integer orderNumber);
}
