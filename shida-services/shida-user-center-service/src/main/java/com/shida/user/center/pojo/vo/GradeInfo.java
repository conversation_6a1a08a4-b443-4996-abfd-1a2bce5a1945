package com.shida.user.center.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

@Setter
@Getter
@Schema(name = "GradeInfo", description = "GradeInfo")
public class GradeInfo implements Serializable {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "年份")
    private Integer year;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}
