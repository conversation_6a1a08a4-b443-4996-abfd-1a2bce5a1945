package com.shida.user.center.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.noRepeatSubmit.NoRepeatSubmit;
import com.shida.pojo.form.KeyForm;
import com.shida.pojo.search.Search;
import com.shida.pojo.vo.PageInfo;
import com.shida.pojo.vo.MenuInfo;
import com.shida.pojo.vo.PermissionInfo;
import com.shida.user.center.pojo.form.MenuForm;
import com.shida.user.center.pojo.form.RolePermissionForm;
import com.shida.user.center.service.IMenuService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@Tag(name = "menu-菜单管理")
@RestController
@RequestMapping("/menu/manage")
public class MenuManageController {
    @Resource
    private IMenuService menuService;

    @Operation(summary = "获取菜单列表")
    @SaCheckPermission("menu:list")
    @PostMapping("/getPageData")
    public PageInfo<MenuInfo> getPageData(@RequestBody Search search) {
        return menuService.getAllMenes(search);
    }

    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_ADD)
    @Operation(summary = "创建菜单")
    @SaCheckPermission("menu:add")
    @PostMapping("/create")
    public String create(@RequestBody @Valid MenuForm menuForm) {
        return menuService.createMenu(menuForm);
    }

    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    @Operation(summary = "修改菜单")
    @SaCheckPermission("menu:edit")
    @PostMapping("/update")
    public String update(@RequestBody @Valid MenuForm menuForm) {
        return menuService.updateMenu(menuForm);
    }


    @NoRepeatSubmit
    @SaCheckPermission("menu:delete")
    @AutoLog(operateType = OperateType.OPERATE_DELETE)
    @Operation(summary = "删除菜单")
    @PostMapping("/delete")
    public String delete(@RequestBody @Valid KeyForm<Long> form) {
        return menuService.deleteById(form.getKey());
    }

    @SaCheckPermission("menu:list")
    @Operation(summary = "根据角色id获取权限")
    @PostMapping("/getPermissionsByRoleId")
    public List<PermissionInfo> getPermissionsByRoleId(@RequestBody @Valid KeyForm<Long> form) {
        return menuService.getPermissionsByRoleId(form.getKey());
    }

    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    @SaCheckPermission("role:change:permissions")
    @Operation(summary = "修改角色权限")
    @PostMapping("/changeRolePermissions")
    public String changeRolePermissions(@RequestBody @Valid RolePermissionForm form) {
        return menuService.changeRolePermissions(form);
    }

}
