package com.shida.user.center.pojo.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
@Accessors(chain = true)
@Schema(name = "MenuForm",description = "菜单表单")
public class MenuForm implements Serializable {
    @Schema(description = "菜单id")
    private Long id;
    @Schema(description = "父级菜单id")
    private Long parentId;
    @Schema(description = "菜单名称")
    @NotBlank(message = "菜单名称不能为空")
    @Size(max = 32,message = "菜单名称不能超过32个字符")
    private String name;
    @Schema(description = "菜单编码")
    @NotBlank(message = "菜单编码不能为空")
    @Size(max = 32,message = "菜单编码不能超过32个字符")
    private String code;
    @Schema(description = "菜单类型")
    @NotBlank(message = "菜单类型不能为空")
    private String type;
    @Schema(description = "菜单权限")
    @NotBlank(message = "菜单权限不能为空")
    @Size(max = 32,message = "菜单权限不能超过32个字符")
    private String permission;
    @Schema(description = "菜单路由")
    @Size(max = 64,message = "菜单路由不能超过64个字符")
    private String router;
    @Schema(description = "菜单组件")
    @Size(max = 128,message = "菜单组件不能超过128个字符")
    private String component;
    @Schema(description = "是否显示")
    @NotNull(message = "是否显示不能为空")
    private Integer visible;
    @Schema(description = "排序")
    @NotNull(message = "排序不能为空")
    private Integer orderNum;
}
