package com.shida.user.center.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * Description:
 *
 * <AUTHOR>
 * @Create 2025/5/27 9:34
 */
@Data
@Schema(name = "NationVo",description = "民族返回类")
@Accessors(chain = true)
public class NationVo implements Serializable {
    private static final long serialVersionUID = 1L;
    @Schema(description = "id")
    private Integer id;
    @Schema(description = "民族")
    private String name;
}
