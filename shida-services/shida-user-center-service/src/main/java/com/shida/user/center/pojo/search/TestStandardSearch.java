package com.shida.user.center.pojo.search;

import com.shida.pojo.search.Search;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "TestStandardSearch", description = "体质健康测试评分标准搜索条件")
public class TestStandardSearch extends Search {

    @Schema(description = "业务编码")
    private String code;

    @Schema(description = "项目标题")
    private String title;

    @Schema(description = "年级")
    private String gradeName;

    @Schema(description = "等级")
    private String level;

    @Schema(description = "性别（1：男生，2：女生）")
    private String gender;
}
