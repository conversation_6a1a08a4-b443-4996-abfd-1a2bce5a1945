package com.shida.user.center.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
@Schema(name ="OrgInfo" ,description = "机构信息")
public class OrgInfo implements Serializable {
    @Schema(description = "机构id")
    private Long id;

    @Schema(description = "机构名称")
    private String name;

    @Schema(description = "机构代码")
    private String code;

    @Schema(description = "父机构代码")
    private Long parentId;

    @Schema(description = "描述信息")
    private String description;

    @Schema(description = "层级，1市，2区县，3学校")
    private Integer floor;

    @Schema(description = "联系人")
    private String contact;

    @Schema(description = "联系人职位")
    private String position;

    @Schema(description = "联系人电话")
    private String telephone;

    @Schema(description = "联系人手机号码")
    private String mobile;

    @Schema(description = "机构管理员")
    private List<UserInfo> masters;
}
