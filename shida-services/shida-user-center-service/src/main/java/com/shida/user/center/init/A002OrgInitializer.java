package com.shida.user.center.init;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.shida.user.center.entity.Org;
import com.shida.user.center.service.IOrgService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Order(2)
@Component
public class A002OrgInitializer implements ApplicationRunner {
    @Resource
    private IOrgService orgService;
    @Resource
    RedissonClient redissonClient;

    @Override
    public void run(ApplicationArguments args) {
        String json = "[{\"code\":\"130100\",\"name\":\"石家庄市\",\"description\":\"石家庄市\",\"id\":130100,\"floor\":1}," +
                "{\"code\":\"130102\",\"name\":\"长安区\",\"description\":\"长安区\",\"id\":130102,\"parentId\":130100,\"floor\":2}," +
                "{\"code\":\"130104\",\"name\":\"桥西区\",\"description\":\"桥西区\",\"id\":130104,\"parentId\":130100,\"floor\":2}," +
                "{\"code\":\"130105\",\"name\":\"新华区\",\"description\":\"新华区\",\"id\":130105,\"parentId\":130100,\"floor\":2}," +
                "{\"code\":\"130171\",\"name\":\"高新区\",\"description\":\"高新区\",\"id\":130171,\"parentId\":130100,\"floor\":2}," +
                "{\"code\":\"130108\",\"name\":\"裕华区\",\"description\":\"裕华区\",\"id\":130108,\"parentId\":130100,\"floor\":2}," +
                "{\"code\":\"130109\",\"name\":\"藁城区\",\"description\":\"藁城区\",\"id\":130109,\"parentId\":130100,\"floor\":2}," +
                "{\"code\":\"130111\",\"name\":\"栾城区\",\"description\":\"栾城区\",\"id\":130111,\"parentId\":130100,\"floor\":2}," +
                "{\"code\":\"130110\",\"name\":\"鹿泉区\",\"description\":\"鹿泉区\",\"id\":130110,\"parentId\":130100,\"floor\":2}," +
                "{\"code\":\"130121\",\"name\":\"井陉县\",\"description\":\"井陉县\",\"id\":130121,\"parentId\":130100,\"floor\":2}," +
                "{\"code\":\"130107\",\"name\":\"井陉矿区\",\"description\":\"井陉县\",\"id\":130107,\"parentId\":130100,\"floor\":2}," +
                "{\"code\":\"130123\",\"name\":\"正定县\",\"description\":\"正定县\",\"id\":130123,\"parentId\":130100,\"floor\":2}," +
                "{\"code\":\"130125\",\"name\":\"行唐县\",\"description\":\"行唐县\",\"id\":130125,\"parentId\":130100,\"floor\":2}," +
                "{\"code\":\"130126\",\"name\":\"灵寿县\",\"description\":\"灵寿县\",\"id\":130126,\"parentId\":130100,\"floor\":2}," +
                "{\"code\":\"130127\",\"name\":\"高邑县\",\"description\":\"高邑县\",\"id\":130127,\"parentId\":130100,\"floor\":2}," +
                "{\"code\":\"130128\",\"name\":\"深泽县\",\"description\":\"深泽县\",\"id\":130128,\"parentId\":130100,\"floor\":2}," +
                "{\"code\":\"130129\",\"name\":\"赞皇县\",\"description\":\"赞皇县\",\"id\":130129,\"parentId\":130100,\"floor\":2}," +
                "{\"code\":\"130130\",\"name\":\"无极县\",\"description\":\"无极县\",\"id\":130130,\"parentId\":130100,\"floor\":2}," +
                "{\"code\":\"130131\",\"name\":\"平山县\",\"description\":\"平山县\",\"id\":130131,\"parentId\":130100,\"floor\":2}," +
                "{\"code\":\"130132\",\"name\":\"元氏县\",\"description\":\"元氏县\",\"id\":130132,\"parentId\":130100,\"floor\":2}," +
                "{\"code\":\"130133\",\"name\":\"赵县\",\"description\":\"赵县\",\"id\":130133,\"parentId\":130100,\"floor\":2}," +
                "{\"code\":\"130181\",\"name\":\"辛集市\",\"description\":\"辛集市\",\"id\":130181,\"parentId\":130100,\"floor\":2}," +
                "{\"code\":\"130183\",\"name\":\"晋州市\",\"description\":\"晋州市\",\"id\":130183,\"parentId\":130100,\"floor\":2}," +
                "{\"code\":\"130184\",\"name\":\"新乐市\",\"description\":\"新乐市\",\"id\":130184,\"parentId\":130100,\"floor\":2}]";

        RLock lock = redissonClient.getLock("lockers:A002OrgInitializer");
        if (lock.tryLock()) {
            try {
                JSONArray jsonArray = JSONUtil.parseArray(json);
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject object = jsonArray.getJSONObject(i);
                    Org org = JSONUtil.toBean(object, Org.class);
                    orgService.saveOrUpdate(org);
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                lock.unlock();
            }
        }
    }
}
