package com.shida.user.center.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shida.pojo.search.Search;
import com.shida.pojo.vo.RoleInfo;
import com.shida.user.center.entity.Role;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shida.user.center.entity.RoleMenu;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 角色信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface RoleMapper extends BaseMapper<Role> {

    IPage<RoleInfo> getPageData(IPage<RoleInfo> objectPage, @Param("search") Search search);

    List<RoleInfo> getUserRolesByUserId(Long userId);

    List<RoleMenu> getRoleMenusByRoleCode(String sysRoleCode);


}
