package com.shida.user.center.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

@Data
@Accessors(chain = true)
@Schema(name = "TestStandardMatrixInfo", description = "矩阵式体质健康测试标准数据信息")
public class TestStandardMatrixInfo implements Serializable {
    
    @Schema(description = "等级")
    private String level;
    
    @Schema(description = "分数")
    private Integer score;
    
    @Schema(description = "各年级数值范围，key为年级，value为数值范围字符串")
    private Map<String, String> gradeValues;
    
    @Schema(description = "各年级原始数值，key为年级，value为原始数值对象")
    private Map<String, GradeValueData> gradeRawValues;
    
    @Data
    @Accessors(chain = true)
    public static class GradeValueData implements Serializable {
        @Schema(description = "记录ID")
        private Long id;
        
        @Schema(description = "最小值")
        private BigDecimal minValue;
        
        @Schema(description = "最大值")
        private BigDecimal maxValue;
        
        @Schema(description = "格式化显示值")
        private String displayValue;
        
        @Schema(description = "是否为无上限（最大值为999.9时表示无上限）")
        private Boolean isMaxValueUnlimited = false;
    }
}