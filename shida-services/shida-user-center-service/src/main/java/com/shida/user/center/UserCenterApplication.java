package com.shida.user.center;

import com.shida.utils.IpAddressUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;

@Slf4j
@SpringBootApplication
@ComponentScan(basePackages = "com.shida")
@EnableFeignClients(basePackages = "com.shida")
public class UserCenterApplication {
    public static void main(String[] args) {
        ConfigurableApplicationContext context = SpringApplication.run(UserCenterApplication.class, args);
        String port = context.getEnvironment().getProperty("server.port");
        // 获取本机IP地址
        String ipaddress = IpAddressUtil.getLocalIp();
        log.debug("UserCenter已启动，doc地址：http://{}:{}/doc.html", ipaddress, port);
    }
}