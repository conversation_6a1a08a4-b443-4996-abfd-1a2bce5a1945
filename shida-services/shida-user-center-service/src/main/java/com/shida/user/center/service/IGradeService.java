package com.shida.user.center.service;

import com.shida.pojo.search.Search;
import com.shida.pojo.vo.KeyValue;
import com.shida.pojo.vo.PageInfo;
import com.shida.user.center.entity.Grade;
import com.baomidou.mybatisplus.extension.service.IService;
import com.shida.user.center.pojo.form.GradeForm;
import com.shida.user.center.pojo.vo.GradeInfo;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
public interface IGradeService extends IService<Grade> {

    PageInfo<GradeInfo> getPageData(Search search);

    String create(GradeForm form);

    String updateGrade(GradeForm form);

    String deleteById(Long id);

    List<KeyValue<String, String>> getXueNianList();

    List<KeyValue<Long, String>> getGradeSelectList();
}
