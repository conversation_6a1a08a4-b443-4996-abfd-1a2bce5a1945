package com.shida.user.center.pojo.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
@Accessors(chain = true)
@Schema(name = "GradeForm", description = "年级表单")
public class GradeForm implements Serializable {
    @Schema(description = "id，更新时必填")
    private Long id;

    @Schema(description = "年份")
    @NotNull(message = "请填写年份")
    private Integer year;

    @Schema(description = "名称")
    @NotBlank(message = "请填写名称")
    @Size(max = 10, message = "名称长度不能超过10")
    private String name;
}
