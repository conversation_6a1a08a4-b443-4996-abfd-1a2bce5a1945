package com.shida.user.center.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shida.IFeignClassService;
import com.shida.enums.RestCode;
import com.shida.exceptions.ServiceException;
import com.shida.pojo.search.Search;
import com.shida.pojo.vo.KeyValue;
import com.shida.pojo.vo.PageInfo;
import com.shida.user.center.entity.Grade;
import com.shida.user.center.entity.Org;
import com.shida.user.center.mapper.GradeMapper;
import com.shida.user.center.pojo.form.GradeForm;
import com.shida.user.center.pojo.vo.GradeInfo;
import com.shida.user.center.service.IGradeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shida.user.center.service.IOrgService;
import io.seata.spring.annotation.GlobalTransactional;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Service
public class GradeServiceImpl extends ServiceImpl<GradeMapper, Grade> implements IGradeService {

    @Resource
    private IFeignClassService feignClassService;

    @Override
    public PageInfo<GradeInfo> getPageData(Search search) {
        IPage<Grade> page = new Page<>(search.getPageNumber(), search.getPageSize());
        page = super.page(page, new LambdaQueryWrapper<Grade>().orderByDesc(Grade::getYear));
        return PageInfo.fromMybatisPage(page, GradeInfo.class);
    }

    @Override
    public String create(GradeForm form) {
        Grade grade = super.getOne(new LambdaQueryWrapper<Grade>().eq(Grade::getYear, form.getYear()));
        if (grade != null) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "该年级入学年份已存在");
        }
        grade = BeanUtil.copyProperties(form, Grade.class);
        grade.setId(Long.valueOf(form.getYear()));
        grade.insert();
        return "success";
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public String updateGrade(GradeForm form) {
        if (form.getId() == null) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "id不能为空");
        }
        Grade grade = super.getOne(new LambdaQueryWrapper<Grade>().eq(Grade::getYear, form.getYear())
                .ne(Grade::getId, form.getId()));
        if (grade != null) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "该年级入学年份已存在");
        }
        grade = super.getById(form.getId());
        Integer oldYear = grade.getYear();
        grade = BeanUtil.copyProperties(form, Grade.class);
        grade.updateById();
        if (!oldYear.equals(form.getYear())) {
            if (feignClassService.checkHasGradeClass(form.getId())) {
                throw new ServiceException(RestCode.ARGUMENT_ERROR, "该年级已存在班级信息，不能进行修改！");
            }
        }
        return "success";
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public String deleteById(Long id) {
        Grade grade = super.getById(id);
        if (grade == null) {
            return "success";
        }
        if (!feignClassService.checkHasGradeClass(id)) {
            grade.deleteById();
        } else {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "该年级已经存在班级数据，不可删除，请知悉！");
        }
        return "success";
    }

    @Override
    public List<KeyValue<String, String>> getXueNianList() {
        List<Grade> gradeList = super.list(new LambdaQueryWrapper<Grade>().select(Grade::getYear).orderByAsc(Grade::getYear));
        List<KeyValue<String, String>> result = new ArrayList<>();
        if (gradeList.isEmpty()) {
            return Collections.emptyList();
        }
        Grade minGrade = gradeList.get(0);
        Grade MaxGrade = gradeList.get(gradeList.size() - 1);
        for (int i = minGrade.getYear(); i <= MaxGrade.getYear(); i++) {
            KeyValue<String, String> keyValue = new KeyValue<>();
            keyValue.setKey(i + "~" + (i + 1)).setValue(i + "~" + (i + 1));
            result.add(keyValue);
        }
        return result;
    }

    @Override
    public List<KeyValue<Long, String>> getGradeSelectList() {
        List<Grade> grades = super.list();
        return grades.stream().map(grade -> new KeyValue<Long,String>(grade.getId(), grade.getName())).collect(Collectors.toList());
    }
}
