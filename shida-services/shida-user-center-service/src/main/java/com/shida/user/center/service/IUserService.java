package com.shida.user.center.service;

import com.shida.pojo.vo.PageInfo;
import com.shida.user.center.entity.User;
import com.baomidou.mybatisplus.extension.service.IService;
import com.shida.user.center.pojo.form.UserForm;
import com.shida.user.center.pojo.search.UserSearch;
import com.shida.user.center.pojo.vo.NationVo;
import com.shida.user.center.pojo.vo.UserInfo;
import com.shida.userCenter.dto.ChangeUserPasswordDto;
import com.shida.userCenter.dto.CreateUserDto;
import com.shida.userCenter.dto.RoleDto;
import com.shida.userCenter.dto.UserDto;

import java.util.List;

/**
 * <p>
 * 账户信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface IUserService extends IService<User> {

    User findByUsername(String username);

    UserDto findUserDtoByUsername(String username);

    UserDto findUserDtoByUserId(Long userId);

    List<UserDto> findUserDtoList(List<Long> userIds);

    String changeUserPassword(ChangeUserPasswordDto dto);

    PageInfo<UserInfo> getPageData(UserSearch search);

    String changeUserStatus(Long userId, Integer status);

    String updateUserInfo(UserForm form);

    String resetPassword(Long userId);

    String createUser(UserForm form);

    UserDto createUser(UserDto userDto);

    List<UserDto> getMasterUserListBySchoolId(Long regionId, Long schoolId);

    List<UserDto> getMasterUserListBySchoolIds(Long regionId, List<Long> schoolIds);

    String deleteUser(Long userId);

    List<UserDto> findListByUsernames(List<String> usernameList);

    List<RoleDto> getUserRoleDtoList(List<Long> userIds);

    List<NationVo> getNationVoList();
}
