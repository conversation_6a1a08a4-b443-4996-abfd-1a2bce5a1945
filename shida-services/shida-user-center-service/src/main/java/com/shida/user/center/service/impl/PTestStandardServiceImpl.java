package com.shida.user.center.service.impl;

import cn.hutool.core.io.IoUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shida.AccountCommonService;
import com.shida.enums.RestCode;
import com.shida.exceptions.ServiceException;
import com.shida.pojo.vo.AccountInfo;
import com.shida.pojo.vo.PageInfo;
import com.shida.user.center.entity.PTestStandard;
import com.shida.user.center.mapper.PTestStandardMapper;
import com.shida.user.center.pojo.search.TestStandardSearch;
import com.shida.user.center.pojo.vo.TestStandardInfo;
import com.shida.user.center.service.IPTestStandardService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import org.apache.poi.ss.usermodel.*;
import org.springframework.web.multipart.MultipartFile;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;

import java.util.*;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;

/**
 * <p>
 * 通用体质健康测试评分标准表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-02
 */
@Service
public class PTestStandardServiceImpl extends ServiceImpl<PTestStandardMapper, PTestStandard> implements IPTestStandardService {

    @Resource
    private PTestStandardMapper testStandardMapper;

    @Override
    public PageInfo<TestStandardInfo> getPageData(TestStandardSearch search) {
        // 不使用分页，查询所有数据
        IPage<TestStandardInfo> page = new Page<>(1, 1000);
        IPage<TestStandardInfo> result = testStandardMapper.getPageData(page, search);
        return PageInfo.fromMybatisPage(result);
    }


    @Override
    public boolean updateTestStandard(TestStandardInfo testStandardInfo) {
        try {
            // 数据验证
            if (testStandardInfo.getId() == null) {
                throw new ServiceException(RestCode.DATA_ERROR, "ID不能为空");
            }

            // 验证分数（0-100的整数）
            if (testStandardInfo.getScore() != null) {
                if (testStandardInfo.getScore() < 0 || testStandardInfo.getScore() > 100) {
                    throw new ServiceException(RestCode.DATA_ERROR, "分数必须在0-100之间");
                }
            }

            // 验证最小值和最大值格式（只允许一位小数）
            if (testStandardInfo.getMinValue() != null) {
                validateDecimalFormat(testStandardInfo.getMinValue().toString(), "最小值");
            }
            if (testStandardInfo.getMaxValue() != null) {
                validateDecimalFormat(testStandardInfo.getMaxValue().toString(), "最大值");
            }

            // 验证最小值小于最大值
            if (testStandardInfo.getMinValue() != null && testStandardInfo.getMaxValue() != null) {
                if (testStandardInfo.getMinValue().compareTo(testStandardInfo.getMaxValue()) >= 0) {
                    throw new ServiceException(RestCode.DATA_ERROR, "最小值必须小于最大值");
                }
            }

            // 构建更新实体，只设置需要更新的字段
            PTestStandard updateEntity = new PTestStandard();
            updateEntity.setId(testStandardInfo.getId());
            updateEntity.setScore(testStandardInfo.getScore());
            updateEntity.setMinValue(testStandardInfo.getMinValue());
            updateEntity.setMaxValue(testStandardInfo.getMaxValue());

            // 执行更新，使用updateById方法
            boolean result = this.updateById(updateEntity);

            System.out.println("更新结果: " + result + ", ID: " + testStandardInfo.getId());
            return result;
        } catch (Exception e) {
            // 记录日志并抛出异常给前端处理
            System.out.println("更新体质健康测试评分标准失败: " + e.getMessage());
            e.printStackTrace();
            throw new ServiceException(RestCode.DATA_ERROR, "更新失败: " + e.getMessage());
        }
    }

    /**
     * 验证小数格式（只允许一位小数）
     */
    private void validateDecimalFormat(String value, String fieldName) {
        if (value == null || value.trim().isEmpty()) {
            return;
        }

        // 正则表达式：整数或最多一位小数
        String regex = "^\\d+(\\.\\d{1})?$";
        if (!value.matches(regex)) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR,fieldName + "格式不正确，只允许整数或最多一位小数");
        }

        // 验证数值范围
        try {
            double doubleValue = Double.parseDouble(value);
//            if (doubleValue < 0) {
//                throw new ServiceException(RestCode.ARGUMENT_ERROR,fieldName + "不能为负数");
//            }
            if (doubleValue > 10000) {
                throw new ServiceException(RestCode.ARGUMENT_ERROR,fieldName + "不能超过10000");
            }
        } catch (NumberFormatException e) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR,fieldName + "格式不正确");
        }
    }

    @Override
    public void importTestStandard(MultipartFile file, String code) {
        try {
            // 获取当前用户信息
            AccountInfo accountInfo = AccountCommonService.getCurrentUser();
            Long userId = accountInfo.getId();
            String genderText = code.startsWith("1") ? "男生" : "女生";

            System.out.println("用户 " + userId + " 开始导入" + genderText + "体质健康测试评分标准，code: " + code);

            // 1. 验证文件格式
            if (file.isEmpty()) {
                throw new ServiceException(RestCode.DATA_ERROR, "上传文件为空");
            }

            String fileName = file.getOriginalFilename();
            if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
                throw new ServiceException(RestCode.DATA_ERROR, "文件格式不正确，只支持Excel格式(.xlsx, .xls)");
            }

            // 2. 解析Excel文件
            Workbook workbook;
            try {
                workbook = WorkbookFactory.create(file.getInputStream());
            } catch (IOException e) {
                throw new ServiceException(RestCode.DATA_ERROR, "Excel文件解析失败: " + e.getMessage());
            }

            Sheet sheet = workbook.getSheetAt(0);
            if (sheet == null) {
                workbook.close();
                throw new ServiceException(RestCode.DATA_ERROR, "Excel文件中没有找到工作表");
            }

            // 验证标题行
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {
                workbook.close();
                throw new ServiceException(RestCode.DATA_ERROR, "Excel文件缺少标题行");
            }

            // 验证列数和标题
            String[] expectedHeaders = {"序号", "年级", "等级", "分数", "最小值", "最大值", "单位"};
            if (headerRow.getLastCellNum() < expectedHeaders.length) {
                workbook.close();
                throw new ServiceException(RestCode.DATA_ERROR, "Excel文件列数不足，期望" + expectedHeaders.length + "列");
            }

            // 3. 先删除所有符合当前code的数据
            QueryWrapper<PTestStandard> deleteWrapper = new QueryWrapper<PTestStandard>()
                .eq("code", code);
            long deletedCount = this.count(deleteWrapper);
            this.remove(deleteWrapper);
            System.out.println("删除了 " + deletedCount + " 条现有数据，code: " + code);

            // 4. 解析数据并新增
            List<String> updateResults = new ArrayList<>();
            int successCount = 0;
            int errorCount = 0;
            List<PTestStandard> newDataList = new ArrayList<>();

            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;

                try {
                    // 解析行数据
                    String gradeName = getCellStringValue(row.getCell(1));
                    String level = getCellStringValue(row.getCell(2));
                    String scoreStr = getCellStringValue(row.getCell(3));
                    String minValueStr = getCellStringValue(row.getCell(4));
                    String maxValueStr = getCellStringValue(row.getCell(5));
                    String unit = getCellStringValue(row.getCell(6));

                    // 验证必填字段
                    if (gradeName.trim().isEmpty() || level.trim().isEmpty()) {
                        errorCount++;
                        updateResults.add("第" + (i + 1) + "行：年级和等级不能为空");
                        continue;
                    }

                    if (scoreStr.trim().isEmpty() || minValueStr.trim().isEmpty() || maxValueStr.trim().isEmpty()) {
                        errorCount++;
                        updateResults.add("第" + (i + 1) + "行：分数、最小值、最大值不能为空");
                        continue;
                    }

                    // 转换数据类型
                    Integer score;
                    BigDecimal minValue;
                    BigDecimal maxValue;

                    try {
                        score = Integer.parseInt(scoreStr.trim());
                        minValue = new BigDecimal(minValueStr.trim());
                        maxValue = new BigDecimal(maxValueStr.trim());
                    } catch (NumberFormatException e) {
                        errorCount++;
                        updateResults.add("第" + (i + 1) + "行：数值格式错误");
                        continue;
                    }

                    // 验证数据范围
                    if (score < 0 || score > 100) {
                        errorCount++;
                        updateResults.add("第" + (i + 1) + "行：分数必须在0-100之间");
                        continue;
                    }

                    if (minValue.compareTo(maxValue) >= 0) {
                        errorCount++;
                        updateResults.add("第" + (i + 1) + "行：最小值必须小于最大值");
                        continue;
                    }

                    // 5. 创建新记录并添加到批量插入列表
                    PTestStandard newEntity = new PTestStandard();
                    newEntity.setCode(code);

                    // 根据code设置title
                    String title = genderText;
                    if (code.contains("TZZSDX")) {
                        title += "体重指数单项评分表";
                    } else if (code.contains("FHLDX")) {
                        title += "肺活量单项评分表";
                    } else {
                        title += "体质健康测试评分标准";
                    }
                    newEntity.setTitle(title);

                    newEntity.setGradeName(gradeName.trim());
                    newEntity.setLevel(level.trim());
                    newEntity.setScore(score);
                    newEntity.setMinValue(minValue);
                    newEntity.setMaxValue(maxValue);
                    newEntity.setUnit(unit.trim().isEmpty() ? "毫升" : unit.trim());

                    newDataList.add(newEntity);
                    successCount++;
                    updateResults.add("第" + (i + 1) + "行：" + gradeName + "-" + level + " 准备新增");

                } catch (Exception e) {
                    errorCount++;
                    updateResults.add("第" + (i + 1) + "行：处理异常 - " + e.getMessage());
                }
            }

            workbook.close();

            // 6. 批量插入新数据
            if (!newDataList.isEmpty()) {
                boolean batchSaveResult = this.saveBatch(newDataList);
                if (!batchSaveResult) {
                    throw new ServiceException(RestCode.DATA_ERROR, "批量插入数据失败");
                }
                System.out.println("批量插入 " + newDataList.size() + " 条新数据成功");
            }

            // 输出导入结果
            String resultMessage = String.format("用户 %s 导入%s体质健康测试评分标准完成：成功 %d 条，失败 %d 条",
                                                userId, genderText, successCount, errorCount);
            System.out.println(resultMessage);

            if (errorCount > 0) {
                System.out.println("导入详情：");
                updateResults.forEach(System.out::println);
            }

            if (successCount == 0 && newDataList.isEmpty()) {
                throw new ServiceException(RestCode.DATA_ERROR, "导入失败，没有成功处理任何记录");
            }

        } catch (Exception e) {
            throw new ServiceException(RestCode.DATA_ERROR, "导入失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取单元格字符串值
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // 处理数字，避免科学计数法
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == Math.floor(numericValue)) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    @Override
    public void downloadTemplate(String code, HttpServletResponse response) {
        try {
            // 获取当前用户信息
            AccountInfo accountInfo = AccountCommonService.getCurrentUser();
            Long userId = accountInfo.getId();

            // 查询现有数据
            TestStandardSearch search = new TestStandardSearch();
            search.setCode(code);
            PageInfo<TestStandardInfo> pageInfo = getPageData(search);
            List<TestStandardInfo> dataList = pageInfo.getRecords();

            // 从数据中获取title作为文件名（去重）
            String fileTitle = "体质健康测试评分标准";
            if (!dataList.isEmpty()) {
                // 获取第一条记录的title作为文件名
                String title = dataList.get(0).getTitle();
                if (title != null && !title.trim().isEmpty()) {
                    fileTitle = title.trim();
                }
            }

            System.out.println("用户 " + userId + " 下载" + fileTitle + "数据，code: " + code);

            // 使用ExcelUtil创建Excel写入器
            ExcelWriter excelWriter = ExcelUtil.getBigWriter();

            // 构建表头别名
            excelWriter.addHeaderAlias("序号", "序号");
            excelWriter.addHeaderAlias("gradeName", "年级");
            excelWriter.addHeaderAlias("level", "等级");
            excelWriter.addHeaderAlias("score", "分数");
            excelWriter.addHeaderAlias("minValue", "最小值");
            excelWriter.addHeaderAlias("maxValue", "最大值");
            excelWriter.addHeaderAlias("unit", "单位");

            // 设置只使用别名
            excelWriter.setOnlyAlias(true);

            // 构建数据列表
            List<Map<String, Object>> exportData = new ArrayList<>();
            for (int i = 0; i < dataList.size(); i++) {
                TestStandardInfo data = dataList.get(i);
                Map<String, Object> rowData = new HashMap<>();
                rowData.put("序号", i + 1);
                rowData.put("gradeName", data.getGradeName() != null ? data.getGradeName() : "");
                rowData.put("level", data.getLevel() != null ? data.getLevel() : "");
                rowData.put("score", data.getScore() != null ? data.getScore() : 0);
                rowData.put("minValue", data.getMinValue() != null ? data.getMinValue() : 0.0);
                rowData.put("maxValue", data.getMaxValue() != null ? data.getMaxValue() : 0.0);
                rowData.put("unit", data.getUnit() != null ? data.getUnit() : "千克");
                exportData.add(rowData);
            }

            // 写入数据
            excelWriter.write(exportData);

            // 导出到响应
            exportToResponse(response, excelWriter, fileTitle + "导入模版.xlsx");

            System.out.println("用户 " + userId + " 成功下载" + fileTitle + "数据");

        } catch (Exception e) {
            throw new ServiceException(RestCode.EXCEL_REPORT_ERROR);
        }
    }

    /**
     * 导出Excel到响应流
     */
    /**
     * 导出 Excel 到前端响应
     */
    private void exportToResponse(HttpServletResponse response, ExcelWriter excelWriter, String fileName) {
        ServletOutputStream out = null;
        try {
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            fileName = java.net.URLEncoder.encode(fileName, "utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            out = response.getOutputStream();
            excelWriter.flush(out, true);
        } catch (Exception e) {
            throw new ServiceException(RestCode.EXCEL_REPORT_ERROR);
        } finally {
            excelWriter.close();
            IoUtil.close(out);
        }
    }
}
