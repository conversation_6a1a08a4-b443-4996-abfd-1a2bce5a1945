package com.shida.user.center.service.impl;

import cn.hutool.core.io.IoUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shida.AccountCommonService;
import com.shida.pojo.vo.KeyValue;
import com.shida.enums.RestCode;
import com.shida.exceptions.ServiceException;
import com.shida.pojo.vo.AccountInfo;
import com.shida.pojo.vo.PageInfo;
import com.shida.user.center.entity.PTestStandard;
import com.shida.user.center.mapper.PTestStandardMapper;
import com.shida.user.center.pojo.search.TestStandardSearch;
import com.shida.user.center.pojo.vo.TestStandardInfo;
import com.shida.user.center.pojo.vo.TestStandardMatrixInfo;
import com.shida.user.center.service.IPTestStandardService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.usermodel.Cell;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 通用体质健康测试评分标准表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-02
 */
@Service
@Slf4j
public class PTestStandardServiceImpl extends ServiceImpl<PTestStandardMapper, PTestStandard> implements IPTestStandardService {

    @Resource
    private PTestStandardMapper testStandardMapper;

    @Override
    public PageInfo<TestStandardInfo> getPageData(TestStandardSearch search) {
        // 不使用分页，查询所有数据
        IPage<TestStandardInfo> page = new Page<>(1, 1000);
        IPage<TestStandardInfo> result = testStandardMapper.getPageData(page, search);
        return PageInfo.fromMybatisPage(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TestStandardMatrixInfo> getMatrixData(TestStandardSearch search) {
        // 查询所有数据
        IPage<TestStandardInfo> page = new Page<>(1, 1000);
        IPage<TestStandardInfo> result = testStandardMapper.getPageData(page, search);
        List<TestStandardInfo> dataList = result.getRecords();

        // 从数据库中动态获取实际存在的年级，并按标准顺序排序
        Set<String> existingGradesSet = dataList.stream()
            .map(TestStandardInfo::getGradeName)
            .filter(grade -> grade != null && !grade.trim().isEmpty())
            .collect(Collectors.toSet());

        // 定义标准年级顺序用于排序
        List<String> standardGradeOrder = Arrays.asList(
            "一年级", "二年级", "三年级", "四年级", "五年级", "六年级",
            "初一", "初二", "初三", "高一", "高二", "高三"
        );

        // 按标准顺序过滤出实际存在的年级
        List<String> gradeOrder = standardGradeOrder.stream()
            .filter(existingGradesSet::contains)
            .collect(Collectors.toList());

        System.out.println("数据库中实际存在的年级: " + existingGradesSet);
        System.out.println("按顺序排列的年级: " + gradeOrder);

        // 按等级和分数的组合进行分组，确保每个分数都有单独的行
        Map<String, List<TestStandardInfo>> scoreGroupMap = dataList.stream()
            .collect(Collectors.groupingBy(item -> item.getLevel() + "_" + item.getScore()));

        // 构建矩阵数据
        List<TestStandardMatrixInfo> matrixList = new ArrayList<>();

        // 从数据库中动态获取实际存在的等级和分数组合，并按分数从高到低排序
        List<TestStandardInfo> uniqueScoreItems = dataList.stream()
            .collect(Collectors.groupingBy(
                item -> item.getLevel() + "_" + item.getScore(),
                Collectors.collectingAndThen(Collectors.toList(), list -> list.get(0))
            ))
            .values()
            .stream()
            .sorted((a, b) -> Integer.compare(b.getScore(), a.getScore())) // 按分数从高到低排序
            .collect(Collectors.toList());

        System.out.println("数据库中实际存在的等级和分数组合（按分数排序）: ");
        uniqueScoreItems.forEach(item ->
            System.out.println("等级: " + item.getLevel() + ", 分数: " + item.getScore())
        );

        for (TestStandardInfo scoreItem : uniqueScoreItems) {
            String groupKey = scoreItem.getLevel() + "_" + scoreItem.getScore();
            if (scoreGroupMap.containsKey(groupKey)) {
                List<TestStandardInfo> scoreData = scoreGroupMap.get(groupKey);

                TestStandardMatrixInfo matrixInfo = new TestStandardMatrixInfo();
                matrixInfo.setLevel(scoreItem.getLevel());
                matrixInfo.setScore(scoreItem.getScore());

                // 构建年级数值映射
                Map<String, String> gradeValues = new LinkedHashMap<>();
                Map<String, TestStandardMatrixInfo.GradeValueData> gradeRawValues = new LinkedHashMap<>();

                // 按年级顺序填充数据
                for (String grade : gradeOrder) {
                    TestStandardInfo gradeData = scoreData.stream()
                        .filter(item -> grade.equals(item.getGradeName()))
                        .findFirst()
                        .orElse(null);

                    if (gradeData != null) {
                        String valueRange = formatValueRange(gradeData.getMinValue(), gradeData.getMaxValue());
                        gradeValues.put(grade, valueRange);

                        // 创建原始数值对象
                        TestStandardMatrixInfo.GradeValueData rawData = new TestStandardMatrixInfo.GradeValueData();

                        // 设置记录ID
                        rawData.setId(gradeData.getId());

                        // 设置原始数值（保持数据库的真实值）
                        BigDecimal minValue = gradeData.getMinValue();
                        BigDecimal maxValue = gradeData.getMaxValue();

                        rawData.setMinValue(minValue);
                        rawData.setMaxValue(maxValue);

                        // 添加一个标识字段来表示是否为无上限
                        if (maxValue != null && maxValue.compareTo(new BigDecimal("999.9")) == 0) {
                            rawData.setIsMaxValueUnlimited(true);
                        } else {
                            rawData.setIsMaxValueUnlimited(false);
                        }
                        rawData.setDisplayValue(valueRange);

                        gradeRawValues.put(grade, rawData);
                    } else {
                        gradeValues.put(grade, "-");

                        // 空数据的原始值对象
                        TestStandardMatrixInfo.GradeValueData rawData = new TestStandardMatrixInfo.GradeValueData();
                        rawData.setMinValue(null);
                        rawData.setMaxValue(null);
                        rawData.setDisplayValue("-");
                        gradeRawValues.put(grade, rawData);
                    }
                }

                matrixInfo.setGradeValues(gradeValues);
                matrixInfo.setGradeRawValues(gradeRawValues);
                matrixList.add(matrixInfo);
            }
        }

        return matrixList;
    }

    /**
     * 格式化数值范围
     */
    private String formatValueRange(BigDecimal minValue, BigDecimal maxValue) {
        if (minValue == null && maxValue == null) {
            return "-";
        }
        return minValue + "-" + maxValue;
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMatrixCell(TestStandardInfo testStandardInfo) {
        try {
            // 数据验证
            if (testStandardInfo.getId() == null) {
                throw new ServiceException(RestCode.DATA_ERROR, "记录ID不能为空");
            }

            // 验证最小值和最大值格式（只允许一位小数）
            if (testStandardInfo.getMinValue() != null) {
                validateDecimalFormat(testStandardInfo.getMinValue().toString(), "最小值");
            }
            if (testStandardInfo.getMaxValue() != null) {
                validateDecimalFormat(testStandardInfo.getMaxValue().toString(), "最大值");
            }

            // 验证最小值小于最大值
            if (testStandardInfo.getMinValue() != null && testStandardInfo.getMaxValue() != null) {
                if (testStandardInfo.getMinValue().compareTo(testStandardInfo.getMaxValue()) >= 0) {
                    throw new ServiceException(RestCode.DATA_ERROR, "最小值必须小于最大值");
                }
            }

            // 检查记录是否存在
            PTestStandard existingRecord = this.getById(testStandardInfo.getId());
            if (existingRecord == null) {
                throw new ServiceException(RestCode.DATA_ERROR, "未找到对应的记录，ID: " + testStandardInfo.getId());
            }

            // 构建更新实体，只设置需要更新的字段
            PTestStandard updateEntity = new PTestStandard();
            updateEntity.setId(testStandardInfo.getId());
            updateEntity.setMinValue(testStandardInfo.getMinValue());
            updateEntity.setMaxValue(testStandardInfo.getMaxValue());

            // 执行更新，使用updateById方法
            boolean result = this.updateById(updateEntity);

            System.out.println("更新矩阵单元格结果: " + result + ", ID: " + testStandardInfo.getId() +
                             ", MinValue: " + testStandardInfo.getMinValue() +
                             ", MaxValue: " + testStandardInfo.getMaxValue());
            return result;
        } catch (Exception e) {
            // 记录日志并抛出异常给前端处理
            System.out.println("更新矩阵单元格失败: " + e.getMessage());
            e.printStackTrace();
            throw new ServiceException(RestCode.DATA_ERROR, "更新失败: " + e.getMessage());
        }
    }

    /**
     * 敏感词汇列表
     */
    private static final List<String> SENSITIVE_WORDS = Arrays.asList(
        "反动", "政治", "暴力", "色情", "赌博", "毒品", "恐怖", "分裂",
        "法轮功", "台独", "藏独", "疆独", "港独", "民运", "六四",
        "习近平", "毛泽东", "邓小平", "江泽民", "胡锦涛", "温家宝",
        "李克强", "xi", "mao", "deng", "jiang", "hu", "wen", "li"
    );

    /**
     * 检查文本是否包含敏感词汇
     */
    private void validateSensitiveWords(String text, String fieldName) {
        if (text == null || text.trim().isEmpty()) {
            return;
        }

        String lowerText = text.toLowerCase();
        for (String sensitiveWord : SENSITIVE_WORDS) {
            if (lowerText.contains(sensitiveWord.toLowerCase())) {
                throw new ServiceException(RestCode.DATA_ERROR,
                    fieldName + "包含敏感词汇：" + sensitiveWord + "，请检查输入内容");
            }
        }
    }

    /**
     * 验证数据格式和内容
     */
    private void validateCellData(String value, String fieldName, String cellPosition) {
        if (value == null) {
            return;
        }

        String trimmedValue = value.trim();
        if (trimmedValue.isEmpty()) {
            return;
        }

        // 1. 检查敏感词汇
        validateSensitiveWords(trimmedValue, fieldName + "(" + cellPosition + ")");

        // 2. 检查长度限制
        if (trimmedValue.length() > 50) {
            throw new ServiceException(RestCode.DATA_ERROR,
                fieldName + "(" + cellPosition + ")长度不能超过50个字符");
        }

        // 3. 检查特殊字符
        if (trimmedValue.matches(".*[<>\"'&].*")) {
            throw new ServiceException(RestCode.DATA_ERROR,
                fieldName + "(" + cellPosition + ")包含非法字符，不允许使用 < > \" ' & 等字符");
        }
    }

    /**
     * 验证数值格式
     */
    private void validateNumericData(String value, String fieldName, String cellPosition) {
        if (value == null || value.trim().isEmpty()) {
            return;
        }

        String trimmedValue = value.trim();

        // 检查是否为有效数字
        try {
            double num = Double.parseDouble(trimmedValue);

            // 检查数值范围
            if (num < -10000 || num > 10000) {
                throw new ServiceException(RestCode.DATA_ERROR,
                    fieldName + "(" + cellPosition + ")数值超出范围，应在-10000到10000之间");
            }

            // 检查小数位数
            if (trimmedValue.contains(".")) {
                String[] parts = trimmedValue.split("\\.");
                if (parts.length > 1 && parts[1].length() > 1) {
                    throw new ServiceException(RestCode.DATA_ERROR,
                        fieldName + "(" + cellPosition + ")最多只能有一位小数");
                }
            }

        } catch (NumberFormatException e) {
            throw new ServiceException(RestCode.DATA_ERROR,
                fieldName + "(" + cellPosition + ")不是有效的数字格式");
        }
    }

    /**
     * 验证分数格式
     */
    private void validateScoreData(String value, String fieldName, String cellPosition) {
        if (value == null || value.trim().isEmpty()) {
            return;
        }

        String trimmedValue = value.trim();

        try {
            int score = Integer.parseInt(trimmedValue);

            if (score < 0 || score > 100) {
                throw new ServiceException(RestCode.DATA_ERROR,
                    fieldName + "(" + cellPosition + ")分数必须在0-100之间");
            }

        } catch (NumberFormatException e) {
            throw new ServiceException(RestCode.DATA_ERROR,
                fieldName + "(" + cellPosition + ")分数必须是整数");
        }
    }

    /**
     * 验证小数格式（只允许一位小数）
     */
    private void validateDecimalFormat(String value, String fieldName) {
        if (value == null || value.trim().isEmpty()) {
            return;
        }

        // 正则表达式：支持负数，整数或最多一位小数
        String regex = "^-?\\d+(\\.\\d{1})?$";
        if (!value.matches(regex)) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR,fieldName + "格式不正确，只允许整数或最多一位小数");
        }

        // 验证数值范围
        try {
            double doubleValue = Double.parseDouble(value);
//            if (doubleValue < 0) {
//                throw new ServiceException(RestCode.ARGUMENT_ERROR,fieldName + "不能为负数");
//            }
            if (doubleValue < -10000 || doubleValue > 10000) {
                throw new ServiceException(RestCode.ARGUMENT_ERROR,fieldName + "必须在-10000到10000之间");
            }
        } catch (NumberFormatException e) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR,fieldName + "格式不正确");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importTestStandard(MultipartFile file, String code) {
        importMatrixTestStandardNew(file, code);
    }

    /**
     * 导入矩阵格式的体质健康测试评分标准（新版本）
     */
    private void importMatrixTestStandardNew(MultipartFile file, String code) {
        try {
            // 获取当前用户信息
            AccountInfo accountInfo = AccountCommonService.getCurrentUser();
            Long userId = accountInfo.getId();
            String genderText = code.startsWith("1") ? "男生" : "女生";

            System.out.println("用户 " + userId + " 开始导入" + genderText + "体质健康测试评分标准矩阵数据，code: " + code);

            // 1. 验证文件格式
            if (file.isEmpty()) {
                throw new ServiceException(RestCode.DATA_ERROR, "上传文件为空");
            }

            String fileName = file.getOriginalFilename();
            if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
                throw new ServiceException(RestCode.DATA_ERROR, "文件格式不正确，只支持Excel格式(.xlsx, .xls)");
            }

            // 2. 解析Excel文件
            Workbook workbook;
            try {
                workbook = WorkbookFactory.create(file.getInputStream());
            } catch (IOException e) {
                throw new ServiceException(RestCode.DATA_ERROR, "Excel文件解析失败: " + e.getMessage());
            }

            Sheet sheet = workbook.getSheetAt(0);
            if (sheet == null) {
                workbook.close();
                throw new ServiceException(RestCode.DATA_ERROR, "Excel文件中没有找到工作表");
            }

            // 验证第一行表头
            Row firstHeaderRow = sheet.getRow(0);
            if (firstHeaderRow == null) {
                workbook.close();
                throw new ServiceException(RestCode.DATA_ERROR, "Excel文件缺少第一行表头");
            }

            // 验证第二行表头
            Row secondHeaderRow = sheet.getRow(1);
            if (secondHeaderRow == null) {
                workbook.close();
                throw new ServiceException(RestCode.DATA_ERROR, "Excel文件缺少第二行表头");
            }

            // 解析年级列表（从第一行表头解析，每个年级占两列）
            List<String> gradeColumns = new ArrayList<>();
            for (int i = 2; i < firstHeaderRow.getLastCellNum(); i += 2) { // 每两列一个年级
                Cell cell = firstHeaderRow.getCell(i);
                if (cell != null) {
                    String gradeName = getCellStringValue(cell).trim();
                    if (!gradeName.isEmpty()) {
                        gradeColumns.add(gradeName);
                    }
                }
            }

            if (gradeColumns.isEmpty()) {
                workbook.close();
                throw new ServiceException(RestCode.DATA_ERROR, "Excel文件中没有找到年级列");
            }

            // 3. 先删除所有符合当前code的数据
            QueryWrapper<PTestStandard> deleteWrapper = new QueryWrapper<PTestStandard>()
                .eq("code", code);
            long deletedCount = this.count(deleteWrapper);
            this.remove(deleteWrapper);
            System.out.println("删除了 " + deletedCount + " 条现有数据，code: " + code);

            // 4. 解析矩阵数据并新增
            List<String> updateResults = new ArrayList<>();
            int successCount = 0;
            int errorCount = 0;
            List<PTestStandard> newDataList = new ArrayList<>();

            // 根据code设置title
            String title = genderText;
            if (code.contains("TZZSDX")) {
                title += "体重指数单项评分表";
            } else if (code.contains("FHLDX")) {
                title += "肺活量单项评分表";
            } else {
                title += "体质健康测试评分标准";
            }

            for (int rowIndex = 2; rowIndex <= sheet.getLastRowNum(); rowIndex++) { // 从第3行开始读取数据
                Row row = sheet.getRow(rowIndex);
                if (row == null) continue;

                try {
                    // 解析等级和分数
                    String level = getCellStringValue(row.getCell(0)).trim();
                    String scoreStr = getCellStringValue(row.getCell(1)).trim();

                    String cellPosition = "第" + (rowIndex + 1) + "行";

                    // 验证数据格式和敏感词汇
                    validateCellData(level, "等级", cellPosition + "A列");
                    validateCellData(scoreStr, "分数", cellPosition + "B列");

                    // 验证必填字段
                    if (level.isEmpty()) {
                        errorCount++;
                        updateResults.add(cellPosition + "：等级不能为空");
                        continue;
                    }

                    // 解析分数
                    Integer score = null;
                    if (!scoreStr.isEmpty()) {
                        validateScoreData(scoreStr, "分数", cellPosition + "B列");
                        try {
                            score = Integer.parseInt(scoreStr);
                        } catch (NumberFormatException e) {
                            errorCount++;
                            updateResults.add(cellPosition + "：分数格式不正确");
                            continue;
                        }
                    }

                    // 解析每个年级的数值范围（每个年级占两列：最小值、最大值）
                    for (int gradeIndex = 0; gradeIndex < gradeColumns.size(); gradeIndex++) {
                        String gradeName = gradeColumns.get(gradeIndex);

                        // 计算最小值和最大值列的位置
                        int minValueCol = 2 + gradeIndex * 2;
                        int maxValueCol = 2 + gradeIndex * 2 + 1;

                        Cell minValueCell = row.getCell(minValueCol);
                        Cell maxValueCell = row.getCell(maxValueCol);

                        String minValueStr = getCellStringValue(minValueCell).trim();
                        String maxValueStr = getCellStringValue(maxValueCell).trim();

                        // 验证数值格式和敏感词汇
                        String minCellPos = cellPosition + "第" + (minValueCol + 1) + "列(" + gradeName + "最小值)";
                        String maxCellPos = cellPosition + "第" + (maxValueCol + 1) + "列(" + gradeName + "最大值)";

                        validateNumericData(minValueStr, "最小值", minCellPos);
                        validateNumericData(maxValueStr, "最大值", maxCellPos);

                        // 如果两个值都为空，跳过这个年级
                        if (minValueStr.isEmpty() && maxValueStr.isEmpty()) {
                            continue;
                        }

                        try {
                            // 解析最小值和最大值
                            BigDecimal minValue = null;
                            BigDecimal maxValue = null;

                            if (!minValueStr.isEmpty()) {
                                if ("-10000".equals(minValueStr)) {
                                    minValue = null; // 表示无下限
                                } else {
                                    validateDecimalFormat(minValueStr, "最小值");
                                    minValue = new BigDecimal(minValueStr);
                                }
                            }

                            if (!maxValueStr.isEmpty()) {
                                if ("10000".equals(maxValueStr)) {
                                    maxValue = new BigDecimal("999.9"); // 表示无上限
                                } else {
                                    validateDecimalFormat(maxValueStr, "最大值");
                                    maxValue = new BigDecimal(maxValueStr);
                                }
                            }

                            // 验证最小值小于最大值
                            if (minValue != null && maxValue != null &&
                                !maxValue.equals(new BigDecimal("999.9")) &&
                                minValue.compareTo(maxValue) >= 0) {
                                throw new Exception("最小值必须小于最大值");
                            }

                            // 创建新记录
                            PTestStandard newEntity = new PTestStandard();
                            newEntity.setCode(code);
                            newEntity.setTitle(title);
                            newEntity.setGradeName(gradeName);
                            newEntity.setLevel(level);
                            newEntity.setScore(score);
                            newEntity.setMinValue(minValue);
                            newEntity.setMaxValue(maxValue);
                            newEntity.setUnit("千克"); // 默认单位

                            newDataList.add(newEntity);
                            successCount++;
                            updateResults.add("第" + (rowIndex + 1) + "行-" + gradeName + "：" + level + " 准备新增");

                        } catch (Exception e) {
                            errorCount++;
                            updateResults.add("第" + (rowIndex + 1) + "行-" + gradeName + "：处理异常 - " + e.getMessage());
                        }
                    }

                } catch (Exception e) {
                    errorCount++;
                    updateResults.add("第" + (rowIndex + 1) + "行：处理异常 - " + e.getMessage());
                }
            }

            workbook.close();

            // 5. 批量插入新数据
            if (!newDataList.isEmpty()) {
                boolean batchSaveResult = this.saveBatch(newDataList);
                if (!batchSaveResult) {
                    throw new ServiceException(RestCode.DATA_ERROR, "批量插入数据失败");
                }
                System.out.println("批量插入 " + newDataList.size() + " 条新数据成功");
            }

            // 输出导入结果
            String resultMessage = String.format("用户 %s 导入%s体质健康测试评分标准矩阵数据完成：成功 %d 条，失败 %d 条",
                                                userId, genderText, successCount, errorCount);
            System.out.println(resultMessage);

            if (errorCount > 0) {
                System.out.println("导入详情：");
                updateResults.forEach(System.out::println);
            }

            if (successCount == 0 && newDataList.isEmpty()) {
                throw new ServiceException(RestCode.DATA_ERROR, "导入失败，没有成功处理任何记录");
            }

        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(RestCode.DATA_ERROR, "导入失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取单元格字符串值
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // 处理数字，避免科学计数法
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == Math.floor(numericValue)) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    @Override
    public void downloadTemplate(String code, HttpServletResponse response) {
        try {
            // 获取当前用户信息
            AccountInfo accountInfo = AccountCommonService.getCurrentUser();
            Long userId = accountInfo.getId();

            // 获取矩阵数据
            TestStandardSearch search = new TestStandardSearch();
            search.setCode(code);
            List<TestStandardMatrixInfo> matrixData = getMatrixData(search);

            // 从数据中获取title作为文件名
            String fileTitle = "体质健康测试评分标准";
            if (!matrixData.isEmpty()) {
                String genderText = code.startsWith("1") ? "男生" : "女生";
                if (code.contains("TZZSDX")) {
                    fileTitle = genderText + "体重指数单项评分表";
                } else if (code.contains("FHLDX")) {
                    fileTitle = genderText + "肺活量单项评分表";
                } else {
                    fileTitle = genderText + "体质健康测试评分标准";
                }
            }

            System.out.println("用户 " + userId + " 下载" + fileTitle + "矩阵模板，code: " + code);

            // 使用 ExcelWriter 创建模板
            ExcelWriter excelWriter = ExcelUtil.getBigWriter();

            // 动态获取年级列表
            List<String> gradeColumns = getGradeColumns(matrixData, code);

            // 构建表头列表
            List<KeyValue<String, String>> headerList = buildMatrixHeaderList(gradeColumns);

            System.out.println("构建的表头列表: " + headerList.size() + " 个");
            for (KeyValue<String, String> header : headerList) {
                System.out.println("表头: " + header.getKey() + " -> " + header.getValue() + " (extra: " + header.getExtra() + ")");
            }

            // 设置表头别名
            JSONObject jsonObject = new JSONObject();
            for (KeyValue<String, String> header : headerList) {
                excelWriter.addHeaderAlias(header.getKey(), header.getValue());
                jsonObject.set(header.getKey(), "");
            }

            excelWriter.setOnlyAlias(true);

            // 设置数据起始行为第3行（因为有两行表头）
            excelWriter.setCurrentRow(2);

            // 准备数据行
            JSONArray jsonArray = new JSONArray();
            if (!matrixData.isEmpty()) {
                // 使用实际数据填充
                for (TestStandardMatrixInfo matrixInfo : matrixData) {
                    JSONObject dataRow = new JSONObject();
                    dataRow.set("level", matrixInfo.getLevel() != null ? matrixInfo.getLevel() : "");
                    dataRow.set("score", matrixInfo.getScore() != null ? matrixInfo.getScore() : 0);

                    // 填充每个年级的最小值和最大值
                    for (String grade : gradeColumns) {
                        String minKey = grade + "_min";
                        String maxKey = grade + "_max";

                        String minValue = "";
                        String maxValue = "";

                        if (matrixInfo.getGradeRawValues() != null && matrixInfo.getGradeRawValues().containsKey(grade)) {
                            TestStandardMatrixInfo.GradeValueData rawData = matrixInfo.getGradeRawValues().get(grade);
                            if (rawData != null) {
                                if (rawData.getMinValue() != null) {
                                    minValue = rawData.getMinValue().toString();
                                }
                                if (rawData.getMaxValue() != null) {
                                    maxValue = rawData.getMaxValue().toString();
                                }
                            }
                        }

                        dataRow.set(minKey, minValue);
                        dataRow.set(maxKey, maxValue);
                    }

                    jsonArray.add(dataRow);
                }
            } else {
                // 当没有数据时，创建默认的年级模板（小学一年级到高三）
                List<String> defaultGrades = getDefaultGradeColumns();

                // 重新构建表头（使用默认年级）
                headerList = buildMatrixHeaderList(defaultGrades);

                // 重新设置表头别名
                for (KeyValue<String, String> header : headerList) {
                    excelWriter.addHeaderAlias(header.getKey(), header.getValue());
                }
                excelWriter.setOnlyAlias(true);

                // 创建几个空的示例行，等级和得分都为空
                for (int i = 0; i < 5; i++) {
                    JSONObject dataRow = new JSONObject();
                    dataRow.set("level", "");  // 等级为空
                    dataRow.set("score", "");  // 得分为空

                    // 为每个默认年级创建空的最小值和最大值
                    for (String grade : defaultGrades) {
                        dataRow.set(grade + "_min", "");
                        dataRow.set(grade + "_max", "");
                    }

                    jsonArray.add(dataRow);
                }
            }

            excelWriter.write(jsonArray);

            // 合并单元格，实现两行表头格式
            mergeMatrixCells(excelWriter, headerList);

            // 导出Excel
            exportToResponse(response, excelWriter, fileTitle + "导入模版.xlsx");

            System.out.println("用户 " + userId + " 成功下载" + fileTitle + "矩阵模板");

        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(RestCode.EXCEL_REPORT_ERROR, "下载模板失败: " + e.getMessage());
        }
    }

    /**
     * 获取默认年级列表（小学一年级到高三）
     */
    private List<String> getDefaultGradeColumns() {
        return Arrays.asList(
            "一年级", "二年级", "三年级", "四年级", "五年级", "六年级",
            "初一", "初二", "初三",
            "高一", "高二", "高三"
        );
    }

    /**
     * 获取年级列表
     */
    private List<String> getGradeColumns(List<TestStandardMatrixInfo> matrixData, String code) {
        List<String> gradeColumns = new ArrayList<>();
        if (!matrixData.isEmpty()) {
            // 从矩阵数据中获取实际存在的年级
            Set<String> existingGradesSet = new LinkedHashSet<>();
            for (TestStandardMatrixInfo matrixInfo : matrixData) {
                if (matrixInfo.getGradeValues() != null) {
                    existingGradesSet.addAll(matrixInfo.getGradeValues().keySet());
                }
            }

            // 定义标准年级顺序
            List<String> standardGradeOrder = Arrays.asList(
                "一年级", "二年级", "三年级", "四年级", "五年级", "六年级",
                "初一", "初二", "初三", "高一", "高二", "高三"
            );

            // 按标准顺序过滤出实际存在的年级
            gradeColumns = standardGradeOrder.stream()
                .filter(existingGradesSet::contains)
                .collect(Collectors.toList());
        } else {
            // 完全没有数据时的默认年级
            gradeColumns = Arrays.asList("一年级", "二年级", "三年级", "四年级", "五年级", "六年级");
        }
        return gradeColumns;
    }

        /**
     * 构建矩阵表头列表
     */
    private List<KeyValue<String, String>> buildMatrixHeaderList(List<String> gradeColumns) {
        List<KeyValue<String, String>> headerList = new ArrayList<>();

        // 添加固定列
        headerList.add(new KeyValue<>("level", "等级"));
        headerList.add(new KeyValue<>("score", "单项得分"));

        // 添加年级列（每个年级两列：最小值、最大值）
        for (String grade : gradeColumns) {
            headerList.add(new KeyValue<>(grade + "_min", "最小值", grade));
            headerList.add(new KeyValue<>(grade + "_max", "最大值", grade));
        }

        return headerList;
    }

    /**
     * 合并矩阵单元格 - 实现两行表头格式
     */
    private void mergeMatrixCells(ExcelWriter excelWriter, List<KeyValue<String, String>> headerList) {
        try {
            log.info("开始合并单元格，实现两行表头格式");

            // 第一步：合并固定列（等级、单项得分）- 垂直合并两行
            excelWriter.merge(0, 1, 0, 0, "等级", true);
            excelWriter.merge(0, 1, 1, 1, "单项得分", true);

            // 第二步：处理年级列 - 第一行合并年级名称，第二行分别显示最小值/最大值
            int colIndex = 2; // 从第3列开始（0-based）

            // 遍历年级列表，每个年级占两列
            for (int i = 2; i < headerList.size(); i += 2) {
                KeyValue<String, String> minHeader = headerList.get(i);
                KeyValue<String, String> maxHeader = headerList.get(i + 1);

                if (minHeader.getExtra() != null) {
                    String gradeName = minHeader.getExtra();

                    // 第一行：合并年级名称（跨两列）
                    excelWriter.merge(0, 0, colIndex, colIndex + 1, gradeName, true);

                    // 第二行：设置最小值和最大值标题
                    excelWriter.getOrCreateCell(colIndex, 1).setCellValue("最小值");
                    excelWriter.getOrCreateCell(colIndex + 1, 1).setCellValue("最大值");

                    colIndex += 2;
                }
            }

            log.info("单元格合并完成");

        } catch (Exception e) {
            log.warn("合并单元格时出现异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 导出到响应
     */
    private void exportToResponse(HttpServletResponse response, ExcelWriter excelWriter, String fileName) {
        ServletOutputStream out = null;
        try {
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            fileName = java.net.URLEncoder.encode(fileName, "utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            out = response.getOutputStream();
            excelWriter.flush(out, true);
        } catch (Exception e) {
            throw new ServiceException(RestCode.EXCEL_REPORT_ERROR);
        } finally {
            excelWriter.close();
            IoUtil.close(out);
        }
    }


    /**
     * 导出矩阵Excel到响应流
     */
    private void exportMatrixToResponse(HttpServletResponse response, Workbook workbook, String fileName) {
        ServletOutputStream out = null;
        try {
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            fileName = java.net.URLEncoder.encode(fileName, "utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            out = response.getOutputStream();
            workbook.write(out);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(RestCode.EXCEL_REPORT_ERROR, "导出失败: " + e.getMessage());
        } finally {
            try {
                workbook.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            IoUtil.close(out);
        }
    }
}
