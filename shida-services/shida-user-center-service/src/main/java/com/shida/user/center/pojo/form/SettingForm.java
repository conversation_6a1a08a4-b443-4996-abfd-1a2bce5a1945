package com.shida.user.center.pojo.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@Schema(name = "SettingForm", description = "设置项信息表")
public abstract class SettingForm implements Serializable {
    @Schema(description = "设置名称")
    private String name;

    @Schema(description = "设置代码")
    private String code;

    @Schema(description = "设置内容，json")
    private Object content;

    @Schema(description = "排序序号")
    private Integer orderNumber;
}
