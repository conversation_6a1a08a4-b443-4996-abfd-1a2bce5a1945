package com.shida.user.center.service;

import com.shida.pojo.search.Search;
import com.shida.pojo.vo.MenuInfo;
import com.shida.pojo.vo.PageInfo;
import com.shida.pojo.vo.PermissionInfo;
import com.shida.user.center.entity.Menu;
import com.baomidou.mybatisplus.extension.service.IService;
import com.shida.user.center.pojo.form.MenuForm;
import com.shida.user.center.pojo.form.RolePermissionForm;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 菜单信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
public interface IMenuService extends IService<Menu> {

    List<PermissionInfo> getPermissionsByRoleId(Long roleId);

    List<PermissionInfo> getPermissionsByRoleIds(List<Long> roleIds);

    List<MenuInfo> getMenusByRoleIds(List<Long> roleIds);

    PageInfo<MenuInfo> getAllMenes(Search search);

    List<MenuInfo> getMenuTreeList();

    String createMenu(MenuForm form);

    String updateMenu(MenuForm menuForm);

    String deleteById(Long id);

    String changeRolePermissions(RolePermissionForm form);
}
