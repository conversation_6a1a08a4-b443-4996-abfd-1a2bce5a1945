package com.shida.user.center.pojo.search;

import com.shida.pojo.search.Search;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(name = "UserSearch", description = "用户搜索条件")
public class UserSearch extends Search {
    @Schema(description = "部门ID")
    private Long deptId;
    @Schema(description = "学校ID")
    private Long schoolId;
    @Schema(description = "角色ID")
    private Long roleId;
}
