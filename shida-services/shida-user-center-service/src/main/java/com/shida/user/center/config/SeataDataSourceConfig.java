package com.shida.user.center.config;

import com.alibaba.druid.pool.DruidDataSource;
import io.seata.rm.datasource.DataSourceProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

@Slf4j
@Configuration
public class SeataDataSourceConfig {

    @Bean
    @ConfigurationProperties(prefix = "spring.datasource")
    public DruidDataSource druidDataSource() {
        DruidDataSource ds = new DruidDataSource();
        // 数据库为mysql0数据库，帮我优化Druid配置,应对高并发的情况，并关闭数据统计监控
        // 初始化连接数
        ds.setInitialSize(5);
        // 最小空闲连接数
        ds.setMinIdle(5);
        // 最大连接数
        ds.setMaxActive(20);
        // 获取连接等待超时的时间
        ds.setMaxWait(60000);
        // 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        ds.setTimeBetweenEvictionRunsMillis(60000);
        // 配置一个连接在池中最小生存的时间，单位是毫秒
        ds.setMinEvictableIdleTimeMillis(600000);
        // 配置间隔多久才进行一次检测，检测连接是否 Alive，单位是毫秒
        ds.setMaxEvictableIdleTimeMillis(1800000);
        // 验证连接有效性的SQL
        ds.setValidationQuery("SELECT 1");
        // 建议配置为true，不影响性能，并且保证安全性。申请连接的时候检测，
        // 如果空闲时间大于timeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效
        ds.setTestWhileIdle(true);
        // 申请连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
        ds.setTestOnBorrow(false);
        // 归还连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
        ds.setTestOnReturn(false);
        // 是否开启
        ds.setEnable(true);

        return ds;
    }

    @Primary
    @Bean("seataDataSource")
    public DataSource dataSource(DruidDataSource dataSource) {
        log.info("Creating Seata proxy data source");
        return new DataSourceProxy(dataSource);
    }

}
