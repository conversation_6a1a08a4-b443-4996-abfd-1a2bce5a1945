package com.shida.user.center.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.support.http.StatViewServlet;
import com.alibaba.druid.support.http.WebStatFilter;
import io.seata.rm.datasource.DataSourceProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Configuration
public class SeataDataSourceConfig {

    @Value("${spring.profiles.active:dev}")
    private String active;

    @Bean
    @ConfigurationProperties(prefix = "spring.datasource")
    public DruidDataSource druidDataSource() {
        DruidDataSource druidDataSource = new DruidDataSource();

        if (active.startsWith("dev")) {
            // 优化Druid 数据源
            druidDataSource.setInitialSize(2);
            // 最小空闲连接数
            druidDataSource.setMinIdle(2);
            // 最大连接数
            druidDataSource.setMaxActive(20);
        } else {
            // 优化Druid 数据源
            druidDataSource.setInitialSize(8);
            // 最小空闲连接数
            druidDataSource.setMinIdle(8);
            // 最大连接数
            druidDataSource.setMaxActive(200);
        }
        // 获取连接时最大等待时间，单位毫秒。配置了max-wait之后，
        // 缺省启用公平锁，并发效率会有所下降，但可以有效避免获取连接超时。如果需要可以通过配置useUnfairLock属性为true使用非公平锁。
        druidDataSource.setMaxWait(60000);
        // 连接有效性检测配置 (防止使用已失效的连接，非常重要),
        // 申请连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能，但保证了安全性。通常可以设置为false，依靠空闲检测来保证
        druidDataSource.setTestOnBorrow(false);
        // 归还连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
        druidDataSource.setTestOnReturn(false);
        // 建议设置为true，不影响性能，并且保证安全性。
        // 申请连接的时候，如果空闲时间大于timeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效
        druidDataSource.setTestWhileIdle(true);
        // 验证连接有效性的SQL
        druidDataSource.setValidationQuery("SELECT 1");
        // # 有两个含义：
        // 1) Destroy线程会检测连接的间隔时间
        // 2) testWhileIdle的判断依据，详细看testWhileIdle属性的说明。单位毫秒。
        druidDataSource.setTimeBetweenEvictionRunsMillis(60000);
        // 一个连接在池中最小生存的时间，单位是毫秒。连接保持空闲而不被驱逐的最小时间。
        druidDataSource.setMinEvictableIdleTimeMillis(300000);
        // 配置间隔多久才进行一次检测，检测连接是否 Alive，单位是毫秒
        druidDataSource.setMaxEvictableIdleTimeMillis(300000);
        // 是否移除泄露的连接/超过时间限制的连接
        druidDataSource.setRemoveAbandoned(true);
        // 泄露连接可以被移除的超时时间，单位秒。值应大于业务中最长运行时间。
        druidDataSource.setRemoveAbandonedTimeout(180);
        // 移除泄露连接时是否输出日志，可以看到泄露连接的堆栈信息，有助于调试。
        druidDataSource.setLogAbandoned(true);
        // 是否开启
        druidDataSource.setEnable(true);

        return druidDataSource;
    }

    @Primary
    @Bean("seataDataSource")
    public DataSource dataSource(DruidDataSource dataSource) {
        log.info("Creating Seata proxy data source");
        return new DataSourceProxy(dataSource);
    }


    /**
     * 配置Druid监控界面的Servlet
     * 访问地址：http://localhost:你的端口号/druid/index.html
     */
    @Bean
    @ConditionalOnProperty(name = "spring.profiles.active", havingValue = "dev")
    public ServletRegistrationBean<StatViewServlet> statViewServlet() {
        ServletRegistrationBean<StatViewServlet> registrationBean =
            new ServletRegistrationBean<>(new StatViewServlet(), "/druid/*");

        // 设置初始化参数
        Map<String, String> initParams = new HashMap<>();

        // 监控页面登录用户名
        initParams.put("loginUsername", "shida_admin");
        // 监控页面登录密码
        initParams.put("loginPassword", "shida@2025");
        // 是否允许重置数据（生产环境建议设置为false）
        initParams.put("resetEnable", "false");
        // 允许访问的IP地址（为空表示所有IP都可以访问）
        initParams.put("allow", "");
        // 拒绝访问的IP地址
        // initParams.put("deny", "*************");

        registrationBean.setInitParameters(initParams);
        return registrationBean;
    }

    /**
     * 配置Web监控的Filter
     * 用于采集web-jdbc关联监控的数据
     */
    @Bean
    @ConditionalOnProperty(name = "spring.profiles.active", havingValue = "dev")
    public FilterRegistrationBean<WebStatFilter> webStatFilter() {
        FilterRegistrationBean<WebStatFilter> registrationBean =
            new FilterRegistrationBean<>(new WebStatFilter());

        // 设置初始化参数
        Map<String, String> initParams = new HashMap<>();
        // 排除一些不必要的url，比如静态资源、druid监控页面本身
        initParams.put("exclusions", "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*");
        // 是否开启session统计
        initParams.put("sessionStatEnable", "true");
        // 配置profileEnable能够监控单个url调用的sql列表
        initParams.put("profileEnable", "true");

        registrationBean.setInitParameters(initParams);
        // 添加过滤规则，所有请求都进行监控
        registrationBean.addUrlPatterns("/*");

        return registrationBean;
    }
}
