package com.shida.user.center.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shida.entity.BaseEntity;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 账户信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_user")
@Schema(name = "User", description = "账户信息")
public class User extends BaseEntity<User> {

    private static final long serialVersionUID = 1L;

    @Schema(description = "账户名")
    private String username;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "电子邮箱")
    private String email;

    @Schema(description = "账号状态，0禁用，1正常")
    private Integer status;

    @Schema(description = "手机号码")
    private String mobile;

    @Schema(description = "密码")
    private String password;

    @Schema(description = "秘钥")
    private String salt;

    @Schema(description = "默认密码")
    private String defaultPassword;

    @Schema(description = "是否是默认密码")
    private Boolean defaultPasswordFlag;

    @Schema(description = "过期时间，为空时永不过期")
    private LocalDateTime expireTime;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
