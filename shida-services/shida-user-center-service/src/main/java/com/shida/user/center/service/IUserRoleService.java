package com.shida.user.center.service;

import com.shida.user.center.entity.UserRole;
import com.baomidou.mybatisplus.extension.service.IService;
import com.shida.userCenter.dto.RoleDto;

import java.util.List;

/**
 * <p>
 * 用户角色表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface IUserRoleService extends IService<UserRole> {

    List<RoleDto> getUserRoleDtoList(List<Long> userIds);
}
