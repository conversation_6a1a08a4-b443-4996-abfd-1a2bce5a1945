package com.shida.user.center.pojo.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
@Accessors(chain = true)
@Schema(name = "OrgForm", description = "机构信息")
public class OrgForm implements Serializable {
    @Schema(description = "机构id")
    @NotNull(message = "机构id不能为空")
    private Long id;
    @Schema(description = "机构名称")
    @NotNull(message = "机构名称不能为空")
    @Size(max = 32, message = "机构名称不能超过32个字符")
    private String name;
    @Schema(description = "机构代码")
    @NotNull(message = "机构代码不能为空")
    @Size(max = 32, message = "机构代码不能超过32个字符")
    private String code;
    @Schema(description = "联系人")
    private String contact;
    @Schema(description = "联系人职位")
    private String position;
    @Schema(description = "联系人电话")
    private String telephone;
    @Schema(description = "联系人手机号码")
    private String mobile;
}
