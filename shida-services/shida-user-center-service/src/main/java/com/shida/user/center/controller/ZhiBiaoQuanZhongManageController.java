package com.shida.user.center.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.noRepeatSubmit.NoRepeatSubmit;
import com.shida.pojo.form.KeyForm;
import com.shida.pojo.vo.KeyValue;
import com.shida.user.center.pojo.form.QuanZhongZhanBiSettingForm;
import com.shida.user.center.pojo.form.SettingForm;
import com.shida.user.center.pojo.vo.QuanZhongZhanBiSettingInfo;
import com.shida.user.center.pojo.vo.SettingInfo;
import com.shida.user.center.service.ISettingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

@Tag(name = "setting-指标权重")
@RestController
@RequestMapping("/zbqz/manage")
public class ZhiBiaoQuanZhongManageController {
    @Resource
    private ISettingService settingService;


    @Operation(summary = "获取指定代码的设置项")
    @PostMapping("/getByCode")
    public QuanZhongZhanBiSettingInfo getByCode(@RequestBody @Valid KeyForm<String> form) {
        QuanZhongZhanBiSettingInfo result = settingService.getSettingByCode(form.getKey(), QuanZhongZhanBiSettingInfo.class);
        if (result == null) {
            result = new QuanZhongZhanBiSettingInfo();
            result.setCode(form.getKey())
                    .setName("权重占比设置");
            List<KeyValue<String, String>> content = new ArrayList<>();
            content.add(new KeyValue<>("50米跑", "20%"));
            content.add(new KeyValue<>("坐位体前屈", "30%"));
            content.add(new KeyValue<>("1分钟跳绳", "20%"));
            result.setContent(content);
        }
        return result;
    }

    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_ADD)
    @SaCheckPermission("setting:add")
    @Operation(summary = "新增设置项")
    @PostMapping("/create")
    public String create(@RequestBody @Valid QuanZhongZhanBiSettingForm form) {
        return settingService.saveOrUpdateSetting(form.getCode(), form.getName(), form.getContent(), form.getOrderNumber());
    }


    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    @SaCheckPermission("setting:update")
    @Operation(summary = "更新指定代码的设置项")
    @PostMapping("/updateByCode")
    public String updateByCode(@RequestBody @Valid QuanZhongZhanBiSettingForm form) {
        return settingService.saveOrUpdateSetting(form.getCode(), form.getName(), form.getContent(), form.getOrderNumber());
    }

    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_DELETE)
    @SaCheckPermission("setting:delete")
    @Operation(summary = "删除指定代码的设置项")
    @PostMapping("/deleteByCode")
    public String deleteByCode(@RequestBody @Valid KeyForm<String> form) {
        return settingService.removeById(form.getKey()) ? "删除成功" : "删除失败";
    }

}
