package com.shida.user.center.controller.feign;

import com.shida.pojo.vo.KeyValue;
import com.shida.user.center.service.ISportProjectService;
import com.shida.userCenter.api.IFeignSportProjectService;
import com.shida.userCenter.dto.SportProjectDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 体育项目Feign服务实现
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Tag(name = "Feign-体育项目服务")
@RestController
@RequestMapping("/sport-project-feign-api")
public class FeignSportProjectController implements IFeignSportProjectService {

    @Resource
    private ISportProjectService sportProjectService;

    @Operation(summary = "获取所有体育项目列表")
    @Override
    public List<SportProjectDto> getAllSportProjects() {
        return sportProjectService.getAllSportProjects();
    }

    @Operation(summary = "获取体质测试相关的项目列表")
    @Override
    public List<SportProjectDto> getFitnessTestProjects() {
        return sportProjectService.getFitnessTestProjects();
    }

    @Operation(summary = "获取运动项目下拉列表")
    @Override
    public List<KeyValue<String, String>> getSportProjectSelectList() {
        return sportProjectService.getSportProjectSelectList();
    }
}
