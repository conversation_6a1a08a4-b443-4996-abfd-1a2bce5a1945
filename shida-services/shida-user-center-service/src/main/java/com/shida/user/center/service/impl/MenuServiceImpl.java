package com.shida.user.center.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.shida.enums.RestCode;
import com.shida.exceptions.ServiceException;
import com.shida.pojo.search.Search;
import com.shida.pojo.vo.MenuInfo;
import com.shida.pojo.vo.PageInfo;
import com.shida.pojo.vo.PermissionInfo;
import com.shida.user.center.entity.Menu;
import com.shida.user.center.entity.RoleMenu;
import com.shida.user.center.mapper.MenuMapper;
import com.shida.user.center.pojo.form.MenuForm;
import com.shida.user.center.pojo.form.RolePermissionForm;
import com.shida.user.center.service.IMenuService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shida.user.center.service.IRoleMenuService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 菜单信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Service
public class MenuServiceImpl extends ServiceImpl<MenuMapper, Menu> implements IMenuService {

    @Resource
    private IRoleMenuService roleMenuService;


    @Override
    public List<PermissionInfo> getPermissionsByRoleId(Long roleId) {
        List<PermissionInfo> result = baseMapper.getPermissionsByRoleIds(CollUtil.newArrayList(roleId));
        List<RoleMenu> roleMenus = roleMenuService.list(new QueryWrapper<RoleMenu>().lambda().eq(RoleMenu::getRoleId, roleId));
        result.forEach(permissionInfo -> {
            RoleMenu rm = roleMenus.stream().filter(roleMenu -> roleMenu.getMenuId().equals(permissionInfo.getId())).findFirst()
                    .orElse(new RoleMenu());
        });
        return result;
    }

    @Override
    public List<PermissionInfo> getPermissionsByRoleIds(List<Long> roleIds) {
        if (CollUtil.isEmpty(roleIds)) {
            return new ArrayList<>();
        }
        return baseMapper.getPermissionsByRoleIds(roleIds);
    }

    @Override
    public List<MenuInfo> getMenusByRoleIds(List<Long> roleIds) {
        if (CollUtil.isEmpty(roleIds)) {
            return new ArrayList<>();
        }
        List<PermissionInfo> permissions = getPermissionsByRoleIds(roleIds);
        List<PermissionInfo> parentList = permissions.stream().filter(permissionInfo ->
                Objects.isNull(permissionInfo.getParentId())).collect(Collectors.toList());
        List<MenuInfo> result = new ArrayList<>();
        for (PermissionInfo parent : parentList) {
            MenuInfo menuInfo = BeanUtil.copyProperties(parent, MenuInfo.class);
            menuInfo.setChildren(getChild(permissions, parent.getId()));
            result.add(menuInfo);
        }

        return result;
    }

    @Override
    public PageInfo<MenuInfo> getAllMenes(Search search) {
        List<Menu> menusList = super.list(new LambdaQueryWrapper<Menu>()
                .like(StrUtil.isNotBlank(search.getKeywords()), Menu::getName, search.getKeywords())
                .or()
                .like(StrUtil.isNotBlank(search.getKeywords()), Menu::getCode, search.getKeywords())
                .orderByAsc(Menu::getOrderNum));
        List<PermissionInfo> permissions = BeanUtil.copyToList(menusList, PermissionInfo.class);
        List<Menu> parents = menusList.stream().filter(menu -> Objects.isNull(menu.getParentId())).collect(Collectors.toList());
        List<MenuInfo> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(parents)) {
            for (Menu parent : parents) {
                MenuInfo menuInfo = BeanUtil.copyProperties(parent, MenuInfo.class);
                assert permissions != null;
                menuInfo.setChildren(getChild(permissions, parent.getId()));
                result.add(menuInfo);
            }
        } else {
            result = BeanUtil.copyToList(menusList, MenuInfo.class);
        }
        return new PageInfo<>(1, 1000, result.size(), result);
    }

    @Override
    public List<MenuInfo> getMenuTreeList() {
        List<Menu> allMenu = super.list(new LambdaQueryWrapper<Menu>().in(Menu::getType, "M", "C").orderByAsc(Menu::getOrderNum));
        List<Menu> parents = allMenu.stream().filter(menu -> Objects.isNull(menu.getParentId())).collect(Collectors.toList());
        List<MenuInfo> result = BeanUtil.copyToList(parents, MenuInfo.class);
        List<PermissionInfo> permissions = BeanUtil.copyToList(allMenu, PermissionInfo.class);
        MenuInfo root = new MenuInfo();
        root.setName("根目录")
                .setCode("ROOT")
                .setChildren(result);
        for (MenuInfo parent : result) {
            parent.setChildren(getChild(permissions, parent.getId()));
        }
        return CollUtil.newArrayList(root);
    }

    @Override
    public String createMenu(MenuForm form) {
        if (super.count(new LambdaQueryWrapper<Menu>().eq(Menu::getCode, form.getCode())) > 0) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "菜单编码已存在");
        }
        if (form.getType().equals("M") || form.getType().equals("C")) {
            if (StrUtil.isBlank(form.getRouter())) {
                throw new ServiceException(RestCode.ARGUMENT_ERROR, "请填写路由地址");
            }
            if (StrUtil.isBlank(form.getComponent())) {
                throw new ServiceException(RestCode.ARGUMENT_ERROR, "请填写组件地址");
            }
        } else {
            form.setRouter(null);
            form.setComponent(null);
        }
        Menu menu = BeanUtil.copyProperties(form, Menu.class);
        menu.insert();
        return "ok";
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String updateMenu(MenuForm form) {
        if (Objects.isNull(form.getId())) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "菜单ID不能为空");
        }
        if (super.count(new LambdaQueryWrapper<Menu>().ne(Menu::getId, form.getId()).eq(Menu::getCode, form.getCode())) > 0) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "菜单编码已存在");
        }
        if (form.getType().equals("M") || form.getType().equals("C")) {
            if (StrUtil.isBlank(form.getRouter())) {
                throw new ServiceException(RestCode.ARGUMENT_ERROR, "请填写路由地址");
            }
            if (StrUtil.isBlank(form.getComponent())) {
                throw new ServiceException(RestCode.ARGUMENT_ERROR, "请填写组件地址");
            }
            Menu menu = BeanUtil.copyProperties(form, Menu.class);
            menu.updateById();
        } else {
            Menu menu = BeanUtil.copyProperties(form, Menu.class);
            menu.updateById();
            baseMapper.setNullByField(menu.getId(), "router");
            baseMapper.setNullByField(menu.getId(), "component");
        }
        return "ok";
    }

    @Override
    public String deleteById(Long id) {
        baseMapper.deleteById(id);
        roleMenuService.remove(new LambdaQueryWrapper<RoleMenu>().eq(RoleMenu::getMenuId, id));
        List<Long> childIds = baseMapper.selectList(new LambdaQueryWrapper<Menu>().eq(Menu::getParentId, id)).stream().map(Menu::getId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(childIds)) {
            baseMapper.deleteBatchIds(childIds);
            roleMenuService.remove(new LambdaQueryWrapper<RoleMenu>().in(RoleMenu::getMenuId, childIds));
        }
        return "ok";
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String changeRolePermissions(RolePermissionForm form) {
        roleMenuService.remove(new LambdaQueryWrapper<RoleMenu>().eq(RoleMenu::getRoleId, form.getRoleId()));
        List<RoleMenu> roleMenus = new ArrayList<>();
        for (Long menuId : form.getMenuIds()) {
            RoleMenu roleMenu = new RoleMenu();
            roleMenu.setRoleId(form.getRoleId()).setMenuId(menuId);
            roleMenus.add(roleMenu);
        }
        roleMenuService.saveBatch(roleMenus);
        return "success";
    }

    private List<MenuInfo> getChild(List<PermissionInfo> permissions, Long parentId) {
        List<MenuInfo> result = null;
        List<PermissionInfo> children = permissions.stream().filter(permissionInfo -> Objects.equals(permissionInfo.getParentId(), parentId)).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(children)) {
            result = new ArrayList<>();
            for (PermissionInfo child : children) {
                MenuInfo childMenu = BeanUtil.copyProperties(child, MenuInfo.class);
                if (permissions.stream().anyMatch(permissionInfo -> Objects.equals(permissionInfo.getParentId(), child.getId()))) {
                    childMenu.setChildren(getChild(permissions, child.getId()));
                }
                result.add(childMenu);
            }
        }
        return result;
    }
}
