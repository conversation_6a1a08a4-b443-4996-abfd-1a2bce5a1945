package com.shida.user.center.pojo.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
@Schema(name = "UserForm", description = "用户表单")
public class UserForm implements Serializable {
    @Schema(description = "用户ID")
    private Long id;
    @Schema(description = "用户名")
    @NotBlank(message = "用户名不能为空")
    @Size(min = 4,max = 20, message = "用户名长度在4-20个字符之间")
    private String username;
    @Schema(description = "真实姓名")
    @NotBlank(message = "真实姓名不能为空")
    private String nickname;
    @Schema(description = "手机号码")
    @NotBlank(message = "手机号码不能为空")
    private String mobile;
    @Schema(description = "电子邮箱")
    private String email;
    @Schema(description = "状态")
    @NotNull(message = "状态不能为空")
    private Integer status;
    @Schema(description = "区县ID")
    @NotNull(message = "区县ID不能为空")
    private Long regionId;
    @Schema(description = "学校ID")
    private Long schoolId;
    @Schema(description = "角色编码")
    private List<String> roleCodes;
}
