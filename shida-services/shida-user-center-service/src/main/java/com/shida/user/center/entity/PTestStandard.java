package com.shida.user.center.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.shida.entity.BaseEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 通用体质健康测试评分标准表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-02
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("p_test_standard")
@Schema(name = "PTestStandard", description = "通用体质健康测试评分标准表")
public class PTestStandard extends BaseEntity<PTestStandard> {

    private static final long serialVersionUID = 1L;

    @Schema(description = "唯一业务编码")
    private String code;

    @Schema(description = "项目标题 (例如: 男生肺活量单项评分表)")
    private String title;

    @Schema(description = "单位 (例如: 毫升, 千克/米²)")
    private String unit;

    @Schema(description = "年级 (例如: 一年级, 初一, 大学)")
    private String gradeName;

    @Schema(description = "等级 (例如: 优秀, 良好, 正常)")
    private String level;

    @Schema(description = "分数")
    private Integer score;

    @Schema(description = "最小值")
    private BigDecimal minValue;

    @Schema(description = "最大值")
    private BigDecimal maxValue;

    // 重写父类的createTime字段，在更新时忽略此字段
    @Schema(description = "创建时间")
    protected LocalDateTime createTime;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
