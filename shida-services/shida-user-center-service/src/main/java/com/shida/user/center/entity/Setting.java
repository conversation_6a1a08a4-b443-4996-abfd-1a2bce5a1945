package com.shida.user.center.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shida.entity.BaseEntity;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 设置项信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-02
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_setting")
@Schema(name = "Setting", description = "设置项信息表")
public class Setting extends BaseEntity<Setting> {

    private static final long serialVersionUID = 1L;

    @Schema(description = "设置名称")
    private String name;

    @Schema(description = "设置代码")
    private String code;

    @Schema(description = "设置内容，json")
    private String content;

    @Schema(description = "区县ID")
    private Long regionId;

    @Schema(description = "学校ID")
    private Long schoolId;

    @Schema(description = "排序序号")
    private Integer orderNumber;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
