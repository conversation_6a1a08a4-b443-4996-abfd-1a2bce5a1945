package com.shida.user.center.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.shida.user.center.entity.User;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shida.user.center.pojo.search.UserSearch;
import com.shida.user.center.pojo.vo.UserInfo;
import com.shida.userCenter.dto.UserDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 账户信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface UserMapper extends BaseMapper<User> {

    UserDto findUserDtoByUsername(String username);

    UserDto findUserDtoByUserId(Long userId);

    List<UserDto> findUserDtoByUserIds(List<Long> userIds);

    IPage<UserInfo> getPageData(IPage<UserInfo> page, @Param("search") UserSearch search);

    List<UserDto> getMasterUserListBySchoolId(@Param("regionId") Long regionId, @Param("schoolId") Long schoolId,@Param("roleCode") String roleCode);

    List<UserDto> getMasterUserListBySchoolIds(@Param("regionId") Long regionId, @Param("schoolIds") List<Long> schoolIds, @Param("roleCode") String roleCode);

    List<UserDto> findUserDtoByUsernames(@Param("usernameList")  List<String> usernameList);
}
