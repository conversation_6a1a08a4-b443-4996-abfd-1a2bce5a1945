package com.shida.user.center.pojo.vo;

import com.shida.pojo.vo.RoleInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
@Schema(name = "UserInfo", description = "账号信息")
public class UserInfo implements Serializable {
    @Schema(description = "ID")
    private Long id;
    @Schema(description = "用户名")
    private String username;
    @Schema(description = "昵称")
    private String nickname;
    @Schema(description = "邮箱")
    private String email;
    @Schema(description = "状态")
    private Integer status;
    @Schema(description = "手机号")
    private String mobile;
    @Schema(description = "token")
    private String token;
    @Schema(description = "角色")
    private List<RoleInfo> roles;
    @Schema(description = "默认密码")
    private String defaultPassword;
    @Schema(description = "是否默认密码")
    private Boolean defaultPasswordFlag;
    @Schema(description = "区县ID")
    private Long regionId;
    @Schema(description = "区县名称")
    private String regionName;
    @Schema(description = "区县code")
    private String regionCode;
    @Schema(description = "部门层次")
    private Integer deptFloor;
    @Schema(description = "上级部门ID")
    private Long parentDeptId;
    @Schema(description = "学校ID")
    private Long schoolId;
    @Schema(description = "学校代码")
    private String schoolCode;
    @Schema(description = "学校名称")
    private String schoolName;
}
