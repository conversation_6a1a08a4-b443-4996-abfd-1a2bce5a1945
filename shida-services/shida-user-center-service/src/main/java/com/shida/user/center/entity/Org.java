package com.shida.user.center.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shida.entity.BaseEntity;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 机构信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_org")
@Schema(name = "Org", description = "机构信息")
public class Org extends BaseEntity<Org> {

    private static final long serialVersionUID = 1L;

    @Schema(description = "机构名称")
    private String name;

    @Schema(description = "机构代码")
    private String code;

    @Schema(description = "父机构代码")
    private Long parentId;

    @Schema(description = "描述信息")
    private String description;

    @Schema(description = "层级，1市，2区县，3学校")
    private Integer floor;

    @Schema(description = "联系人")
    private String contact;

    @Schema(description = "联系人职位")
    private String position;

    @Schema(description = "联系人电话")
    private String telephone;

    @Schema(description = "联系人手机号码")
    private String mobile;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
