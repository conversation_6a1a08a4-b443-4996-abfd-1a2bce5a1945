package com.shida.user.center.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shida.IFeignSchoolService;
import com.shida.constant.RoleConstant;
import com.shida.dto.SchoolDto;
import com.shida.excel.ExcelHelper;
import com.shida.pojo.search.Search;
import com.shida.pojo.vo.KeyValue;
import com.shida.pojo.vo.PageInfo;
import com.shida.user.center.entity.Org;
import com.shida.user.center.entity.Role;
import com.shida.user.center.mapper.OrgMapper;
import com.shida.user.center.pojo.excel.RegionAccountExcel;
import com.shida.user.center.pojo.form.OrgForm;
import com.shida.user.center.pojo.vo.OrgInfo;
import com.shida.user.center.pojo.vo.UserInfo;
import com.shida.user.center.service.IOrgService;
import com.shida.user.center.service.IRoleService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <p>
 * 机构信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Service
public class OrgServiceImpl extends ServiceImpl<OrgMapper, Org> implements IOrgService {

    @Resource
    private IFeignSchoolService schoolService;
    @Resource
    private IRoleService roleService;

    @Override
    public Org findByCode(String code) {
        LambdaQueryWrapper<Org> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Org::getCode, code);
        return super.getOne(queryWrapper);
    }

    @Override
    public List<KeyValue<String, String>> getRegionSelectList() {
        List<Org> list = super.list(new LambdaQueryWrapper<Org>()
            .select(Org::getId, Org::getName)
            .eq(Org::getFloor, 2)
            .orderByAsc(Org::getId));
        return list.stream().map(org -> new KeyValue<>(org.getId().toString(), org.getName())).collect(Collectors.toList());
    }

    @Override
    public List<KeyValue<String, String>> getSchoolSelectList(Long regionId) {
        Org region = super.getById(regionId);
        if (region == null) {
            return Collections.emptyList();
        }
        List<SchoolDto> schoolList = schoolService.getByRegionId(regionId);
        return schoolList.stream().map(school -> new KeyValue<>(school.getId().toString(), school.getName(), school.getPeriod())).collect(Collectors.toList());
    }

    @Override
    public PageInfo<OrgInfo> getRegionList(Search search) {
        IPage<Org> page = new Page<>(search.getPageNumber(), search.getPageSize());

        page = super.page(page, new LambdaQueryWrapper<Org>()
            .eq(Org::getFloor, 2)
            .orderByAsc(Org::getId));
        PageInfo<OrgInfo> result = PageInfo.fromMybatisPage(page, OrgInfo.class);
        List<Long> regionIds = result.getRecords().stream().map(OrgInfo::getId).collect(Collectors.toList());
        List<UserInfo> regionAdminList = CollUtil.isNotEmpty(regionIds) ? baseMapper.getRegionAdminList(regionIds, RoleConstant.COUNTY_ADMIN_CODE) : new ArrayList<>();

        result.getRecords().forEach(record -> {
            List<UserInfo> masters = regionAdminList.stream().filter(regionAdmin -> regionAdmin.getRegionId().equals(record.getId()))
                .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(masters)) {
                record.setMasters(masters);
            }
        });

        return result;
    }

    @Override
    public String updateOrgInfo(OrgForm orgForm) {
        Org org = new Org();
        BeanUtil.copyProperties(orgForm, org);
        org.updateById();
        return "ok";
    }

    @Override
    public void exportAccount(Long regionId, HttpServletResponse response) {
        List<RegionAccountExcel> excelList = new ArrayList<>();
        Org org = baseMapper.selectById(regionId);
        List<UserInfo> userInfoList = baseMapper.getRegionAdminList(CollUtil.newArrayList(regionId), RoleConstant.COUNTY_ADMIN_CODE);
        excelList = BeanUtil.copyToList(userInfoList, RegionAccountExcel.class);
        Role role = roleService.findByCode(RoleConstant.COUNTY_ADMIN_CODE);
        AtomicInteger index = new AtomicInteger(1);
        excelList.forEach(excel -> {
            UserInfo userInfo = userInfoList.stream().filter(u -> u.getId().equals(excel.getId())).findFirst().orElse(new UserInfo());
            excel.setIndex(index.getAndIncrement());
            excel.setNickname(userInfo.getNickname())
                .setEmail(userInfo.getEmail())
                .setRoleName(role.getName());
        });

        ExcelHelper.writeToResponse(RegionAccountExcel.class, excelList, org.getName() + "-管理员账号信息.xlsx", response);
    }

    @Override
    public void exportAllRegionAccount(Search search, HttpServletResponse response) {
        search.setPageNumber(1).setPageSize(10000);
        PageInfo<OrgInfo> pageInfo = this.getRegionList(search);
        List<RegionAccountExcel> excelList = new ArrayList<>();
        AtomicInteger index = new AtomicInteger(1);
        Role role = roleService.findByCode(RoleConstant.COUNTY_ADMIN_CODE);
        pageInfo.getRecords().forEach(region -> {
            List<UserInfo> userInfoList = region.getMasters();
            if (CollUtil.isNotEmpty(userInfoList)) {
                excelList.addAll(BeanUtil.copyToList(userInfoList, RegionAccountExcel.class));
                excelList.forEach(excel -> {
                    excel.setIndex(index.getAndIncrement());
                    excel.setRoleName(role.getName());
                });
            }
        });
        ExcelHelper.writeToResponse(RegionAccountExcel.class, excelList, "区县管理员账号信息.xlsx", response);
    }

}
