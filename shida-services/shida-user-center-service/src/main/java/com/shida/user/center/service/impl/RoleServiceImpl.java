package com.shida.user.center.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shida.enums.RestCode;
import com.shida.exceptions.ServiceException;
import com.shida.pojo.search.Search;
import com.shida.pojo.vo.KeyValue;
import com.shida.pojo.vo.PageInfo;
import com.shida.pojo.vo.RoleInfo;
import com.shida.user.center.entity.Role;
import com.shida.user.center.entity.RoleMenu;
import com.shida.user.center.mapper.RoleMapper;
import com.shida.user.center.pojo.form.RoleForm;
import com.shida.user.center.service.IRoleMenuService;
import com.shida.user.center.service.IRoleService;
import com.shida.userCenter.dto.RoleDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 角色信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Service
public class RoleServiceImpl extends ServiceImpl<RoleMapper, Role> implements IRoleService {
    @Resource
    private IRoleMenuService roleMenuService;

    @Override
    public Role findByCode(String code) {
        LambdaQueryWrapper<Role> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Role::getCode, code);
        return super.getOne(queryWrapper);
    }

    @Override
    public PageInfo<RoleInfo> getPageData(Search search) {
        IPage<RoleInfo> page = baseMapper.getPageData(new Page<>(search.getPageNumber(), search.getPageSize()), search);
        return PageInfo.fromMybatisPage(page);
    }

    @Override
    public List<RoleInfo> getUserRolesByUserId(Long userId) {
        return baseMapper.getUserRolesByUserId(userId);
    }

    @Override
    public String changeStatus(Long roleId, Integer status) {
        Role role = super.getById(roleId);
        if (role == null) {
            return "角色不存在";
        }
        role.setStatus(status);
        role.updateById();
        return "ok";
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String create(RoleForm form) {
        Role role = super.getOne(new LambdaQueryWrapper<Role>().eq(Role::getCode, form.getCode()));
        if (role != null) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "角色代码已存在");
        }
        if (!form.getSysRoleFlag() && StrUtil.isEmpty(form.getSysRoleCode())) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "内置角色代码不能为空");
        }
        role = BeanUtil.copyProperties(form, Role.class);
        role.insert();
        // 非内置角色，所属内置角色不为空，则继承内置角色权限
        if (!form.getSysRoleFlag() && StrUtil.isNotEmpty(form.getSysRoleCode())) {
            List<RoleMenu> roleMenus = baseMapper.getRoleMenusByRoleCode(form.getSysRoleCode());
            List<RoleMenu> newRoleMenus = new ArrayList<>();
            for (RoleMenu roleMenu : roleMenus) {
                RoleMenu newRoleMenu = new RoleMenu();
                newRoleMenu.setRoleId(role.getId()).setMenuId(roleMenu.getMenuId());
                newRoleMenus.add(newRoleMenu);
            }
            if (CollUtil.isNotEmpty(newRoleMenus)) {
                roleMenuService.saveBatch(newRoleMenus);
            }
        }
        return "success";
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String updateRole(RoleForm form) {
        if (Objects.isNull(form.getId())) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "角色ID不能为空");
        }
        Role role = super.getOne(new LambdaQueryWrapper<Role>().eq(Role::getCode, form.getCode()).ne(Role::getId, form.getId()));
        if (role != null) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "角色代码已存在");
        }
        if (!form.getSysRoleFlag() && StrUtil.isEmpty(form.getSysRoleCode())) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "内置角色代码不能为空");
        }
        role = BeanUtil.copyProperties(form, Role.class);
        role.updateById();
        // 非内置角色，所属内置角色不为空，则继承内置角色权限
        if (!form.getSysRoleFlag() && StrUtil.isNotEmpty(form.getSysRoleCode())) {
            List<RoleMenu> roleMenus = baseMapper.getRoleMenusByRoleCode(form.getSysRoleCode());
            List<RoleMenu> newRoleMenus = new ArrayList<>();
            for (RoleMenu roleMenu : roleMenus) {
                RoleMenu newRoleMenu = new RoleMenu();
                newRoleMenu.setRoleId(role.getId()).setMenuId(roleMenu.getMenuId());
                newRoleMenus.add(newRoleMenu);
            }
            roleMenuService.remove(new LambdaQueryWrapper<RoleMenu>().eq(RoleMenu::getRoleId, role.getId()));
            if (CollUtil.isNotEmpty(newRoleMenus)) {
                roleMenuService.saveBatch(newRoleMenus);
            }
        }
        return "success";
    }

    @Override
    public List<KeyValue<String, String>> getRoleSelectList(String roleCode) {
        LambdaQueryWrapper<Role> queryWrapper = new QueryWrapper<Role>()
            .lambda()
            .and(StrUtil.isNotBlank(roleCode), wapper -> wapper.eq(Role::getCode, roleCode)
                .or()
                .eq(Role::getSysRoleCode, roleCode)
            )
            .select(Role::getCode, Role::getName);
        List<Role> roles = baseMapper.selectList(queryWrapper);
        return roles.stream().map(item -> new KeyValue<>(item.getCode(), item.getName())).collect(Collectors.toList());
    }

    @Override
    public List<RoleDto> getRolestList(String roleCode) {
        LambdaQueryWrapper<Role> queryWrapper = new QueryWrapper<Role>().lambda().eq(Role::getCode, roleCode).or().eq(Role::getSysRoleCode, roleCode).select(Role::getCode, Role::getName);
        List<Role> roles = baseMapper.selectList(queryWrapper);
        return roles.stream().map(item -> BeanUtil.copyProperties(item, RoleDto.class)).collect(Collectors.toList());
    }
}
