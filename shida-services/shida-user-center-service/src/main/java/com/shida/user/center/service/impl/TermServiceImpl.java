package com.shida.user.center.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shida.enums.RestCode;
import com.shida.exceptions.ServiceException;
import com.shida.pojo.vo.KeyValue;
import com.shida.pojo.vo.PageInfo;
import com.shida.user.center.entity.Term;
import com.shida.user.center.mapper.TermMapper;
import com.shida.user.center.pojo.form.TermForm;
import com.shida.user.center.pojo.search.TermSearch;
import com.shida.user.center.pojo.vo.TermInfo;
import com.shida.user.center.service.ITermService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Service
public class TermServiceImpl extends ServiceImpl<TermMapper, Term> implements ITermService {

    @Override
    public PageInfo<TermInfo> getPageData(TermSearch search) {
        IPage<Term> page = new Page<>(search.getPageNumber(), search.getPageSize());
        page = super.page(page, new LambdaQueryWrapper<Term>().orderByDesc(Term::getId));
        return PageInfo.fromMybatisPage(page, TermInfo.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String create(TermForm form) {
        if (super.exists(new LambdaQueryWrapper<Term>().eq(Term::getTermName, form.getTermName()))) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "学期名称已存在");
        }
        if (form.getStartDate().isAfter(form.getStopDate())) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "开始时间不能大于结束时间");
        }

        Term term = BeanUtil.copyProperties(form, Term.class);
        LocalDate startDate = form.getStartDate().atStartOfDay().toLocalDate();
        LocalDate stopDate = form.getStopDate().plusMonths(1).atStartOfDay().toLocalDate();

        // 求两个时间中间的月份列表
        List<Integer> months = new ArrayList<>();
        do {
            months.add(startDate.getMonthValue());
            startDate = startDate.plusMonths(1);
        } while (startDate.isBefore(stopDate));

        term.setMonths(StrUtil.join(",", months));

        term.setStatus(0);
        term.insert();
        return "success";
    }

    @Override
    public String updateTerm(TermForm form) {
        if (Objects.isNull(form.getId())) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "id不能为空");
        }
        if (super.exists(new LambdaQueryWrapper<Term>().ne(Term::getId, form.getId())
            .eq(Term::getTermName, form.getTermName()))) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "学期名称已存在");
        }
        if (form.getStartDate().isAfter(form.getStopDate())) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "开始时间不能大于结束时间");
        }

        Term term = BeanUtil.copyProperties(form, Term.class);
        LocalDate startDate = form.getStartDate().atStartOfDay().toLocalDate();
        LocalDate stopDate = form.getStopDate().plusMonths(1).atStartOfDay().toLocalDate();

        // 求两个时间中间的月份列表
        List<Integer> months = new ArrayList<>();
        do {
            months.add(startDate.getMonthValue());
            startDate = startDate.plusMonths(1);
        } while (startDate.isBefore(stopDate));

        term.setMonths(StrUtil.join(",", months));

        term.setStatus(0);
        term.updateById();
        return "success";
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String changeStatus(Long id, Integer status) {
        Term term = super.getById(id);
        if (term.getStatus().equals(2)) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "该学期已归档，不能修改状态，请知悉。");
        }
        term.setStatus(status);
        term.updateById();
        if (status == 2) {
            // TODO 归档操作
        } else if (status == 1) {
            LambdaUpdateWrapper<Term> wrapper = new LambdaUpdateWrapper<>();
            wrapper.set(Term::getStatus, 0)
                .ne(Term::getId, id)
                .ne(Term::getStatus, 2);
            super.update(wrapper);
        }
        return "success";
    }

    @Override
    public List<KeyValue<String, String>> getTermSelectList() {
        List<Term> termList = super.list();
        if (termList != null && !termList.isEmpty()) {
            return termList.stream().map(term -> new KeyValue<>(term.getId().toString(), term.getTermName(), term.getStatus()+"")).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }
}
