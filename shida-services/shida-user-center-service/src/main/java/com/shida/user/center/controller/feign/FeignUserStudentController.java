package com.shida.user.center.controller.feign;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.shida.pojo.vo.KeyValue;
import com.shida.user.center.entity.UserStudent;
import com.shida.user.center.service.IUserStudentService;
import com.shida.userCenter.api.IFeignUserStudentService;
import com.shida.userCenter.dto.UserStudentDto;
import io.seata.spring.annotation.GlobalTransactional;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Tag(name = "Feign-学生证件号唯一性检查")
@RestController
public class FeignUserStudentController implements IFeignUserStudentService {

    private static final String STUDENT_CERT_NUMBER_KEY = "student:cert_number:";

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Resource
    private IUserStudentService userStudentService;

    @Operation(summary = "检查学生证件号是否已存在")
    @Override
    public Boolean checkHasByCertNumber(String certNumber) {
        String cNumber = StrUtil.trim(certNumber);
        if (StrUtil.isBlank(cNumber)) {
            return true;
        }
        Boolean has = redisTemplate.hasKey(STUDENT_CERT_NUMBER_KEY + cNumber);
        if (!has) {
            has = userStudentService.lambdaQuery().eq(UserStudent::getCertNumber, cNumber).exists();
        }
        if (has) {
            redisTemplate.opsForValue().set(STUDENT_CERT_NUMBER_KEY + cNumber, cNumber, 10, TimeUnit.DAYS);
        }
        return has;
    }

    @Operation(summary = "批量检查学生证件号是否已存在")
    @Override
    public List<KeyValue<String, Boolean>> getHasCertNumbersByCertNumbers(List<String> certNumbers) {
        if (CollUtil.isEmpty(certNumbers)) {
            return new ArrayList<>();
        }
        List<String> trimCertNumbers = certNumbers.stream().map(StrUtil::trim).collect(Collectors.toList());
        List<UserStudent> userStudents = userStudentService.lambdaQuery().in(UserStudent::getCertNumber, trimCertNumbers).list();
        List<KeyValue<String, Boolean>> result = new ArrayList<>();
        trimCertNumbers.forEach(certNumber -> {
            String cNumber = StrUtil.trim(certNumber);
            if (StrUtil.isBlank(cNumber)) {
                result.add(new KeyValue<>(certNumber, true));
            }
            Boolean has = redisTemplate.hasKey(STUDENT_CERT_NUMBER_KEY + cNumber);
            if (!has) {
                has = userStudents.stream().anyMatch(userStudent -> userStudent.getCertNumber().equals(cNumber));
            }
            result.add(new KeyValue<>(certNumber, has));
        });
        return result;
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Operation(summary = "添加用户学生关联信息")
    @Override
    public UserStudentDto addUserStudent(UserStudentDto userStudentDto) {
        UserStudent userStudent = BeanUtil.copyProperties(userStudentDto, UserStudent.class);
        userStudentService.save(userStudent);
        userStudentDto.setId(userStudent.getId());
        return userStudentDto;
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Operation(summary = "批量添加用户学生关联信息")
    @Override
    public List<UserStudentDto> batchAddUserStudent(List<UserStudentDto> userStudentList) {
        if (CollUtil.isEmpty(userStudentList)) {
            return new ArrayList<>();
        }
        userStudentList.forEach(userStudentDto -> {
            if (StrUtil.isBlank(userStudentDto.getCertNumber().trim())) {
                throw new RuntimeException("学生证件号码不能为空");
            }
            if (Objects.isNull(userStudentDto.getUserId())) {
                throw new RuntimeException("学生账户ID不能为空");
            }
            if (Objects.isNull(userStudentDto.getRegionId())) {
                throw new RuntimeException("学生区县ID不能为空");
            }
            if (Objects.isNull(userStudentDto.getSchoolId())) {
                throw new RuntimeException("学生学校ID不能为空");
            }
        });
        List<UserStudent> userStudents = userStudentList.stream().map(userStudentDto -> BeanUtil.copyProperties(userStudentDto, UserStudent.class)).collect(Collectors.toList());
        userStudentService.saveBatch(userStudents);
        userStudents.forEach(userStudent -> userStudentList.stream().filter(us -> us.getCertNumber().equals(userStudent.getCertNumber()))
            .findFirst().ifPresent(us -> us.setId(userStudent.getId())));
        return userStudentList;
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Operation(summary = "更新用户学生关联信息", description = "更新学生信息，转学时，修改证件号码时用")
    @Override
    public String updateUserStudentById(UserStudentDto userStudentDto) {
        if (Objects.isNull(userStudentDto.getId())) {
            throw new RuntimeException("学生ID不能为空");
        }
        UserStudent userStudent = userStudentService.getById(userStudentDto.getId());
        if (userStudent != null) {
            BeanUtil.copyProperties(userStudentDto, userStudent);
            userStudentService.updateById(userStudent);
        } else {
            userStudent = BeanUtil.copyProperties(userStudentDto, UserStudent.class);
            userStudentService.save(userStudent);
        }
        redisTemplate.delete(STUDENT_CERT_NUMBER_KEY + userStudent.getCertNumber());
        return "success";
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Operation(summary = "根据证件号码删除")
    @Override
    public String deleteUserStudentByCertNumber(String certNumber) {
        UserStudent userStudent = userStudentService.lambdaQuery().eq(UserStudent::getCertNumber, certNumber).one();
        if (userStudent != null) {
            userStudentService.removeById(userStudent);
        }
        redisTemplate.delete(STUDENT_CERT_NUMBER_KEY + certNumber);
        return "success";
    }

}
