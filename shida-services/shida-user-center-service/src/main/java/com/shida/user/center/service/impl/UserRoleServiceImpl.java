package com.shida.user.center.service.impl;

import com.shida.user.center.entity.UserRole;
import com.shida.user.center.mapper.UserRoleMapper;
import com.shida.user.center.service.IUserRoleService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shida.userCenter.dto.RoleDto;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 用户角色表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Service
public class UserRoleServiceImpl extends ServiceImpl<UserRoleMapper, UserRole> implements IUserRoleService {

    @Override
    public List<RoleDto> getUserRoleDtoList(List<Long> userIds) {
        return baseMapper.getUserRoleDtoList(userIds);
    }
}
