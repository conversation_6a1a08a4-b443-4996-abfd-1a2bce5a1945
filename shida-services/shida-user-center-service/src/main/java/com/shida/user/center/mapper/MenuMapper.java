package com.shida.user.center.mapper;

import com.shida.pojo.vo.MenuInfo;
import com.shida.pojo.vo.PermissionInfo;
import com.shida.user.center.entity.Menu;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 菜单信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
public interface MenuMapper extends BaseMapper<Menu> {

    List<PermissionInfo> getPermissionsByRoleIds(@Param("roleIds") List<Long> roleIds);

    void setNullByField(@Param("id") Long id, @Param("fieldName") String fieldName);

    List<MenuInfo> getRoleFullMenus(@Param("roleIds") List<Long> roleIds);
}
