package com.shida.user.center.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.stp.StpUtil;
import com.shida.AccountCommonService;
import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.config.AppConfigure;
import com.shida.constant.RedisKeyConstant;
import com.shida.noRepeatSubmit.NoRepeatSubmit;
import com.shida.pojo.form.KeyForm;
import com.shida.pojo.vo.AccountInfo;
import com.shida.pojo.vo.PageInfo;
import com.shida.user.center.pojo.form.UserForm;
import com.shida.user.center.pojo.search.UserSearch;
import com.shida.user.center.pojo.vo.UserInfo;
import com.shida.user.center.service.IUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

@Tag(name = "user-用户管理")
@RestController
@RequestMapping("/user/manage")
public class UserManageController {
    @Resource
    private IUserService userService;

    @SaCheckPermission("user:list")
    @Operation(summary = "获取用户列表")
    @PostMapping("/getPageData")
    public PageInfo<UserInfo> getPageData(@RequestBody UserSearch search) {
        AccountInfo accountInfo = AccountCommonService.getCurrentUser();
        assert accountInfo != null;
        if (!accountInfo.getRegionCode().equals(AppConfigure.getCityCode())) {
            search.setDeptId(accountInfo.getRegionId());
        }
        return userService.getPageData(search);
    }


    @SaCheckPermission("user:change:status")
    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    @Operation(summary = "修改账户状态")
    @PostMapping("/changeUserStatus/{userId}/{status}")
    public String changeUserStatus(@PathVariable Long userId, @PathVariable Integer status) {
        return userService.changeUserStatus(userId, status);
    }

    @SaCheckPermission("user:add")
    @Operation(summary = "创建账户")
    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_ADD)
    @PostMapping("/create")
    public String create(@RequestBody @Valid UserForm form) {
        return userService.createUser(form);
    }

    @SaCheckPermission("user:edit")
    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    @Operation(summary = "修改账户信息")
    @PostMapping("/update")
    public String update(@RequestBody @Valid UserForm form) {
        return userService.updateUserInfo(form);
    }

    @SaCheckPermission("user:reset:password")
    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    @Operation(summary = "重置密码")
    @PostMapping("/resetPassword")
    public String resetPassword(@RequestBody @Valid KeyForm<Long> form) {
        return userService.resetPassword(form.getKey());
    }

    @SaCheckPermission("user:delete")
    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_DELETE)
    @Operation(summary = "删除账户")
    @PostMapping("/delete")
    public String deleteUser(@RequestBody @Valid KeyForm<Long> form) {
        return userService.deleteUser(form.getKey());
    }
}
