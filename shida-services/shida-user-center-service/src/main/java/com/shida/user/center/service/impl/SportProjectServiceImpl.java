package com.shida.user.center.service.impl;

import com.shida.pojo.vo.KeyValue;
import com.shida.user.center.entity.SportProject;
import com.shida.user.center.mapper.SportProjectMapper;
import com.shida.user.center.service.ISportProjectService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 体育项目信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Service
public class SportProjectServiceImpl extends ServiceImpl<SportProjectMapper, SportProject> implements ISportProjectService {

    @Override
    public List<KeyValue<String, String>> getSportProjectSelectList() {
        List<SportProject> list = super.list();
        return list.stream().map(sportProject -> new KeyValue<>(sportProject.getCode(), sportProject.getName()))
                .collect(Collectors.toList());
    }
}
