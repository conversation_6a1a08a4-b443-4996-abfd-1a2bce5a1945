package com.shida.user.center.service.impl;

import cn.dev33.satoken.secure.SaSecureUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shida.IFeignAccountService;
import com.shida.IFeignSchoolService;
import com.shida.constant.RoleConstant;
import com.shida.dto.SchoolDto;
import com.shida.enums.NationEnum;
import com.shida.enums.RestCode;
import com.shida.exceptions.ServiceException;
import com.shida.pojo.vo.*;
import com.shida.user.center.entity.Role;
import com.shida.user.center.entity.User;
import com.shida.user.center.entity.UserOrg;
import com.shida.user.center.entity.UserRole;
import com.shida.user.center.mapper.UserMapper;
import com.shida.user.center.pojo.form.UserForm;
import com.shida.user.center.pojo.search.UserSearch;
import com.shida.user.center.pojo.vo.NationVo;
import com.shida.user.center.pojo.vo.UserInfo;
import com.shida.user.center.service.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shida.userCenter.dto.ChangeUserPasswordDto;
import com.shida.userCenter.dto.CreateUserDto;
import com.shida.userCenter.dto.RoleDto;
import com.shida.userCenter.dto.UserDto;
import com.shida.utils.IdUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 账户信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements IUserService {
    @Resource
    private IFeignSchoolService feignSchoolService;
    @Resource
    private IFeignAccountService feignAccountService;
    @Resource
    private IUserOrgService userOrgService;
    @Resource
    private IRoleService roleService;
    @Resource
    private IUserRoleService userRoleService;
    @Resource
    private IMenuService menuService;

    @Override
    public User findByUsername(String username) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getUsername, username);
        return super.getOne(queryWrapper);
    }

    @Override
    public UserDto findUserDtoByUsername(String username) {
        UserDto result = baseMapper.findUserDtoByUsername(username);
        if (result != null && result.getSchoolId() != null) {
            SchoolDto schoolDto = feignSchoolService.getByRegionIdAndSchoolId(result.getRegionId(), result.getSchoolId());
            if (schoolDto != null) {
                result.setSchoolName(schoolDto.getName());
                result.setSchoolCode(schoolDto.getCode());
            }
        }
        if (result != null) {
            List<RoleInfo> roles = roleService.getUserRolesByUserId(result.getId()).stream().filter(roleInfo -> roleInfo.getStatus() == 1).collect(Collectors.toList());
            List<PermissionInfo> permissions = menuService.getPermissionsByRoleIds(roles.stream().map(RoleInfo::getId).collect(Collectors.toList()));
            List<MenuInfo> menus = menuService.getMenusByRoleIds(roles.stream().map(RoleInfo::getId).collect(Collectors.toList()));
            return result.setRoles(roles).setPermissions(permissions).setMenus(menus);
        }
        return result;
    }

    @Override
    public UserDto findUserDtoByUserId(Long userId) {
        UserDto result = baseMapper.findUserDtoByUserId(userId);
        if (result != null && result.getSchoolId() != null) {
            SchoolDto schoolDto = feignSchoolService.getByRegionIdAndSchoolId(result.getRegionId(), result.getSchoolId());
            if (schoolDto != null) {
                result.setSchoolName(schoolDto.getName());
                result.setSchoolCode(schoolDto.getCode());
            }
        }
        if (result != null) {
            List<RoleInfo> roles = roleService.getUserRolesByUserId(result.getId()).stream().filter(roleInfo -> roleInfo.getStatus() == 1).collect(Collectors.toList());
            List<PermissionInfo> permissions = menuService.getPermissionsByRoleIds(roles.stream().map(RoleInfo::getId).collect(Collectors.toList()));
            List<MenuInfo> menus = menuService.getMenusByRoleIds(roles.stream().map(RoleInfo::getId).collect(Collectors.toList()));
            if(roles.stream().anyMatch(roleInfo -> roleInfo.getAdminFlag()==1)){
                result.setAdminFlag(true);
            }
            return result.setRoles(roles).setPermissions(permissions).setMenus(menus);
        }
        return result;
    }

    @Override
    public List<UserDto> findUserDtoList(List<Long> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return new ArrayList<>();
        }
        List<UserDto> result = baseMapper.findUserDtoByUserIds(userIds);
        if (CollUtil.isNotEmpty(result)) {
            Map<Long, List<UserDto>> deptGroups = result.stream().collect(Collectors.groupingBy(UserDto::getRegionId));
            for (Long deptId : deptGroups.keySet()) {
                List<SchoolDto> schoolDtos = feignSchoolService.getByRegionIdAndSchoolIds(deptId, deptGroups.get(deptId).stream().map(UserDto::getSchoolId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
                for (UserDto userDto : deptGroups.get(deptId)) {
                    schoolDtos.stream().filter(schoolDto -> schoolDto.getId().equals(userDto.getSchoolId()))
                            .findFirst().ifPresent(schoolDto -> {
                                userDto.setSchoolName(schoolDto.getName());
                                userDto.setSchoolCode(schoolDto.getCode());
                            });
                    List<RoleInfo> roles = roleService.getUserRolesByUserId(userDto.getId()).stream().filter(roleInfo -> roleInfo.getStatus() == 1).collect(Collectors.toList());
                    List<PermissionInfo> permissions = menuService.getPermissionsByRoleIds(roles.stream().map(RoleInfo::getId).collect(Collectors.toList()));
                    List<MenuInfo> menus = menuService.getMenusByRoleIds(roles.stream().map(RoleInfo::getId).collect(Collectors.toList()));
                    userDto.setRoles(roles).setPermissions(permissions).setMenus(menus);
                }
            }
        }
        return result;
    }



    @Override
    public List<UserDto> findListByUsernames(List<String> usernameList) {
        if (CollUtil.isEmpty(usernameList)) {
            return new ArrayList<>();
        }
        List<UserDto> result = baseMapper.findUserDtoByUsernames(usernameList);
        if (CollUtil.isNotEmpty(result)) {
            Map<Long, List<UserDto>> deptGroups = result.stream().collect(Collectors.groupingBy(UserDto::getRegionId));
            for (Long deptId : deptGroups.keySet()) {
                List<SchoolDto> schoolDtos = feignSchoolService.getByRegionIdAndSchoolIds(deptId, deptGroups.get(deptId).stream().map(UserDto::getSchoolId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
                for (UserDto userDto : deptGroups.get(deptId)) {
                    schoolDtos.stream().filter(schoolDto -> schoolDto.getId().equals(userDto.getSchoolId()))
                            .findFirst().ifPresent(schoolDto -> {
                                userDto.setSchoolName(schoolDto.getName());
                                userDto.setSchoolCode(schoolDto.getCode());
                            });
                    List<RoleInfo> roles = roleService.getUserRolesByUserId(userDto.getId()).stream().filter(roleInfo -> roleInfo.getStatus() == 1).collect(Collectors.toList());
                    List<PermissionInfo> permissions = menuService.getPermissionsByRoleIds(roles.stream().map(RoleInfo::getId).collect(Collectors.toList()));
                    List<MenuInfo> menus = menuService.getMenusByRoleIds(roles.stream().map(RoleInfo::getId).collect(Collectors.toList()));
                    userDto.setRoles(roles).setPermissions(permissions).setMenus(menus);
                }
            }
        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String changeUserPassword(ChangeUserPasswordDto dto) {
        if (!dto.getNewPassword().equals(dto.getConfirmPassword())) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "新密码与确认密码不一致");
        }
        User user = super.getById(dto.getUserId());
        if (user == null) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "用户不存在");
        }
        String password = SaSecureUtil.md5BySalt(dto.getOldPassword(), user.getSalt());
        if (!user.getPassword().equals(password)) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "旧密码错误");
        }
        user.setPassword(SaSecureUtil.md5BySalt(dto.getNewPassword(), user.getSalt()));
        user.setDefaultPasswordFlag(false);
        super.updateById(user);
        feignAccountService.kickOutUser(user.getId());
        return "ok";
    }

    @Override
    public PageInfo<UserInfo> getPageData(UserSearch search) {
        IPage<UserInfo> page = new Page<>(search.getPageNumber(), search.getPageSize());
        page = baseMapper.getPageData(page, search);
        Map<Long, List<UserInfo>> deptGroups = page.getRecords().stream().filter(
                        userInfo -> userInfo.getDeptFloor().equals(2)
                                && Objects.nonNull(userInfo.getSchoolId()))
                .collect(Collectors.groupingBy(UserInfo::getRegionId));
        for (Long deptId : deptGroups.keySet()) {
            List<Long> schoolIds = deptGroups.get(deptId).stream()
                    .map(UserInfo::getSchoolId).distinct().collect(Collectors.toList());
            List<SchoolDto> schoolDtoList = feignSchoolService.getByRegionIdAndSchoolIds(deptId, schoolIds);
            deptGroups.get(deptId).forEach(userInfo -> {
                schoolDtoList.stream().filter(schoolDto -> schoolDto.getId().equals(userInfo.getSchoolId()))
                        .findFirst().ifPresent(schoolDto -> {
                            userInfo.setSchoolName(schoolDto.getName());
                            userInfo.setSchoolCode(schoolDto.getCode());
                        });
            });
        }
        if (CollUtil.isNotEmpty(page.getRecords())) {
            page.getRecords().forEach(userInfo -> {
                List<RoleInfo> roles = roleService.getUserRolesByUserId(userInfo.getId());
                userInfo.setRoles(roles);
            });
        }

        return PageInfo.fromMybatisPage(page);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String changeUserStatus(Long userId, Integer status) {
        User user = super.getById(userId);
        if (user == null) {
            return "ok";
        }
        user.setStatus(status);
        super.updateById(user);
        if (status == 0) {
            feignAccountService.kickOutUser(userId);
        }
        return "ok";
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String updateUserInfo(UserForm form) {
        if (Objects.isNull(form.getId())) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "用户ID不能为空");
        }

        if (super.count(new LambdaQueryWrapper<User>().ne(User::getId, form.getId())
                .eq(User::getUsername, form.getUsername())) > 0) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "账户已存在，请更改账户名称");
        }

        User user = BeanUtil.copyProperties(form, User.class);
        super.updateById(user);
        UserDto userDto = baseMapper.findUserDtoByUserId(form.getId());
        UserOrg userOrg = userOrgService.getOne(new LambdaQueryWrapper<UserOrg>().eq(UserOrg::getUserId, user.getId())
                .last("limit 1"));
        if (!userDto.getRegionId().equals(form.getRegionId())) {
            userOrg.setOrgId(form.getRegionId());
        }
        if (Objects.nonNull(form.getSchoolId()) && !userDto.getSchoolId().equals(form.getSchoolId())) {
            userOrg.setSchoolId(form.getSchoolId());
        }
        List<UserRole> roleCodes = new ArrayList<>();
        if (CollUtil.isNotEmpty(form.getRoleCodes())) {
            for (String code : form.getRoleCodes()) {
                Role role = roleService.getOne(new LambdaQueryWrapper<Role>().eq(Role::getCode, code));
                roleCodes.add(new UserRole().setUserId(user.getId()).setRoleId(role.getId()));
            }
            userRoleService.remove(new LambdaQueryWrapper<UserRole>().eq(UserRole::getUserId, user.getId()));
            userRoleService.saveBatch(roleCodes);
        }
        userOrg.updateById();
        return "ok";
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String resetPassword(Long userId) {
        String password = RandomUtil.randomString(8);
        User user = new User();
        user.setId(userId);
        user.setSalt(RandomUtil.randomString(32));
        String passwordBySalt = SaSecureUtil.md5BySalt(password, user.getSalt());
        user.setPassword(passwordBySalt);
        user.setDefaultPassword(password);
        user.setDefaultPasswordFlag(true);
        user.updateById();
        feignAccountService.kickOutUser(userId);
        return "ok";
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String createUser(UserForm form) {
        addUser(form);
        return "ok";
    }

    /**
     * 新增用户
     * @param form
     * @return 用户ID
     */
    private UserDto addUser(UserForm form) {
        if (super.count(new LambdaQueryWrapper<User>().eq(User::getUsername, form.getUsername())) > 0) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "账户已存在，请更改账户名称");
        }

        User user = BeanUtil.copyProperties(form, User.class);
        String password = RandomUtil.randomString(8);
        user.setSalt(RandomUtil.randomString(32));
        user.setPassword(SaSecureUtil.md5BySalt(password, user.getSalt()));
        user.setDefaultPassword(password);
        user.setDefaultPasswordFlag(true);
        user.setId(IdUtil.getSnowflakeNextId());
        user.insert();
        List<UserRole> userRoles = new ArrayList<>();
        if (CollUtil.isEmpty(form.getRoleCodes())) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "请选择角色");
        }
        boolean adminFlag = false;
        for (String code : form.getRoleCodes()) {
            Role role = roleService.getOne(new LambdaQueryWrapper<Role>().eq(Role::getCode, code));
            userRoles.add(new UserRole().setUserId(user.getId()).setRoleId(role.getId()));
            if (!adminFlag) {
                adminFlag = role.getAdminFlag();
            }
        }
        userRoleService.saveBatch(userRoles);
        UserOrg userOrg = new UserOrg();
        userOrg.setUserId(user.getId()).setOrgId(form.getRegionId());
        if (Objects.nonNull(form.getSchoolId())) {
            userOrg.setSchoolId(form.getSchoolId());
        }
        userOrg.setMasterFlag(adminFlag);
        boolean insert = userOrg.insert();
        if (!insert) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "新增用户失败");
        }
        UserDto userDto = BeanUtil.copyProperties(user, UserDto.class);
        userDto.setRoles(userRoles.stream().map(userRole -> {
            Role role = roleService.getById(userRole.getRoleId());
            return BeanUtil.copyProperties(role, RoleInfo.class);
        }).collect(Collectors.toList()));
        return userDto;
    }

    @Override
    public UserDto createUser(UserDto userDto) {
        UserForm form = BeanUtil.copyProperties(userDto, UserForm.class);
        form.setRoleCodes(userDto.getRoles().stream().map(RoleInfo::getRoleCode).collect(Collectors.toList()));
        return addUser(form);
    }

    @Override
    public List<UserDto> getMasterUserListBySchoolId(Long regionId, Long schoolId) {
        return baseMapper.getMasterUserListBySchoolId(regionId, schoolId, RoleConstant.SCHOOL_ADMIN_CODE);
    }

    @Override
    public List<UserDto> getMasterUserListBySchoolIds(Long regionId, List<Long> schoolIds) {
        return baseMapper.getMasterUserListBySchoolIds(regionId, schoolIds, RoleConstant.SCHOOL_ADMIN_CODE);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String deleteUser(Long userId) {
        User user = super.getById(userId);
        if (user == null) {
            return "ok";
        }
        user.deleteById();
        userRoleService.remove(new LambdaQueryWrapper<UserRole>().eq(UserRole::getUserId, userId));
        userOrgService.remove(new LambdaQueryWrapper<UserOrg>().eq(UserOrg::getUserId, userId));
        feignAccountService.kickOutUser(userId);
        return "ok";
    }

    @Override
    public List<RoleDto> getUserRoleDtoList(List<Long> userIds) {
        return userRoleService.getUserRoleDtoList(userIds);
    }

    @Override
    public List<NationVo> getNationVoList() {
        List<NationVo> nationList = new ArrayList<>();
        for (NationEnum p : NationEnum.values()) {
            nationList.add(new NationVo().setId(p.getCode()).setName(p.getMessage()));
        }
        return nationList;
    }
}
