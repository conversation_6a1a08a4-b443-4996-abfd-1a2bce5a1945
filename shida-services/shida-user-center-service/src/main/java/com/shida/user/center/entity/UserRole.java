package com.shida.user.center.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shida.entity.BaseEntity;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户角色表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_user_role")
@Schema(name = "UserRole", description = "用户角色表")
public class UserRole extends BaseEntity<UserRole> {

    private static final long serialVersionUID = 1L;

    private Long roleId;

    private Long userId;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
