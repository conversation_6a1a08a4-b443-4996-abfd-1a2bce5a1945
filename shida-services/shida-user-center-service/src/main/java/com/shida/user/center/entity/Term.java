package com.shida.user.center.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shida.entity.BaseEntity;

import java.io.Serializable;
import java.time.LocalDate;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_term")
@Schema(name = "Term", description = "")
public class Term extends BaseEntity<Term> {

    private static final long serialVersionUID = 1L;

    @Schema(description = "学年名称")
    private String yearName;

    @Schema(description = "学期名称")
    private String termName;

    @Schema(description = "开始时间")
    private LocalDate startDate;

    @Schema(description = "结束日期")
    private LocalDate stopDate;

    @Schema(description = "学期状态，0禁用，1启用，2归档")
    private Integer status;

    @Schema(description = "包含月份,以,号分割")
    private String months;


    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
