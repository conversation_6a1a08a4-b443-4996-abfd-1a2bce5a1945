package com.shida.user.center.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shida.entity.BaseEntity;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 菜单信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_menu")
@Schema(name = "Menu", description = "菜单信息表")
public class Menu extends BaseEntity<Menu> {

    private static final long serialVersionUID = 1L;

    @Schema(description = "菜单名称")
    private String name;

    @Schema(description = "菜单代码")
    private String code;

    @Schema(description = "菜单类型（M目录 C菜单 F按钮）")
    private String type;

    @Schema(description = "权限代码")
    private String permission;

    @Schema(description = "排序字段")
    private Integer orderNum;

    @Schema(description = "父菜单ID")
    private Long parentId;

    @Schema(description = "路由地址")
    private String router;

    @Schema(description = "组件地址")
    private String component;

    @Schema(description = "菜单显示状态，0隐藏，1显示")
    private Integer visible;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
