package com.shida.user.center.pojo.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
@Accessors(chain = true)
@Schema(name = "RoleForm", description = "角色表单")
public class RoleForm implements Serializable {
    @Schema(description = "ID")
    private Long id;
    @Schema(description = "名称")
    @NotBlank(message = "名称不能为空")
    @Size(max = 32, message = "名称长度不能超过32")
    private String name;
    @Schema(description = "角色代码")
    @NotBlank(message = "角色代码不能为空")
    @Size(max = 32, message = "角色代码长度不能超过32")
    private String code;
    @Schema(description = "角色状态")
    @NotNull(message = "角色状态不能为空")
    private Integer status;
    @Schema(description = "所属内置角色代码")
    private String sysRoleCode;
    @Schema(description = "所属内置角色标识")
    private Boolean sysRoleFlag;
}
