package com.shida.user.center.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@Schema(name = "TestStandardInfo", description = "体质健康测试评分标准信息")
public class TestStandardInfo implements Serializable {
    
    @Schema(description = "主键ID")
    private Long id;
    
    @Schema(description = "创建时间")
    private String createTime;
    
    @Schema(description = "唯一业务编码")
    private String code;
    
    @Schema(description = "项目标题")
    private String title;
    
    @Schema(description = "单位")
    private String unit;
    
    @Schema(description = "年级")
    private String gradeName;
    
    @Schema(description = "等级")
    private String level;
    
    @Schema(description = "分数")
    private Integer score;
    
    @Schema(description = "最小值")
    private BigDecimal minValue;
    
    @Schema(description = "最大值")
    private BigDecimal maxValue;
    
    @Schema(description = "性别（从code推断：1开头为男生，2开头为女生）")
    private String gender;
}
