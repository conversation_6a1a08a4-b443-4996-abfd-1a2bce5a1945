package com.shida.user.center.controller.feign;

import cn.hutool.core.bean.BeanUtil;
import com.shida.user.center.entity.Org;
import com.shida.user.center.service.IOrgService;
import com.shida.userCenter.api.IFeignRegionService;
import com.shida.userCenter.dto.OrgDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Tag(name = "Feign-地区服务")
@RestController
public class FeignRegionController implements IFeignRegionService {
    @Resource
    private IOrgService orgService;

    @Operation(summary = "获取区县信息")
    @Override
    public OrgDto getRegionById(Long regionId) {
        Org region = orgService.getById(regionId);
        if (region.getFloor().equals(2)) {
            return BeanUtil.copyProperties(region, OrgDto.class);
        } else {
            return null;
        }
    }
}
