package com.shida.user.center.controller.feign;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.shida.user.center.entity.Org;
import com.shida.user.center.service.IOrgService;
import com.shida.userCenter.api.IFeignRegionService;
import com.shida.userCenter.dto.OrgDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Tag(name = "Feign-地区服务")
@RestController
public class FeignRegionController implements IFeignRegionService {
    @Resource
    private IOrgService orgService;

    @Operation(summary = "获取区县信息")
    @Override
    public OrgDto getRegionById(Long regionId) {
        Org region = orgService.getById(regionId);
        if (region.getFloor().equals(2)) {
            return BeanUtil.copyProperties(region, OrgDto.class);
        } else {
            return null;
        }
    }


    @Operation(summary = "根据区县ID获取list")
    @Override
    public List<OrgDto> getRegionListByIds(List<Long> regionIds) {
        if (CollUtil.isEmpty(regionIds)) {
            return Collections.emptyList();
        }
        List<Org> regionList = orgService.list(new LambdaQueryWrapper<Org>().in(Org::getId, regionIds));
        return BeanUtil.copyToList(regionList, OrgDto.class);
    }

}
