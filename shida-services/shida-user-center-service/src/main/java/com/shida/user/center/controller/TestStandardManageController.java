package com.shida.user.center.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.noRepeatSubmit.NoRepeatSubmit;
import com.shida.pojo.vo.PageInfo;
import com.shida.user.center.pojo.search.TestStandardSearch;
import com.shida.user.center.pojo.vo.TestStandardInfo;
import com.shida.user.center.pojo.vo.TestStandardMatrixInfo;
import com.shida.user.center.service.IPTestStandardService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

@Tag(name = "testStandard-体质健康测试评分标准")
@RestController
@RequestMapping("/test-standard/manage")
public class TestStandardManageController {

    @Resource
    private IPTestStandardService testStandardService;

    @SaCheckPermission(value = "testStandard:list")
    @Operation(summary = "获取体质健康测试评分标准矩阵数据")
    @PostMapping("/getMatrixData")
    public List<TestStandardMatrixInfo> getMatrixData(@RequestBody @Valid TestStandardSearch search) {
        return testStandardService.getMatrixData(search);
    }


    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    @SaCheckPermission(value = "testStandard:update")
    @Operation(summary = "更新矩阵单元格数据")
    @PostMapping("/updateMatrixCell")
    public boolean updateMatrixCell(@RequestBody @Valid TestStandardInfo testStandardInfo) {
        return testStandardService.updateMatrixCell(testStandardInfo);
    }

    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_IMPORT)
    @SaCheckPermission(value = "testStandard:import")
    @Operation(summary = "批量导入体质健康测试评分标准")
    @PostMapping("/import")
    public String importTestStandard(@RequestParam("file") MultipartFile file,
                                    @RequestParam("code") String code) {
        testStandardService.importTestStandard(file, code);
        return "导入成功";
    }

    @NoRepeatSubmit
    @AutoLog(operateType = OperateType.OPERATE_EXPORT)
    @SaCheckPermission(value = "testStandard:export")
    @Operation(summary = "下载导入模板")
    @PostMapping("/downloadTemplate")
    public void downloadTemplate(@RequestParam("code") String code, HttpServletResponse response) {
        testStandardService.downloadTemplate(code, response);
    }
}
