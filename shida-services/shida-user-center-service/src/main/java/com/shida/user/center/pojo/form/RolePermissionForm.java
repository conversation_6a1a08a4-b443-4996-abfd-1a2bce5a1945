package com.shida.user.center.pojo.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@Component
@Schema(name = "RolePermissionForm", description = "角色权限表单")
public class RolePermissionForm implements Serializable {
    @Schema(description = "角色id")
    @NotNull(message = "角色id不能为空")
    private Long roleId;
    @Schema(description = "全选的菜单id")
    private List<Long> menuIds;
}
