package com.shida.user.center.init;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.shida.constant.RoleConstant;
import com.shida.user.center.entity.Menu;
import com.shida.user.center.entity.Role;
import com.shida.user.center.entity.RoleMenu;
import com.shida.user.center.service.IMenuService;
import com.shida.user.center.service.IRoleMenuService;
import com.shida.user.center.service.IRoleService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

@Order(4)
@Component
public class A004MenuInitializer implements ApplicationRunner {
    @Resource
    RedissonClient redissonClient;
    @Resource
    IMenuService menuService;
    @Resource
    IRoleService roleService;
    @Resource
    IRoleMenuService roleMenuService;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        RLock lock = redissonClient.getLock("lockers:A004MenuInitializer");
        if (lock.tryLock()) {
            try {
                initMenu();
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                lock.unlock();
            }
        }
    }

    private void initMenu() {
        String menuListStr = "[" +
                "{\"id\":1,\"name\":\"系统设置\",\"code\":\"SystemSetting\",\"type\":\"M\",\"permission\":\"system\",\"orderNum\":1,\"router\":\"/settings\",\"component\":\"Settings\",\"visible\":1}," +
                "{\"id\":2,\"name\":\"基础管理\",\"code\":\"BaseSetting\",\"type\":\"M\",\"permission\":\"base\",\"orderNum\":2,\"router\":\"/base\",\"component\":\"Base\",\"visible\":1}," +
                "{\"id\":101,\"name\":\"角色管理\",\"parentId\":1,\"code\":\"RoleManage\",\"type\":\"C\",\"permission\":\"system:role\",\"orderNum\":1,\"router\":\"/role/index\",\"component\":\"Settings/RoleManage/index\",\"visible\":1}," +
                "{\"id\":102,\"name\":\"账户管理\",\"parentId\":1,\"code\":\"UserManage\",\"type\":\"C\",\"permission\":\"system:user\",\"orderNum\":2,\"router\":\"/user/index\",\"component\":\"Settings/UserManage/index\",\"visible\":1}," +
                "{\"id\":103,\"name\":\"菜单管理\",\"parentId\":1,\"code\":\"MenuManage\",\"type\":\"C\",\"permission\":\"system:menu\",\"orderNum\":3,\"router\":\"/menu/index\",\"component\":\"Settings/MenuManage/index\",\"visible\":1}," +
                "{\"id\":201,\"name\":\"区县管理\",\"parentId\":2,\"code\":\"RegionManage\",\"type\":\"C\",\"permission\":\"system:region\",\"orderNum\":4,\"router\":\"/region/index\",\"component\":\"Settings/RegionManage/index\",\"visible\":1}," +
                "{\"id\":202,\"name\":\"学校管理\",\"parentId\":2,\"code\":\"SchoolManage\",\"type\":\"C\",\"permission\":\"system:school\",\"orderNum\":5,\"router\":\"/school/index\",\"component\":\"Settings/SchoolManage/index\",\"visible\":1}," +
                "{\"id\":101001,\"name\":\"列表\",\"parentId\":101,\"code\":\"RoleList\",\"type\":\"F\",\"permission\":\"role:list\",\"orderNum\":1,\"router\":null,\"component\":null,\"visible\":1}," +
                "{\"id\":101002,\"name\":\"新增\",\"parentId\":101,\"code\":\"RoleAdd\",\"type\":\"F\",\"permission\":\"role:add\",\"orderNum\":2,\"router\":null,\"component\":null,\"visible\":1}," +
                "{\"id\":101003,\"name\":\"编辑\",\"parentId\":101,\"code\":\"RoleEdit\",\"type\":\"F\",\"permission\":\"role:edit\",\"orderNum\":3,\"router\":null,\"component\":null,\"visible\":1}," +
                "{\"id\":101004,\"name\":\"启用/禁用\",\"parentId\":101,\"code\":\"ChangeRoleStatus\",\"type\":\"F\",\"permission\":\"role:change:status\",\"orderNum\":4,\"router\":null,\"component\":null,\"visible\":1}," +
                "{\"id\":101005,\"name\":\"分配权限\",\"parentId\":101,\"code\":\"ChangePermissions\",\"type\":\"F\",\"permission\":\"role:change:permissions\",\"orderNum\":5,\"router\":null,\"component\":null,\"visible\":1}," +
                "{\"id\":102001,\"name\":\"列表\",\"parentId\":102,\"code\":\"UserList\",\"type\":\"F\",\"permission\":\"user:list\",\"orderNum\":1,\"router\":null,\"component\":null,\"visible\":1}," +
                "{\"id\":102002,\"name\":\"新增\",\"parentId\":102,\"code\":\"UserAdd\",\"type\":\"F\",\"permission\":\"user:add\",\"orderNum\":2,\"router\":null,\"component\":null,\"visible\":1}," +
                "{\"id\":102003,\"name\":\"编辑\",\"parentId\":102,\"code\":\"UserEdit\",\"type\":\"F\",\"permission\":\"user:edit\",\"orderNum\":3,\"router\":null,\"component\":null,\"visible\":1}," +
                "{\"id\":102004,\"name\":\"启用/禁用\",\"parentId\":102,\"code\":\"ChangeUserStatus\",\"type\":\"F\",\"permission\":\"user:change:status\",\"orderNum\":4,\"router\":null,\"component\":null,\"visible\":1}," +
                "{\"id\":102005,\"name\":\"重置密码\",\"parentId\":102,\"code\":\"ResetPassword\",\"type\":\"F\",\"permission\":\"user:reset:password\",\"orderNum\":4,\"router\":null,\"component\":null,\"visible\":1}," +
                "{\"id\":103001,\"name\":\"列表\",\"parentId\":103,\"code\":\"MenuList\",\"type\":\"F\",\"permission\":\"menu:list\",\"orderNum\":1,\"router\":null,\"component\":null,\"visible\":1}," +
                "{\"id\":103002,\"name\":\"新增\",\"parentId\":103,\"code\":\"AddMenu\",\"type\":\"F\",\"permission\":\"menu:add\",\"orderNum\":2,\"router\":null,\"component\":null,\"visible\":1}," +
                "{\"id\":103003,\"name\":\"修改\",\"parentId\":103,\"code\":\"EditMenu\",\"type\":\"F\",\"permission\":\"menu:edit\",\"orderNum\":3,\"router\":null,\"component\":null,\"visible\":1}," +
                "{\"id\":103004,\"name\":\"删除\",\"parentId\":103,\"code\":\"DeleteMenu\",\"type\":\"F\",\"permission\":\"menu:delete\",\"orderNum\":4,\"router\":null,\"component\":null,\"visible\":1}," +
                "]";

        List<Menu> menuList = JSONUtil.toList(menuListStr, Menu.class);
        Role role = roleService.findByCode(RoleConstant.SUPER_ADMIN_CODE);
        List<RoleMenu> roleMenuList = new ArrayList<>();
        AtomicLong index = new AtomicLong(1);
        for (Menu menu : menuList) {
            if (menuService.count(new LambdaQueryWrapper<Menu>().eq(Menu::getCode, menu.getCode())) == 0) {
                menu.insertOrUpdate();
            }
            RoleMenu rm = new RoleMenu().setRoleId(role.getId()).setMenuId(menu.getId());
            rm.setId(index.getAndIncrement());
            roleMenuList.add(rm);
        }
        roleMenuService.saveOrUpdateBatch(roleMenuList);
    }

}
