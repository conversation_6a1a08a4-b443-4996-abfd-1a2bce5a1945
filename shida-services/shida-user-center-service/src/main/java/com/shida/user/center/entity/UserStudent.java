package com.shida.user.center.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shida.entity.BaseEntity;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 账户学生关联信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-27
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_user_student")
@Schema(name = "UserStudent", description = "账户学生关联信息表")
public class UserStudent extends BaseEntity<UserStudent> {

    private static final long serialVersionUID = 1L;

    @Schema(description = "证件号码")
    private String certNumber;

    @Schema(description = "账户ID")
    private Long userId;

    @Schema(description = "区县ID")
    private Long regionId;

    @Schema(description = "学校ID")
    private Long schoolId;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
