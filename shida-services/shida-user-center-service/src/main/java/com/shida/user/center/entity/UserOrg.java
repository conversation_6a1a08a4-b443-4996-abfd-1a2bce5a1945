package com.shida.user.center.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shida.entity.BaseEntity;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户部门表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_user_org")
@Schema(name = "UserOrg", description = "用户部门表")
public class UserOrg extends BaseEntity<UserOrg> {

    private static final long serialVersionUID = 1L;

    @Schema(description = "账户ID")
    private Long userId;

    @Schema(description = "角色ID")
    private Long orgId;

    @Schema(description = "管理员标识")
    private Boolean masterFlag;

    @Schema(description = "学校ID")
    private Long schoolId;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
