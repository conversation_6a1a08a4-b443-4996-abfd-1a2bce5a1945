package com.shida.user.center.controller;

import com.shida.dto.SchoolDto;
import com.shida.pojo.form.KeyForm;
import com.shida.pojo.vo.KeyValue;
import com.shida.pojo.vo.MenuInfo;
import com.shida.user.center.pojo.vo.NationVo;
import com.shida.user.center.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Tag(name = "common-通用接口")
@RestController
@RequestMapping("/common")
public class CommonController {

    @Resource
    private IOrgService orgService;
    @Resource
    private IMenuService menuService;
    @Resource
    private IGradeService gradeService;
    @Resource
    private ISportProjectService sportProjectService;
    @Resource
    private ITermService termService;
    @Resource
    private IUserService userService;
    @Resource
    private IRoleService roleService;

    @Operation(summary = "获取地区下拉列表")
    @PostMapping("/org/getRegionSelectList")
    public List<KeyValue<String, String>> getRegionSelectList() {
        return orgService.getRegionSelectList();
    }

    @Operation(summary = "获取学校下拉列表", description = "获取学校下拉列表，当 key 为 区县ID，不可为空")
    @PostMapping("/org/getSchoolSelectList")
    public List<KeyValue<String, String>> getSchoolSelectList(@RequestBody @Valid KeyForm<Long> form) {
        return orgService.getSchoolSelectList(form.getKey());
    }

    @Operation(summary = "获取菜单树列表")
    @PostMapping("/org/getMenuTreeList")
    public List<MenuInfo> getMenuTreeList() {
        return menuService.getMenuTreeList();
    }

    @Operation(summary = "获取学年列表")
    @PostMapping("/grade/getXueNianList")
    public List<KeyValue<String, String>> getXueNianList() {
        return gradeService.getXueNianList();
    }

    @Operation(summary = "获取年级列表")
    @PostMapping("/grade/getGradeSelectList")
    public List<KeyValue<Long, String>> getGradeSelectList() {
        return gradeService.getGradeSelectList();
    }

    @Operation(summary = "获取运动项目列表")
    @PostMapping("/sport/getSportProjectSelectList")
    public List<KeyValue<String, String>> getSportProjectSelectList() {
        return sportProjectService.getSportProjectSelectList();
    }

    @Operation(summary = "获取学期列表", description = "获取学期列表，当 extra 为 1 时表示当前学期")
    @PostMapping("/term/getTermSelectList")
    public List<KeyValue<String, String>> getTermSelectList() {
        return termService.getTermSelectList();
    }

    @Operation(summary = "获取民族列表")
    @PostMapping("/nation/getNationList")
    public List<NationVo> getNationList() {
        return userService.getNationVoList();
    }

    @Operation(summary = "获取角色列表", description = "获取角色下拉列表,key为角色代码，可为空")
    @PostMapping("/role/getRoleSelectList")
    public List<KeyValue<String, String>> getRoleSelectList(@RequestBody KeyForm<String> form) {
        return roleService.getRoleSelectList(form.getKey());
    }
}
