package com.shida.user.center.controller.feign;

import cn.hutool.core.bean.BeanUtil;
import com.shida.user.center.service.ITermService;
import com.shida.userCenter.api.IFeignTermService;
import com.shida.userCenter.dto.TermDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Tag(name = "Feign-学期服务")
@RestController
public class FeignTermController implements IFeignTermService {
    @Resource
    private ITermService termService;


    @Operation(summary = "根据ID获取学期信息")
    @Override
    public TermDto getById(Long id) {
        return BeanUtil.copyProperties(termService.getById(id), TermDto.class);
    }

    @Override
    public List<TermDto> getTermList(List<Long> termIds) {
        return BeanUtil.copyToList(termService.listByIds(termIds), TermDto.class);
    }
}
