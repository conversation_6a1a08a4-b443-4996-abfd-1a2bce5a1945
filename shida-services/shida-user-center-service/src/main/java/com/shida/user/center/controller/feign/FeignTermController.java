package com.shida.user.center.controller.feign;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.shida.pojo.vo.PageInfo;
import com.shida.user.center.entity.Term;
import com.shida.user.center.pojo.search.TermSearch;
import com.shida.user.center.pojo.vo.TermInfo;
import com.shida.user.center.service.ITermService;
import com.shida.userCenter.api.IFeignTermService;
import com.shida.userCenter.dto.TermDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Tag(name = "Feign-学期服务")
@RestController
public class FeignTermController implements IFeignTermService {
    @Resource
    private ITermService termService;


    @Operation(summary = "根据ID获取学期信息")
    @Override
    public TermDto getById(Long id) {
        return BeanUtil.copyProperties(termService.getById(id), TermDto.class);
    }

    @Operation(summary = "根据ID列表获取学期信息")
    @Override
    public List<TermDto> getTermList(List<Long> termIds) {
        return BeanUtil.copyToList(termService.listByIds(termIds), TermDto.class);
    }

    @Operation(summary = "根据学期名称获取list")
    @Override
    public List<TermDto> getTermListByName(List<String> termNames) {
        if (CollUtil.isEmpty(termNames)) {
            return Collections.emptyList();
        }
        List<Term> termList = termService.list(new LambdaQueryWrapper<Term>().in(Term::getTermName, termNames));
        return BeanUtil.copyToList(termList, TermDto.class);
    }

    @Operation(summary = "获取所有学期")
    @Override
    public List<TermDto> getAllTermList() {
        return BeanUtil.copyToList(termService.list(), TermDto.class);
    }

    @Operation(summary = "带分页信息的学期List")
    @Override
    public PageInfo<TermDto> getPageData(@RequestBody Integer pageNumber, Integer pageSize) {
        PageInfo<TermDto> result = new PageInfo<>();
        TermSearch search = new TermSearch();
        search.setPageNumber(pageNumber);
        search.setPageSize(pageSize);
        PageInfo<TermInfo> pageData = termService.getPageData(search);
        List<TermInfo> records = pageData.getRecords();
        List<TermDto> termDtoList = BeanUtil.copyToList(records, TermDto.class);
        BeanUtil.copyProperties(pageData, result);
        result.setRecords(termDtoList);
        return result;
    }


}
