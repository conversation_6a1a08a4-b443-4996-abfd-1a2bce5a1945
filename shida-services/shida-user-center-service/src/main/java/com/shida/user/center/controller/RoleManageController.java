package com.shida.user.center.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.noRepeatSubmit.NoRepeatSubmit;
import com.shida.pojo.form.KeyForm;
import com.shida.pojo.search.Search;
import com.shida.pojo.vo.KeyValue;
import com.shida.pojo.vo.PageInfo;
import com.shida.pojo.vo.RoleInfo;
import com.shida.user.center.pojo.form.RoleForm;
import com.shida.user.center.service.IRoleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Tag(name = "role-角色管理")
@RestController
@RequestMapping("/role/manage")
public class RoleManageController {
    @Resource
    private IRoleService roleService;

    @SaCheckPermission("role:list")
    @Operation(description = "获取角色列表")
    @PostMapping("/getPageData")
    public PageInfo<RoleInfo> getPageData(@RequestBody Search search) {
        return roleService.getPageData(search);
    }

    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    @NoRepeatSubmit
    @Operation(description = "修改角色状态")
    @SaCheckPermission("role:change:status")
    @PostMapping("/changeStatus/{status}")
    public String changeStatus(@RequestBody KeyForm<Long> form,
                               @PathVariable @Schema(description = "状态") Integer status) {
        return roleService.changeStatus(form.getKey(), status);
    }


    @AutoLog(operateType = OperateType.OPERATE_ADD)
    @NoRepeatSubmit
    @Operation(description = "添加角色")
    @SaCheckPermission("role:add")
    @PostMapping("/create")
    public String create(@RequestBody @Valid RoleForm form) {
        return roleService.create(form);
    }

    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    @NoRepeatSubmit
    @Operation(description = "修改角色")
    @SaCheckPermission("role:edit")
    @PostMapping("/update")
    public String update(@RequestBody @Valid RoleForm form) {
        return roleService.updateRole(form);
    }

}
