package com.shida.user.center.mapper;

import com.shida.pojo.vo.KeyValue;
import com.shida.user.center.entity.Org;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shida.user.center.pojo.vo.UserInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 机构信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface OrgMapper extends BaseMapper<Org> {

    List<KeyValue<String, String>> getRegionSelectList();

    List<UserInfo> getRegionAdminList(@Param("regionIds") List<Long> regionIds, @Param("roleCode") String roleCode);
}
