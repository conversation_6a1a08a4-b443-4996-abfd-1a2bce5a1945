<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.user.center.mapper.UserRoleMapper">

    <select id="getUserRoleDtoList" resultType="com.shida.userCenter.dto.RoleDto">
        select r.*, ur.user_id from t_user_role ur
        left join t_role r on ur.role_id = r.id
        where ur.user_id in
        <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
            #{userId}
        </foreach>
    </select>
</mapper>
