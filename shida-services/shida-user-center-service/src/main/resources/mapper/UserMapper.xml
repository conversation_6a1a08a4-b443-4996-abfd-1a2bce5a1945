<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.user.center.mapper.UserMapper">

    <select id="findUserDtoByUsername" resultType="com.shida.userCenter.dto.UserDto">
        select u.id,
               u.username,
               u.nickname,
               u.email,
               u.status,
               u.mobile,
               u.salt,
               u.password,
               u.default_password,
               u.default_password_flag,
               u.expire_time,
               o.id        as deptId,
               o.floor     as deptFloor,
               o.parent_id as parentDeptId,
               uo.school_id,
               uo.org_id   as region_id,
               o.name      as region_name,
               o.code      as region_code
        from t_user u
                 inner join t_user_org uo on uo.user_id = u.id
                 inner join t_org o on uo.org_id = o.id
        where u.username = #{username} limit 1
    </select>
<!--    <select id="findUserDtoByUserId" resultType="com.shida.userCenter.dto.UserDto">-->
<!--        select u.id,-->
<!--               u.username,-->
<!--               u.nickname,-->
<!--               u.email,-->
<!--               u.status,-->
<!--               u.mobile,-->
<!--               r.code as roleCode,-->
<!--               r.name as roleName,-->
<!--               r.id   as roleId,-->
<!--               u.salt,-->
<!--               u.password,-->
<!--               u.default_password,-->
<!--               u.default_password_flag,-->
<!--               u.expire_time,-->
<!--               r.admin_flag,-->
<!--               o.floor as deptFloor,-->
<!--               o.parent_id as parentDeptId,-->
<!--               uo.org_id as region_id,-->
<!--               o.name as region_name,-->
<!--               o.code as region_code,-->
<!--               uo.school_id-->
<!--        from t_user u-->
<!--                 inner join t_user_role ur on ur.user_id = u.id-->
<!--                 inner join t_role r on ur.role_id = r.id-->
<!--                 inner join t_user_org uo on uo.user_id = u.id-->
<!--                 inner join t_org o on uo.org_id = o.id-->
<!--        where u.id = #{userId}-->
<!--    </select>-->
    <select id="findUserDtoByUserId" resultType="com.shida.userCenter.dto.UserDto">
        select u.id,
               u.username,
               u.nickname,
               u.email,
               u.status,
               u.mobile,
               u.salt,
               u.password,
               u.default_password,
               u.default_password_flag,
               u.expire_time,
               o.floor as deptFloor,
               o.parent_id as parentDeptId,
               uo.org_id as region_id,
               o.name as region_name,
               o.code as region_code,
               uo.school_id
        from t_user u
                 inner join t_user_org uo on uo.user_id = u.id
                 inner join t_org o on uo.org_id = o.id
        where u.id = #{userId}
    </select>
    <select id="findUserDtoByUserIds" resultType="com.shida.userCenter.dto.UserDto">
        select u.id,
        u.username,
        u.nickname,
        u.email,
        u.status,
        u.mobile,
        -- r.code as roleCode,
       -- r.name as roleName,
        -- r.id as roleId,
        u.salt,
        u.password,
        u.default_password,
        u.default_password_flag,
        u.expire_time,
       -- r.admin_flag,
        o.floor as deptFloor,
        o.parent_id as parentDeptId,
        uo.org_id as region_id,
        o.name as region_name,
        o.code as region_code,
        uo.school_id
        from t_user u
        -- inner join t_user_role ur on ur.user_id = u.id
        -- inner join t_role r on ur.role_id = r.id
        inner join t_user_org uo on uo.user_id = u.id
        inner join t_org o on uo.org_id = o.id
        where u.id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="findUserDtoByUsernames" resultType="com.shida.userCenter.dto.UserDto">
        select u.id,
        u.username,
        u.nickname,
        u.email,
        u.status,
        u.mobile,
        -- r.code as roleCode,
        -- r.name as roleName,
        -- r.id as roleId,
        u.salt,
        u.password,
        u.default_password,
        u.default_password_flag,
        u.expire_time,
        -- r.admin_flag,
        o.floor as deptFloor,
        o.parent_id as parentDeptId,
        uo.org_id as region_id,
        o.name as region_name,
        o.code as region_code,
        uo.school_id
        from t_user u
        -- inner join t_user_role ur on ur.user_id = u.id
        -- inner join t_role r on ur.role_id = r.id
        inner join t_user_org uo on uo.user_id = u.id
        inner join t_org o on uo.org_id = o.id
        where u.username in
        <foreach item="item" index="index" collection="usernameList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="getPageData" resultType="com.shida.user.center.pojo.vo.UserInfo">
        select DISTINCT u.id,
        u.username,
        u.nickname,
        u.email,
        u.status,
        u.mobile,
        u.salt,
        u.password,
        u.default_password,
        u.default_password_flag,
        u.expire_time,
        o.floor as deptFloor,
        o.parent_id as parentDeptId,
        uo.org_id as region_id,
        o.name as region_name,
        o.code as region_code,
        uo.school_id
        from t_user u
        inner join t_user_role ur on ur.user_id = u.id
        inner join t_role r on ur.role_id = r.id
        inner join t_user_org uo on uo.user_id = u.id
        inner join t_org o on uo.org_id = o.id
        <where>
            <if test="search.keywords!=null and search.keywords!=''">
                and (
                u.username like CONCAT('%',#{search.keywords},'%')
                OR u.nickname like CONCAT('%',#{search.keywords},'%')
                OR u.mobile like CONCAT('%',#{search.keywords},'%')
                )
            </if>
            <if test="search.deptId != null">
                and uo.org_id = #{search.deptId}
            </if>
            <if test="search.schoolId!=null">
                and uo.school_id = #{search.schoolId}
            </if>
            <if test="search.roleId != null">
                and ur.role_id = #{search.roleId}
            </if>
        </where>
        ORDER BY u.id ASC
    </select>
    <select id="getMasterUserListBySchoolId" resultType="com.shida.userCenter.dto.UserDto">
        select DISTINCT u.id,
                        u.username,
                        u.nickname,
                        u.email,
                        u.status,
                        u.mobile,
                        u.salt,
                        u.default_password,
                        u.default_password_flag,
                        u.expire_time,
                        o.id        as deptId,
                        o.name      as deptName,
                        o.code      as deptCode,
                        o.floor     as deptFloor,
                        o.parent_id as parentDeptId,
                        uo.org_id   as region_id,
                        o.name as region_name,
                        o.code as region_code,
                        uo.school_id
        from t_user u
                 inner join t_user_role ur on ur.user_id = u.id
                 inner join t_role r on ur.role_id = r.id
                 inner join t_user_org uo on uo.user_id = u.id
                 inner join t_org o on uo.org_id = o.id
        where uo.school_id = #{schoolId}
          and uo.master_flag = 1
          and r.code = #{roleCode}
          and uo.org_id = #{regionId}
    </select>
    <select id="getMasterUserListBySchoolIds" resultType="com.shida.userCenter.dto.UserDto">
        select DISTINCT u.id,
        u.username,
        u.nickname,
        u.email,
        u.status,
        u.mobile,
        u.salt,
        u.default_password,
        u.default_password_flag,
        u.expire_time,
        o.floor as deptFloor,
        o.parent_id as parentDeptId,
        uo.org_id as region_id,
        o.name as region_name,
        o.code as region_code,
        uo.school_id
        from t_user u
        inner join t_user_role ur on ur.user_id = u.id
        inner join t_role r on ur.role_id = r.id
        inner join t_user_org uo on uo.user_id = u.id
        inner join t_org o on uo.org_id = o.id
        where uo.school_id in
        <foreach item="item" index="index" collection="schoolIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        and uo.master_flag = 1
        and r.`code` = #{roleCode}
        and uo.org_id = #{regionId}
    </select>

</mapper>
