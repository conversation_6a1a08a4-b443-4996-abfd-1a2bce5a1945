<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.user.center.mapper.MenuMapper">
    <update id="setNullByField">
        update t_menu set `${fieldName}` = null WHERE id = #{id}
    </update>

    <select id="getPermissionsByRoleIds" resultType="com.shida.pojo.vo.PermissionInfo">
        select DISTINCT m.*
        from
        t_role_menu rm
        left join t_menu m on rm.menu_id = m.id
        where
        rm.role_id in
        <foreach item="item" index="index" collection="roleIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by m.order_num
    </select>
    <select id="getRoleFullMenus" resultType="com.shida.pojo.vo.MenuInfo">
        select DISTINCT m.*
        from
        t_role_menu rm
        left join t_menu m on rm.menu_id = m.id
        where
        rm.role_id in
        <foreach item="item" index="index" collection="roleIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND rm.half_checked = 0
        order by m.order_num
    </select>
</mapper>
