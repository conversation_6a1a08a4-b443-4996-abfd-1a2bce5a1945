<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.user.center.mapper.RoleMapper">

    <select id="getPageData" resultType="com.shida.pojo.vo.RoleInfo">
        SELECT r.id,
        r.`name` AS roleName,
        r.`code` AS roleCode,
        r.admin_flag AS adminFlag,
        (SELECT count(1) FROM  t_user t1 INNER JOIN t_user_role t2 ON t1.id = t2.user_id WHERE t2.role_id = r.id) AS userCount,
        r.status,
        r.sys_role_flag,
        r.sys_role_code
        FROM t_role r
        <where>
            <if test="search.keywords != null and search.keywords != ''">
                AND r.`name` LIKE CONCAT('%', #{search.keywords}, '%')
                OR r.`code` LIKE CONCAT('%', #{search.keywords}, '%')
            </if>
        </where>
        ORDER BY r.id ASC
    </select>
    <select id="getUserRolesByUserId" resultType="com.shida.pojo.vo.RoleInfo">
        SELECT t1.id,
        t1.`name` AS roleName,
        t1.`code` AS roleCode,
        t1.admin_flag,
        t1.status,
        t1.sys_role_flag,
        t1.sys_role_code
        FROM t_role t1
        INNER JOIN t_user_role t2 ON t1.id = t2.role_id
        WHERE t2.user_id = #{userId}
    </select>
    <select id="getRoleMenusByRoleCode" resultType="com.shida.user.center.entity.RoleMenu">
        SELECT t1.id,
        t1.role_id,
        t1.menu_id
        FROM t_role_menu t1
        INNER JOIN t_role t2 ON t1.role_id = t2.id
        WHERE t2.code = #{roleCode}
    </select>
</mapper>
