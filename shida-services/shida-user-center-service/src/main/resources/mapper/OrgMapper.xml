<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.user.center.mapper.OrgMapper">

    <select id="getRegionSelectList" resultType="com.shida.pojo.vo.KeyValue">
        SELECT id as `key`, `name` as `value`
        FROM t_org
        WHERE (floor = 2)
        ORDER BY id ASC
    </select>
    <select id="getRegionAdminList" resultType="com.shida.user.center.pojo.vo.UserInfo">
        SELECT
        t1.id,
        t1.username,
        t1.nickname,
        t1.email,
        t1.mobile,
        t1.default_password,
        t1.default_password_flag,
        t1.`status`,
        t5.id AS regionId,
        t5.`code` AS regionCode,
        t5.`name` AS regionName
        FROM
        t_user t1
        INNER JOIN t_user_org t2 ON t1.id = t2.user_id
        INNER JOIN t_user_role t3 ON t1.id = t3.user_id
        LEFT JOIN t_role t4 ON t3.role_id = t4.id
        INNER JOIN t_org t5 ON t2.org_id = t5.id
        WHERE
        t2.org_id IN
        <foreach collection="regionIds" item="regionId" open="(" separator="," close=")">
            #{regionId}
        </foreach>
        AND t2.master_flag = 1
        AND t4.`code` = #{roleCode}
    </select>
</mapper>
