<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.user.center.mapper.PTestStandardMapper">

    <select id="getPageData" resultType="com.shida.user.center.pojo.vo.TestStandardInfo">
        SELECT DISTINCT
        id,
        code,
        title,
        unit,
        grade_name as gradeName,
        `level`,
        score,
        min_value as minValue,
        max_value as `maxValue`
        FROM
        p_test_standard
        <where>
            <if test="search != null and search.code != null and search.code != ''">
                AND code = #{search.code}
            </if>
            <if test="search != null and search.title != null and search.title != ''">
                AND title LIKE CONCAT('%', #{search.title}, '%')
            </if>
        </where>
    </select>
</mapper>
