
CREATE TABLE `t_grade`  (
                            `id` bigint NOT NULL,
                            `create_time` datetime NULL DEFAULT NULL,
                            `year` int NULL DEFAULT NULL COMMENT '年份',
                            `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '名称',
                            PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;


CREATE TABLE `t_menu`  (
                           `id` bigint NOT NULL,
                           `create_time` datetime NULL DEFAULT NULL,
                           `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '菜单名称',
                           `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '菜单代码',
                           `type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '菜单类型（M目录 C菜单 F按钮）',
                           `permission` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '权限代码',
                           `order_num` int NULL DEFAULT NULL COMMENT '排序字段',
                           `parent_id` bigint NULL DEFAULT NULL COMMENT '父菜单ID',
                           `router` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '路由地址',
                           `component` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '组件地址',
                           `visible` int NULL DEFAULT NULL COMMENT '菜单显示状态，0隐藏，1显示',
                           PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '菜单信息表' ROW_FORMAT = Dynamic;


CREATE TABLE `t_org`  (
                          `id` bigint NOT NULL,
                          `create_time` datetime NULL DEFAULT NULL,
                          `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '机构名称',
                          `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '机构代码',
                          `parent_id` bigint NULL DEFAULT NULL COMMENT '父机构代码',
                          `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述信息',
                          `floor` int NULL DEFAULT NULL COMMENT '层级，1市，2区县，3学校',
                          PRIMARY KEY (`id`) USING BTREE,
                          UNIQUE INDEX `code`(`code` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '机构信息' ROW_FORMAT = Dynamic;


CREATE TABLE `t_role`  (
                           `id` bigint NOT NULL,
                           `create_time` datetime NULL DEFAULT NULL,
                           `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '角色代码',
                           `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '角色名称',
                           `admin_flag` bit(1) NULL DEFAULT NULL COMMENT '是否是管理员角色',
                           `status` int NULL DEFAULT NULL COMMENT '状态，0禁用，1启用',
                           `sys_role_flag` bit(1) NULL DEFAULT NULL COMMENT '内置角色标识',
                           `sys_role_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '内置角色代码，自定义角色时用',
                           PRIMARY KEY (`id`) USING BTREE,
                           UNIQUE INDEX `code`(`code` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色信息' ROW_FORMAT = Dynamic;


CREATE TABLE `t_role_menu`  (
                                `id` bigint NOT NULL,
                                `create_time` datetime NULL DEFAULT NULL,
                                `role_id` bigint NULL DEFAULT NULL COMMENT '角色ID',
                                `menu_id` bigint NULL DEFAULT NULL COMMENT '菜单ID',
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色权限表' ROW_FORMAT = Dynamic;


CREATE TABLE `t_sport_project`  (
                                    `id` bigint NOT NULL,
                                    `create_time` datetime NULL DEFAULT NULL,
                                    `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目名称',
                                    `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目代码',
                                    `description` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '体育项目信息表' ROW_FORMAT = Dynamic;


CREATE TABLE `t_term`  (
                           `id` bigint NOT NULL,
                           `create_time` datetime NULL DEFAULT NULL,
                           `year_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '学年名称',
                           `term_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '学期名称',
                           `start_date` date NULL DEFAULT NULL COMMENT '开始时间',
                           `stop_date` date NULL DEFAULT NULL COMMENT '结束日期',
                           `status` int NULL DEFAULT NULL COMMENT '学期状态，0禁用，1启用，2归档',
                           `months` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '包含月份',
                           PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;


CREATE TABLE `t_user`  (
                           `id` bigint NOT NULL,
                           `create_time` datetime NULL DEFAULT NULL,
                           `username` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '账户名',
                           `nickname` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '昵称',
                           `email` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '电子邮箱',
                           `status` int NULL DEFAULT NULL COMMENT '账号状态，0禁用，1正常',
                           `mobile` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '手机号码',
                           `password` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '密码',
                           `salt` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '秘钥',
                           `default_password` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '默认密码',
                           `default_password_flag` bit(1) NULL DEFAULT NULL COMMENT '是否是默认密码',
                           `expire_time` datetime NULL DEFAULT NULL COMMENT '过期时间，为空时永不过期',
                           PRIMARY KEY (`id`) USING BTREE,
                           UNIQUE INDEX `username`(`username` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '账户信息' ROW_FORMAT = Dynamic;


CREATE TABLE `t_user_org`  (
                               `id` bigint NOT NULL,
                               `create_time` datetime NULL DEFAULT NULL,
                               `user_id` bigint NULL DEFAULT NULL COMMENT '账户ID',
                               `org_id` bigint NULL DEFAULT NULL COMMENT '角色ID',
                               `school_id` bigint NULL DEFAULT NULL COMMENT '学校ID',
                               `master_flag` bit(1) NULL DEFAULT NULL COMMENT '管理员标识',
                               PRIMARY KEY (`id`) USING BTREE,
                               INDEX `user_id`(`user_id` ASC) USING BTREE,
                               INDEX `org_id`(`org_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户部门表' ROW_FORMAT = Dynamic;


CREATE TABLE `t_user_role`  (
                                `id` bigint NOT NULL,
                                `create_time` datetime NULL DEFAULT NULL,
                                `role_id` bigint NULL DEFAULT NULL,
                                `user_id` bigint NULL DEFAULT NULL,
                                PRIMARY KEY (`id`) USING BTREE,
                                INDEX `role_id`(`role_id` ASC) USING BTREE,
                                INDEX `user_id`(`user_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户角色表' ROW_FORMAT = Dynamic;


CREATE TABLE `t_user_student`  (
                                   `id` bigint NOT NULL,
                                   `create_time` datetime NULL DEFAULT NULL,
                                   `cert_number` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '证件号码',
                                   `user_id` bigint NULL DEFAULT NULL COMMENT '账户ID',
                                   `region_id` bigint NULL DEFAULT NULL COMMENT '区县ID',
                                   `school_id` bigint NULL DEFAULT NULL COMMENT '学校ID',
                                   PRIMARY KEY (`id`) USING BTREE,
                                   UNIQUE INDEX `cert_number`(`cert_number` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '账户学生关联信息表' ROW_FORMAT = Dynamic;


CREATE TABLE `undo_log`  (
                             `id` bigint NOT NULL AUTO_INCREMENT,
                             `branch_id` bigint NOT NULL COMMENT 'branch transaction id',
                             `xid` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'global transaction id',
                             `context` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'undo_log context,such as serialization',
                             `rollback_info` longblob NOT NULL COMMENT 'rollback info',
                             `log_status` int NOT NULL COMMENT '0:normal status,1:defense status',
                             `log_created` datetime(6) NOT NULL COMMENT 'create datetime',
                             `log_modified` datetime(6) NOT NULL COMMENT 'modify datetime',
                             PRIMARY KEY (`id`) USING BTREE,
                             UNIQUE INDEX `ux_undo_log`(`xid` ASC, `branch_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 63 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'AT transaction mode undo table' ROW_FORMAT = DYNAMIC;



CREATE TABLE `t_setting` (
                             `id` bigint NOT NULL,
                             `create_time` datetime DEFAULT NULL,
                             `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设置名称',
                             `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设置代码',
                             `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设置内容，json',
                             `region_id` bigint DEFAULT NULL COMMENT '区县ID',
                             `school_id` bigint DEFAULT NULL COMMENT '学校ID',
                             `order_number` int NOT NULL DEFAULT '99' COMMENT '排序',
                             PRIMARY KEY (`id`),
                             UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='设置信息表' ROW_FORMAT = DYNAMIC;
