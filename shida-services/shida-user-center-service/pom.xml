<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.shida</groupId>
        <artifactId>shida-services</artifactId>
        <version>0.1.0</version>
    </parent>

    <artifactId>shida-user-center-service</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shida</groupId>
            <artifactId>shida-user-center-apis</artifactId>
            <version>0.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.shida</groupId>
            <artifactId>shida-sso-service-apis</artifactId>
            <version>0.1.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.shida</groupId>
            <artifactId>shida-region-service-api</artifactId>
            <version>0.1.0</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
    <build>
        <finalName>tzjk-user-center-service</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <!-- 将依赖到的包都放进去 -->
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
