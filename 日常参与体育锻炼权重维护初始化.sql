-- 日常参与体育锻炼权重维护初始化数据
INSERT INTO `t_setting` (
  `id`,
  `create_time`,
  `name`,
  `code`,
  `content`,
  `region_id`,
  `school_id`,
  `order_number`
) VALUES (
  FLOOR(RAND() * 9000000000000000) + 1000000000000000,
  NOW(),
  '日常参与体育锻炼权重维护',
  'RCCYTYDL',
  '{
    "weightItems": [
      {
        "id": 1,
        "name": "体育课出勤",
        "weight": 70,
        "scoreSettings": [
          {
            "level": "优秀",
            "minValue": 90,
            "maxValue": 100,
            "score": 0.7
          },
          {
            "level": "良好", 
            "minValue": 80,
            "maxValue": 89,
            "score": 0.35
          },
          {
            "level": "不及格",
            "minValue": 0,
            "maxValue": 79,
            "score": 0
          }
        ]
      },
      {
        "id": 2,
        "name": "课间操出勤率",
        "weight": 30,
        "scoreSettings": [
          {
            "level": "优秀",
            "minValue": 90,
            "maxValue": 100,
            "score": 0.3
          },
          {
            "level": "良好",
            "minValue": 80,
            "maxValue": 89,
            "score": 0.15
          },
          {
            "level": "不及格",
            "minValue": 0,
            "maxValue": 79,
            "score": 0
          }
        ]
      }
    ]
  }',
  9999,
  9999,
  1
);

-- 体质健康标准分值设置初始化数据
INSERT INTO `t_setting` (
  `id`,
  `create_time`,
  `name`,
  `code`,
  `content`,
  `region_id`,
  `school_id`,
  `order_number`
) VALUES (
  FLOOR(RAND() * 9000000000000000) + 1000000000000000,
  NOW(),
  '体质健康标准分值设置',
  'TZJKBZ',
  '{
    "scoreSettings": [
      {
        "id": 1,
        "projectName": "国家学生体质健康标准",
        "levels": [
          {
            "level": "良好",
            "minValue": 80,
            "maxValue": 100,
            "score": 4.0
          },
          {
            "level": "及格",
            "minValue": 60,
            "maxValue": 79,
            "score": 3.0
          },
          {
            "level": "不及格",
            "minValue": 0,
            "maxValue": 59,
            "score": 2.0
          }
        ]
      }
    ]
  }',
  9999,
  9999,
  2
);

-- 体育与健康课分值设置初始化数据
INSERT INTO `t_setting` (
  `id`,
  `create_time`,
  `name`,
  `code`,
  `content`,
  `region_id`,
  `school_id`,
  `order_number`
) VALUES (
  FLOOR(RAND() * 9000000000000000) + 1000000000000000,
  NOW(),
  '体育与健康课分值设置',
  'TYYJKK',
  '{
    "scoreSettings": [
      {
        "id": 1,
        "projectName": "体育与健康标准测试",
        "levels": [
          {
            "level": "良好",
            "minValue": 80,
            "maxValue": 100,
            "score": 2.0
          },
          {
            "level": "及格",
            "minValue": 60,
            "maxValue": 79,
            "score": 1.5
          },
          {
            "level": "不及格",
            "minValue": 0,
            "maxValue": 59,
            "score": 1.0
          }
        ]
      }
    ]
  }',
  9999,
  9999,
  3
);

