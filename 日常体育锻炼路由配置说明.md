# 日常体育锻炼模块路由配置说明

## 路由配置

需要在路由配置中添加以下路由：

```javascript
// 在 src/router/dymaicRouter.ts 或相关路由文件中添加
{
  path: '/middle-physical/daily-physical-exercise',
  name: 'DailyPhysicalExercise',
  component: () => import('@/views/MiddlePhysical/DailyPhysicalExercise/index.vue'),
  meta: {
    title: '日常体育锻炼',
    requiresAuth: true,
    permissions: ['dailyPhysicalExercise:list']
  }
},
{
  path: '/middle-physical/daily-physical-exercise/detail',
  name: 'DailyPhysicalExerciseDetail',
  component: () => import('@/views/MiddlePhysical/DailyPhysicalExercise/Detail.vue'),
  meta: {
    title: '日常体育锻炼详情',
    requiresAuth: true,
    permissions: ['dailyPhysicalExercise:detail']
  }
}
```

## 菜单配置

在后台管理系统中添加菜单项：

```json
{
  "menuName": "日常体育锻炼",
  "menuPath": "/middle-physical/daily-physical-exercise",
  "menuIcon": "trophy",
  "parentId": "体育中考菜单ID",
  "orderNum": 3,
  "menuType": "M",
  "visible": "0",
  "status": "0",
  "permissions": "dailyPhysicalExercise:list"
}
```

## 权限配置

需要在权限管理中配置以下权限：

1. **dailyPhysicalExercise:list** - 查看日常体育锻炼列表
2. **dailyPhysicalExercise:detail** - 查看日常体育锻炼详情  
3. **dailyPhysicalExercise:export** - 导出日常体育锻炼成绩
4. **dailyPhysicalExercise:calculate** - 重新计算成绩

## 角色权限分配

### 市级管理员
- ✅ dailyPhysicalExercise:list (查看列表)
- ✅ dailyPhysicalExercise:detail (查看详情)
- ✅ dailyPhysicalExercise:export (导出数据)
- ✅ dailyPhysicalExercise:calculate (重新计算成绩)

### 区县管理员  
- ✅ dailyPhysicalExercise:list (查看列表)
- ✅ dailyPhysicalExercise:detail (查看详情)
- ✅ dailyPhysicalExercise:export (导出数据)
- ❌ dailyPhysicalExercise:calculate (不能重新计算成绩)

### 学校管理员
- ✅ dailyPhysicalExercise:list (查看列表)
- ✅ dailyPhysicalExercise:detail (查看详情)
- ❌ dailyPhysicalExercise:export (不能导出数据)
- ❌ dailyPhysicalExercise:calculate (不能重新计算成绩)

## 功能特性

### 1. 权限控制
- 市级管理员：可选择任意区县、学校、班级查看数据，可重新计算成绩
- 区县管理员：只能查看本区县数据，区县选择框禁用
- 学校管理员：只能查看本学校数据，区县和学校选择框禁用

### 2. 成绩计算规则
- 体育课出勤率权重：70%
- 课间操出勤率权重：30%
- 综合得分 = 体育课得分 × 0.7 + 课间操得分 × 0.3

### 3. 评分标准
#### 体育课出勤率对应分值：
- 出勤率≥90%：0.7分
- 90%>出勤率≥80%：0.35分
- 出勤率<80%：0分

#### 课间操出勤率对应分值：
- 出勤率≥90%：0.3分  
- 90%>出勤率≥80%：0.15分
- 出勤率<80%：0分

#### 综合成绩等级：
- 0.9-1.0：优秀
- 0.7-0.89：良好  
- 0.6-0.69：及格
- <0.6：不及格

### 4. 主要功能
- 分页查询学生日常体育锻炼综合成绩
- 查看学生详细的成绩计算过程
- 导出成绩数据（Excel格式）
- 重新计算指定学期的所有学生成绩
- 支持按学期、区县、学校、班级、是否残疾/伤病等条件筛选

## 数据库表设计建议

可能需要创建或修改以下表：

```sql
-- 日常体育锻炼成绩汇总表（可选，也可以实时计算）
CREATE TABLE b_daily_physical_exercise_score (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    student_id BIGINT NOT NULL COMMENT '学生ID',
    term_id BIGINT NOT NULL COMMENT '学期ID', 
    pe_attendance_rate DECIMAL(5,2) COMMENT '体育课出勤率',
    pe_attendance_score DECIMAL(3,2) COMMENT '体育课出勤得分',
    pe_weighted_score DECIMAL(3,2) COMMENT '体育课权重得分',
    interclass_exercise_rate DECIMAL(5,2) COMMENT '课间操出勤率',
    interclass_exercise_score DECIMAL(3,2) COMMENT '课间操出勤得分', 
    interclass_weighted_score DECIMAL(3,2) COMMENT '课间操权重得分',
    total_score DECIMAL(3,2) COMMENT '综合得分',
    grade_level VARCHAR(10) COMMENT '成绩等级',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_student_term (student_id, term_id)
) COMMENT='日常体育锻炼综合成绩表';
```

## 部署注意事项

1. 确保后端服务已部署并配置了正确的数据库连接
2. 前端路由配置正确，菜单权限已分配
3. 相关的体育课出勤和课间操出勤数据已存在
4. 建议先在测试环境验证成绩计算逻辑的正确性
