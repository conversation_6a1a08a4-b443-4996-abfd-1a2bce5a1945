package com.shida;


import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "shida-sso-center-service", contextId = "account-api", configuration = FeignInterceptor.class)
public interface IFeignAccountService {

    @PostMapping("/account-feign-api/kickOutUser")
    String kickOutUser(@RequestParam("userId") Long userId);
}
