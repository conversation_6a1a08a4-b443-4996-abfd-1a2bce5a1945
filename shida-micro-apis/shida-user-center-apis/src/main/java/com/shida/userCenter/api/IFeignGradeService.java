package com.shida.userCenter.api;

import com.shida.FeignInterceptor;
import com.shida.userCenter.dto.GradeDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@FeignClient(name = "shida-user-center-service", contextId = "grade-api", configuration = FeignInterceptor.class)
public interface IFeignGradeService {

    /**
     * 根据ID获取年级信息
     * @param gradeId 年级IDs
     * @return GradeDto
     */
    @PostMapping("/grade-feign-api/getGradeById")
    GradeDto getGradeById(@RequestParam("gradeId") Long gradeId);


    /**
     * 根据ID列表获取年级信息
     * @param gradeIds 年级IDs
     * @return List<GradeDto>
     */
    @PostMapping("/grade-feign-api/getGradeList")
    List<GradeDto> getGradeList(@RequestBody @NotEmpty(message = "gradeIds不能为空") List<Long> gradeIds);

    /**
     * 根据名称列表获取年级信息
     * @param gradeNameList 年级名称列表
     * @return List<GradeDto>
     */
    @PostMapping("/grade-feign-api/getGradeListByName")
    List<GradeDto> getGradeListByName(@RequestBody @NotEmpty(message = "gradeNameList不能为空")  List<String> gradeNameList);
}
