package com.shida.userCenter.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@Schema(name = "RoleDto", description = "角色信息")
public class RoleDto implements Serializable {
    @Schema(description = "角色ID")
    private Long id;
    @Schema(description = "角色代码")
    private String code;

    @Schema(description = "角色名称")
    private String name;

    @Schema(description = "是否是管理员角色")
    private Boolean adminFlag;

    @Schema(description = "是否启用，0禁用，1启用")
    private Integer status;

    @Schema(description = "是否是内置角色")
    private Boolean sysRoleFlag;

    @Schema(description = "自定义角色所属内置角色代码")
    private String sysRoleCode;

    @Schema(description = "用户id")
    private Long userId;
}
