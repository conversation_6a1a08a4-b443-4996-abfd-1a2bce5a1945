package com.shida.userCenter.dto;

import com.shida.pojo.vo.MenuInfo;
import com.shida.pojo.vo.PermissionInfo;
import com.shida.pojo.vo.RoleInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
@Accessors(chain = true)
@Schema(name = "UserDto", description = "用户信息")
public class UserDto implements Serializable {
    @Schema(description = "ID")
    private Long id;
    @Schema(description = "用户名")
    private String username;
    @Schema(description = "昵称")
    private String nickname;
    @Schema(description = "邮箱")
    private String email;
    @Schema(description = "状态")
    private Integer status;
    @Schema(description = "手机号")
    private String mobile;
    @Schema(description = "是否默认密码")
    private Boolean defaultPasswordFlag;
    @Schema(description = "密码")
    private String password;
    @Schema(description = "默认密码")
    private String defaultPassword;
    @Schema(description = "密码盐")
    private String salt;
    @Schema(description = "过期时间，为空时永不过期")
    private LocalDateTime expireTime;
    @Schema(description = "是否管理员")
    private Boolean adminFlag;
    @Schema(description = "部门ID")
    private Long regionId;
    @Schema(description = "部门名称")
    private String regionName;
    @Schema(description = "部门code")
    private String regionCode;
    @Schema(description = "学校ID")
    private Long schoolId;
    @Schema(description = "学校名称")
    private String schoolName;
    @Schema(description = "学校代码")
    private String schoolCode;
    @Schema(description = "角色列表")
    List<RoleInfo> roles;
    @Schema(description = "权限列表")
    List<PermissionInfo> permissions;
    @Schema(description = "菜单列表")
    private List<MenuInfo> menus;
    @Schema(description = "身份证号")
    private String idCard;
}
