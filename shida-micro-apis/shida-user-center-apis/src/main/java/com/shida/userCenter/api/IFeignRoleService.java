package com.shida.userCenter.api;


import com.shida.FeignInterceptor;
import com.shida.pojo.form.KeyForm;
import com.shida.pojo.vo.KeyValue;
import com.shida.pojo.vo.RoleInfo;
import com.shida.userCenter.dto.RoleDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "shida-user-center-service",contextId = "role-api", configuration = FeignInterceptor.class)
public interface IFeignRoleService {

    @PostMapping("/role-feign-api/getByCode")
    RoleDto getByCode(@RequestParam("code") String code);

    @PostMapping("/role-feign-api/getRoleSelectList")
    List<RoleDto> getRoleSelectList(@RequestParam("roleCode") String roleCode);

}
