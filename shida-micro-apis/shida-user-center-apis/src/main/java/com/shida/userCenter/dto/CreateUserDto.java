package com.shida.userCenter.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.userCenter
 * @ClassName: CreateUserDto
 * @Version 1.0
 * @date 2025/8/18 15:18
 */
@Data
@Accessors(chain = true)
@Schema(name = "CreateUserDto", description = "新增教师信息")
public class CreateUserDto implements Serializable {
    private static final long serialVersionUID = 1L;
    @Schema(name = "userId", description = "用户id")
    private Long userId;
    @Schema(name = "idCard", description = "身份证号")
    private String idCard;
}
