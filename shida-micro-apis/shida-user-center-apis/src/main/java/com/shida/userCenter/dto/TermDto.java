package com.shida.userCenter.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;

@Data
@Accessors(chain = true)
@Schema(name = "TermDto", description = "学期")
public class TermDto implements Serializable {
    @Schema(description = "学期ID")
    private Long id;

    @Schema(description = "学年名称")
    private String yearName;

    @Schema(description = "学期名称")
    private String termName;

    @Schema(description = "开始时间")
    private LocalDate startDate;

    @Schema(description = "结束日期")
    private LocalDate stopDate;

    /**
     * @see com.shida.enums.TermStatudEnum
     */
    @Schema(description = "学期状态，0禁用，1启用，2归档")
    private Integer status;

    @Schema(description = "包含月份,以,号分割")
    private String months;

}
