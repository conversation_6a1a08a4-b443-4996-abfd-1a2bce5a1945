package com.shida.userCenter.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@Schema(name ="OrgDto" ,description = "机构信息")
public class OrgDto implements Serializable {

    @Schema(description = "机构id")
    private Long id;

    @Schema(description = "机构名称")
    private String name;

    @Schema(description = "机构代码")
    private String code;

    @Schema(description = "父机构代码")
    private Long parentId;

    @Schema(description = "描述信息")
    private String description;

    @Schema(description = "层级，1市，2区县，3学校")
    private Integer floor;
}
