package com.shida.userCenter.api;

import com.shida.FeignInterceptor;
import com.shida.pojo.vo.PageInfo;
import com.shida.userCenter.dto.TermDto;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotEmpty;
import java.util.List;

import java.util.List;

@FeignClient(name = "shida-user-center-service",contextId = "term-api", configuration = FeignInterceptor.class)
public interface IFeignTermService {

    /**
     * 根据ID查询学期
     * @param id
     * @return
     */
    @PostMapping("/term-feign-api/getById")
    TermDto getById(@RequestParam("id") Long id);

    /**
     * 根据ID列表获取学期信息
     * @param termIds 学期IDs
     * @return List<TermDto>
     */
    @PostMapping("/term-feign-api/getTermList")
    List<TermDto> getTermList(@RequestBody @NotEmpty(message = "termIds不能为空") List<Long> termIds);

    @PostMapping("/term-feign-api/getTermListByName")
    List<TermDto> getTermListByName(@RequestBody @NotEmpty(message = "termNames不能为空")  List<String> termNames);

    /**
     * 获取所有学期
     * @return List<TermDto>
     */
    @PostMapping("/term-feign-api/getAllTermList")
    List<TermDto> getAllTermList();

    @PostMapping("/term-feign-api/getTermListByPage")
    PageInfo<TermDto> getPageData(@RequestParam("pageNumber")  Integer pageNumber,@RequestParam("pageSize")  Integer pageSize);
}
