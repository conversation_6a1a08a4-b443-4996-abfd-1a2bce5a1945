package com.shida.userCenter.api;

import com.shida.FeignInterceptor;
import com.shida.pojo.vo.KeyValue;
import com.shida.userCenter.dto.ChangeUserPasswordDto;
import com.shida.userCenter.dto.CreateUserDto;
import com.shida.userCenter.dto.RoleDto;
import com.shida.userCenter.dto.UserDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;


/**
 * 赶进度未用熔断器，后期考虑添加
 */
@FeignClient(name = "shida-user-center-service",contextId = "user-api", configuration = FeignInterceptor.class)
public interface IFeignUserService {

    /**
     * 因为全ShidaRestControllerAdvice针对返回做了统一处理
     * 所以Feign接口路径必须含以 /*-feign-api/*开头，例如：/user-feign-api/getByUsername
     */
    @PostMapping("/user-feign-api/getById")
    UserDto getUserDto(@RequestParam("userId") Long userId);

    /**
     * 根据账户名获取用户信息
     */
    @PostMapping("/user-feign-api/getByUsername")
    UserDto getUserDto(@RequestParam("username") String username);

    /**
     * 根据用户ID列表批量获取用户信息
     *
     * @param userIds
     * @return
     */
    @PostMapping("/user-feign-api/getList")
    List<UserDto> getUserDtoList(@RequestBody List<Long> userIds);

    /**
     * 根据用户名列表获取用户信息列表
     * @param usernameList
     * @return
     */
    @PostMapping("/user-feign-api/getListByUsernames")
    List<UserDto> getListByUsernames(@RequestBody  List<String> usernameList);

    /**
     * 修改用户密码
     *
     * @param dto
     * @return
     */
    @PostMapping("/user-feign-api/changeUserPassword")
    String changeUserPassword(@RequestBody @Valid ChangeUserPasswordDto dto);


    /**
     * 根据学校ID获取学校管理员列表
     *
     * @param regionId
     * @param schoolId
     * @return
     */
    @PostMapping("/user-feign-api/getMasterUserListBySchoolId")
    List<UserDto> getMasterUserListBySchoolId(@RequestParam("regionId") Long regionId, @RequestParam("schoolId") Long schoolId);

    /**
     * 根据学校IDs获取学校管理员列表
     *
     * @param regionId
     * @param schoolIds
     * @return
     */
    @PostMapping("/user-feign-api/getMasterUserListBySchoolIds")
    List<UserDto> getMasterUserListBySchoolIds(@RequestParam("regionId") Long regionId,
                                               @RequestBody @NotEmpty(message = "学校ID列表不能为空") List<Long> schoolIds);


    /**
     * 修改用户单个字段信息
     * @param userId
     * @param form
     * @return
     */
    @PostMapping("/user-feign-api/changeFieldValue")
    String changeFieldValue(@RequestParam("userId") Long userId, @RequestBody KeyValue<String, ?> form);

    /**
     * 批量新增学校管理员用户信息
     * @param userList
     * @return
     */
    @PostMapping("/user-feign-api/batchCreateUser")
    List<UserDto> batchCreateUser(@RequestBody List<UserDto> userList);
    /**
     * 新增用户
     * @param userDto
     * @return
     */
    @PostMapping("/user-feign-api/createUser")
    UserDto createUser(@RequestBody @Valid UserDto userDto);
    /**
     * 获取用户角色列表
     * @param userIds
     * @return
     */
    @PostMapping("/user-feign-api/getUserRoleDtoList")
    List<RoleDto> getUserRoleDtoList(@RequestBody List<Long> userIds);

    /**
     * 更新用户信息
     * @param userDto
     * @return
     */
    @PostMapping("/user-feign-api/updateUser")
    String updateUser(@RequestBody UserDto userDto);

    /**
     * 删除用户
     * @param userId
     * @return
     */
    @PostMapping("/user-feign-api/deleteUser")
    String deleteUser(@RequestParam("userId") Long userId);
}
