package com.shida.userCenter.api;

import com.shida.FeignInterceptor;
import com.shida.pojo.vo.KeyValue;
import com.shida.userCenter.dto.UserStudentDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

@FeignClient(name = "shida-user-center-service",contextId = "user-student-api", configuration = FeignInterceptor.class)
public interface IFeignUserStudentService {
    @PostMapping("/user-student-feign-api/checkHasByCertNumber")
    Boolean checkHasByCertNumber(@RequestParam("certNumber") String certNumber);

    @PostMapping("/user-student-feign-api/getHasCertNumbersByCertNumbers")
    List<KeyValue<String, Boolean>> getHasCertNumbersByCertNumbers(@RequestBody List<String> certNumbers);

    @PostMapping("/user-student-feign-api/addUserStudent")
    UserStudentDto addUserStudent(@RequestBody @Valid UserStudentDto userStudentDto);

    @PostMapping("/user-student-feign-api/batchAddUserStudent")
    List<UserStudentDto> batchAddUserStudent(@RequestBody List<UserStudentDto> userStudentList);

    @PostMapping("/user-student-feign-api/updateUserStudentById")
    String updateUserStudentById(@RequestBody @Valid UserStudentDto userStudentDto);

    @PostMapping("/user-student-feign-api/deleteUserStudentByCertNumber")
    String deleteUserStudentByCertNumber(@RequestParam("certNumber") String certNumber);
}
