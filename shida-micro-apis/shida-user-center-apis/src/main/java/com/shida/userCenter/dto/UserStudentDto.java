package com.shida.userCenter.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Accessors(chain = true)
@Schema(name = "UserStudentDto", description = "账户学生关联信息表")
public class UserStudentDto implements Serializable {
    @Schema(description = "主键ID")
    private Long id;
    @Schema(description = "账户ID")
    @NotNull(message = "账户ID不能为空")
    private Long userId;
    @Schema(description = "区县ID")
    @NotNull(message = "区县ID不能为空")
    private Long regionId;
    @Schema(description = "学校ID")
    @NotNull(message = "学校ID不能为空")
    private Long schoolId;
    @Schema(description = "证件号码")
    @NotNull(message = "证件号码不能为空")
    private String certNumber;
}
