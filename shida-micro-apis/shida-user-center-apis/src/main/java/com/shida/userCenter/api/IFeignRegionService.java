package com.shida.userCenter.api;


import com.shida.FeignInterceptor;
import com.shida.userCenter.dto.OrgDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "shida-user-center-service",contextId = "region-api", configuration = FeignInterceptor.class)
public interface IFeignRegionService {
    /**
     * 根据区域ID获取区域信息
     *
     * @param regionId
     * @return
     */
    @PostMapping("/region-feign-api/getRegionById")
    OrgDto getRegionById(@RequestParam("regionId") Long regionId);
}
