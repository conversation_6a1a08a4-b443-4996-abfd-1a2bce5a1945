package com.shida.userCenter.api;


import com.shida.FeignInterceptor;
import com.shida.userCenter.dto.OrgDto;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@FeignClient(name = "shida-user-center-service",contextId = "region-api", configuration = FeignInterceptor.class)
public interface IFeignRegionService {
    /**
     * 根据区域ID获取区域信息
     *
     * @param regionId
     * @return
     */
    @PostMapping("/region-feign-api/getRegionById")
    OrgDto getRegionById(@RequestParam("regionId") Long regionId);

    @PostMapping("/region-feign-api/getRegionListByIds")
    List<OrgDto> getRegionListByIds(@RequestBody @NotEmpty(message = "regionIds不能为空") List<Long> regionIds);
}
