package com.shida.userCenter.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@Schema(name = "GradeDto", description = "年级信息")
public class GradeDto implements Serializable {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "年份")
    private Integer year;

    @Schema(description = "名称")
    private String name;
}
