package com.shida.userCenter.api;

import com.shida.userCenter.dto.SportProjectDto;
import com.shida.pojo.vo.KeyValue;
import com.shida.FeignInterceptor;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * 体育项目Feign服务接口
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@FeignClient(name = "shida-user-center-service",
             path = "/sport-project-feign-api",
             contextId = "sport-project-api",
             configuration = FeignInterceptor.class)
public interface IFeignSportProjectService {

    /**
     * 获取所有体育项目
     *
     * @return 体育项目列表
     */
    @PostMapping("/getAllSportProjects")
    List<SportProjectDto> getAllSportProjects();

    /**
     * 获取体质测试相关的项目列表
     *
     * @return 体质测试项目列表
     */
    @PostMapping("/getFitnessTestProjects")
    List<SportProjectDto> getFitnessTestProjects();

    /**
     * 获取运动项目下拉列表
     *
     * @return 项目下拉列表
     */
    @PostMapping("/getSportProjectSelectList")
    List<KeyValue<String, String>> getSportProjectSelectList();
}
