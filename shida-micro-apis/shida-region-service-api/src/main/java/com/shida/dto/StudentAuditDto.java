package com.shida.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.dto
 * @ClassName: StudentAuditDto
 * @Version 1.0
 * @date 2025/9/4 18:42
 */
@Data
@Accessors(chain = true)
@Schema(name ="StudentAuditDto" ,description = "学生市内转入审核信息")
public class StudentAuditDto {
    @Schema(description = "审核状态")
    @NotNull(message = "审核状态不能为空")
    private Integer status;
    @Schema(description = "审核原因")
    @NotBlank(message = "审核原因不能为空")
    private String reason;
    @Schema(description = "学籍号")
    @NotBlank(message = "学籍号不能为空")
    private String xjh;
    @Schema(description = "审核时间")
    private LocalDateTime createTime;
    @Schema(description = "审核人")
    private String createUser;
    @Schema(description = "审核人ID")
    private Long createUserId;
}
