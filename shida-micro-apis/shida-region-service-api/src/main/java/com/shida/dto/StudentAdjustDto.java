package com.shida.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.dto
 * @ClassName: StudentAdjustDto
 * @Version 1.0
 * @date 2025/9/4 10:37
 */

@Data
@Accessors(chain = true)
@Schema(name ="StudentAdjustDto" ,description = "学生转出信息")
public class StudentAdjustDto {

    @Schema(description = "学生ID")
    private Long studentId;

    @Schema(description = "原区县名称")
    private String outRegionName;

    @Schema(description = "原区县ID")
    private Long outRegionId;

    @Schema(description = "转入学校ID")
    private Long intoSchoolId;
    @Schema(description = "转入学校名称")
    private String intoSchoolName;
    @Schema(description = "转入年级ID")
    private Long intoGradeId;
    @Schema(description = "转入年级名称")
    private String intoGradeName;
    @Schema(description = "转入班级ID")
    private Long intoClazzId;
    @Schema(description = "转入班级名称")
    private String intoClazzName;
    @Schema(description = "转出原因")
    private String outReason;
    @Schema(description = "申请日期")
    private LocalDate applyDate;
}
