package com.shida.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@Schema(name ="SchoolDto" ,description = "学校信息")
public class SchoolDto implements Serializable {
    @Schema(description = "学校id")
    private Long id;
    @Schema(description = "学校名称")
    private String name;
    @Schema(description = "学校代码")
    private String code;
    @Schema(description = "学校描述")
    private String description;
    @Schema(description = "联系人")
    private String contacts;
    @Schema(description = "联系电话")
    private String telephone;
    @Schema(description = "区县id")
    private Long regionId;
    @Schema(description = "区县名称")
    private String regionName;
    @Schema(description = "学段")
    private String period;
    @Schema(description = "学校类型,1公办，2民办")
    private Integer schoolType;
}
