package com.shida;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * name: 微服务服务名称
 * contextId： FeignClient实例唯一标识符，当两个实例的name相同时（同一服务的不同配置），必须使用contextId指定，
 * 这是因为spring在初始化Bean的时候会使用FeignClient的name或url作为Bean的名称，这时如果两个name相同，
 * 就会抛出BeanDefinitionOverrideException异常导致项目无法启动，因此使用contextId来生成唯一Bean名称
 */
@FeignClient(name = "shida-region-service", contextId = "clazz-api", configuration = FeignInterceptor.class)
public interface IFeignClassService {

    /**
     * 检查年级下是否有班级
     *
     * @param gradeId
     * @return
     */
    @PostMapping("/region-feign-api/checkHasGradeClass")
    Boolean checkHasGradeClass(@RequestParam("gradeId") Long gradeId);

    /**
     * 修改年级入学年份
     *
     * @param gradeId
     * @param year
     * @return
     */
    @PostMapping("/region-feign-api/changeGradeEnrollYear")
    String changeGradeEnrollYear(@RequestHeader("X-Tenant-Id") Long regionId, @RequestParam("gradeId") Long gradeId, @RequestParam("year") Integer year);
}
