package com.shida;

import com.shida.dto.SchoolDto;
import feign.HeaderMap;
import feign.Headers;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "shida-region-service",contextId = "school-api", configuration = FeignInterceptor.class)
public interface IFeignSchoolService {

    @PostMapping("/region-school-feign-api/getSchoolByRegionIdAndSchoolId")
    SchoolDto getByRegionIdAndSchoolId(@RequestHeader("X-Tenant-Id") Long regionId,
                                       @RequestParam("schoolId") Long schoolId);

    /**
     * 根据区县ID获取学校列表
     *
     * @param regionId 区县ID
     * @return
     */
    @PostMapping("/region-school-feign-api/getSchoolListByRegionId")
    List<SchoolDto> getByRegionId(@RequestHeader("X-Tenant-Id") Long regionId);

    /**
     * 根据区县ID与学校IDS获取学校列表
     *
     * @param regionId  区县ID
     * @param schoolIds 学校IDS
     * @return
     */
    @PostMapping("/region-school-feign-api/getByRegionIdAndSchoolIds")
    List<SchoolDto> getByRegionIdAndSchoolIds(@RequestHeader("X-Tenant-Id") Long regionId,
                                              @RequestBody List<Long> schoolIds);
}
