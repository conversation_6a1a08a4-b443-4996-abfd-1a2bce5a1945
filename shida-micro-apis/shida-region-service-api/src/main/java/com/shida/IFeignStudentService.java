package com.shida;

import com.shida.dto.StudentAdjustDto;
import com.shida.dto.StudentAuditDto;
import com.shida.dto.StudentDto;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida
 * @ClassName: IFeignStudentService
 * @Version 1.0
 * @date 2025/9/3 18:01
 */

@FeignClient(name = "shida-region-service",contextId = "student-api", configuration = FeignInterceptor.class)
public interface IFeignStudentService {
    @PostMapping("/region-student-feign-api/getStudentInfoByXjh")
    StudentDto getStudentInfoByXjh(@RequestParam("xjh") @Schema(description = "学籍号") String xjh, @RequestHeader("X-Tenant-Id") Long regionId);
    @PostMapping("/region-student-feign-api/saveShiNeiStudentAdjust")
    Integer saveStudentAdjust(@RequestBody StudentAdjustDto studentAdjustDto, @RequestHeader("X-Tenant-Id") Long regionId);
    @PostMapping("/region-student-feign-api/auditStudentAdjust")
    Integer auditStudentAdjust(@RequestBody StudentAuditDto studentAuditDto, @RequestHeader("X-Tenant-Id") Long regionId);
    @PostMapping("/region-student-feign-api/updateShiNeiIn")
    Integer updateShiNeiOut(@RequestBody StudentAdjustDto studentAdjustDto, @RequestHeader("X-Tenant-Id") Long regionId);
    @PostMapping("/region-student-feign-api/deleteStudent")
    Integer deleteStudent(@RequestParam("xjh") String xjh, @RequestHeader("X-Tenant-Id") Long regionId);
}
