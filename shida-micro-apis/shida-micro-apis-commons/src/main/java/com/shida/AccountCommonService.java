package com.shida;

import cn.dev33.satoken.stp.StpUtil;
import com.shida.constant.RedisKeyConstant;
import com.shida.pojo.vo.AccountInfo;
import org.springframework.stereotype.Service;

@Service
public class AccountCommonService {
    public static AccountInfo getCurrentUser() {
        if (!StpUtil.isLogin()) {
            return null;
        }
        return (AccountInfo) StpUtil.getSession().get(RedisKeyConstant.CURRENT_USER_SESSION);
    }
}
