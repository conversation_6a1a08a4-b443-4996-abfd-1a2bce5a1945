package com.shida;

import cn.dev33.satoken.same.SaSameUtil;
import cn.dev33.satoken.stp.StpUtil;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.stereotype.Component;

@Component
public class FeignInterceptor implements RequestInterceptor {
    // 为 Feign 的 RCP调用 添加请求头Same-Token
    @Override
    public void apply(RequestTemplate requestTemplate) {
        System.out.println("FeignInterceptor=========" + SaSameUtil.SAME_TOKEN + "=" + SaSameUtil.getToken());
        requestTemplate.header(SaSameUtil.SAME_TOKEN, SaSameUtil.getToken());
        // 如果希望被调用方有会话状态，此处就还需要将 satoken 添加到请求头中
        requestTemplate.header(StpUtil.getTokenName(), StpUtil.getTokenValue());
    }
}
