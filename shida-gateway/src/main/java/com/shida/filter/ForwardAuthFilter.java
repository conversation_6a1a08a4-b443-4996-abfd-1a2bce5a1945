package com.shida.filter;

import cn.dev33.satoken.same.SaSameUtil;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * 我们的子服务一般不能通过外网直接访问，必须通过网关转发才是一个合法的请求。
 * 这种鉴权需求牵扯到两个环节： 网关转发鉴权 、 服务间内部调用鉴权
 */
@Component
public class ForwardAuthFilter implements GlobalFilter {
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest newRequest = exchange
                .getRequest()
                .mutate()
                // 为请求追加 Same-Token 参数
                .header(SaSameUtil.SAME_TOKEN, SaSameUtil.getToken())
                .build();
        ServerWebExchange newExchange = exchange.mutate().request(newRequest).build();
        System.out.println("gateway---->"+newRequest.getPath() +" ---->SAME_TOKEN="+ SaSameUtil.getToken());
        return chain.filter(newExchange);
    }
}
