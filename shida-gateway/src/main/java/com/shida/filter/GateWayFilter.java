package com.shida.filter;

import cn.dev33.satoken.stp.StpUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpCookie;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.MultiValueMap;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * <AUTHOR>
 * @Date 2024/6/26 19:29
 * @PackageName:com.shida.filter
 * @ClassName: GateWayFilter
 * @Version 1.0
 */
@Component
@Slf4j
public class GateWayFilter implements GlobalFilter, Ordered {

    @Value("${sa-token.not-check-login:}")
    private List<String> noCheckLoginUrls;

    @SneakyThrows
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        MultiValueMap<String, String> headers = exchange.getRequest().getHeaders();
        ServerHttpResponse response = exchange.getResponse();
        ServerHttpRequest request = exchange.getRequest();
        AntPathMatcher antPathMatcher = new AntPathMatcher();

        boolean noCheckLogin = false;
        String currentPath = request.getURI().getPath();
        for (String noCheckLoginUrl : noCheckLoginUrls) {
            if (antPathMatcher.match(noCheckLoginUrl, currentPath)) {
                noCheckLogin = true;
                break;
            }
        }
        if (noCheckLogin) {
            return chain.filter(exchange);
        } else {
            if (headers.containsKey("Authorization")
                    || request.getCookies().containsKey("authorization")
                    || request.getCookies().containsKey("Authorization")) {
                return chain.filter(exchange);
            }
            response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
            return response.writeWith(Mono.just(getError(response)));
        }
    }

    @Override
    public int getOrder() {
        return 0;
    }

    @SneakyThrows
    private DataBuffer getError(ServerHttpResponse response) {
        //3.1设置状态码
        response.setStatusCode(HttpStatus.FORBIDDEN);

        //3.2封装返回数据
        Map<String, Object> map = new HashMap<>();
        map.put("message", "您还未登录，请勿非法请求");
        map.put("status", 403);
        map.put("data", null);

        // 创建ObjectMapper实例
        ObjectMapper objectMapper = new ObjectMapper();
        //3.3作JSON转换
        byte[] bytes = objectMapper.writeValueAsString(map).getBytes(StandardCharsets.UTF_8);

//            byte[] bytes = JacksonUtils.toJsonBytes(map);
        //3.4调用bufferFactory方法,生成DataBuffer对象
        return response.bufferFactory().wrap(bytes);
    }
}
