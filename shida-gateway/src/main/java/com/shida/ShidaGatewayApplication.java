package com.shida;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;

import java.net.InetAddress;

@SpringBootApplication
public class ShidaGatewayApplication {
    public static void main(String[] args) {
        ConfigurableApplicationContext context = SpringApplication.run(ShidaGatewayApplication.class, args);

        try {
            String port = context.getEnvironment().getProperty("server.port");
            InetAddress inetAddress = InetAddress.getLocalHost();
            String ip = inetAddress.getHostAddress();
            System.out.println("网关服务启动成功=======================" + ip);
            System.out.println("聚合文档地址：http://localhost:" + port + "/doc.html");
            System.out.println("聚合文档地址：http://" + ip + ":" + port + "/doc.html");
        } catch (Exception ignored) {
        }
    }
}
