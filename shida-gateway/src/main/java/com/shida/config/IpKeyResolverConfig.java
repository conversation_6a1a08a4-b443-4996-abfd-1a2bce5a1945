package com.shida.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.ratelimit.KeyResolver;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Random;

@Slf4j
@Configuration
public class IpKeyResolverConfig {
    @Bean(value = "ipKeyResolver")
    KeyResolver ipKeyResolver() {
        return exchange -> {
            String ip = exchange.getRequest().getRemoteAddress().getAddress().getHostAddress();
            HttpHeaders headers = exchange.getRequest().getHeaders();
            List<String> tokenList = headers.getOrEmpty("Authorization");
            String token, ipAddress = ip;
            if (!tokenList.isEmpty()) {
                token = tokenList.get(0);
                ipAddress = ip + "_" + token;
            }
            log.debug("ipKeyResolver->{}", ipAddress);
//            Random random = new Random();
//            int length = 6;
//            int number = random.nextInt((int) Math.pow(10, length)) + (int) Math.pow(10, length - 1);
//            String ipAddress = "99." +number ;
            return Mono.just(ipAddress);
        };
    }
}
