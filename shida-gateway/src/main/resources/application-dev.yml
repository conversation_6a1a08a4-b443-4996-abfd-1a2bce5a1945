server:
  port: 8080

knife4j:
  gateway:
    # 开启gateway聚合组件
    enabled: true
    # 设置聚合模式采用discover服务发现的模式
    strategy: discover
    discover:
      enabled: true
      version: openapi3

logging:
  file:
    path: /var/log/${spring.application.name}

spring:
  config:
    import:
      - optional:nacos:redis.yaml?namespace=sjz-healthy
      - optional:nacos:sa-token.yaml?namespace=sjz-healthy
  cloud:
    nacos:
      discovery:
        # 开启nacos作为服务注册中心，默认值：true
        enabled: true
        # nacos集群服务注册地址
        server-addr: http://127.0.0.1:8848
        # nacos用户名
        username: nacos
        # nacos密码
        password: nacos
        # 命名空间，默认 public,可设置dev,pro等，相同特征的服务分类，先去nacos命名空间创建
        namespace: sjz-healthy
        # 分组，默认 DEFAULT_GROUP 相同特征的服务划分的更细
        group: DEFAULT_GROUP
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        username: ${spring.cloud.nacos.discovery.username}
        password: ${spring.cloud.nacos.discovery.password}
        namespace: ${spring.cloud.nacos.discovery.namespace}
        group: ${spring.cloud.nacos.discovery.group}
    gateway:
#      discovery:
#        locator:
          # 是否与服务发现组件结合，通过serviceId转发到具体的实例
#          enabled: true
      routes:
        # 用户中心服务
        - id: shida-user-center-service
          uri: lb://shida-user-center-service
          predicates:
            - Path=/shida-user-center-api/**
          filters:
            - StripPrefix=1
            - name: RequestRateLimiter
              args:
                key-resolver: '#{@ipKeyResolver}'
                redis-rate-limiter:
                  replenishRate: 10 #令牌桶每秒填充平均速率。
                  burstCapacity: 30 #令牌桶总容量。
        # 认证中心服务
        - id: shida-sso-center-service
          uri: lb://shida-sso-center-service
          predicates:
            - Path=/shida-sso-center-api/**
          filters:
            - StripPrefix=1
            - name: RequestRateLimiter
              args:
                key-resolver: '#{@ipKeyResolver}'
                redis-rate-limiter:
                  replenishRate: 10 #令牌桶每秒填充平均速率。
                  burstCapacity: 30 #令牌桶总容量。
        # 操作日志服务
        - id: shida-log-center-service
          uri: lb://shida-log-center-service
          predicates:
            - Path=/log-center-service/**
          filters:
            - StripPrefix=1
            - name: RequestRateLimiter
              args:
                key-resolver: '#{@ipKeyResolver}'
                redis-rate-limiter:
                  replenishRate: 10 #令牌桶每秒填充平均速率。
                  burstCapacity: 30 #令牌桶总容量。
        # 区县服务
        - id: shida-region-service
          uri: lb://shida-region-service
          predicates:
            - Path=/region-service/**
          filters:
            - StripPrefix=1
            - name: RequestRateLimiter
              args:
                key-resolver: '#{@ipKeyResolver}'
                redis-rate-limiter:
                  replenishRate: 10 #令牌桶每秒填充平均速率。
                  burstCapacity: 30 #令牌桶总容量。


