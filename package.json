{"name": "shida-tizhi-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "serve": "nvm use 22 && vite --mode development", "build": "vite --mode production build", "preview": "vite preview"}, "dependencies": {"@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "@wangeditor/plugin-upload-attachment": "^1.1.0", "axios": "^1.10.0", "compressorjs": "^1.2.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.18", "element-plus": "^2.10.4", "nprogress": "^0.2.0", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "sass": "^1.89.2", "unocss": "^66.3.3", "unocss-preset-weapp": "^66.0.2", "vue": "^3.5.17", "vue-json-pretty": "^2.5.0", "vue-router": "^4.0.16"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/node": "^24.0.15", "@vitejs/plugin-vue": "^6.0.0", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "vite": "^7.0.4", "vite-plugin-vue-devtools": "^7.7.7"}}