# 课间操考勤率路由配置说明

## 概述
本文档说明如何为"课间操考勤率"功能添加前端路由配置，使其与"体育课出勤率"在同一级别显示。

## 已完成的配置

### 1. API模块配置
- 创建了 `src/apis/modules/regions/RecessExerciseRateManage.ts` 文件
- 包含课间操考勤率相关的所有API接口：
  - `getPageData`: 获取课间操考勤列表
  - `downloadBatchImportTemplate`: 下载批量导入模板
  - `batchImport`: 批量导入
  - `deleteData`: 删除课间操考勤记录
  - `export`: 导出课间操考勤记录

### 2. 页面组件配置
- 主页面：`src/views/MiddlePhysical/ProcessCheckRecord/DEAttendanceRecord/index.vue`
- 导入组件：`src/views/MiddlePhysical/ProcessCheckRecord/DEAttendanceRecord/components/SportRateRecodeImport.vue`
- 已修改所有API调用，使用新的 `RecessExerciseRateManage` API模块
- 更新了权限标识：
  - `recessExerciseRate:import` - 导入权限
  - `recessExerciseRate:export` - 导出权限
  - `recessExerciseRate:delete` - 删除权限

### 3. 路由配置
本项目使用动态路由系统，路由信息从后端菜单数据动态生成。要完成路由配置，需要在后端菜单管理中添加以下菜单项：

#### 菜单配置结构
```
体育中考 (父级菜单)
├── 过程性考核记录 (子菜单)
    ├── 体育课出勤记录 (现有)
    └── 课间操考勤记录 (新增)
```

#### 后端菜单配置参数
- **菜单名称**: 课间操考勤记录
- **菜单编码**: recessExerciseRecord (建议)
- **路由路径**: /middle-physical/process-check-record/recess-exercise-record
- **组件路径**: MiddlePhysical/ProcessCheckRecord/DEAttendanceRecord/index
- **菜单类型**: 菜单 (M)
- **显示顺序**: 与体育课出勤记录同级
- **父级菜单**: 过程性考核记录

## 权限配置
需要在角色权限管理中为相关角色分配以下权限：
- `recessExerciseRate:import` - 导入课间操考勤数据
- `recessExerciseRate:export` - 导出课间操考勤数据
- `recessExerciseRate:delete` - 删除课间操考勤记录

## 测试步骤
1. 启动前端项目：`npm run dev`
2. 登录系统
3. 在菜单管理中添加课间操考勤记录菜单项
4. 为相关角色分配权限
5. 重新登录，验证菜单是否显示
6. 测试课间操考勤率功能的各项操作

## 注意事项
- 确保后端API接口已实现并正确配置
- 确保菜单权限配置正确
- 如果遇到路由问题，检查动态路由生成逻辑
- 建议在测试环境中先验证功能完整性




