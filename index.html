<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>体质健康后台管理</title>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
    <script>
        window.onload = function () {
            var scripts = document.getElementsByTagName('script');
            for (var i = 0; i < scripts.length; i++) {
                var src = scripts[i].src;
                if (src.indexOf('app.') > -1) {
                    // console.log('app.===>', src);
                    let xhr = new XMLHttpRequest();
                    // xhr 获取文件
                    xhr.open('GET', src + "?t=" + new Date().getTime(), false);
                    xhr.onreadystatechange = function () {
                        // console.log('xhr.readyState===>', xhr.readyState);
                        if (xhr.readyState === 4) {
                            if (xhr.status >= 200 && xhr.status < 300) {
                                // console.log('xhr.status===>', xhr.status);
                                // console.log('xhr.responseText===>', xhr.responseText);
                                if (xhr.responseText && xhr.responseText.indexOf("<!DOCTYPE html>") === 0 ) {
                                    document.location.reload();
                                }
                            }
                        }
                    }
                    xhr.send(null);
                    break;
                }
            }
        }
    </script>
  </body>
</html>
