# 🏃‍♂️ 学生体质健康标准测试结果系统实现文档

## 📋 目录
- [项目概述](#项目概述)
- [系统架构](#系统架构)
- [数据库设计](#数据库设计)
- [后端实现](#后端实现)
- [前端实现](#前端实现)
- [核心功能实现](#核心功能实现)
- [部署说明](#部署说明)
- [常见问题](#常见问题)

---

## 🎯 项目概述

### 功能需求
实现一个完整的学生体质健康标准测试结果管理系统，包含：
- 📈 学生体质测试数据展示（身高、体重、BMI计算）
- 🏃 动态体育项目成绩显示（从数据库动态获取）
- 📥 Excel批量导入功能
- 📤 数据导出功能
- 🗑️ 数据删除功能
- 🔍 多维度搜索功能

### 技术栈
- **后端**: Spring Boot + MyBatis Plus + Feign + Seata
- **前端**: Vue 3 + Element Plus + TypeScript + Axios
- **数据库**: MySQL
- **架构**: 微服务架构

---

## 🏗️ 系统架构

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────────┐
│   前端 Vue3     │───▶│   网关 Gateway   │───▶│  区域业务服务       │
│                 │    │                  │    │  region-business    │
└─────────────────┘    └──────────────────┘    └─────────────────────┘
                                │                         │
                                │                         │ Feign调用
                                ▼                         ▼
                       ┌──────────────────┐    ┌─────────────────────┐
                       │  用户中心服务    │◀───│  体质测试数据管理   │
                       │  user-center     │    │  Excel导入导出      │
                       └──────────────────┘    │  数据查询统计       │
                                │               └─────────────────────┘
                                ▼
                       ┌──────────────────┐
                       │   MySQL数据库    │
                       └──────────────────┘
```

### 微服务职责划分

**用户中心服务 (user-center-service)**
- 体育项目管理
- 用户权限管理
- 基础数据维护

**区域业务服务 (region-business-service)**
- 体质测试数据管理
- Excel导入导出
- 业务逻辑处理

---

## 🗄️ 数据库设计

### 主要数据表

#### 1. 体质测试记录表 (`b_student_tizhiceshi_record`)
```sql
CREATE TABLE `b_student_tizhiceshi_record` (
  `id` bigint NOT NULL,
  `create_time` datetime DEFAULT NULL,
  `student_id` bigint DEFAULT NULL COMMENT '学生ID',
  `clazz_id` bigint DEFAULT NULL COMMENT '班级ID',
  `school_id` bigint DEFAULT NULL COMMENT '学校ID',
  `term_id` bigint DEFAULT NULL COMMENT '学期ID',
  `SFSB` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否伤病',
  `SFCJ` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否残疾',
  `SG` decimal(5,1) DEFAULT NULL COMMENT '身高(cm)',
  `TZ` decimal(5,2) DEFAULT NULL COMMENT '体重(kg)',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='体质健康标准测试结果记录表';
```

#### 2. 体育项目表 (`t_sport_project`)
```sql
CREATE TABLE `t_sport_project` (
  `id` bigint NOT NULL,
  `create_time` datetime DEFAULT NULL,
  `name` varchar(32) DEFAULT NULL COMMENT '项目名称',
  `code` varchar(32) DEFAULT NULL COMMENT '项目代码',
  `description` varchar(128) DEFAULT NULL COMMENT '描述',
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='体育项目信息表';
```

### 初始化数据脚本
```sql
-- 体质健康标准测试项目初始化数据
INSERT INTO `t_sport_project` (`id`, `create_time`, `name`, `code`, `description`) VALUES
(1, NOW(), '肺活量', 'VITAL_CAPACITY', '测试学生心肺功能'),
(2, NOW(), '50米跑', '50M_RUN', '测试学生速度素质'),
(3, NOW(), '坐位体前屈', 'SIT_AND_REACH', '测试学生身体柔韧性'),
(4, NOW(), '立定跳远', 'STANDING_LONG_JUMP', '测试学生下肢爆发力'),
(5, NOW(), '引体向上(男)', 'PULL_UP', '测试男学生上肢力量'),
(6, NOW(), '仰卧起坐(女)', 'SIT_UP', '测试女学生腹肌力量'),
(7, NOW(), '800米跑(女)', '800M_RUN', '测试女学生耐力'),
(8, NOW(), '1000米跑(男)', '1000M_RUN', '测试男学生耐力');
```

---

## ⚙️ 后端实现

### 1. 微服务通信 - Feign接口设计

#### 📁 Feign接口定义
```java
// shida-micro-apis/shida-user-center-apis/src/main/java/com/shida/userCenter/api/IFeignSportProjectService.java
package com.shida.userCenter.api;

import com.shida.FeignInterceptor;
import com.shida.pojo.vo.KeyValue;
import com.shida.userCenter.dto.SportProjectDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * 体育项目服务Feign接口
 */
@FeignClient(name = "shida-user-center-service", 
             contextId = "sport-project-api", 
             configuration = FeignInterceptor.class)
public interface IFeignSportProjectService {

    /**
     * 获取体质测试相关的项目列表
     */
    @PostMapping("/sport-project-feign-api/getFitnessTestProjects")
    List<SportProjectDto> getFitnessTestProjects();

    /**
     * 获取运动项目下拉列表
     */
    @PostMapping("/sport-project-feign-api/getSportProjectSelectList")
    List<KeyValue<String, String>> getSportProjectSelectList();
}
```

**🔑 核心要点**：
- 使用 `@FeignClient` 注解标识微服务调用
- `contextId` 避免多个Feign接口冲突
- `configuration = FeignInterceptor.class` 统一配置拦截器
- 路径使用 `/sport-project-feign-api/` 前缀，符合项目规范

#### 📁 Feign接口实现
```java
// shida-services/shida-user-center-service/src/main/java/com/shida/user/center/controller/feign/FeignSportProjectController.java
package com.shida.user.center.controller.feign;

import com.shida.pojo.vo.KeyValue;
import com.shida.user.center.service.ISportProjectService;
import com.shida.userCenter.api.IFeignSportProjectService;
import com.shida.userCenter.dto.SportProjectDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 体育项目Feign服务实现
 */
@Tag(name = "Feign-体育项目服务")
@RestController
public class FeignSportProjectController implements IFeignSportProjectService {

    @Resource
    private ISportProjectService sportProjectService;

    @Operation(summary = "获取体质测试相关的项目列表")
    @Override
    public List<SportProjectDto> getFitnessTestProjects() {
        return sportProjectService.getFitnessTestProjects();
    }

    @Operation(summary = "获取运动项目下拉列表")
    @Override
    public List<KeyValue<String, String>> getSportProjectSelectList() {
        return sportProjectService.getSportProjectSelectList();
    }
}
```

### 2. 业务服务层实现

#### 📁 体育项目服务
```java
// shida-services/shida-user-center-service/src/main/java/com/shida/user/center/service/impl/SportProjectServiceImpl.java
package com.shida.user.center.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shida.pojo.vo.KeyValue;
import com.shida.user.center.entity.SportProject;
import com.shida.user.center.mapper.SportProjectMapper;
import com.shida.user.center.service.ISportProjectService;
import com.shida.userCenter.dto.SportProjectDto;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 体育项目信息表 服务实现类
 */
@Service
public class SportProjectServiceImpl extends ServiceImpl<SportProjectMapper, SportProject> implements ISportProjectService {

    @Override
    public List<SportProjectDto> getAllSportProjects() {
        List<SportProject> sportProjects = super.list();
        return sportProjects.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<SportProjectDto> getFitnessTestProjects() {
        // 🔍 根据项目代码过滤体质测试相关项目
        LambdaQueryWrapper<SportProject> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(SportProject::getCode, 
                "VITAL_CAPACITY", "50M_RUN", "SIT_AND_REACH", 
                "STANDING_LONG_JUMP", "PULL_UP", "SIT_UP", "800M_RUN", "1000M_RUN");
        
        List<SportProject> sportProjects = super.list(wrapper);
        return sportProjects.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<KeyValue<String, String>> getSportProjectSelectList() {
        List<SportProject> sportProjects = super.list();
        return sportProjects.stream()
                .map(project -> new KeyValue<>(project.getCode(), project.getName()))
                .collect(Collectors.toList());
    }

    /**
     * 将实体转换为DTO
     */
    private SportProjectDto convertToDto(SportProject sportProject) {
        SportProjectDto dto = new SportProjectDto();
        BeanUtil.copyProperties(sportProject, dto);
        return dto;
    }
}
```

**🔑 核心要点**：
- 使用 `LambdaQueryWrapper` 构建查询条件，类型安全
- `in` 操作符过滤特定的体育项目代码
- 使用 `BeanUtil.copyProperties` 进行对象转换

#### 📁 学生体质测试服务
```java
// shida-services/shida-region-business-service/src/main/java/com/shida/region/business/service/impl/StudentTizhiceshiRecordServiceImpl.java
@Service
public class StudentTizhiceshiRecordServiceImpl extends ServiceImpl<StudentTizhiceshiRecordMapper, StudentTizhiceshiRecord> implements IStudentTizhiceshiRecordService {

    @Resource
    private IFeignSportProjectService feignSportProjectService;

    @Override
    public PageInfo<StudentTiZhiCeShiInfo> getPageData(StudentTiZhiCeShiSearch search) {
        // 📄 分页查询
        IPage<StudentTiZhiCeShiInfo> page = new Page<>(search.getPageNumber(), search.getPageSize());
        page = baseMapper.getPageData(page, search);

        // 🏃 获取体育项目列表
        List<SportProjectDto> sportProjects = feignSportProjectService.getFitnessTestProjects();

        // 📊 处理每条记录
        for (StudentTiZhiCeShiInfo record : page.getRecords()) {
            // 设置区域和学期信息
            record.setRegionName(orgDto.getName());
            if (termDto != null) {
                record.setTermName(termDto.getTermName());
            }

            // 计算BMI相关信息
            calculateBMI(record);
            
            // 初始化项目测试结果
            initializeProjectResults(record, sportProjects);
            
            // 计算总分和等级
            calculateTotalScoreAndGrade(record);
        }

        return PageInfo.fromMybatisPage(page);
    }

    /**
     * 🧮 计算BMI相关信息
     */
    private void calculateBMI(StudentTiZhiCeShiInfo record) {
        if (record.getSg() != null && record.getTz() != null &&
            record.getSg().compareTo(BigDecimal.ZERO) > 0) {

            // BMI = 体重(kg) / (身高(m))²
            BigDecimal heightInMeters = record.getSg().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
            BigDecimal bmi = record.getTz().divide(heightInMeters.multiply(heightInMeters), 2, RoundingMode.HALF_UP);
            record.setBmi(bmi);

            // 根据BMI值计算成绩和分数
            calculateBMIScore(record, bmi);
        }
    }

    /**
     * 根据BMI值计算成绩和分数
     */
    private void calculateBMIScore(StudentTiZhiCeShiInfo record, BigDecimal bmi) {
        // 根据国家标准计算BMI等级
        if (bmi.compareTo(new BigDecimal("18.5")) < 0) {
            record.setBmiScore("偏瘦");
            record.setBmiPoints(new BigDecimal("80"));
        } else if (bmi.compareTo(new BigDecimal("24")) <= 0) {
            record.setBmiScore("正常");
            record.setBmiPoints(new BigDecimal("100"));
        } else if (bmi.compareTo(new BigDecimal("28")) <= 0) {
            record.setBmiScore("偏胖");
            record.setBmiPoints(new BigDecimal("80"));
        } else {
            record.setBmiScore("肥胖");
            record.setBmiPoints(new BigDecimal("60"));
        }
    }

    /**
     * 🏃 初始化项目测试结果
     */
    private void initializeProjectResults(StudentTiZhiCeShiInfo record, List<SportProjectDto> sportProjects) {
        Map<String, StudentTiZhiCeShiInfo.ProjectTestResult> projectResults = new HashMap<>();

        for (SportProjectDto project : sportProjects) {
            if (!"BMI".equals(project.getCode())) { // BMI已经单独处理
                StudentTiZhiCeShiInfo.ProjectTestResult result = new StudentTiZhiCeShiInfo.ProjectTestResult();
                result.setProjectCode(project.getCode());
                result.setProjectName(project.getName());
                result.setResult("--"); // 默认值，实际应该从数据库查询
                result.setScore(BigDecimal.ZERO);
                result.setGrade("--");
                projectResults.put(project.getCode(), result);
            }
        }

        record.setProjectResults(projectResults);
    }
}
```

### 3. Excel导入导出功能

#### 📁 导入模板下载
```java
@Override
public void downloadBatchImportTemplate(HttpServletResponse response, String regionId, Long termId) {
    TermDto termDto = feignTermService.getById(termId);
    if (termDto == null) {
        throw new ServiceException(RestCode.ARGUMENT_ERROR, "学期信息不存在");
    }
    
    // 🏃 从数据库查询体育项目
    List<SportProjectDto> sportProjects = feignSportProjectService.getFitnessTestProjects();
    
    ExcelWriter excelWriter = ExcelUtil.getBigWriter();
    
    // 📋 设置基础表头
    List<String> headers = new ArrayList<>();
    headers.add("学生姓名");
    headers.add("学籍号");
    headers.add("性别");
    headers.add("班级名称");
    headers.add("是否残疾");
    headers.add("是否伤病");
    headers.add("身高(cm)");
    headers.add("体重(kg)");
    
    // 🎯 动态添加体育项目表头
    for (SportProjectDto project : sportProjects) {
        headers.add(project.getName());
    }
    
    // ✍️ 写入表头
    excelWriter.writeHeadRow(headers);
    
    // 📤 导出Excel
    response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
    try {
        response.setHeader("Content-Disposition", "attachment;filename=" + 
            java.net.URLEncoder.encode(termDto.getTermName() + "体质测试导入模板.xlsx", "UTF-8"));
    } catch (Exception e) {
        response.setHeader("Content-Disposition", "attachment;filename=" + termDto.getTermName() + "体质测试导入模板.xlsx");
    }
    
    try {
        excelWriter.flush(response.getOutputStream(), true);
    } catch (IOException e) {
        log.error("模板下载失败", e);
        throw new ServiceException(RestCode.SERVER_ERROR, "模板下载失败");
    } finally {
        excelWriter.close();
    }
}
```

**🔑 核心要点**：
- 动态查询体育项目，生成对应的Excel表头
- 使用Hutool的 `ExcelWriter` 简化Excel操作
- 正确设置HTTP响应头，支持中文文件名

#### 📁 批量导入功能
```java
@Override
@GlobalTransactional(rollbackFor = Exception.class)
public List<ExcelBatchImportResponse> batchImport(MultipartFile file, Long termId) {
    // 🔐 权限验证
    AccountInfo accountInfo = AccountCommonService.getCurrentUser();
    String schoolName = accountInfo.getSchoolName();
    if (StrUtil.isBlank(schoolName)) {
        throw new ServiceException(RestCode.ARGUMENT_ERROR, "导入失败，体质测试请使用学校账号导入");
    }

    // 📖 读取Excel数据 - 使用通用方式读取
    List<List<Object>> excelData;
    try {
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        excelData = reader.read();
    } catch (Exception e) {
        log.error("读取Excel文件失败", e);
        throw new ServiceException(RestCode.ARGUMENT_ERROR, "读取Excel文件失败");
    }

    if (CollUtil.isEmpty(excelData) || excelData.size() <= 1) {
        throw new ServiceException(RestCode.ARGUMENT_ERROR, "Excel文件内容为空");
    }

    // 📋 获取表头
    List<Object> headers = excelData.get(0);
    log.info("Excel表头: {}", headers);

    // 📊 获取数据行
    List<List<Object>> dataRows = excelData.subList(1, excelData.size());

    // 🔄 数据验证和转换逻辑
    return new ArrayList<>();
}
```

### 4. 控制器层实现

```java
// shida-services/shida-region-business-service/src/main/java/com/shida/region/business/controller/TiYuZhongKao/StudentTiZhiBiaoZhunCeShiController.java
@Tag(name = "StudentTiZhiBiaoZhunCeShiController-学生体质健康标准测试结果记录表")
@RestController
@RequestMapping("/tizhiceshi/manage")
public class StudentTiZhiBiaoZhunCeShiController {

    @Resource
    private IStudentTizhiceshiRecordService studentTizhiceshiRecordService;

    @Resource
    private IFeignSportProjectService feignSportProjectService;

    @SaCheckPermission(value = "tizhiceshi:list")
    @Operation(summary = "获取学生体质健康标准测试结果列表")
    @PostMapping("/getPageData")
    @AutoLog(value = "查询学生体质测试结果", operateType = OperateType.OPERATE_SEARCH)
    public PageInfo<StudentTiZhiCeShiInfo> getPageData(@RequestBody @Valid StudentTiZhiCeShiSearch search) {
        return studentTizhiceshiRecordService.getPageData(search);
    }

    @SaCheckPermission(value = "tizhiceshi:import")
    @Operation(summary = "下载导入模板")
    @GetMapping("/downloadBatchImportTemplate")
    @AutoLog(value = "下载体质测试导入模板", operateType = OperateType.OPERATE_DOWNLOAD)
    public void downloadBatchImportTemplate(HttpServletResponse response,
                                          @RequestParam String regionId,
                                          @RequestParam Long termId) {
        studentTizhiceshiRecordService.downloadBatchImportTemplate(response, regionId, termId);
    }

    @SaCheckPermission(value = "tizhiceshi:import")
    @Operation(summary = "批量导入")
    @PostMapping("/batchImport")
    @NoRepeatSubmit
    @AutoLog(value = "批量导入体质测试数据", operateType = OperateType.OPERATE_IMPORT)
    public List<ExcelBatchImportResponse> batchImport(@RequestParam("file") MultipartFile file,
                                                    @RequestParam Long termId) {
        return studentTizhiceshiRecordService.batchImport(file, termId);
    }

    @SaCheckPermission(value = "tizhiceshi:delete")
    @Operation(summary = "删除数据")
    @PostMapping("/deleteData")
    @AutoLog(value = "删除体质测试数据", operateType = OperateType.OPERATE_DELETE)
    public boolean deleteData(@RequestBody Map<String, Object> params) {
        Long studentId = Long.valueOf(params.get("studentId").toString());
        Long termId = Long.valueOf(params.get("termId").toString());
        return studentTizhiceshiRecordService.deleteData(studentId, termId);
    }

    @SaCheckPermission(value = "tizhiceshi:export")
    @Operation(summary = "导出数据")
    @PostMapping("/export")
    @AutoLog(value = "导出体质测试数据", operateType = OperateType.OPERATE_EXPORT)
    public void export(@RequestBody StudentTiZhiCeShiSearch search, HttpServletResponse response) {
        studentTizhiceshiRecordService.exportTiZhiCeShi(search, response);
    }
}
```

**🔑 核心要点**：
- `@SaCheckPermission` 权限控制，确保接口安全
- `@AutoLog` 操作日志记录，便于审计
- `@NoRepeatSubmit` 防止重复提交
- `@Valid` 参数校验，确保数据合法性

---

## 🎨 前端实现

### 1. API接口定义

```typescript
// TiZhiJianKang-admin/src/apis/modules/regions/TiZhiCeShiManage.ts
import { http } from '@/utils/request'
import type { PageInfo } from '@/commons/PageInfo'

export interface StudentTiZhiCeShiSearch {
  termId: number
  schoolId?: number
  clazzId?: number
  sfcj?: boolean
  sfsb?: boolean
  keywords?: string
  pageNumber: number
  pageSize: number
}

export interface StudentTiZhiCeShiInfo {
  id: number
  studentName: string
  xjh: string
  xb: number
  clazzName: string
  schoolName: string
  regionName: string
  sfcj: boolean
  sfsb: boolean
  sg: number
  tz: number
  bmi: number
  bmiPoints: number
  totalScore: number
  grade: string
  projectResults: Record<string, any>
}

// 🔍 获取分页数据
export const getPageData = (search: StudentTiZhiCeShiSearch): Promise<PageInfo<StudentTiZhiCeShiInfo>> => {
  return http.post('/tizhiceshi/manage/getPageData', search)
}

// 📥 下载导入模板
export const downloadBatchImportTemplate = (regionId: string, termId: number) => {
  return http.download('/tizhiceshi/manage/downloadBatchImportTemplate', {
    regionId,
    termId
  })
}

// 📤 批量导入
export const batchImport = (file: File, termId: number) => {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('termId', termId.toString())
  return http.post('/tizhiceshi/manage/batchImport', formData)
}

// 📊 导出数据
export const exportData = (search: StudentTiZhiCeShiSearch) => {
  return http.download('/tizhiceshi/manage/export', search)
}

// 🗑️ 删除数据
export const deleteData = (studentId: number, termId: number) => {
  return http.post('/tizhiceshi/manage/deleteData', { studentId, termId })
}
```

### 2. 主界面实现

```vue
<!-- TiZhiJianKang-admin/src/views/MiddlePhysical/ProcessCheckRecord/TiZhiCeShi/index.vue -->
<template>
  <div class="app-container">
    <!-- 🔍 搜索区域 -->
    <div class="search-container">
      <el-form :model="searchForm" ref="searchFormRef" inline>
        <el-form-item label="学期" prop="termId" required>
          <TermSelector v-model="searchForm.termId" @change="handleSearch" />
        </el-form-item>
        
        <el-form-item label="学校" prop="schoolId" v-if="showSchoolSelector">
          <SchoolSelector 
            v-model="searchForm.schoolId" 
            :regionId="currentUser.regionId"
            @change="handleSearch" 
          />
        </el-form-item>
        
        <el-form-item label="班级" prop="clazzId">
          <ClazzSelector 
            v-model="searchForm.clazzId"
            :schoolId="searchForm.schoolId || currentUser.schoolId"
            @change="handleSearch"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon> 搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon> 重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 🔧 工具栏 -->
    <div class="toolbar">
      <el-button type="success" @click="handleImport" v-permission="'tizhiceshi:import'">
        <el-icon><Upload /></el-icon> 导入
      </el-button>
      <el-button type="info" @click="handleDownloadTemplate" v-permission="'tizhiceshi:import'">
        <el-icon><Download /></el-icon> 下载模板
      </el-button>
      <el-button type="warning" @click="handleExport" v-permission="'tizhiceshi:export'">
        <el-icon><Download /></el-icon> 导出
      </el-button>
    </div>

    <!-- 📊 数据表格 -->
    <el-table 
      :data="tableData" 
      border 
      stripe
      v-loading="loading"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="studentName" label="学生姓名" width="100" />
      <el-table-column prop="xjh" label="学籍号" width="120" />
      <el-table-column prop="xb" label="性别" width="60">
        <template #default="{ row }">
          {{ row.xb === 1 ? '男' : '女' }}
        </template>
      </el-table-column>
      <el-table-column prop="clazzName" label="班级" width="100" />
      <el-table-column prop="sg" label="身高(cm)" width="90" />
      <el-table-column prop="tz" label="体重(kg)" width="90" />
      
      <!-- 🧮 BMI列 -->
      <el-table-column label="体重指数" width="120">
        <template #default="{ row }">
          <div v-if="row.bmi">
            <div>BMI: {{ row.bmi }}</div>
            <div class="text-sm text-gray-500">{{ row.bmiScore }}</div>
          </div>
          <span v-else>--</span>
        </template>
      </el-table-column>

      <!-- 🏃 动态体育项目列 -->
      <el-table-column 
        v-for="project in sportProjects" 
        :key="project.code"
        :label="project.name"
        width="120"
      >
        <template #default="{ row }">
          <div v-if="row.projectResults && row.projectResults[project.code]">
            <div>{{ row.projectResults[project.code].result || '--' }}</div>
            <div class="text-sm text-gray-500">
              {{ row.projectResults[project.code].score || '--' }}分
            </div>
          </div>
          <span v-else>--</span>
        </template>
      </el-table-column>

      <el-table-column prop="totalScore" label="总分" width="80" />
      <el-table-column prop="grade" label="等级" width="80">
        <template #default="{ row }">
          <el-tag 
            :type="getGradeTagType(row.grade)"
            size="small"
          >
            {{ row.grade }}
          </el-tag>
        </template>
      </el-table-column>

      <!-- 🔧 操作列 -->
      <el-table-column label="操作" width="120" fixed="right">
        <template #default="{ row }">
          <el-button 
            type="danger" 
            size="small" 
            @click="handleDelete(row)"
            v-permission="'tizhiceshi:delete'"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 📄 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="searchForm.pageNumber"
        v-model:page-size="searchForm.pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSearch"
        @current-change="handleSearch"
      />
    </div>

    <!-- 📥 导入组件 -->
    <TiZhiCeShiImport 
      ref="importRef"
      @success="handleSearch"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Upload, Download } from '@element-plus/icons-vue'
import * as TiZhiCeShiAPI from '@/apis/modules/regions/TiZhiCeShiManage'
import { useUserStore } from '@/store/modules/user'
import { useTableCommons } from '@/hooks/useTableCommons'

// 🏪 Store
const userStore = useUserStore()
const currentUser = computed(() => userStore.userInfo)

// 📊 表格通用逻辑
const {
  tableData,
  total,
  loading,
  searchForm,
  handleSearch,
  handleReset,
  handleSelectionChange
} = useTableCommons({
  searchApi: TiZhiCeShiAPI.getPageData,
  defaultSearch: {
    termId: null,
    schoolId: null,
    clazzId: null,
    pageNumber: 1,
    pageSize: 20
  }
})

// 🏃 体育项目数据
const sportProjects = ref<Array<{code: string, name: string}>>([])

// 🎯 获取体育项目列表
const getSportProjects = async () => {
  try {
    // 这里调用获取体育项目的API
    // sportProjects.value = await API.getSportProjects()
  } catch (error) {
    console.error('获取体育项目失败:', error)
  }
}

// 📥 导入功能
const importRef = ref()
const handleImport = () => {
  if (!searchForm.value.termId) {
    ElMessage.warning('请先选择学期')
    return
  }
  importRef.value?.open(searchForm.value.termId)
}

// 📤 下载模板
const handleDownloadTemplate = async () => {
  if (!searchForm.value.termId) {
    ElMessage.warning('请先选择学期')
    return
  }
  
  try {
    await TiZhiCeShiAPI.downloadBatchImportTemplate(
      currentUser.value.regionId.toString(),
      searchForm.value.termId
    )
    ElMessage.success('模板下载成功')
  } catch (error) {
    ElMessage.error('模板下载失败')
  }
}

// 📊 导出功能
const handleExport = async () => {
  try {
    await TiZhiCeShiAPI.exportData(searchForm.value)
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

// 🗑️ 删除功能
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这条记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await TiZhiCeShiAPI.deleteData(row.studentId, row.termId)
    ElMessage.success('删除成功')
    handleSearch()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 🏷️ 等级标签类型
const getGradeTagType = (grade: string) => {
  switch (grade) {
    case '优秀': return 'success'
    case '良好': return 'primary'
    case '及格': return 'warning'
    case '不及格': return 'danger'
    default: return 'info'
  }
}

// 🎯 计算属性
const showSchoolSelector = computed(() => {
  return !currentUser.value.schoolAdminFlag
})

// 🚀 初始化
onMounted(() => {
  getSportProjects()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.search-container {
  background: #fff;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 4px;
}

.toolbar {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.text-sm {
  font-size: 12px;
}

.text-gray-500 {
  color: #6b7280;
}
</style>
```

### 3. 导入组件实现

```vue
<!-- TiZhiJianKang-admin/src/views/MiddlePhysical/ProcessCheckRecord/TiZhiCeShi/components/TiZhiCeShiImport.vue -->
<template>
  <el-dialog
    v-model="visible"
    title="批量导入体质测试数据"
    width="600px"
    :close-on-click-modal="false"
  >
    <div class="import-container">
      <!-- 📋 导入说明 -->
      <el-alert
        title="导入说明"
        type="info"
        :closable="false"
        show-icon
      >
        <template #default>
          <ol>
            <li>请先下载导入模板，按照模板格式填写数据</li>
            <li>支持xlsx格式文件，文件大小不超过10MB</li>
            <li>学生姓名、学籍号为必填项</li>
            <li>体育项目成绩请按照实际测试结果填写</li>
          </ol>
        </template>
      </el-alert>

      <!-- 📁 文件上传 -->
      <div class="upload-section">
        <el-upload
          ref="uploadRef"
          :auto-upload="false"
          :show-file-list="true"
          :limit="1"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          :before-upload="beforeUpload"
          accept=".xlsx,.xls"
          drag
        >
          <el-icon class="el-icon--upload">
            <Upload />
          </el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              只能上传xlsx/xls文件，且不超过10MB
            </div>
          </template>
        </el-upload>
      </div>

      <!-- ❌ 错误信息显示 -->
      <div v-if="errorMessages.length > 0" class="error-section">
        <el-alert
          title="导入错误"
          type="error"
          :closable="false"
          show-icon
        >
          <template #default>
            <div class="error-list">
              <div v-for="(error, index) in errorMessages" :key="index">
                {{ error }}
              </div>
            </div>
          </template>
        </el-alert>
      </div>
    </div>

    <!-- 🔧 操作按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleConfirm"
          :loading="importing"
          :disabled="!selectedFile"
        >
          {{ importing ? '导入中...' : '确定导入' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Upload } from '@element-plus/icons-vue'
import * as TiZhiCeShiAPI from '@/apis/modules/regions/TiZhiCeShiManage'

// 📡 事件定义
const emit = defineEmits<{
  success: []
}>()

// 📊 响应式数据
const visible = ref(false)
const importing = ref(false)
const selectedFile = ref<File | null>(null)
const errorMessages = ref<string[]>([])
const uploadRef = ref()

// 📂 文件选择
const handleFileChange = (file: any) => {
  selectedFile.value = file.raw
  errorMessages.value = []
}

// 🗑️ 文件移除
const handleFileRemove = () => {
  selectedFile.value = null
  errorMessages.value = []
}

// ✅ 上传前验证
const beforeUpload = (file: File) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                  file.type === 'application/vnd.ms-excel'
  
  if (!isExcel) {
    ElMessage.error('只能上传Excel文件！')
    return false
  }

  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过10MB！')
    return false
  }

  return true
}

// ✅ 确认导入
const handleConfirm = async () => {
  if (!selectedFile.value) {
    ElMessage.warning('请选择要导入的文件')
    return
  }

  importing.value = true
  errorMessages.value = []

  try {
    const result = await TiZhiCeShiAPI.batchImport(selectedFile.value, termId.value)
    
    if (result && result.length > 0) {
      // 显示导入错误
      errorMessages.value = result.map(item => 
        `第${item.rowNum}行: ${item.errorMessage}`
      )
    } else {
      ElMessage.success('导入成功')
      handleCancel()
      emit('success')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '导入失败')
  } finally {
    importing.value = false
  }
}

// ❌ 取消导入
const handleCancel = () => {
  visible.value = false
  selectedFile.value = null
  errorMessages.value = []
  uploadRef.value?.clearFiles()
}

// 🎯 对外接口
const termId = ref<number>()

const open = (id: number) => {
  termId.value = id
  visible.value = true
}

defineExpose({
  open
})
</script>

<style scoped>
.import-container {
  padding: 20px 0;
}

.upload-section {
  margin: 20px 0;
}

.error-section {
  margin-top: 20px;
}

.error-list {
  max-height: 200px;
  overflow-y: auto;
}

.dialog-footer {
  text-align: right;
}
</style>
```

---

## 🔧 核心功能实现

### 1. BMI计算逻辑

```java
/**
 * 🧮 计算BMI相关信息
 */
private void calculateBMI(StudentTiZhiCeShiInfo record) {
    if (record.getSg() != null && record.getTz() != null &&
        record.getSg().compareTo(BigDecimal.ZERO) > 0) {

        // BMI = 体重(kg) / (身高(m))²
        BigDecimal heightInMeters = record.getSg().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
        BigDecimal bmi = record.getTz().divide(heightInMeters.multiply(heightInMeters), 2, RoundingMode.HALF_UP);
        record.setBmi(bmi);

        // 根据BMI值计算成绩和分数
        calculateBMIScore(record, bmi);
    }
}

/**
 * 根据BMI值计算成绩和分数 - 符合国家体质健康标准
 */
private void calculateBMIScore(StudentTiZhiCeShiInfo record, BigDecimal bmi) {
    if (bmi.compareTo(new BigDecimal("18.5")) < 0) {
        record.setBmiScore("偏瘦");
        record.setBmiPoints(new BigDecimal("80"));
    } else if (bmi.compareTo(new BigDecimal("24")) <= 0) {
        record.setBmiScore("正常");
        record.setBmiPoints(new BigDecimal("100"));
    } else if (bmi.compareTo(new BigDecimal("28")) <= 0) {
        record.setBmiScore("偏胖");
        record.setBmiPoints(new BigDecimal("80"));
    } else {
        record.setBmiScore("肥胖");
        record.setBmiPoints(new BigDecimal("60"));
    }
}
```

**🔑 核心要点**：
- 使用 `BigDecimal` 确保计算精度
- `RoundingMode.HALF_UP` 四舍五入规则
- 符合国家体质健康标准的BMI分级

### 2. 动态项目结果初始化

```java
/**
 * 🏃 初始化项目测试结果
 */
private void initializeProjectResults(StudentTiZhiCeShiInfo record, List<SportProjectDto> sportProjects) {
    Map<String, StudentTiZhiCeShiInfo.ProjectTestResult> projectResults = new HashMap<>();

    for (SportProjectDto project : sportProjects) {
        if (!"BMI".equals(project.getCode())) { // BMI已经单独处理
            StudentTiZhiCeShiInfo.ProjectTestResult result = new StudentTiZhiCeShiInfo.ProjectTestResult();
            result.setProjectCode(project.getCode());
            result.setProjectName(project.getName());
            result.setResult("--"); // 默认值，实际应该从数据库查询
            result.setScore(BigDecimal.ZERO);
            result.setGrade("--");
            projectResults.put(project.getCode(), result);
        }
    }

    record.setProjectResults(projectResults);
}
```

### 3. 权限控制实现

```java
// 🔐 后端权限注解
@SaCheckPermission(value = "tizhiceshi:list")
@PostMapping("/getPageData")
public PageInfo<StudentTiZhiCeShiInfo> getPageData(@RequestBody @Valid StudentTiZhiCeShiSearch search) {
    return studentTizhiceshiRecordService.getPageData(search);
}
```

```vue
<!-- 🔐 前端权限指令 -->
<el-button 
  type="success" 
  @click="handleImport" 
  v-permission="'tizhiceshi:import'"
>
  <el-icon><Upload /></el-icon> 导入
</el-button>
```

---

## 🚀 部署说明

### 1. 数据库准备

```bash
# 1. 创建数据库表
mysql -u root -p your_database < database_schema.sql

# 2. 初始化体育项目数据
mysql -u root -p your_database < 体质测试项目初始化数据.sql
```

### 2. 后端服务启动

```bash
# 1. 启动用户中心服务
cd shida-services/shida-user-center-service
mvn spring-boot:run

# 2. 启动区域业务服务  
cd shida-services/shida-region-business-service
mvn spring-boot:run
```

### 3. 前端服务启动

```bash
# 1. 安装依赖
cd TiZhiJianKang-admin
npm install

# 2. 启动开发服务器
npm run dev

# 3. 构建生产版本
npm run build
```

### 4. 配置检查清单

- ✅ **数据库连接配置**: 检查application.yml中的数据库连接信息
- ✅ **Nacos配置中心**: 确保Nacos服务正常运行
- ✅ **Redis缓存配置**: 验证Redis连接配置
- ✅ **文件上传路径配置**: 设置正确的文件存储路径
- ✅ **跨域配置**: 配置前端域名的跨域访问
- ✅ **权限数据初始化**: 确保权限菜单数据已初始化

---

## ❓ 常见问题

### 1. 🚨 Feign调用返回空数组

**问题现象**: `getFitnessTestProjects()` 返回 `[]`

**排查步骤**:
```sql
-- 1. 检查数据库中是否有数据
SELECT * FROM t_sport_project;

-- 2. 检查查询条件是否匹配
SELECT * FROM t_sport_project WHERE code IN (
  'VITAL_CAPACITY', '50M_RUN', 'SIT_AND_REACH', 
  'STANDING_LONG_JUMP', 'PULL_UP', 'SIT_UP', '800M_RUN', '1000M_RUN'
);
```

**解决方案**:
```sql
-- 如果没有数据，执行初始化脚本
INSERT INTO `t_sport_project` (`id`, `create_time`, `name`, `code`, `description`) VALUES
(1, NOW(), '肺活量', 'VITAL_CAPACITY', '测试学生心肺功能'),
(2, NOW(), '50米跑', '50M_RUN', '测试学生速度素质');
-- ... 继续插入其他数据
```

### 2. 📁 Excel导入失败

**问题现象**: 文件上传后提示格式错误或数据验证失败

**解决方案**:
- ✅ 确保使用系统提供的导入模板
- ✅ 检查文件格式为xlsx或xls
- ✅ 验证必填字段（学生姓名、学籍号）不为空
- ✅ 检查数据格式：身高(cm)、体重(kg)为数值型
- ✅ 确认体育项目成绩格式正确

### 3. 🔐 权限验证失败

**问题现象**: 接口调用提示权限不足

**排查步骤**:
```sql
-- 检查用户权限
SELECT p.permission FROM user_permissions p 
WHERE p.user_id = ? AND p.permission LIKE 'tizhiceshi:%';

-- 检查角色权限
SELECT r.role_code, p.permission FROM user_roles ur
JOIN roles r ON ur.role_id = r.id
JOIN role_permissions rp ON r.id = rp.role_id  
JOIN permissions p ON rp.permission_id = p.id
WHERE ur.user_id = ?;
```

**解决方案**:
- ✅ 确认用户角色权限配置
- ✅ 验证权限代码拼写正确
- ✅ 检查Token是否有效且未过期

### 4. 🧮 BMI计算不准确

**问题现象**: BMI计算结果异常或为空

**解决方案**:
```java
// 添加更严格的数据验证
if (record.getSg() != null && record.getTz() != null &&
    record.getSg().compareTo(BigDecimal.ZERO) > 0 &&
    record.getTz().compareTo(BigDecimal.ZERO) > 0 &&
    record.getSg().compareTo(new BigDecimal("300")) < 0 && // 身高不超过300cm
    record.getTz().compareTo(new BigDecimal("500")) < 0) { // 体重不超过500kg
    // 进行BMI计算
}
```

### 5. 🌐 前端页面加载异常

**问题现象**: 页面白屏或组件渲染错误

**排查步骤**:
1. 检查浏览器控制台错误信息
2. 验证API接口是否正常返回数据
3. 检查Vue组件的响应式数据绑定

**解决方案**:
```javascript
// 添加错误边界处理
try {
  const result = await TiZhiCeShiAPI.getPageData(searchForm.value)
  tableData.value = result.records || []
} catch (error) {
  console.error('数据加载失败:', error)
  ElMessage.error('数据加载失败，请重试')
}
```

---

## 📈 系统特性总结

### 🎯 核心特性
- ✅ **微服务架构**: 服务解耦，易于维护扩展
- ✅ **动态配置**: 体育项目可通过数据库配置，无需硬编码
- ✅ **权限控制**: 细粒度权限管理，确保数据安全
- ✅ **Excel处理**: 完整的导入导出功能，支持动态模板
- ✅ **数据计算**: 精确的BMI计算和国家标准评级
- ✅ **用户体验**: 现代化的前端界面，响应式设计

### 🔧 技术亮点
- **Feign微服务调用**: 实现服务间高效通信
- **BigDecimal精度计算**: 确保数值计算准确性
- **动态Excel模板**: 根据数据库配置生成表头
- **Composition API**: 现代Vue开发模式，代码更清晰
- **TypeScript支持**: 提高代码质量和开发效率
- **权限指令**: 前后端统一的权限控制机制

### 📚 扩展建议

#### 性能优化
- 添加Redis缓存体育项目数据
- 实现数据库连接池优化
- 前端虚拟滚动优化大数据量展示

#### 功能增强
- 集成图表展示，提供数据分析
- 支持批量操作（批量删除、修改）
- 添加数据导入历史记录
- 实现数据变更审计日志

#### 移动端适配
- 响应式设计优化
- 开发移动端专用界面
- 支持移动端数据录入

#### 系统集成
- 与教务系统数据同步
- 对接体测设备数据采集
- 集成消息通知系统

---

## 🎉 结语

本文档详细介绍了学生体质健康标准测试结果系统的完整实现过程，从数据库设计到前后端开发，再到部署运维，提供了全方位的技术指导。

通过本系统的实现，学校可以：
- 📊 **高效管理**学生体质健康数据
- 📈 **科学分析**学生体质发展趋势  
- 📋 **规范化**体质测试数据采集
- 🎯 **个性化**学生体质健康指导

系统采用现代化的技术架构，具有良好的可扩展性和维护性，为学校体育教学管理提供了强有力的数据支撑。

---

**📞 技术支持**  
如有技术问题，请参考常见问题章节或联系开发团队获取支持。

**📅 更新日志**  
- v1.0.0: 初始版本，包含基础功能
- v1.1.0: 优化BMI计算逻辑，增加数据验证
- v1.2.0: 完善Excel导入导出功能

---

*本文档最后更新时间: 2025年9月5日*
