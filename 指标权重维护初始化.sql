-- 指标权重维护初始化SQL语句
INSERT INTO `t_setting` (
  `id`,
  `create_time`, 
  `name`,
  `code`,
  `content`,
  `region_id`,
  `school_id`,
  `order_number`
) VALUES (
  FLOOR(RAND() * 9000000000000000) + 1000000000000000,
  NOW(),
  '指标权重维护',
  'ZBQZWH',
  '{
    "indicators": [
      {
        "id": 1,
        "name": "身体质量指数(BMI)",
        "weight": 15,
        "category": "小学一年级至大学四年级"
      },
      {
        "id": 2,
        "name": "肺活量",
        "weight": 15,
        "category": "小学一年级至大学四年级"
      },
      {
        "id": 3,
        "name": "50米跑",
        "weight": 20,
        "category": "小学一、二年级"
      },
      {
        "id": 4,
        "name": "坐位体前屈",
        "weight": 30,
        "category": "小学一、二年级"
      },
      {
        "id": 5,
        "name": "1分钟跳绳",
        "weight": 20,
        "category": "小学一、二年级"
      },
      {
        "id": 6,
        "name": "50米跑",
        "weight": 20,
        "category": "小学三、四年级"
      },
      {
        "id": 7,
        "name": "坐位体前屈",
        "weight": 20,
        "category": "小学三、四年级"
      },
      {
        "id": 8,
        "name": "1分钟跳绳",
        "weight": 20,
        "category": "小学三、四年级"
      },
      {
        "id": 9,
        "name": "1分钟仰卧起坐",
        "weight": 10,
        "category": "小学三、四年级"
      },
      {
        "id": 10,
        "name": "50米跑",
        "weight": 20,
        "category": "小学五、六年级"
      },
      {
        "id": 11,
        "name": "坐位体前屈",
        "weight": 10,
        "category": "小学五、六年级"
      },
      {
        "id": 12,
        "name": "1分钟跳绳",
        "weight": 10,
        "category": "小学五、六年级"
      },
      {
        "id": 13,
        "name": "1分钟仰卧起坐",
        "weight": 20,
        "category": "小学五、六年级"
      },
      {
        "id": 14,
        "name": "50米×8往返跑",
        "weight": 10,
        "category": "小学五、六年级"
      },
      {
        "id": 15,
        "name": "50米跑",
        "weight": 20,
        "category": "初中、高中各年级"
      },
      {
        "id": 16,
        "name": "坐位体前屈",
        "weight": 10,
        "category": "初中、高中各年级"
      },
      {
        "id": 17,
        "name": "立定跳远",
        "weight": 10,
        "category": "初中、高中各年级"
      },
      {
        "id": 18,
        "name": "引体向上(男)/1分钟仰卧起坐",
        "weight": 10,
        "category": "初中、高中各年级"
      },
      {
        "id": 19,
        "name": "1000米跑(男)/800米跑(女)",
        "weight": 20,
        "category": "初中、高中各年级"
      }
    ]
  }',
  9999,
  9999,
  1
);
