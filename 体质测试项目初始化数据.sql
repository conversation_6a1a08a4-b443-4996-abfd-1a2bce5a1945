-- 体质健康标准测试项目初始化数据
INSERT INTO `t_sport_project` (`id`, `create_time`, `name`, `code`, `description`) VALUES
(1, NOW(), '肺活量', 'VITAL_CAPACITY', '测试学生心肺功能'),
(2, NOW(), '50米跑', '50M_RUN', '测试学生速度素质'),
(3, NOW(), '坐位体前屈', 'SIT_AND_REACH', '测试学生身体柔韧性'),
(4, NOW(), '立定跳远', 'STANDING_LONG_JUMP', '测试学生下肢爆发力'),
(5, NOW(), '引体向上(男)', 'PULL_UP', '测试男学生上肢力量'),
(6, NOW(), '仰卧起坐(女)', 'SIT_UP', '测试女学生腹肌力量'),
(7, NOW(), '800米跑(女)', '800M_RUN', '测试女学生耐力'),
(8, NOW(), '1000米跑(男)', '1000M_RUN', '测试男学生耐力');

-- 如果需要更新已有数据，使用以下语句：
-- UPDATE `t_sport_project` SET `name` = '肺活量', `description` = '测试学生心肺功能' WHERE `code` = 'VITAL_CAPACITY';
-- UPDATE `t_sport_project` SET `name` = '50米跑', `description` = '测试学生速度素质' WHERE `code` = '50M_RUN';
-- UPDATE `t_sport_project` SET `name` = '坐位体前屈', `description` = '测试学生身体柔韧性' WHERE `code` = 'SIT_AND_REACH';
-- UPDATE `t_sport_project` SET `name` = '立定跳远', `description` = '测试学生下肢爆发力' WHERE `code` = 'STANDING_LONG_JUMP';
-- UPDATE `t_sport_project` SET `name` = '引体向上(男)', `description` = '测试男学生上肢力量' WHERE `code` = 'PULL_UP';
-- UPDATE `t_sport_project` SET `name` = '仰卧起坐(女)', `description` = '测试女学生腹肌力量' WHERE `code` = 'SIT_UP';
-- UPDATE `t_sport_project` SET `name` = '800米跑(女)', `description` = '测试女学生耐力' WHERE `code` = '800M_RUN';
-- UPDATE `t_sport_project` SET `name` = '1000米跑(男)', `description` = '测试男学生耐力' WHERE `code` = '1000M_RUN';
