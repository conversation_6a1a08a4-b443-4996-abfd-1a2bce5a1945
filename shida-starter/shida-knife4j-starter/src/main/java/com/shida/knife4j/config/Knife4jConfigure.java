package com.shida.knife4j.config;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.collection.CollUtil;
import io.swagger.v3.oas.models.Operation;
import org.springdoc.core.customizers.OpenApiCustomiser;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.Map;

@Configuration
public class Knife4jConfigure {
    @Resource
    private ApplicationContext applicationContext;

    @Bean
    public OpenApiCustomiser saTokenPermissionCustomizer() {
        return openApi -> {
            openApi.getPaths().forEach((path, pathItem) -> {
                // 处理各种HTTP方法
                processOperation(pathItem.getGet(), path, "GET");
                processOperation(pathItem.getPost(), path, "POST");
            });
        };
    }

    private void processOperation(Operation operation, String path, String httpMethod) {
        if (operation == null) return;

        try {
            Map<String, Object> controllerMap = applicationContext.getBeansWithAnnotation(RestController.class);
            for (Object controller : controllerMap.values()) {
                // 修复：获取原始类而不是代理类
                Class<?> controllerClass = controller.getClass();
                if (controllerClass.getName().contains("$$EnhancerBySpringCGLIB$$")) {
                    // 如果是CGLIB代理类，获取父类（原始类）
                    controllerClass = controllerClass.getSuperclass();
                }

                // 修复：使用getDeclaredMethods而不是getMethods以获取所有声明的方法
                for (Method m : controllerClass.getDeclaredMethods()) {
                    // 修复：检查所有类型的Mapping注解
                    Annotation mappingAnnotation = findMappingAnnotation(m, httpMethod);
                    if (mappingAnnotation != null) {
                        String fullPath = getFullPath(controllerClass, m, httpMethod);
                        if (path.equals(fullPath)) {
                            // 找到匹配的方法，获取SaCheckPermission注解
                            SaCheckPermission permission = m.getAnnotation(SaCheckPermission.class);
                            if (permission != null) {
                                String originalSummary = operation.getSummary() != null ? operation.getSummary() : "";
                                String permissionValue = CollUtil.join(CollUtil.newArrayList(permission.value()), ",");
                                operation.setSummary(originalSummary + "·" + permissionValue);

                                // 也可以添加到描述中
                                String originalDesc = operation.getDescription() != null ? operation.getDescription() : "";
                                operation.setDescription(originalDesc + "\n\n**权限要求**: " + permissionValue);
                            }
                            return;
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            // 异常处理
        }
    }


    // 新增：查找匹配的Mapping注解
    private Annotation findMappingAnnotation(Method method, String httpMethod) {
        switch (httpMethod) {
            case "GET":
                return method.getAnnotation(GetMapping.class) != null ?
                        method.getAnnotation(GetMapping.class) :
                        method.getAnnotation(RequestMapping.class);
            case "POST":
                return method.getAnnotation(PostMapping.class) != null ?
                        method.getAnnotation(PostMapping.class) :
                        method.getAnnotation(RequestMapping.class);
            default:
                return method.getAnnotation(RequestMapping.class);
        }
    }

    // 新增：获取完整路径
    private String getFullPath(Class<?> controllerClass, Method method, String httpMethod) {
        RequestMapping classMapping = controllerClass.getAnnotation(RequestMapping.class);
        String basePath = classMapping != null && classMapping.value().length > 0 ?
                classMapping.value()[0] : "";

        String methodPath = "";
        Annotation mappingAnnotation = findMappingAnnotation(method, httpMethod);
        if (mappingAnnotation != null) {
            methodPath = getMappingPath(mappingAnnotation);
        }

        return joinPaths(basePath, methodPath);
    }


    private String getMappingPath(Annotation annotation) {
        try {
            Method valueMethod = annotation.annotationType().getMethod("value");
            String[] values = (String[]) valueMethod.invoke(annotation);
            return values.length > 0 ? values[0] : "";
        } catch (Exception e) {
            return "";
        }
    }

    private String joinPaths(String path1, String path2) {
        String p1 = path1.startsWith("/") ? path1 : "/" + path1;
        String p2 = path2.startsWith("/") ? path2.substring(1) : path2;
        return p1 + (p2.isEmpty() ? "" : "/" + p2);
    }
}
