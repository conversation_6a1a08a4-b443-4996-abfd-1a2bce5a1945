package com.shida.noRepeatSubmit;

import cn.dev33.satoken.stp.StpUtil;
import com.shida.enums.RestCode;
import com.shida.exceptions.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.TimeUnit;

/**
 * @Author: 老木头
 * @Date: 2021/12/16 8:34
 */
@Aspect
@Slf4j
@Component
public class NoRepeatSubmitAspect {

    @Resource
    private RedissonClient redissonClient;

    @Around("@annotation(noRepeatSubmit)")
    public Object around(ProceedingJoinPoint joinPoint, NoRepeatSubmit noRepeatSubmit) throws Throwable {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        Assert.notNull(attributes, "attributes can not null");
        HttpServletRequest request = attributes.getRequest();
        Assert.notNull(request, "request can not null");
        String noRepeatKey = noRepeatSubmit.key() + request.getSession().getId();
        if (StpUtil.isLogin()) {
            noRepeatKey = noRepeatSubmit.key() + StpUtil.getTokenInfo().getTokenValue() + "-" + request.getRequestURI();
        }
        long expire = noRepeatSubmit.value();

        RLock locker = redissonClient.getFairLock(noRepeatKey);
        boolean locked = locker.tryLock(0L, expire, TimeUnit.SECONDS);
        if (locked) {
            Object result;
            try {
                result = joinPoint.proceed();
            } finally {
                //解锁
                locker.unlock();
            }
            return result;
        } else {
            throw new ServiceException(RestCode.NO_REPEAT);
        }
    }
}
