package com.shida.exception;


import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotPermissionException;
import cn.dev33.satoken.exception.NotRoleException;
import cn.hutool.core.net.URLEncoder;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.shida.config.AppConfigure;
import com.shida.enums.RestCode;
import com.shida.exception.dto.ThreeMessageDto;
import com.shida.exceptions.ServiceException;
import com.shida.pojo.vo.RestMessage;
import com.shida.utils.SecretUtils;
import feign.FeignException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.MethodParameter;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpResponse;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date ：Created in 2021/11/13 9:19
 * @description：
 * @modified By：
 */
@RestControllerAdvice
public class ShidaRestControllerAdvice implements ResponseBodyAdvice<Object> {
    @Resource
    ObjectMapper objectMapper;

    Logger logger = LoggerFactory.getLogger(ShidaRestControllerAdvice.class);

    @Override
    public boolean supports(MethodParameter methodParameter, Class<? extends HttpMessageConverter<?>> aClass) {
        boolean swaggerApi = methodParameter.getMethod().getName().equals("openapiJson");
        return !swaggerApi && !methodParameter.getMethod().getReturnType().isAssignableFrom(RestMessage.class);
    }

    @Override
    public Object beforeBodyWrite(Object o, MethodParameter methodParameter, MediaType mediaType, Class<? extends HttpMessageConverter<?>> aClass, ServerHttpRequest serverHttpRequest, ServerHttpResponse serverHttpResponse) {
        boolean swaggerApi = serverHttpRequest.getURI().getPath().matches("/v3/api-docs");
        boolean feignApi = serverHttpRequest.getURI().getPath().matches("/.*-feign-api/.*");
        if (swaggerApi || feignApi) {
            return o;
        }
        String path = serverHttpRequest.getURI().getPath();
        if (path.equals("/oauth2/synUser")) {
            return o;
        }
        // 加密响应
        if (AppConfigure.securityStatus() && !AppConfigure.getSecurityExcludeUrls().contains(serverHttpRequest.getURI().getPath())) {
            String key = RandomUtil.randomString(16);
            serverHttpResponse.getHeaders().set("secret-key", key);
            if (((ServletServerHttpResponse) serverHttpResponse).getServletResponse().getStatus() == 200) {
                RestMessage message;

                if (Objects.isNull(o)) {
                    message = RestMessage.success("ok", null);
                } else if (o instanceof String) {
                    message = RestMessage.success(o.toString());
                } else {
                    message = RestMessage.success(o);
                }
                serverHttpResponse.getHeaders().set("Content-Type", "application/encrypted-data");
                try {
                    String json = objectMapper.writeValueAsString(message);
                    return SecretUtils.encrypt(json, key);
                } catch (Exception e) {
                    return SecretUtils.encrypt(JSONUtil.toJsonStr(message), key);
                }
            } else {
                Integer errCode = ((ServletServerHttpResponse) serverHttpResponse).getServletResponse().getStatus();
                RestCode restCode = Arrays.stream(RestCode.values()).filter(r -> r.code().equals(errCode))
                        .findFirst().
                        orElse(RestCode.UNKNOWN);
                return RestMessage.error(restCode);
            }
        } else {
            if (((ServletServerHttpResponse) serverHttpResponse).getServletResponse().getStatus() == 200) {
                if (Objects.isNull(o)) {
                    return JSONUtil.toJsonStr(RestMessage.success("ok", null));
                } else if (o instanceof String) {
                    return JSONUtil.toJsonStr(RestMessage.success(o.toString()));
                } else {
                    return RestMessage.success(o);
                }
            } else {
                Integer errCode = ((ServletServerHttpResponse) serverHttpResponse).getServletResponse().getStatus();
                RestCode restCode = Arrays.stream(RestCode.values()).filter(r -> r.code().equals(errCode))
                        .findFirst().
                        orElse(RestCode.UNKNOWN);
                return RestMessage.error(restCode);
            }
        }
    }


    // 全局异常拦截（拦截项目中的所有异常）
    @ExceptionHandler
    public RestMessage handlerException(Exception e, HttpServletRequest request, HttpServletResponse response) {
        LogError(request, e);
        response.setStatus(RestCode.UNKNOWN.code());
        response.setContentType("application/json;utf-8");
        return RestMessage.error(RestCode.UNKNOWN.code(), RestCode.UNKNOWN.message());
    }

    // 全局认证异常拦截
    @ExceptionHandler
    public RestMessage handlerException(SecurityException e, HttpServletRequest request, HttpServletResponse response) {
        LogError(request, e);
        response.setStatus(RestCode.AUTHORIZED_ERROR.code());
        response.setContentType("application/json;utf-8");
        return RestMessage.error(RestCode.AUTHORIZED_ERROR.code(), e.getMessage());
    }
    // 全局认证异常拦截
    @ExceptionHandler
    public String handlerException(SynThreeException e, HttpServletRequest request, HttpServletResponse response) {
        LogError(request, e);
        response.setStatus(RestCode.AUTHORIZED_ERROR.code());
        response.setContentType("application/json;utf-8");
        ThreeMessageDto threeMessageDto = new ThreeMessageDto();
        threeMessageDto.setRetcode("500001");
        threeMessageDto.setMsg("同步数据出错了:"+e.getMessage());
        return JSONUtil.toJsonStr(Arrays.asList(threeMessageDto));
    }
    // Feign异常拦截
    @ExceptionHandler
    public RestMessage handlerException(FeignException e, HttpServletRequest request, HttpServletResponse response) {
        LogError(request, e);
        RestMessage error = JSONUtil.toBean(e.contentUTF8(), RestMessage.class);
        response.setStatus(e.status());
        response.setContentType("application/json;utf-8");
        return RestMessage.error(e.status(), error.getMessage());
    }

    @ExceptionHandler
    public RestMessage handlerException(DuplicateKeyException e, HttpServletRequest request, HttpServletResponse response) {
        LogError(request, e);
        response.setStatus(RestCode.ARGUMENT_ERROR.code());
        response.setContentType("application/json;utf-8");
        return RestMessage.error(RestCode.ARGUMENT_ERROR.code(), "数据已存在，请勿重复添加");
    }

    @ExceptionHandler
    public RestMessage handlerException(MethodArgumentNotValidException e, HttpServletRequest request, HttpServletResponse response) {
        LogError(request, e);
        StringBuilder errMsg = new StringBuilder();
        List<FieldError> fieldErrors = e.getBindingResult().getFieldErrors();
        if (!CollectionUtils.isEmpty(fieldErrors)) {
            for (int i = 0; i < fieldErrors.size(); i++) {
                if (i > 0) {
                    errMsg.append(", ");
                }
                FieldError error = fieldErrors.get(i);
                errMsg.append(error.getDefaultMessage());
            }
        }
        response.setStatus(RestCode.ARGUMENT_ERROR.code());
        return RestMessage.error(RestCode.ARGUMENT_ERROR.code(), errMsg.toString());
    }

    @ExceptionHandler
    public RestMessage handlerException(HttpRequestMethodNotSupportedException e, HttpServletRequest request, HttpServletResponse response) {
        LogError(request, e);
        response.setStatus(RestCode.UNKNOWN.code());
        return RestMessage.error(RestCode.UNKNOWN.code(), "请求方式错误");
    }

    @ExceptionHandler
    public RestMessage handlerException(ServiceException e, HttpServletRequest request, HttpServletResponse response) {
        LogError(request, e);
        if (e.getCode() == 411 || e.getCode() == 412 || e.getCode() == 413) {
            response.setContentType("application/json");
            if (StrUtil.isBlank(e.getMessage())) {
                RestCode restCode = RestCode.valueOfCode(e.getCode());
                response.setHeader("errmsg", URLEncoder.DEFAULT.encode(restCode.message(), StandardCharsets.UTF_8));
            } else {
                response.setHeader("errmsg", URLEncoder.DEFAULT.encode(e.getMessage(), StandardCharsets.UTF_8));
            }
        }
        response.setStatus(e.getCode());
        return RestMessage.error(e.getCode(), e.getMessage());
    }

    @ExceptionHandler
    public RestMessage handlerException(NotLoginException e, HttpServletRequest request, HttpServletResponse response) {
        LogError(request, e);
        response.setStatus(RestCode.UNAUTHORIZED.code());
        String message = NotLoginException.DEFAULT_MESSAGE;
        if (e.getType().equals(NotLoginException.NOT_TOKEN)) {
            message = NotLoginException.DEFAULT_MESSAGE;
        } else if (e.getType().equals(NotLoginException.INVALID_TOKEN)) {
            message = NotLoginException.INVALID_TOKEN_MESSAGE;
        } else if (e.getType().equals(NotLoginException.TOKEN_TIMEOUT)) {
            message = NotLoginException.TOKEN_TIMEOUT_MESSAGE;
        } else if (e.getType().equals(NotLoginException.BE_REPLACED)) {
            message = NotLoginException.BE_REPLACED_MESSAGE;
        } else if (e.getType().equals(NotLoginException.KICK_OUT)) {
            message = NotLoginException.KICK_OUT_MESSAGE;
        }
        return RestMessage.error(RestCode.UNAUTHORIZED.code(), message);
    }

    @ExceptionHandler
    public RestMessage handlerException(NotRoleException e, HttpServletRequest request, HttpServletResponse response) {
        LogError(request, e);
        response.setStatus(RestCode.FORBIDDEN.code());
        return RestMessage.error(RestCode.FORBIDDEN.code(), RestCode.FORBIDDEN.message());
    }

    @ExceptionHandler
    public RestMessage handlerException(NotPermissionException e, HttpServletRequest request, HttpServletResponse response) {
        LogError(request, e);
        response.setStatus(RestCode.FORBIDDEN.code());
        return RestMessage.error(RestCode.FORBIDDEN.code(), RestCode.FORBIDDEN.message());
    }

    private String LogError(HttpServletRequest request, Exception e) {
        String path = request.getRequestURI();
        Map<String, String[]> m = request.getParameterMap();
        StringBuilder stringBuilder = new StringBuilder(path);
        stringBuilder.append(" params=").append("{");
        m.forEach((key, val) -> {
            stringBuilder.append(key).append(":");
            for (int i = 0; i < val.length; i++) {
                stringBuilder.append(val[i]).append(",");
            }
        });
        stringBuilder.append("}");
        String outPath = stringBuilder.toString();
        logger.error(outPath, e);
        return outPath;
    }
}
