package com.shida.exception.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/1/3 14:16
 */
@Data
@Accessors(chain = true)
@Schema(description = "消息响应")
public class ThreeMessageDto implements Serializable {
    @Schema(description = "返回码(000000为成功，其他为失败，详情可见公共返回码说明)")
    private String retcode;
    @Schema(description = "返回说明")
    private String msg;
    @Schema(description = "返回数据")
    private DataDto data;
}
