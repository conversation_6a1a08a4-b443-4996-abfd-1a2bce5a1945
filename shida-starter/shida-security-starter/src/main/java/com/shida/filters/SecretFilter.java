package com.shida.filters;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.shida.config.AppConfigure;
import com.shida.enums.RestCode;
import com.shida.exceptions.ServiceException;
import com.shida.pojo.vo.RestMessage;
import com.shida.utils.SecretUtils;
import org.springframework.http.server.RequestPath;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.annotation.Resource;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

@WebFilter(urlPatterns = "/**")
@Component
public class SecretFilter extends OncePerRequestFilter {
    AntPathMatcher antPathMatcher;

    public SecretFilter() {
        antPathMatcher = new AntPathMatcher();
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {  // 1. 从 HttpServletRequest 对象中获取请求体内容
        String path = request.getServletPath();
        // 判断path是否是
        if (antPathMatcher.match("/*-feign-api/**", path)) {
            filterChain.doFilter(request, response);
        } else if (AppConfigure.securityStatus() && !AppConfigure.getSecurityExcludeUrls().contains(request.getServletPath())) {
            if (request.getMethod().equalsIgnoreCase("POST")) {
                String requestBody = getRequestBody(request);
                JSONObject jsonBody;
                if (StrUtil.isNotBlank(requestBody)) {
                    // 2. 解析请求体内容为JSON对象
                    jsonBody = JSONUtil.parseObj(requestBody);
                } else {
                    jsonBody = new JSONObject();
                }

                // 3. 解密
                String key = request.getHeader("secret-key");
                String data = jsonBody.getStr("data");
                if (StrUtil.isBlank(key)) {
                    response.setStatus(RestCode.ARGUMENT_ERROR.code());
                    response.setContentType("application/json; charset=UTF-8"); // 设置字符集
                    response.setCharacterEncoding("UTF-8"); // 明确指定响应的字符编码
                    response.getWriter().write(JSONUtil.toJsonStr(RestMessage.error(RestCode.ARGUMENT_ERROR.code(), "当前接口模式为加密模式，header缺失秘钥")));
                    return;
                }


                String decryptData = SecretUtils.desEncrypt(data, key);
                if (JSONUtil.isTypeJSON(decryptData)) {
                    jsonBody = JSONUtil.parseObj(decryptData);
                }
                // 4. 包装 HttpServletRequest 对象为自定义的 RequestWrapper 对象，以便后续的处理
                RequestWrapper requestWrapper = new RequestWrapper(request, jsonBody.toJSONString(1));
                // 6. 调用下一个过滤器或 Servlet
                filterChain.doFilter(requestWrapper, response);
            } else {
                filterChain.doFilter(request, response);
            }
        } else {
            filterChain.doFilter(request, response);
        }
    }

    /**
     * 获取请求体内容。
     *
     * @param request HttpServletRequest对象
     * @return 请求体内容
     * @throws IOException 如果读取请求体内容时发生I/O异常
     */
    private String getRequestBody(HttpServletRequest request) throws IOException {
        BufferedReader reader = new BufferedReader(new InputStreamReader(request.getInputStream()));
        StringBuilder sb = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            sb.append(line);
        }
        return sb.toString();
    }
}
