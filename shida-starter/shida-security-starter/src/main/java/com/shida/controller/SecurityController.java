package com.shida.controller;

import com.shida.config.AppConfigure;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "安全控制")
@RequestMapping("")
@RestController
public class SecurityController {

    @Operation(summary = "获取安全模式状态")
    @PostMapping("securityStatus")
    public Boolean securityStatus() {
        return AppConfigure.securityStatus();
    }
}
