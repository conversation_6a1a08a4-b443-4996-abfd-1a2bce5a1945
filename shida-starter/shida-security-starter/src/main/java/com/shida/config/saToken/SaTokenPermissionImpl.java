package com.shida.config.saToken;

import cn.dev33.satoken.stp.StpInterface;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import com.shida.constant.RedisKeyConstant;
import com.shida.pojo.vo.AccountInfo;
import com.shida.pojo.vo.PermissionInfo;
import com.shida.pojo.vo.RoleInfo;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class SaTokenPermissionImpl implements StpInterface {

    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        AccountInfo info = (AccountInfo) StpUtil.getSession().get(RedisKeyConstant.CURRENT_USER_SESSION);
        if (info == null) {
            return new ArrayList<>();
        }
        return info.getPermissions().stream().map(PermissionInfo::getPermission).collect(Collectors.toList());
    }

    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
        AccountInfo info = (AccountInfo) StpUtil.getSession().get(RedisKeyConstant.CURRENT_USER_SESSION);
        if (info == null) {
            return new ArrayList<>();
        }
        return info.getRoles().stream().map(RoleInfo::getRoleCode).collect(Collectors.toList());
    }
}
