package com.shida.config.saToken;

import cn.dev33.satoken.stp.StpUtil;
import com.shida.constant.RedisKeyConstant;
import com.shida.enums.RestCode;
import com.shida.exceptions.ServiceException;
import com.shida.pojo.vo.AccountInfo;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

@Component
public class CheckDefaultPasswordInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        AccountInfo currentUser = null;
        try {
            currentUser = (AccountInfo) StpUtil.getSession().get(RedisKeyConstant.CURRENT_USER_SESSION);
        } catch (Exception ignored) {
        }

        if (Objects.nonNull(currentUser)) {
            if (currentUser.getDefaultPasswordFlag()) {
                throw new ServiceException("请修改默认密码，再进行其他操作。");
            } else {
                return true;
            }
        }
        return false;
    }
}
