package com.shida.config.saToken;

import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.context.model.SaRequest;
import cn.dev33.satoken.filter.SaServletFilter;
import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.same.SaSameUtil;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.shida.enums.RestCode;
import com.shida.exceptions.ServiceException;
import com.shida.properties.UploadFileProperties;
import com.shida.properties.UploadUrlProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Configuration
@Slf4j
public class SaTokenConfigure implements WebMvcConfigurer {

    @Value("${sa-token.not-check-login:}")
    private List<String> noCheckLoginUrls;

    @Resource
    private CheckDefaultPasswordInterceptor checkDefaultPasswordInterceptor;
    @Resource
    private UploadFileProperties uploadFileProperties;
    @Resource
    private UploadUrlProperties uploadUrlProperties;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 调试输出
        noCheckLoginUrls.forEach(System.out::println);

        // 注册路由拦截器，自定义认证规则
        registry.addInterceptor(new SaInterceptor(handle -> StpUtil.checkLogin()))
            .addPathPatterns("/**")
            // 排除不需要登录的接口
            .excludePathPatterns(noCheckLoginUrls)
            .excludePathPatterns("/*-feign-api/**")
            .excludePathPatterns(uploadUrlProperties.getAvatar() + "**",
                uploadUrlProperties.getEditor() + "**");

        registry.addInterceptor(checkDefaultPasswordInterceptor)
            .addPathPatterns("/**")
            // 排除不需要登录的接口
            .excludePathPatterns(noCheckLoginUrls)
            .excludePathPatterns("/*-feign-api/**")
            .excludePathPatterns(uploadUrlProperties.getAvatar() + "**",
                uploadUrlProperties.getEditor() + "**");
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 静态资源处理
        registry.addResourceHandler(uploadUrlProperties.getImage() + "**",
                uploadUrlProperties.getAvatar() + "**",
                uploadUrlProperties.getEditor() + "**")
            .addResourceLocations("file:" + uploadFileProperties.getImagePath(),
                "file:" + uploadFileProperties.getAvatarPath(),
                "file:" + uploadFileProperties.getEditorPath());
    }


    /**
     * 注册 Sa-Token 全局过滤器
     * 采用 内部服务同源策略，防止外部直接访问内部接口，只能通过gateway访问内部服务
     */
    @Bean
    public SaServletFilter getSaServletFilter() {
        SaServletFilter filter = new SaServletFilter()
            .addInclude("/**");
        // 把noCheckLoginUrls加入到排除名单
        for (String url : noCheckLoginUrls) {
            filter.addExclude(url);
        }
        filter.setAuth(obj -> SaSameUtil.checkCurrentRequestToken())
            .setError(e -> {
                throw new ServiceException(RestCode.FORBIDDEN, "无效的Same-Token，内部服务不允许外部访问");
            });
        return filter;
    }
}
