package com.shida.pojo;

import com.shida.autolog.LogType;
import com.shida.autolog.OperateType;
import com.shida.pojo.search.Search;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Schema(name = "LogSearch", description = "日志搜索条件")
public class LogSearch extends Search {
    @Schema(description = "日志类型")
    LogType logType;
    @Schema(description = "操作类型")
    Integer operateType;
    @Schema(description = "开始日期")
    private LocalDate startDate;
    @Schema(description = "结束日期")
    private LocalDate endDate;
    @Schema(description = "开始时间")
    private LocalDateTime startDateTime;
    @Schema(description = "结束时间")
    private LocalDateTime endDateTime;

//
//    @Schema(description = "开始时间")
//    public LocalDateTime getStartDateTime() {
//        return startDate == null ? null : startDate.atStartOfDay();
//    }
//
//    @Schema(description = "开始时间")
//    public LocalDateTime getEndDateTime() {
//        return endDate == null ? null : endDate.atStartOfDay();
//    }

}
