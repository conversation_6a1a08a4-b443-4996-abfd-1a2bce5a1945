package com.shida.autolog;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 操作记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-19
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(name = "Log", description = "操作记录表")
public class SystemLog implements Serializable {

    @Schema(description = "主键ID")
    @JsonSerialize(contentUsing = ToStringSerializer.class)
    private Long id;

    @Schema(description = "操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "耗时")
    private Long costTime;

    @Schema(description = "ip地址")
    private String ip;

    @Schema(description = "入参")
    private String requestParam;

    @Schema(description = "响应结果")
    private String responseResult;

    @Schema(description = "请求类型")
    private String requestType;

    @Schema(description = "请求路径")
    private String requestUrl;

    @Schema(description = "请求方法")
    private String method;

    @Schema(description = "操作人")
    private String username;

    @Schema(description = "操作人ID")
    private Long userId;

    @Schema(description = "操作人部门ID")
    private Long deptId;

    @Schema(description = "操作人部门code")
    private String deptCode;

    @Schema(description = "操作人角色")
    private String deptName;

    @Schema(description = "操作人角色ID")
    private String roleId;

    @Schema(description = "操作人角色")
    private String roleName;

    @Schema(description = "操作人机构ID")
    private Long orgId;

    @Schema(description = "操作人机构名称")
    private String orgName;

    @Schema(description = "操作内容")
    private String logContent;

    @Schema(description = "日志类型")
    private Integer logType;

    @Schema(description = "操作类型")
    private Integer operateType;

    @Schema(description = "操作对象")
    private String operateObject;

    @Schema(description = "详情描述")
    private String description;
}
