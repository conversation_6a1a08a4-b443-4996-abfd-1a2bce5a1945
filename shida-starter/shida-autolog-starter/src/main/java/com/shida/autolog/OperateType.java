package com.shida.autolog;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.Getter;

@Getter
public enum OperateType implements IEnum<Integer> {
    OPERATE_ADD(301, "添加"),
    OPERATE_DELETE(302, "删除"),
    OPERATE_EDIT(303, "修改"),
    OPERATE_SEARCH(304, "查询"),
    OPERATE_IMPORT(305, "导入"),
    OPERATE_EXPORT(306, "导出"),
    OPERATE_UPLOAD_FILE(307, "上传"),
    UNKNOWN(400, "未知"),;

    private final int value;
    private final String desc;

    OperateType(final int value, final String desc) {
        this.value = value;
        this.desc = desc;
    }

    /**
     * 根据code获取模板需使用的数据值
     *
     * @param value
     * @return
     */
    public static OperateType get(Integer value) {
        OperateType[] sexs = OperateType.values();
        for (OperateType t : sexs) {
            if (t.getValue().equals(value)) {
                return t;
            }
        }

        return null;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }

    @Override
    public String toString() {
        return this.name();
    }
}
