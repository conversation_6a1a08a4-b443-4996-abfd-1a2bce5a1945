package com.shida.autolog;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.Getter;

@Getter
public enum LogType implements IEnum<Integer> {
    LOGIN(1, "用户登录"),
    LOGOUT(2, "用户退出"),
    OPERATE(300, "操作"),;

    private final int value;
    private final String desc;

    LogType(final int value, final String desc) {
        this.value = value;
        this.desc = desc;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }

    @Override
    public String toString() {
        return this.name();
    }
}
