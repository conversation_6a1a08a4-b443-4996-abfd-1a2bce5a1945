package com.shida.autolog;


import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.shida.constant.RedisKeyConstant;
import com.shida.pojo.vo.AccountInfo;
import com.shida.utils.IpAddressUtil;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @date ：Created in 2021/11/11 20:44
 * @description：
 * @modified By：
 */
@Aspect
@Component
public class PrintLogAspect {

    private ExecutorService executorService;

    @PostConstruct
    public void init() {
        executorService = Executors.newFixedThreadPool(1000);
    }

    //    @Pointcut("@annotation(org.springframework.web.bind.annotation.PostMapping) || " +
//            "@annotation(org.springframework.web.bind.annotation.GetMapping)")
    @Pointcut("execution(* com.shida..*Controller.*(..))")
    public void logPointCut() {
    }

    @Around("logPointCut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        long beginTime = System.currentTimeMillis();
        AccountInfo currentUser;
        try {
            currentUser = (AccountInfo) StpUtil.getSession().get(RedisKeyConstant.CURRENT_USER_SESSION);
        } catch (Exception e) {
            currentUser = null;
        }
        //执行方法
        Object result = point.proceed();
        //执行时长(毫秒)
        long time = System.currentTimeMillis() - beginTime;
        if (Objects.isNull(currentUser)) {
            try {
                currentUser = (AccountInfo) StpUtil.getSession().get(RedisKeyConstant.CURRENT_USER_SESSION);
            } catch (Exception ignored) {
            }
        }
        HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
        AccountInfo finalCurrentUser = currentUser;
        executorService.execute(() -> printLog(request, finalCurrentUser, point, result, time));
        return result;
    }

    private void printLog(HttpServletRequest request, AccountInfo user, ProceedingJoinPoint joinPoint, Object result, Long useTime) {
        String url = request.getRequestURI();
        StringBuilder stringBuilder = new StringBuilder("================START ").append(LocalDateTime.now()).append("================");
        stringBuilder.append(StrUtil.LF).append("requestUrl:").append(StrUtil.TAB).append(url);
        stringBuilder.append(StrUtil.LF).append("methodType:").append(StrUtil.TAB).append(request.getMethod());
        stringBuilder.append(StrUtil.LF).append("params:").append(StrUtil.TAB).append(StrUtil.TAB).append(getRequestParams(request, joinPoint));
        stringBuilder.append(StrUtil.LF).append("result:").append(StrUtil.TAB).append(StrUtil.TAB).append(JSONUtil.toJsonStr(result));
        stringBuilder.append(StrUtil.LF).append("userTime:").append(StrUtil.TAB).append(useTime).append("ms");
        stringBuilder.append(StrUtil.LF).append("user:").append(StrUtil.TAB).append(StrUtil.TAB).append(JSONUtil.toJsonStr(user));
        stringBuilder.append(StrUtil.LF).append("ipAddr:").append(StrUtil.TAB).append(StrUtil.TAB).append(IpAddressUtil.getIpAddr(request));
        stringBuilder.append(StrUtil.LF).append("================END ").append(LocalDateTime.now()).append("================");
        System.out.println(stringBuilder);
    }

    /**
     * @param request:   request
     * @param joinPoint: joinPoint
     * @Description: 获取请求参数
     * @author: scott
     * @date: 2020/4/16 0:10
     * @Return: java.lang.String
     */
    private String getRequestParams(HttpServletRequest request, JoinPoint joinPoint) {
        String httpMethod = request.getMethod();
        String params = "";
        if ("POST".equals(httpMethod) || "PUT".equals(httpMethod) || "PATCH".equals(httpMethod)) {
            Object[] paramsArray = joinPoint.getArgs();
            // java.lang.IllegalStateException: It is illegal to call this method if the current request is not in asynchronous mode (i.e. isAsyncStarted() returns false)
            //  https://my.oschina.net/mengzhang6/blog/2395893
            Object[] arguments = new Object[paramsArray.length];
            for (int i = 0; i < paramsArray.length; i++) {
                if (paramsArray[i] instanceof ServletRequest ||
                        paramsArray[i] instanceof ServletResponse ||
                        paramsArray[i] instanceof HttpSession ||
                        paramsArray[i] instanceof MultipartFile) {
                    //ServletRequest不能序列化，从入参里排除，否则报异常：java.lang.IllegalStateException: It is illegal to call this method if the current request is not in asynchronous mode (i.e. isAsyncStarted() returns false)
                    //ServletResponse不能序列化 从入参里排除，否则报异常：java.lang.IllegalStateException: getOutputStream() has already been called for this response
                    continue;
                }
                arguments[i] = paramsArray[i];
            }
            if (arguments.length == 1) {
                params = JSONUtil.toJsonStr(arguments[0]);
            } else {
                params = JSONUtil.toJsonStr(arguments);
            }
        } else {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            // 请求的方法参数值
            Object[] args = joinPoint.getArgs();
            // 请求的方法参数名称
            LocalVariableTableParameterNameDiscoverer u = new LocalVariableTableParameterNameDiscoverer();
            String[] paramNames = u.getParameterNames(method);
            if (args != null && paramNames != null) {
                for (int i = 0; i < args.length; i++) {
                    params += "  " + paramNames[i] + ": " + args[i];
                }
            }
        }
        return params;
    }
}
