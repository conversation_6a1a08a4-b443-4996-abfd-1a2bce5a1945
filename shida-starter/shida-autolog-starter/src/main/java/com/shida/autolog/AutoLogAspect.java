package com.shida.autolog;


import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.shida.constant.RedisKeyConstant;
import com.shida.pojo.vo.AccountInfo;
import com.shida.pojo.vo.RoleInfo;
import com.shida.utils.IpAddressUtil;
import io.swagger.v3.oas.annotations.Operation;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2021/11/11 20:44
 * @description：
 * @modified By：
 */
@Aspect
@Component
public class AutoLogAspect {
    @Resource
    private IOperateLogService operateLogService;

    private ThreadPoolTaskExecutor executorService;

    @PostConstruct
    public void init() {
        executorService = new ThreadPoolTaskExecutor();
        executorService.setCorePoolSize(16);
        executorService.setMaxPoolSize(10000);
        executorService.setQueueCapacity(10000);
        executorService.setKeepAliveSeconds(20);
        executorService.setThreadNamePrefix("autoLog-thread-");
        executorService.setRejectedExecutionHandler(new java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy()); // 拒绝策略
        executorService.initialize(); // 初始化
    }

    @Pointcut("@annotation(com.shida.autolog.AutoLog)")
    public void logPointCut() {
    }

    @Around("logPointCut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        long beginTime = System.currentTimeMillis();
        AccountInfo currentUser;
        try {
            currentUser = (AccountInfo) StpUtil.getSession().get(RedisKeyConstant.CURRENT_USER_SESSION);
        } catch (Exception e) {
            currentUser = null;
        }
        //执行方法
        Object result = point.proceed();
        //执行时长(毫秒)
        long time = System.currentTimeMillis() - beginTime;
        if (Objects.isNull(currentUser)) {
            try {
                currentUser = (AccountInfo) StpUtil.getSession().get(RedisKeyConstant.CURRENT_USER_SESSION);
            } catch (Exception ignored) {
            }
        }
        if (Objects.nonNull(operateLogService)) {
            HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
            AccountInfo finalCurrentUser = currentUser;
            String requestUrl = request.getRequestURI();
            String requestMethod = request.getMethod();
            String requestParams = getRequestParams(request, point);
            String requestIpAddress = IpAddressUtil.getIpAddr(request);
            JSONObject requestMap = new JSONObject();
            requestMap.set("requestUrl", requestUrl)
                .set("requestMethod", requestMethod)
                .set("requestParams", requestParams)
                .set("requestIpAddress", requestIpAddress);
            executorService.execute(() -> saveSysLog(requestMap, point, time, finalCurrentUser, result));
        }
        return result;
    }


    private void saveSysLog(JSONObject request, ProceedingJoinPoint joinPoint,
                            long time, AccountInfo currentUser, Object result) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        SystemLog sysLog = new SystemLog();
        AutoLog autoLog = method.getAnnotation(AutoLog.class);
        Operation apiOperation = method.getAnnotation(Operation.class);
        //注解上的描述,操作日志内容
        sysLog.setLogContent(autoLog.value());
        String description = autoLog.description();
        //注解上的详情
        sysLog.setLogType(autoLog.logType().getValue());
        sysLog.setOperateType(autoLog.operateType().getValue());
        if (apiOperation != null && StrUtil.isBlank(sysLog.getLogContent())) {
            sysLog.setLogContent(apiOperation.summary());
        }
        sysLog.setDescription(description);
        //请求的方法名
        String className = joinPoint.getTarget().getClass().getName();
        String methodName = signature.getName();
        sysLog.setMethod(className + "." + methodName + "()");
        sysLog.setRequestType(request.getStr("requestMethod"));
        sysLog.setRequestUrl(request.getStr("requestUrl"));
        //设置操作类型
        if (sysLog.getLogType().equals(LogType.LOGIN)) {
            sysLog.setOperateType(LogType.LOGIN.getValue());
        }
        //设置操作类型
        if (sysLog.getLogType().equals(LogType.LOGOUT)) {
            sysLog.setOperateType(LogType.LOGOUT.getValue());
        }
        //设置操作类型
        if (Objects.isNull(sysLog.getOperateType())) {
            if (sysLog.getLogType().equals(LogType.OPERATE)) {
                sysLog.setOperateType(getOperateType(methodName).getValue());
            }
        }

        // 请求的参数
        sysLog.setRequestParam(request.getStr("requestParams"));
        // 响应结果
        sysLog.setResponseResult(Objects.nonNull(result) ? JSONUtil.toJsonStr(result) : null);
        //设置IP地址
        sysLog.setIp(request.getStr("requestIpAddress"));
        //获取登录用户信息
        if (currentUser != null) {
            sysLog.setUserId(currentUser.getId());
            sysLog.setUsername(currentUser.getUsername());
            sysLog.setRoleName(currentUser.getRoles().stream().map(RoleInfo::getRoleName).collect(Collectors.joining(",")));
            String roleIds = currentUser.getRoles().stream().map(item -> item.getId().toString()).collect(Collectors.joining(","));
            sysLog.setRoleId(roleIds);
            sysLog.setDeptId(currentUser.getRegionId());
            sysLog.setDeptCode(currentUser.getRegionCode());
            sysLog.setDeptName(currentUser.getRegionName());
            sysLog.setDeptId(currentUser.getRegionId());
            sysLog.setOrgId(currentUser.getRegionId());
            sysLog.setOrgName(currentUser.getRegionName());
        }
        //耗时
        sysLog.setCostTime(time);
        sysLog.setCreateTime(LocalDateTime.now());
        //保存系统日志
        operateLogService.addLog(sysLog);
    }

    /**
     * 获取操作类型
     */
    private IEnum<Integer> getOperateType(String methodName) {
        if (methodName.contains("login")) {
            return LogType.LOGIN;
        }
        if (methodName.contains("logout")) {
            return LogType.LOGOUT;
        }
        if (methodName.startsWith("list") || methodName.startsWith("get")) {
            return OperateType.OPERATE_SEARCH;
        }
        if (methodName.startsWith("add")) {
            return OperateType.OPERATE_ADD;
        }
        if (methodName.startsWith("edit")) {
            return OperateType.OPERATE_EDIT;
        }
        if (methodName.startsWith("delete")) {
            return OperateType.OPERATE_DELETE;
        }
        if (methodName.startsWith("import")) {
            return OperateType.OPERATE_IMPORT;
        }
        if (methodName.startsWith("export")) {
            return OperateType.OPERATE_EXPORT;
        }
        return OperateType.UNKNOWN;
    }

    /**
     * @param request:   request
     * @param joinPoint: joinPoint
     * @Description: 获取请求参数
     * @author: scott
     * @date: 2020/4/16 0:10
     * @Return: java.lang.String
     */
    private String getRequestParams(HttpServletRequest request, JoinPoint joinPoint) {
        String httpMethod = request.getMethod();
        String params = "";
        if ("POST".equals(httpMethod) || "PUT".equals(httpMethod) || "PATCH".equals(httpMethod)) {
            Object[] paramsArray = joinPoint.getArgs();
            // java.lang.IllegalStateException: It is illegal to call this method if the current request is not in asynchronous mode (i.e. isAsyncStarted() returns false)
            //  https://my.oschina.net/mengzhang6/blog/2395893
            Object[] arguments = new Object[paramsArray.length];
            for (int i = 0; i < paramsArray.length; i++) {
                if (paramsArray[i] instanceof ServletRequest ||
                    paramsArray[i] instanceof ServletResponse ||
                    paramsArray[i] instanceof HttpSession ||
                    paramsArray[i] instanceof MultipartFile) {
                    //ServletRequest不能序列化，从入参里排除，否则报异常：java.lang.IllegalStateException: It is illegal to call this method if the current request is not in asynchronous mode (i.e. isAsyncStarted() returns false)
                    //ServletResponse不能序列化 从入参里排除，否则报异常：java.lang.IllegalStateException: getOutputStream() has already been called for this response
                    continue;
                }
                arguments[i] = paramsArray[i];
            }
            if (arguments.length == 1) {
                params = JSONUtil.toJsonStr(arguments[0]);
            } else if (arguments.length == 0) {
                params = "";
            } else {
                params = JSONUtil.toJsonStr(arguments);
            }
        } else {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            // 请求的方法参数值
            Object[] args = joinPoint.getArgs();
            // 请求的方法参数名称
            LocalVariableTableParameterNameDiscoverer u = new LocalVariableTableParameterNameDiscoverer();
            String[] paramNames = u.getParameterNames(method);
            if (args != null && paramNames != null) {
                for (int i = 0; i < args.length; i++) {
                    params += "  " + paramNames[i] + ": " + args[i];
                }
            }
        }
        return params;
    }
}
