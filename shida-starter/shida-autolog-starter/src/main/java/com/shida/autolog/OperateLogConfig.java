package com.shida.autolog;

import cn.hutool.json.JSONUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OperateLogConfig {

    Logger log = LoggerFactory.getLogger(OperateLogConfig.class);
    @Bean
    @ConditionalOnMissingBean(IOperateLogService.class)
    public IOperateLogService operateLogService() {
        return sysLog -> log.debug("sysLog:{}", JSONUtil.toJsonStr(sysLog));
    }
}
