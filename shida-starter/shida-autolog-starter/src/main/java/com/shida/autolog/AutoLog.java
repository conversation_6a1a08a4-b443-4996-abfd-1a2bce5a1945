package com.shida.autolog;


import java.lang.annotation.*;

/**
 * {@link #description}
 *
 * <AUTHOR>
 * @date ：Created in 2021/11/11 20:32
 * @description：
 * @modified By：
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Documented
public @interface AutoLog {
    /**
     * 日志内容
     *
     * @return
     */
    String value() default "";

    /**
     * 日志类型
     *
     * @return 0:操作日志;1:登录日志;2:定时任务;
     */
    LogType logType() default LogType.OPERATE;

    /**
     * 操作日志类型
     *
     * @return （1查询，2添加，3修改，4删除）
     */
    OperateType operateType() default OperateType.OPERATE_SEARCH;

    /**
     * 操作详情，支持SpEL
     * 例如：#form.username+'登录了'
     */
    String description() default "";
}
