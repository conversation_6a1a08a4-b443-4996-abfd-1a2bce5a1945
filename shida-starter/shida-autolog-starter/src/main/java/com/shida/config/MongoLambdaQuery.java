package com.shida.config;

import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.io.Serializable;
import java.util.List;

/**
 * 为 mongodb 提供lambda查询
 *
 * @Author: 老木头
 * @Date: 2022/4/13 8:41
 */
public class MongoLambdaQuery<T extends Serializable> extends Query {
    public MongoLambdaQuery() {
//        super.addCriteria(Criteria.where("id").ne(null));
    }

    /**
     * 判断值相等
     *
     * @param func
     * @param val
     * @return
     */
    public MongoLambdaQuery<T> is(SFunction<T> func, Object val) {
        return is(true, func, val);
    }

    /**
     * 判断值相等
     *
     * @param condition
     * @param func
     * @param val
     * @return
     */
    public MongoLambdaQuery<T> is(boolean condition, SFunction<T> func, Object val) {
        if (condition) {
            super.addCriteria(Criteria.where(LambdaTypeInfo.convertToFieldName(func)).is(val));
        }
        return this;
    }

    public MongoLambdaQuery<T> lt(SFunction<T> func, Object val) {
        return lt(true, func, val);
    }

    public MongoLambdaQuery<T> lt(boolean condition, SFunction<T> func, Object val) {
        if (condition) {
            super.addCriteria(Criteria.where(LambdaTypeInfo.convertToFieldName(func)).lt(val));
        }
        return this;
    }
    public MongoLambdaQuery<T> lte(SFunction<T> func, Object val) {
        return lte(true, func, val);
    }

    public MongoLambdaQuery<T> lte(boolean condition, SFunction<T> func, Object val) {
        if (condition) {
            super.addCriteria(Criteria.where(LambdaTypeInfo.convertToFieldName(func)).lte(val));
        }
        return this;
    }

    public MongoLambdaQuery<T> gt(SFunction<T> func, Object val) {
        return gt(true, func, val);
    }

    public MongoLambdaQuery<T> gt(boolean condition, SFunction<T> func, Object val) {
        if (condition) {
            super.addCriteria(Criteria.where(LambdaTypeInfo.convertToFieldName(func)).gt(val));
        }
        return this;
    }

    public MongoLambdaQuery<T> gte(SFunction<T> func, Object val) {
        return gte(true, func, val);
    }

    public MongoLambdaQuery<T> gte(boolean condition, SFunction<T> func, Object val) {
        if (condition) {
            super.addCriteria(Criteria.where(LambdaTypeInfo.convertToFieldName(func)).gte(val));
        }
        return this;
    }
    /**
     * 判断值不相等
     *
     * @param func
     * @param val
     * @return
     */
    public MongoLambdaQuery<T> ne(SFunction<T> func, Object val) {
        return ne(true, func, val);
    }

    /**
     * 判断值不相等
     *
     * @param condition
     * @param func
     * @param val
     * @return
     */
    public MongoLambdaQuery<T> ne(boolean condition, SFunction<T> func, Object val) {
        if (condition) {
            super.addCriteria(Criteria.where(LambdaTypeInfo.convertToFieldName(func)).ne(val));
        }
        return this;
    }

    /**
     * 模糊查询
     *
     * @param func
     * @param val
     * @return
     */
    public MongoLambdaQuery<T> like(SFunction<T> func, Object val) {
        return like(true, func, val);
    }

    /**
     * 模糊查询
     *
     * @param condition
     * @param func
     * @param val
     * @return
     */
    public MongoLambdaQuery<T> like(boolean condition, SFunction<T> func, Object val) {
        if (condition) {
            super.addCriteria(Criteria.where(LambdaTypeInfo.convertToFieldName(func)).regex("^.*" + val + ".*$"));
        }
        return this;
    }

    /**
     * 模糊查询
     *
     * @param func
     * @param val
     * @return
     */
    public MongoLambdaQuery<T> leftLike(SFunction<T> func, Object val) {
        return leftLike(true, func, val);
    }

    /**
     * 模糊查询
     *
     * @param condition
     * @param func
     * @param val
     * @return
     */
    public MongoLambdaQuery<T> leftLike(boolean condition, SFunction<T> func, Object val) {
        if (condition) {
            super.addCriteria(Criteria.where(LambdaTypeInfo.convertToFieldName(func)).regex(val + ".*$"));
        }
        return this;
    }

    /**
     * 模糊查询
     *
     * @param func
     * @param val
     * @return
     */
    public MongoLambdaQuery<T> rightLike(SFunction<T> func, Object val) {
        return rightLike(true, func, val);
    }

    /**
     * 模糊查询
     *
     * @param condition
     * @param func
     * @param val
     * @return
     */
    public MongoLambdaQuery<T> rightLike(boolean condition, SFunction<T> func, Object val) {
        if (condition) {
            super.addCriteria(Criteria.where(LambdaTypeInfo.convertToFieldName(func)).regex("^.*" + val));
        }
        return this;
    }

    /**
     * in
     *
     * @param func
     * @param val
     * @return
     */
    public MongoLambdaQuery<T> in(SFunction<T> func, List<Object> val) {
        return in(true, func, val);
    }

    /**
     * in
     *
     * @param condition
     * @param func
     * @param val
     * @return
     */
    public MongoLambdaQuery<T> in(boolean condition, SFunction<T> func, List<Object> val) {
        if (condition) {
            super.addCriteria(Criteria.where(LambdaTypeInfo.convertToFieldName(func)).in(val));
        }
        return this;
    }

    /**
     * notIn
     *
     * @param func
     * @param val
     * @return
     */
    public MongoLambdaQuery<T> notIn(SFunction<T> func, List<Object> val) {
        return notIn(true, func, val);
    }

    /**
     * in
     *
     * @param condition
     * @param func
     * @param val
     * @return
     */
    public MongoLambdaQuery<T> notIn(boolean condition, SFunction<T> func, List<Object> val) {
        if (condition) {
            super.addCriteria(Criteria.where(LambdaTypeInfo.convertToFieldName(func)).nin(val));
        }
        return this;
    }

    /**
     * between
     *
     * @param func
     * @param start
     * @param end
     * @return
     */
    public MongoLambdaQuery<T> between(SFunction<T> func, Object start, Object end) {
        return between(true, func, start, end);
    }

    /**
     * between
     *
     * @param condition
     * @param func
     * @param start
     * @param end
     * @return
     */
    public MongoLambdaQuery<T> between(boolean condition, SFunction<T> func, Object start, Object end) {
        if (condition) {
            super.addCriteria(Criteria.where(LambdaTypeInfo.convertToFieldName(func)).gt(start).lte(end));
        }
        return this;
    }

    /**
     * order by
     *
     * @param func
     * @return
     */
    public MongoLambdaQuery<T> orderBy(SFunction<T> func) {
        return orderBy(true, func);
    }

    /**
     * order by
     *
     * @param condition
     * @param func
     * @return
     */
    public MongoLambdaQuery<T> orderBy(boolean condition, SFunction<T> func) {
        if (condition) {
            super.with(Sort.by(Sort.Direction.ASC, LambdaTypeInfo.convertToFieldName(func)));
        }
        return this;
    }

    /**
     * order by
     *
     * @param func
     * @param sortType
     * @return
     */
    public MongoLambdaQuery<T> orderBy(SFunction<T> func, Sort.Direction sortType) {
        return orderBy(true, func, sortType);
    }

    /**
     * order by
     *
     * @param condition
     * @param func
     * @return
     */
    public MongoLambdaQuery<T> orderBy(boolean condition, SFunction<T> func, Sort.Direction sortType) {
        if (condition) {
            super.with(Sort.by(sortType, LambdaTypeInfo.convertToFieldName(func)));
        }
        return this;
    }

    /**
     * order by
     *
     * @param func
     * @return
     */
    public MongoLambdaQuery<T> orderByDesc(SFunction<T> func) {
        return orderByDesc(true, func);
    }

    /**
     * order by
     *
     * @param condition
     * @param func
     * @return
     */
    public MongoLambdaQuery<T> orderByDesc(boolean condition, SFunction<T> func) {
        if (condition) {
            super.with(Sort.by(Sort.Direction.DESC, LambdaTypeInfo.convertToFieldName(func)));
        }
        return this;
    }
}
