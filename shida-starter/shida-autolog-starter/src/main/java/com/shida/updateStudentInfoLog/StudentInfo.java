package com.shida.updateStudentInfoLog;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Description:
 *
 * <AUTHOR>
 * @Create 2025/1/13 11:39
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(name = "StudentInfo", description = "学生信息编辑记录表")
public class StudentInfo<T> implements Serializable {

    @Schema(description = "主键ID")
    @JsonSerialize(contentUsing = ToStringSerializer.class)
    private Long id;

    @Schema(description = "操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "操作类型")
    private Integer type;

    @Schema(description = "身份证号")
    private String idCard;

    @Schema(description = "本次学生信息")
    private T studentInfo;

}
