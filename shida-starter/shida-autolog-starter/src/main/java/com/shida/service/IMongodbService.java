package com.shida.service;


import com.shida.config.MongoLambdaQuery;
import com.shida.pojo.vo.PageInfo;
import org.bson.Document;
import org.springframework.data.mongodb.core.query.Query;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/11/11 22:00
 * @description：
 * @modified By：
 */
public interface IMongodbService<Key, T extends Serializable> {
    T save(T entity);

    Boolean remove(T entity);

    Boolean remove(Query query);

    Boolean remove(MongoLambdaQuery<T> lambdaQuery);

    Boolean removeBatch(List<T> entities);

    Boolean removeById(Key id);

    T getById(Key id);

    T getOne(Query query);

    T getOne(MongoLambdaQuery<T> lambdaQuery);

    List<T> list();

    List<T> list(Query query);

    List<T> list(MongoLambdaQuery<T> lambdaQuery);

    PageInfo<T> page(Long pageNumber, Long pageSize, Query query);

    PageInfo<T> page(Long pageNumber, Long pageSize, MongoLambdaQuery<T> query);

    PageInfo<T> page(Long pageNumber, Long pageSize, Document query);

    Long count();

    Long count(Query query);

    Long count(MongoLambdaQuery<T> query);
}
