package com.shida.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.mongodb.client.*;
import com.shida.config.MongoLambdaQuery;
import com.shida.pojo.vo.PageInfo;
import com.shida.service.IMongodbService;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import javax.annotation.Resource;
import java.io.Serializable;
import java.lang.reflect.ParameterizedType;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：Created in 2021/11/11 22:05
 * @description：
 * @modified By：
 */
public abstract class MongodbServiceImpl<Key, T extends Serializable> implements IMongodbService<Key, T> {
    private Class<T> entityClass;

    public MongodbServiceImpl() {
        this.entityClass = (Class<T>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[1];
    }

    @Resource
    private MongoTemplate mongoTemplate;

    @Override
    public T save(T entity) {
        return mongoTemplate.save(entity);
    }

    @Override
    public Boolean remove(T entity) {
        return mongoTemplate.remove(entity).getDeletedCount() > 0;
    }

    @Override
    public Boolean remove(Query query) {
        return mongoTemplate.remove(query, entityClass).getDeletedCount() > 0;
    }

    @Override
    public Boolean remove(MongoLambdaQuery<T> lambdaQuery) {
        return mongoTemplate.remove(lambdaQuery).getDeletedCount() > 0;
    }

    @Override
    public Boolean removeBatch(List<T> entities) {
        return null;
    }

    @Override
    public Boolean removeById(Key id) {
        Query query = Query.query(Criteria.where("id").is(id));
        query.allowDiskUse(true);
        return mongoTemplate.remove(query, entityClass).getDeletedCount() > 0;
    }

    @Override
    public T getById(Key id) {
        return mongoTemplate.findById(id, entityClass);
    }

    @Override
    public T getOne(Query query) {
        if (ObjectUtil.isEmpty(query)) {
            query = Query.query(Criteria.where("id").ne(-1L));
        }
        query.allowDiskUse(true);
        return mongoTemplate.findOne(query, entityClass);
    }

    @Override
    public T getOne(MongoLambdaQuery<T> lambdaQuery) {
        lambdaQuery.allowDiskUse(true);
        return mongoTemplate.findOne(lambdaQuery, entityClass);
    }

    @Override
    public List<T> list() {
        return mongoTemplate.findAll(entityClass);
    }

    @Override
    public List<T> list(Query query) {
        query.allowDiskUse(true);
        return mongoTemplate.find(query, entityClass);
    }

    @Override
    public List<T> list(MongoLambdaQuery<T> lambdaQuery) {
        lambdaQuery.allowDiskUse(true);
        return mongoTemplate.find(lambdaQuery, entityClass);
    }

    @Override
    public Long count() {
        Query query = Query.query(Criteria.where("id").ne(-1L));
        query.allowDiskUse(true);
        return mongoTemplate.count(query, entityClass);
    }

    @Override
    public Long count(Query query) {
        if (ObjectUtil.isEmpty(query)) {
            query = Query.query(Criteria.where("id").ne(-1L));
        }
        query.allowDiskUse(true);
        return mongoTemplate.count(query, entityClass);
    }

    @Override
    public Long count(MongoLambdaQuery<T> query) {
        query.allowDiskUse(true);
        return mongoTemplate.count(query, entityClass);
    }

    @Override
    public PageInfo<T> page(Long pageNumber, Long pageSize, Query query) {
        long total;

        if (query.getQueryObject().isEmpty()) {
            String tableName = StrUtil.toCamelCase(StrUtil.toUnderlineCase(entityClass.getSimpleName()));
            MongoCollection<Document> collection = mongoTemplate.getCollection(tableName);
            total = collection.estimatedDocumentCount();
        } else {
            total = mongoTemplate.count(query, entityClass);
        }

        if (total > 0) {
            Query searchQuery = Query.of(query);
            searchQuery.allowDiskUse(true);
            searchQuery.limit(pageSize.intValue())
                    .skip((pageNumber > 1 ? pageNumber - 1 : 0) * pageSize);
            List<T> records = mongoTemplate.find(searchQuery, entityClass);
            return new PageInfo<>(pageNumber, pageSize, total, records);
        } else {
            return new PageInfo<>(pageNumber, pageSize, total, new ArrayList<>());
        }
    }

    @Override
    public PageInfo<T> page(Long pageNumber, Long pageSize, MongoLambdaQuery<T> query) {
        query.allowDiskUse(true);
        long total;
        if (query.getQueryObject().isEmpty()) {
            String tableName = StrUtil.toCamelCase(StrUtil.toUnderlineCase(entityClass.getSimpleName()));
            MongoCollection<Document> collection = mongoTemplate.getCollection(tableName);
            total = collection.estimatedDocumentCount();
        } else {
            total = mongoTemplate.count(query, entityClass);
        }

        if (total > 0) {
            Query searchQuery = Query.of(query);
            searchQuery.allowDiskUse(true);
            searchQuery.limit(pageSize.intValue())
                    .skip((pageNumber > 1 ? pageNumber - 1 : 0) * pageSize);
            List<T> records = mongoTemplate.find(searchQuery, entityClass);
            return new PageInfo<>(pageNumber, pageSize, total, records);
        } else {
            return new PageInfo<>(pageNumber, pageSize, total, new ArrayList<>());
        }
    }


    @Override
    public PageInfo<T> page(Long pageNumber, Long pageSize, Document query) {
        MongoDatabase db = mongoTemplate.getDb();
        String tableName = StrUtil.toCamelCase(StrUtil.toUnderlineCase(entityClass.getSimpleName()));
        MongoCollection<Document> collection = db.getCollection(tableName);
        System.out.println("mongodb--------------> tableName:" + tableName);
        System.out.println("mongodb--------------> query:" + query);
        long total;
        if (query.isEmpty()) {
            total = collection.estimatedDocumentCount();
        } else {
            total = collection.countDocuments(query);
        }

        System.out.println("mongodb--------------> total:" + total);
        if (total > 0) {
            int skip = Long.valueOf((pageNumber > 1 ? pageNumber - 1 : 0) * pageSize).intValue();
            System.out.println("mongodb--------------> record start");
            FindIterable<Document> documents = collection.find(query)
                    .skip(skip)
                    .limit(pageSize.intValue())
                    .sort(new Document("_id", -1));
            List<T> records = new ArrayList<>();
            System.out.println("mongodb--------------> record end");

            for (Document document : documents){
                Map<String, String> copyMap = new HashMap<>();
                copyMap.put("_id", "id");
                CopyOptions options = CopyOptions.create();
                options.setFieldMapping(copyMap);
                T record = BeanUtil.toBean(document, entityClass, options);
                records.add(record);
                System.out.println("mongodb--------------> record：" + JSONUtil.toJsonStr(record));
            }

            return new PageInfo<>(pageNumber, pageSize, total, records);
        } else {
            return new PageInfo<>(pageNumber, pageSize, total, new ArrayList<>());
        }
    }
}
