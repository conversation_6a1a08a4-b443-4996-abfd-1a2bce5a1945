package com.shida.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.shida.autolog.IOperateLogService;
import com.shida.autolog.SystemLog;
import com.shida.utils.IdUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class SystemLogServiceImpl extends MongodbServiceImpl<Long, SystemLog> implements IOperateLogService {
    @Override
    public void addLog(SystemLog sysLog) {
        if (ObjectUtil.isNull(sysLog.getId())) {
            sysLog.setId(IdUtil.getSnowflakeNextId());
        }
        try {
            SystemLog save = super.save(sysLog);
            log.debug("日志记录成功:"+JSONUtil.toJsonStr(sysLog));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
