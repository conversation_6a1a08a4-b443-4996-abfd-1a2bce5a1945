<script lang="ts" setup>
import {computed, ref} from 'vue'

interface SchoolPeriodType {
  [key: string]: {
    period: number;
    name: string;
  }
}

const SchoolPeriod = ref({
  XIAO_XUE: {period: 1, name: '小学'},
  CHU_ZHONG: {period: 2, name: '初中'},
  GAO_ZHONG: {period: 3, name: '高中'},
  JIU_NIAN: {period: 4, name: '九年一贯制'},
  SHIER_NIAN: {period: 5, name: '十二年一贯制'},
  LIAN_DU: {period: 6, name: '初高连读'}
})

const props = defineProps({
  modelValue: {
    type: [Number, String, null],
    default: null
  },
  disabled: {
    type: Boolean,
    default: false
  },
  showType: {
    type: String,
    default: 'label'
  },
  periodList: {
    type: Array,
    default: () => []
  },
  from: {
    type: String,
    default: 'school'
  }
})
const emit = defineEmits(['update:modelValue', 'change'])

const value = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value) // 这里的 value 是表单输入的值
  },
})

const handleChange = () => {
  emit('change')
}

// 学段列表
const schoolPeriodList = computed(() => {
  const schoolPeriod = SchoolPeriod.value as SchoolPeriodType;
  const periods = Object.values(schoolPeriod)
  if (props.periodList.length > 0) {
    const _periods = periods.filter(item => props.periodList.includes(item.period))
    return _periods.map(item => {
      return {
        value: item.period,
        label: item.name
      }
    });
  } else {
    if(props.from === 'school') {
      return periods.map(item => {
        return {
          value: item.period,
          label: item.name
        }
      });
    }
    else {
      return periods.filter(item => (item.period == 1 || item.period == 2 || item.period == 3 )).map(item => {
        return {
          value: item.period,
          label: item.name
        }
      });
    }
  }
})
// 学段名称
const periodName = computed(() => {
  const schoolPeriod = schoolPeriodList.value.find(item => item.value === value.value)
  return schoolPeriod?.label || '未知';
})
const textStyles = ref(["info", "primary", "success", "warning", "danger"])
</script>

<template>
  <div style="width: 100%">
    <el-select v-if="showType!=='label'" v-model="value" :disabled="disabled" clearable placeholder="选择学段" @change="handleChange"
               style="width: 100%">
      <el-option v-for="(item, index) in schoolPeriodList" :key="index" :label="item.label"
                 :value="item.value"></el-option>
    </el-select>
    <el-text v-else-if="value" :type="textStyles[(value - 1)%5]">{{ periodName }}</el-text>
  </div>
</template>

<style lang="scss" scoped>

</style>
