<script lang="ts" setup>
import {computed, onMounted, ref} from 'vue'
import {useTermStore} from "@/store/modules/term";

const termList = ref<any>(useTermStore().termList)

const props = defineProps({
  modelValue: {
    type: [Number, String, null],
    default: null
  },
  disabled: {
    type: Boolean,
    default: false
  },
  showType: {
    type: String,
    default: 'label'
  },
  placeholder: {
    type: String,
    default: '学期'
  }
})
const emit = defineEmits(['update:modelValue'])
const ph = ref('')

const value = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value) // 这里的 value 是表单输入的值
  },
})

onMounted(async () => {
  if (termList.value.length == 0) {
    await useTermStore().setTermList()
    termList.value = useTermStore().termList
  }
  if (props.placeholder) {
    ph.value = props.placeholder
  }
})

const termName = computed(() => {
  const term = termList.value.find((item: any) => item.key === value.value)
  return term?.value || '未知';
})

const textStyles = ref(["info", "primary", "success", "warning", "danger"])
</script>

<template>
  <div style="width: 100%">
    <el-select v-if="showType!=='label'" v-model="value" :disabled="disabled" clearable :placeholder="ph"
               style="width: 100%">
      <el-option v-for="(item, index) in termList" :key="index" :label="item.value" :value="item.key"></el-option>
    </el-select>
    <el-text v-else-if="value" :type="textStyles[(value - 1)%5]">{{ termName }}</el-text>
  </div>
</template>

<style lang="scss" scoped>

</style>
