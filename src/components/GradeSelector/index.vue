<script lang="ts" setup>
import {computed, onMounted, ref} from 'vue'
import {useDeptStore} from "@/store";

const gradeList = ref<any>(useDeptStore().gradeList)

const props = defineProps({
  modelValue: {
    type: [Number, String, null],
    default: null
  },
  disabled: {
    type: Boolean,
    default: false
  },
  showType: {
    type: String,
    default: 'label'
  },
})
const emit = defineEmits(['update:modelValue', 'change'])

const value = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value) // 这里的 value 是表单输入的值
  },
})

onMounted(async () => {
  if (gradeList.value.length == 0) {
    await useDeptStore().setGradeList()
    gradeList.value = useDeptStore().gradeList
  }
})

const handleChange = () => {
  emit('change')
}

const gradeName = computed(() => {
  const garde = gradeList.value.find((item: any) => item.key === value.value)
  return garde?.value || '未知';
})

const textStyles = ref(["info", "primary", "success", "warning", "danger"])
</script>

<template>
  <div style="width: 100%">
    <el-select v-if="showType!=='label'" v-model="value" :disabled="disabled" clearable placeholder="年级" @change="handleChange"
               style="width: 100%">
      <el-option v-for="(item, index) in gradeList" :key="index" :label="item.value" :value="item.key"></el-option>
    </el-select>
    <el-text v-else-if="value" :type="textStyles[(value - 1)%5]">{{ gradeName }}</el-text>
  </div>
</template>

<style lang="scss" scoped>

</style>
