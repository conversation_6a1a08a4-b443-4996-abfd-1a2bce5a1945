<script lang="ts" setup>
import {computed, ref} from 'vue'

interface ProjectType {
  [key: string]: {
    code: string;
    name: string;
  }
}

const Project = ref({
  WU_SHI: {code: '1', name: '50米跑'},
  LI_DING: {code: '2', name: '立定跳远'},
  YIN_TI: {code: '3', name: '引体向上'},
})

const props = defineProps({
  modelValue: {
    type: [String, null],
    default: null
  },
  disabled: {
    type: Boolean,
    default: false
  },
  showType: {
    type: String,
    default: 'label'
  },
})
const emit = defineEmits(['update:modelValue'])

const value = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value) // 这里的 value 是表单输入的值
  },
})

// 检测项目列表
const projectList = computed(() => {
  const target = Project.value as ProjectType;
  return Object.values(target).map(item => {
    return {
      value: item.code,
      label: item.name
    }
  });
})
// 检测项目名称
const projectName = computed(() => {
  const target = projectList.value.find(item => item.value == value.value)
  return target?.label || '未知';
})
</script>

<template>
  <div style="width: 100%">
    <el-select v-if="showType!=='label'" v-model="value" :disabled="disabled" clearable style="width: 100%">
      <el-option v-for="(item, index) in projectList" :key="index" :label="item.label"
                 :value="item.value"></el-option>
    </el-select>
    <el-text v-else-if="value" type="info">{{ projectName }}</el-text>
  </div>
</template>

<style lang="scss" scoped>

</style>
