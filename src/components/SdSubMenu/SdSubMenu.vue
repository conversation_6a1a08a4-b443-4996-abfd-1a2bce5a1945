<script setup lang="ts">
import router from "@/router";

const props = defineProps<{
  menu: any
}>()

const navigateTo = (path: string) => {
  router.push(path);
}

</script>

<template>
  <el-sub-menu v-if="menu.children && menu.children.length > 0 && menu.meta.type==='M'"
               :index="menu.path">
    <template #title>
      <el-icon>
        <ChromeFilled/>
      </el-icon>
      <span>{{ props.menu.meta.title}}</span>
    </template>
    <template v-for="(child, childIndex) in menu.children">
      <template v-if="child.children && child.children.length > 0 && child.meta.type==='M'">
        <SdSubMenu :menu="child"></SdSubMenu>
      </template>
      <template v-else>
        <el-menu-item :key="childIndex" :index="`${child.path}`" @click="navigateTo(child.path)">
          <el-icon>
            <ChromeFilled/>
          </el-icon>
          <span>{{ child?.meta?.title || '' }} </span>
        </el-menu-item>
      </template>
    </template>
  </el-sub-menu>
  <el-menu-item v-else :index="menu.redirect || menu.path" @click="navigateTo(menu.path)">
    <el-icon>
      <ChromeFilled/>
    </el-icon>
    <span>{{ menu.meta?.title||menu.name }}</span>
  </el-menu-item>
</template>

<style scoped lang="scss">


.el-menu-item.is-active {
  background-color: rgba(255, 255, 255, 0.3) !important;
  // 为字体添加阴影
  text-shadow: 0px 0px 1px rgba(255, 255, 255, 0.8);
  font-size: 16px;
  // 图标一直旋转动画
  :deep(.el-icon) {
    animation: spin 1s linear infinite;
  }
}

// 定义旋转动画
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-360deg);
  }
}
</style>
