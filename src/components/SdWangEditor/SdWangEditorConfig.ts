import Compressor from 'compressorjs';
import request from "@/utils/request";
import Notification from '@/commons/Notification'

const compressImage = async (file: any): Promise<File> => {
  return new Promise((resolve) => {
    // 创建新的文件对象以确保修改生效
    const imageType = file.type === 'image/png' ? 'image/jpeg' : file.type;

    new Compressor(file, {
      quality: 0.8, // 设置压缩质量
      maxWidth: 1920, // 设置最大宽度
      maxHeight: 1080, // 设置最大高度
      mimeType: imageType, // 强制转换为JPEG格式（如果原图是PNG）
      convertSize: Infinity, // 禁用自动转换为JPEG
      success(result) {
        console.log("Compressor--->原始文件大小:", file.size / 1024 + "kb")
        console.log("Compressor--->压缩后文件大小:", result.size / 1024 + "kb")
        // 创建新的文件对象以确保修改生效
        const newFile = new File([result], file.name, {
          type: imageType,
          lastModified: Date.now()
        });

        resolve(newFile);
      },
      error(err) {
        console.error('压缩失败:', err);
        resolve(file); // 压缩失败返回原文件
      }
    });
  });
};


export const toolbarConfig = {
  excludeKeys: ['codeBlock', 'group-video', 'insertLink'],
  insertKeys: {
    index: 22, // 自定义插入的位置
    keys: ['uploadAttachment'], // “上传附件”菜单
  }
}

export const editorConfig = {
  placeholder: '请输入内容...',
  // 服务地址前缀
  uploadUrl: "",
  serviceUrlPrefix: "",
  regionId: '',
  MENU_CONF: {
    uploadImage: {
      maxNumberOfFiles: 1,
      allowedFileTypes: ['.png', '.jpg'],
      customUpload: async (file: File, insertFn: Function) => {
        const uploadFun = async (file: File) => {
          const formData = new FormData();
          formData.append('file', file);
          formData.append('FileType', 'EDITOR_FILE');
          try {
            console.log("editorConfig", editorConfig)
            const response = await request.post(editorConfig.uploadUrl, formData, {
              headers: {
                'X-Tenant-Id': editorConfig.regionId
              }
            })
            return response.data
          } catch (e) {
            throw e
          }
        }
        debugger
        // 1. 自己实现上传
        if (file.size < 200 * 1024) {
          // 上传文件
          const data = await uploadFun(file) as any;
          let url
          if (editorConfig.serviceUrlPrefix) {
            url = import.meta.env.VITE_PROXY_PREFIX + editorConfig.serviceUrlPrefix + data?.url
          } else {
            url = import.meta.env.VITE_PROXY_PREFIX + data?.url
          }
          insertFn(url)
        } else {
          let _file = await compressImage(file);
          let compressTimes = 1000
          while (_file.size > 1024 * 200 && compressTimes > 0) {
            _file = await compressImage(_file);
            compressTimes--
          }
          const data = await uploadFun(file) as any;
          let url
          if (editorConfig.serviceUrlPrefix) {
            url = import.meta.env.VITE_PROXY_PREFIX + editorConfig.serviceUrlPrefix + data?.url
          } else {
            url = import.meta.env.VITE_PROXY_PREFIX + data?.url
          }
          console.log("upload--->url", url)
          // 从 res 中找到 url alt href ，然后插入图片
          insertFn(url)
        }
      },
    },
    uploadAttachment: {
      allowedFileTypes: ['.pdf', '.doc', '.docx', '.xls', '.xlsx'],
      customUpload: async (file: File, insertFn: Function) => {
        const uploadFun = async (file: File) => {
          const formData = new FormData();
          formData.append('file', file);
          formData.append('FileType', 'EDITOR_FILE');
          try {
            const response = await request.post(editorConfig.uploadUrl, formData, {
              headers: {
                'X-Tenant-Id': editorConfig.regionId
              }
            })
            return response.data
          } catch (e) {
            throw e
          }
        }
        // 1. 自己实现上传
        if (file.size < 10 * 1024 * 1024) {
          // 上传文件
          const data = await uploadFun(file) as any;
          let url
          if (editorConfig.serviceUrlPrefix) {
            url = import.meta.env.VITE_PROXY_PREFIX + editorConfig.serviceUrlPrefix + data?.url
          }
          insertFn(data.name, url)
        } else {
          Notification.showErrorNotify("文件过大，请上传小于10M的文件")
        }
      }
    }
  }
}
