<template>
  <div class="editor">
    <Toolbar
      :default-config="toolbarConfig"
      :editor="editor"
      mode="default"
      style="border-bottom: 1px solid #ccc"
    />
    <Editor
      v-model="valueHtml"
      :default-config="editorConfig"
      :mode="mode"
      style="height: calc(100% - 80px)"
      @onChange="emit('update:modelValue', valueHtml)"
      @onCreated="handleCreated"
    />
  </div>
</template>

<script setup>
import '@wangeditor/editor/dist/css/style.css' // 引入 css

import {onMounted, onBeforeUnmount, ref, onUpdated} from 'vue';
import {Boot} from '@wangeditor/editor'
import {Editor, Toolbar} from '@wangeditor/editor-for-vue'
import {toolbarConfig, editorConfig} from "./SdWangEditorConfig.ts";
import attachmentModule from '@wangeditor/plugin-upload-attachment'

try {
// 注册。要在创建编辑器之前注册，且只能注册一次，不可重复注册。
  Boot.registerModule(attachmentModule)
} catch (e) {
}


const editor = ref(null);
const valueHtml = ref('');

const mode = 'default'; // 或 'simple' 取决于你的需求

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  readonly: {
    type: Boolean,
    default: false
  },
  serviceUrlPrefix: {
    type: String,
    default: ''
  },
  regionId: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue']);

// placeholder占位文字动态传入，要在创建编辑器（onCreated）之前操作
if (props.placeholder) {
  editorConfig.placeholder = props.placeholder
}

const handleCreated = (editorInstance) => {
  editor.value = Object.seal(editorInstance);
};

onMounted(() => {
  setTimeout(() => {
    editorConfig.regionId = props.regionId
    if (props.serviceUrlPrefix) {
      editorConfig.serviceUrlPrefix = props.serviceUrlPrefix
    } else {
      editorConfig.serviceUrlPrefix = `/region-service`
    }
    editorConfig.uploadUrl = `${editorConfig.serviceUrlPrefix}/file/manage/upload`
  }, 500)
})

onUpdated(() => {
  if (props.modelValue) {
    valueHtml.value = props.modelValue
  }
  if (props.regionId) {
    editorConfig.regionId = props.regionId
  }
  if (props.serviceUrlPrefix) {
    editorConfig.serviceUrlPrefix = props.serviceUrlPrefix
    editorConfig.uploadUrl = `${editorConfig.serviceUrlPrefix}/file/manage/upload`
  }
})

onBeforeUnmount(() => {
  editor.value?.destroy()
})

</script>

<style lang="scss" scoped>
.editor {
  border: 1px solid #ccc;
  min-height: 300px;
  height: 100%;
  overflow: hidden;
  width: 100%;
}
</style>
