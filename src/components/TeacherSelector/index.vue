<script lang="ts" setup>
import {computed, ref, watchEffect} from "vue";
import {apis} from "@/apis";
import {useTeacherStore} from "@/store";
import type { KeyValue } from "@/commons/KeyValue";

const teacherList = ref<Array<KeyValue>>([]);

const props = defineProps({
  modelValue: {
    type: [String, null],
    default: null
  },
  regionId: {
    type: String,
    default: ''
  },
  schoolId: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  },
  showType: {
    type: String,
    default: 'label'
  }
})
const teacherStore = useTeacherStore()

watchEffect(async () => {
  if (props.schoolId) {
    if (teacherStore.schoolTeacherList?.length) {
      const teachers = teacherStore.schoolTeacherList?.find(item => item.schoolId === props.schoolId)?.teachers
      teacherList.value = teachers as Array<KeyValue>
    } else {
      const response = await apis.Commons.getTeacherSelectList(props.schoolId, props.regionId)
      teacherList.value = response.data
      teacherStore.setSchoolTeachers(props.schoolId, response.data)
    }
  } else {
    teacherList.value = []
  }
})

const emit = defineEmits(['update:modelValue'])

const value = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value) // 这里的 value 是表单输入的值
  },
})

const teacherName = computed(() => {
  const item = teacherList.value.find(item => item.key === value.value)
  return item ? item.value + '(' + item.extra + ')' : '无教师'
})

</script>

<template>
  <div style="width: 100%">
    <el-select v-if="showType!=='label'" v-model="value" :disabled="disabled" clearable filterable
               placeholder="请选择教师">
      <el-option v-for="(item, index) in teacherList" :key="index" :label="item.value+'('+item.extra+')'"
                 :value="item.key"></el-option>
    </el-select>
    <el-text v-else-if="value">{{ teacherName }}</el-text>
  </div>
</template>

<style lang="scss" scoped>

</style>
