<script lang="ts" setup>
import {computed, ref} from 'vue'

interface SchoolTypeClass {
  [key: string]: {
    type: number;
    name: string;
  }
}

const SchoolType = ref({
  GONG_BAN: {type: 1, name: '公办'},
  MIN_BAN: {type: 2, name: '民办'},
  GONG_MIN_TONG_BAN: {type: 3, name: '公民同办'}
})

const props = defineProps({
  modelValue: {
    type: [Number, null],
    default: null
  },
  disabled: {
    type: Boolean,
    default: false
  },
  showType: {
    type: String,
    default: 'label'
  }
})
const emit = defineEmits(['update:modelValue'])

const value = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value) // 这里的 value 是表单输入的值
  },
})

// 列表
const schoolTypeList = computed(() => {
  const schoolType = SchoolType.value as SchoolTypeClass;
  return Object.values(schoolType).map(item => {
    return {
      value: item.type,
      label: item.name
    }
  });
})
// 名称
const typeName = computed(() => {
  const schoolType = schoolTypeList.value.find(item => item.value === value.value)
  return schoolType?.label || '未知';
})
const textStyles = ref(["info", "primary", "success", "warning", "danger"])
</script>

<template>
  <div style="width: 100%">
    <el-select v-if="showType!=='label'" v-model="value" :disabled="disabled" clearable placeholder="办学性质">
      <el-option v-for="(item, index) in schoolTypeList" :key="index" :label="item.label"
                 :value="item.value"></el-option>
    </el-select>
    <el-text v-else-if="value" :type="textStyles[(value - 1)%5]">{{ typeName }}</el-text>
  </div>
</template>

<style lang="scss" scoped>

</style>
