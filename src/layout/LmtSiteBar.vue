<template>
  <div class="lmt-side-bar bg-gradient-to-r from-indigo-800 to-indigo-700 z-1">
    <el-menu :collapse="menuCollapse" :default-active="activeIndex" active-text-color="#ffd04b"
             background-color="#6265ef" class="el-menu-vertical-demo" text-color="#fff">
      <template v-for="(route, index) in layoutRoutes" :key="index">
        <SdSubMenu :menu="route"></SdSubMenu>
      </template>
    </el-menu>
  </div>
</template>

<script lang="ts" setup>
import {computed, onMounted, onUpdated, ref, watchEffect} from 'vue';
import router from "@/router"

let activeIndex = ref('');

// 过滤出 Layout 下的路由
const layoutRoutes = computed(() => {
  let routes = router.getRoutes()

  routes = routes.filter(route => route.meta?.hidden === false && route.children && route.children.length)
    .filter(route => route.meta?.parent === null || route.meta?.parent === undefined)
    .sort((a: any, b: any) => a.meta?.orderNum - b.meta?.orderNum)

  const initChildren = (parent: any) => {
    if (parent.children) {
      // 明确指定 item 的类型
      parent.children = parent.children.filter((item: any) => item.meta?.hidden === false)
      if (parent.children && parent.children.length) {
        for (const child of parent.children) {
          if (child.children && child.children.length) {
            initChildren(child)
          }
        }
      }
    }
  }

  for (const route of routes) {
    if (route.children && route.children.length) {
      initChildren(route)
    }
  }
  return routes
})

let menuWidth = ref(220)
const menuCollapse = ref(false)

let index: number = 0;


onMounted(() => {
  activeIndex.value = router.currentRoute.value.path
  menuWidth.value = document.getElementsByClassName('lmt-side-bar')[0]?.clientWidth || 220
  menuCollapse.value = !(menuWidth.value > 100)
  // 监听窗口大小变化，并根据窗口宽度动态调整菜单宽度和折叠状态
  window.addEventListener('resize', () => {
    // 页面防抖
    if (0 != index) {
      clearTimeout(index)
    }

    index = setTimeout(() => {
      menuWidth.value = document.getElementsByClassName('lmt-side-bar')[0].clientWidth
      menuCollapse.value = !(menuWidth.value > 100)
      index = 0
    }, 100)
  })
  console.log("watchEffect", router.currentRoute.value.path)
})

watchEffect(() => {
  activeIndex.value= router.currentRoute.value.path
  console.log("watchEffect", router.currentRoute.value.path)
})

onUpdated(()=>{
  activeIndex.value = router.currentRoute.value.path
  console.log("onupdate", router.currentRoute.value.path)
})

</script>

<style lang="scss" scoped>
.lmt-side-bar {
  width: var(--lmt-sidebar-width);
  height: calc(100vh - var(--lmt-header-height));
  border-right: none;
  overflow: hidden;
  // 宽度变化时增加过渡动画
  transition: width 0.3s ease;
  resize: horizontal;
}

.el-menu {
  border: 0 !important;
  background-color: rgb(67 56 202 / var(--un-to-opacity, 1)) var(--un-gradient-to-position);
}

</style>
