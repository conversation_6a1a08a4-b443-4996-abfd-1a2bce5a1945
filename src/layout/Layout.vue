<template>
  <div class="lmt-layout">
    <LmtHeader/>
    <div class="flex">
      <div>
        <LmtSiteBar/>
      </div>
      <div class="lmt-main-container">
        <!-- 添加路由过渡动画 -->
        <router-view v-slot="{ Component }">
          <transition mode="out-in" name="fade-slide">
            <div class="main-container-inner" :key="router.currentRoute.value.fullPath">
              <component :is="Component"/>
            </div>
          </transition>
        </router-view>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import LmtHeader from "@/layout/LmtHeader.vue";
import LmtSiteBar from "@/layout/LmtSiteBar.vue";
import {useRouter} from "vue-router";

const router = useRouter();
</script>


<style lang="scss" scoped>
.lmt-layout {
  min-width: 800px;
}

// .lmt-main-container {
//   height: calc(100vh - var(--lmt-header-height) - var(--lmt-main-padding));
//   overflow: auto;
//   padding: var(--lmt-main-padding) var(--lmt-main-padding) 0 var(--lmt-main-padding);
// }

.lmt-main-container {
  // 添加以下两行保证动画容器定位准确
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  width: 100%;
  height: calc(100vh - var(--lmt-header-height) - var(--lmt-main-padding));
  padding: var(--lmt-main-padding) var(--lmt-main-padding) 0 var(--lmt-main-padding);


  :deep(.fade-slide-enter-active),
  :deep(.fade-slide-leave-active) {
    transition: all 0.3s ease-in-out;
  }

  :deep(.fade-slide-enter-from) {
    opacity: 0;
    transform: translateX(30px);
  }

  :deep(.fade-slide-leave-to) {
    opacity: 0;
    transform: translateX(-30px);
  }
}

.main-container-inner {
  height: 100%;
}
</style>
