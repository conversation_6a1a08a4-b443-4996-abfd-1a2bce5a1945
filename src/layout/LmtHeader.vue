<template>
  <div class="lmt-header w-full bg-gradient-to-r from-indigo-800 to-purple-800 shadow-lg">
    <div>
      <el-link class="m-l-4" underline="never" @click="gotoHome">{{ commons.settings.siteTitle }}</el-link>
    </div>
    <div class="m-r-4 flex items-center">
      <el-link class="m-r-4" underline="never" @click="gotoPersonal">
        <el-avatar src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"/>
        <div class="m-l-2 font-size-5">{{ currentUserInfo?.username }}</div>
      </el-link>
      <div class="m-l-2 font-size-5 color-amber-500 cursor-pointer" @click="logout">
        退出
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import router from "@/router";
import type {UserInfo} from '@/store/modules/user'
import {useUserStore} from '@/store/modules/user'
import {apis} from "@/apis";
import commons from "@/commons/Settings";

const userStore = useUserStore()
const currentUserInfo: UserInfo | null = userStore.currentUserInfo

const logout = async () => {
  try {
    await apis.Account.logout()
  } catch (error) {
    console.log(error)
  } finally {
    userStore.logout()
    await router.replace("/login")
  }
}
const gotoHome = () => {
  router.push("/")
}
const gotoPersonal = () => {
  router.push('/personal')
}
</script>
<style lang="scss" scoped>

.lmt-header {
  font-size: 2rem;
  color: #fff;
  line-height: var(--lmt-header-height);
  height: var(--lmt-header-height);
  display: flex;
  align-items: center;
  justify-content: space-between;

  .el-link {
    font-size: 2rem;
    color: #fff;
  }
}
</style>
