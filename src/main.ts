import {createApp} from 'vue'
// 添加: 引入 UnoCSS 默认样式
import 'uno.css'
import './styles/index.scss'
// 添加: nprogress 样式
import "nprogress/nprogress.css";
// 引入 Element Plus
import ElementPlus from 'element-plus'
// 添加: 引入 Element Plus 样式
import dayjs from "dayjs"
import 'element-plus/dist/index.css'
// 添加: 引入 Element Plus 图标
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
// element 使用中文
import zhCn from 'element-plus/es/locale/lang/zh-cn'
// 引入App
import App from './App.vue'
// 引入router
import router from './router'
// 引入store
import store from '@/store/index'
// 引入Apis
import apis from '@/commons/Settings'
// 引入自定义指令集
import {setupDirective} from "@/directive";
// 自定义SubMenu组件
import SdSubMenu from "@/components/SdSubMenu/SdSubMenu.vue";


const app = createApp(App)
// 添加: 引入 Element Plus 图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}
// 注册 SdSubMenu 组件
app.component('SdSubMenu', SdSubMenu);

// 全局注册 自定义指令(directive)
setupDirective(app);
app.config.globalProperties.$dayjs=dayjs
app
  .use(router)
  .use(store)
  .use(apis)
  .use(ElementPlus, {size: 'default', zIndex: 3000, locale: zhCn})
  .mount('#app')

// console.log('import.meta.env', import.meta.env);


