// 操作框样式
.lmt-operation-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: .5rem;
  line-height: 2rem;
  gap: 1rem;

  .lmt-operation {
    display: flex;
    flex-flow: nowrap;
  }

  .lmt-search {
    display: flex;
    justify-content: end;
    align-items: center;
    gap: 0.5rem;

    .el-select, .el-input {
      max-width: 220px;
      min-width: 120px;
    }

    .el-input-group {
      width: 200px;
      min-width: 200px;
    }
  }
}

.el-table {
  .cell {
    /** 非最后一个el-link margin-right=0.5rem */
    .el-link:not(:last-child) {
      margin-right: 0.5rem;
    }
  }
}

.lmt-html-viewer {
  position: relative;
  border: 1px solid #ebeef5;
  // 设置阴影，让内容看起来像一张纸
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  img {
    max-width: 100%;
  }
}

.primary-text {
  color: var(--el-color-primary);
}

.warning-text {
  color: var(--el-color-warning);
}

.success-text {
  color: var(--el-color-success);
}

.danger-text {
  color: var(--el-color-danger);
}

.info-text {
  color: var(--el-color-info);
}

.padding-20{
  padding: 20px;
}

// 学籍异动学生查询结果
.shift-qry-result {
  position: relative;
  margin-bottom: 15px;
  padding-left: 70px;
  .fake-label {
    position: absolute;
    top: 50%;
    left: 2px;
    transform: translate(0, -50%);
  }
  .search-tips {
    font-style: italic;
  }
}
