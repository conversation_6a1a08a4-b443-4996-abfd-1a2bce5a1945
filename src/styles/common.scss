// 操作框样式
.lmt-operation-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: .5rem;
  line-height: 2rem;
  gap: 1rem;

  .lmt-operation {
    display: flex;
    flex-flow: nowrap;
  }

  .lmt-search {
    display: flex;
    justify-content: end;
    align-items: center;
    gap: 0.5rem;

    .el-select, .el-input {
      max-width: 220px;
      min-width: 120px;
    }

    .el-input-group {
      width: 200px;
      min-width: 200px;
    }
  }
}

.el-table {
  .cell {
    /** 非最后一个el-link margin-right=0.5rem */
    .el-link:not(:last-child) {
      margin-right: 0.5rem;
    }
  }
}

.lmt-html-viewer {
  position: relative;
  border: 1px solid #ebeef5;
  // 设置阴影，让内容看起来像一张纸
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  img {
    max-width: 100%;
  }
}

.primary-text {
  color: var(--el-color-primary);
}

.warning-text {
  color: var(--el-color-warning);
}

.success-text {
  color: var(--el-color-success);
}

.danger-text {
  color: var(--el-color-danger);
}

.info-text {
  color: var(--el-color-info);
}

.padding-20{
  padding: 20px;
}

// 学籍异动学生查询结果
.shift-qry-result {
  position: relative;
  margin-bottom: 15px;
  padding-left: 70px;
  .fake-label {
    position: absolute;
    top: 50%;
    left: 2px;
    transform: translate(0, -50%);
  }
  .search-tips {
    font-style: italic;
  }
}

// 统一左右布局的页面
.sub-menu-in-page {
  height: 100%;
  .el-aside {
    max-height: calc(100% - 16px);
    overflow-x: hidden;
    overflow-y: auto;
    background: #f8f9fa;
    border: 1px solid #e4e7ed;
  }
  .el-main {
    padding: 0 0 0 16px;
  }
  .menu-title {
    padding: 16px;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    background: #fff;
    border-bottom: 1px solid #e4e7ed;
  }
  .menu-item {
    padding: 12px 16px;
    cursor: pointer;
    color: #606266;
    transition: all 0.3s;
    border-left: 3px solid transparent;

    &:hover {
      background: #ecf5ff;
      color: #409eff;
    }

    &.active {
      background: #ecf5ff;
      color: #409eff;
      border-left-color: #409eff;
      font-weight: 500;
    }
  }

  .menu-text {
    font-size: 14px;
  }
}

// 信息展示列表
.info-list {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  flex-wrap: wrap;
  & > li {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    flex: 0 0 50%;
    margin-bottom: 10px;
  }
  .desc-txt {
    color: #909399;
  }
  .result-txt {
    color: #606266;
    padding-right: 15px;
  }
}

// 弹窗内
.modal-page {
  .header {
    border-left: 5px solid var(--el-color-primary);
    margin-bottom: 10px;
    padding-left: 10px;
  }
  .el-table {
    margin-bottom: 15px;
  }
}
