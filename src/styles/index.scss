@use "common.scss";

:root {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 14px;

  body {
    margin: 0;
    background-color: #ffffff;
    overflow: hidden;
  }

  h1, h2, h3, h4, h5, h6 {
    margin: 0;
  }

  // 头部高度
  --lmt-header-height: 5rem;
  // 左侧菜单宽度
  --lmt-sidebar-width: 220px;
  // 主体内边距
  --lmt-main-padding: 1rem;
  // 翻页区占位高度
  --lmt-pager-height: 45px;
}

// 当屏幕宽度小于800px时，将左侧菜单宽度变更为64px
@media screen and (max-width: 800px) {
  :root {
    --lmt-sidebar-width: 64px;
  }
}
