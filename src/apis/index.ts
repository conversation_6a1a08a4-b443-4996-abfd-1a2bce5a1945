const modules: any = {}

// 使用 Vite 的 import.meta.glob 方法来动态加载模块
const modulesPaths = import.meta.glob('./modules/**/*.ts', {eager: true})

// 遍历所有模块路径，动态导入并注册模块
for (const path in modulesPaths) {
  // 获取模块内容而不是路径字符串
  const module: any = modulesPaths[path]
  // 优化正则表达式，支持子目录路径
  const moduleName = path.replace(/^.*[\\\/](.*)\.ts$/, '$1')
  // 按照子目录结构注册模块
  const moduleKeys = moduleName.split(/[\\\/]/)
  let currentLevel = modules
  while (moduleKeys.length > 1) {
    const key = moduleKeys.shift()!
    if (!currentLevel[key]) {
      currentLevel[key] = {}
    }
    currentLevel = currentLevel[key]
  }
  currentLevel[moduleKeys[0]] = module.default
}

export const apis = modules



