import request from "@/utils/request";

export default {
  getRegionSelectList: () => request.post('/shida-user-center-api/common/org/getRegionSelectList'),
  getSchoolSelectList: (regionId: string) => request.post('/shida-user-center-api/common/org/getSchoolSelectList', {key: regionId}),
  getTeacherSelectList: (schoolId: string, regionId: string) => request.post(`/region-service/common/teacher/getTeacherSelectList?schoolId=${schoolId}`, null, {headers: {'X-Tenant-Id': regionId}}),
  getMenuTreeList: () => request.post('/shida-user-center-api/common/org/getMenuTreeList'),
  getXueNianSelectList: () => request.post('/shida-user-center-api/common/grade/getXueNianList'),
  getSchoolSelectListCounty: (regionId: string) => request.post('/region-service/common/school/getSchoolSelectList', {}, {headers: {'X-Tenant-Id': regionId}}),
  getSportProjectSelectList: () => request.post('/shida-user-center-api/common/sport/getSportProjectSelectList'),
  getTermSelectList: () => request.post(`/shida-user-center-api/common/term/getTermSelectList`),
  getGradeSelectList: () => request.post('/shida-user-center-api/common/grade/getGradeSelectList'),
  getClazzSelectList: (schoolId: string, gradeId: string, period: string, regionId: string) => request.post(`/region-service/common/clazz/getClazzSelectList?schoolId=${schoolId}&gradeId=${gradeId}&period=${period}`, null, {headers: {'X-Tenant-Id': regionId}}),
  getNationList: () => request.post('/shida-user-center-api/common/nation/getNationList'),
}
