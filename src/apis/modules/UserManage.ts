import request from "@/utils/request";

export default {
  getPageData: (data: any) => request.post('/shida-user-center-api/user/manage/getPageData', data),
  update: (data: any) => request.post('/shida-user-center-api/user/manage/update', data),
  delete: (userId: string) => request.post('/shida-user-center-api/user/manage/delete', {key: userId}),
  create: (data: any) => request.post('/shida-user-center-api/user/manage/create', data),
  changeUserStatus: (userId: string, status: number) => request.post(`/shida-user-center-api/user/manage/changeUserStatus/${userId}/${status}`),
  resetPassword: (userId: string) => request.post(`/shida-user-center-api/user/manage/resetPassword`, {key: userId}),
}
