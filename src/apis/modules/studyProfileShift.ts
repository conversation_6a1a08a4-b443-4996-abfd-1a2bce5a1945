import request from "@/utils/request";
export default {
  //检索学籍
  jianSuoSuspend: (data: any,regionId:any) => request.post('/region-service/student/manage/getDetailByCertNo', data,{headers: {'X-Tenant-Id': regionId}}),
  //获取休学列表
  getSuspendList: (data: any,regionId:any) => request.post('/region-service/student/adjust/getSuspendPageData', data,{headers: {'X-Tenant-Id': regionId}}),
  //新增休学
  addSuspend: (data: any,regionId:any) => request.post('/region-service/student/adjust/saveSuspend', data,{headers: {'X-Tenant-Id': regionId}}),
  //新增复学
  addRestore: (data: any,regionId:any) => request.post('/region-service/student/adjust/updateSuspend', data,{headers: {'X-Tenant-Id': regionId}}),
  getClazzSelectList: (schoolId: string, gradeId: string, regionId: string) => request.post(`/region-service/common/clazz/getClazzSelectList?schoolId=${schoolId}&gradeId=${gradeId}`, null, {headers: {'X-Tenant-Id': regionId}}),
/*  changeStatus: (roleId: string, status: number) => request.post(`/shida-user-center-api/role/manage/changeStatus/${status}`, {key: roleId}),
  create: (data: any) => request.post('/shida-user-center-api/role/manage/create', data),
  update: (data: any) => request.post('/shida-user-center-api/role/manage/update', data),
  getRoleSelectList: (data: any) => request.post('/shida-user-center-api/role/manage/getRoleSelectList', data),*/

}
