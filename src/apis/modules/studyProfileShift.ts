import request from "@/utils/request";
export default {
  //检索学籍
  jianSuoSuspend: (data: any,regionId:any) => request.post('/region-service/student/manage/getDetailByCertNo', data,{headers: {'X-Tenant-Id': regionId}}),
  //市内转入专用
  shiNeiSuoSuspend:(data: any,regionId:any) => request.post('/shida-user-center-api/common/student/getStudentInfoByXjh', data,{headers: {'X-Tenant-Id': regionId}}),
  //获取休学列表
  getSuspendList: (data: any,regionId:any) => request.post('/region-service/student/adjust/getSuspendPageData', data,{headers: {'X-Tenant-Id': regionId}}),
  //新增休学
  addSuspend: (data: any,regionId:any) => request.post('/region-service/student/adjust/saveSuspend', data,{headers: {'X-Tenant-Id': regionId}}),
  //新增复学
  addRestore: (data: any,regionId:any) => request.post('/region-service/student/adjust/updateSuspend', data,{headers: {'X-Tenant-Id': regionId}}),
  //获取市外转入列表
  getCityInList: (data: any,regionId:any) => request.post('/region-service/student/adjust/getShiWaiInPageData', data,{headers: {'X-Tenant-Id': regionId}}),
  //获取市外转出列表
  getCityOutList: (data: any,regionId:any) => request.post('/region-service/student/adjust/getShiWaiOutPageData', data,{headers: {'X-Tenant-Id': regionId}}),
  //新增市外转入
  addCityIn: (data: any,regionId:any) => request.post('/region-service/student/adjust/saveShiWaiIn', data,{headers: {'X-Tenant-Id': regionId}}),
  //新增市外转出
  addCityOut: (data: any,regionId:any) => request.post('/region-service/student/adjust/saveShiWaiOut', data,{headers: {'X-Tenant-Id': regionId}}),
  //获取市内转入列表
  getCityneiInList: (data: any,regionId:any) => request.post('/region-service/student/adjust/getShiNeiInPageData', data,{headers: {'X-Tenant-Id': regionId}}),
  //获取市内转出列表
  getCityneiOutList: (data: any,regionId:any) => request.post('/region-service/student/adjust/getShiNeiOutPageData', data,{headers: {'X-Tenant-Id': regionId}}),
  //新增市内转入
  addCityNeiIn:(data: any,regionId:any) => request.post('/region-service/student/adjust/saveShiNeiIn', data,{headers: {'X-Tenant-Id': regionId}}),
  //修改市内转入
  editCityNeiIn:(data: any,regionId:any) => request.post('/region-service/student/adjust/updateShiNeiIn', data,{headers: {'X-Tenant-Id': regionId}}),
  //撤销市内转出
  cheXiaoNeiOut:(data: any,regionId:any) => request.post('/region-service/student/adjust/deleteShiNeiIn', data,{headers: {'X-Tenant-Id': regionId}}),
  //审核市内转入
  shenHeNeiOut:(data: any,regionId:any) => request.post('/region-service/student/adjust/auditShiNeiIn', data,{headers: {'X-Tenant-Id': regionId}}),
  //获取市内转入审核记录
  getShenHeNeiJiLu:(data: any,regionId:any) => request.post('/region-service/student/adjust/getShiNeiProcess', data,{headers: {'X-Tenant-Id': regionId}}),
  //获取班级
  getClazzSelectList: (schoolId: string, gradeId: string, regionId: string) => request.post(`/region-service/common/clazz/getClazzSelectList?schoolId=${schoolId}&gradeId=${gradeId}`, null, {headers: {'X-Tenant-Id': regionId}}),
  //获取学期
  getXueQIList:(data:any)=>request.post('/shida-user-center-api/term-feign-api/getAllTermList'),
  //导出学籍异动信息
  //休学复学导出
  xiuorfuexport: (data: any, regionId: string) => request.download('/region-service/student/adjust/exportSuspend', data, {headers: {'X-Tenant-Id': regionId}}),
  //市外转入转出
  outcityOutOrInexport: (data: any, regionId: string) => request.download('/region-service/student/adjust/exportShiWai', data, {headers: {'X-Tenant-Id': regionId}}),
  //获取留级跳级
  getLiuOrTiaoList:(data: any,regionId:any) => request.post('/region-service/student/adjust/getStayPageData', data,{headers: {'X-Tenant-Id': regionId}}),
  //添加留级跳级
  addLiuOrTiao:(data: any,regionId:any) => request.post('/region-service/student/adjust/saveStay', data,{headers: {'X-Tenant-Id': regionId}}),
  /*  changeStatus: (roleId: string, status: number) => request.post(`/shida-user-center-api/role/manage/changeStatus/${status}`, {key: roleId}),
  create: (data: any) => request.post('/shida-user-center-api/role/manage/create', data),
  update: (data: any) => request.post('/shida-user-center-api/role/manage/update', data),
  getRoleSelectList: (data: any) => request.post('/shida-user-center-api/role/manage/getRoleSelectList', data),*/

}
