import request from "@/utils/request";

export default {
  getPageData: (data: any, regionId: string) => request.post('/region-service/tiJian/manage/getPageDataByBodyShape', data, {headers: {'X-Tenant-Id': regionId}}),
  import: (data: any, regionId: string) => request.post('/region-service/tiJian/manage/importDataByBodyShape', data, {headers: {'X-Tenant-Id': regionId}}),
  export: (data: any, regionId: string) => request.download('/region-service/tiJian/manage/exportByBodyShape', data, {headers: {'X-Tenant-Id': regionId}}),
  exportTemplate: (data: any, regionId: string) => request.download('/region-service/tiJian/manage/downTemplateByBodyShape', data, {headers: {'X-Tenant-Id': regionId}}),
}
