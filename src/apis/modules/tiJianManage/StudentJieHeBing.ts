import request from '@/utils/request'

export default {
  getPageData: (data: any, regionId: string) => request.post('/region-service/tiJian/manage/getStudentJieHeBingPageData', data, {headers: {'X-Tenant-Id': regionId}}),
  exportTemplate: (data: any, regionId: string) => request.download('/region-service/tiJian/manage/downloadStudentJieHeBingImportTemplate', data, {headers: {'X-Tenant-Id': regionId}}),
  import: (data: any, regionId: string) => request.post('/region-service/tiJian/manage/importStudentJieHeBing', data, {headers: {'X-Tenant-Id': regionId}}),
  export: (data: any, regionId: string) => request.download('/region-service/tiJian/manage/exportStudentJieHeBing', data, {headers: {'X-Tenant-Id': regionId}}),
}
