import request from "@/utils/request";

export default {
  getPageData: (data: any, regionId: string) => request.post('/region-service/tiJian/manage/getPageDataByOther', data, {headers: {'X-Tenant-Id': regionId}}),
  import: (data: any, regionId: string) => request.post('/region-service/tiJian/manage/importDataByOther', data, {headers: {'X-Tenant-Id': regionId}}),
  export: (data: any, regionId: string) => request.download('/region-service/tiJian/manage/exportByOther', data, {headers: {'X-Tenant-Id': regionId}}),
  exportTemplate: (data: any, regionId: string) => request.download('/region-service/tiJian/manage/downTemplateByOther', data, {headers: {'X-Tenant-Id': regionId}}),
}
