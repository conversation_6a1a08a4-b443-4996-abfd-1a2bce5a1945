import request from "@/utils/request";

export default {
  getPageData: (data: any, regionId: string) => request.post('/region-service/jingFei/manage/getJingFeiPageData', data, {headers: {'X-Tenant-Id': regionId}}),
  update: (data: any, regionId: string) => request.post(`/region-service/jingFei/manage/update`, data, {headers: {'X-Tenant-Id': regionId}}),
  import: (data: any, regionId: string) => request.post('/region-service/tiJian/manage/importDataByVison', data, {headers: {'X-Tenant-Id': regionId}}),
  export: (data: any, regionId: string) => request.download('/region-service/tiJian/manage/exportByVison', data, {headers: {'X-Tenant-Id': regionId}}),
  exportTemplate: (data: any, regionId: string) => request.download('/region-service/tiJian/manage/downTemplateByVison', data, {headers: {'X-Tenant-Id': regionId}}),
}
