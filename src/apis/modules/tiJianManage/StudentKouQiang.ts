import request from '@/utils/request'

export default {
  getPageData: (data: any, regionId: string) => request.post('/region-service/tiJian/manage/getStudentKouQiangPageData', data, {headers: {'X-Tenant-Id': regionId}}),
  exportTemplate: (data: any, regionId: string) => request.download('/region-service/tiJian/manage/downloadStudentKouQiangImportTemplate', data, {headers: {'X-Tenant-Id': regionId}}),
  import: (data: any, regionId: string) => request.post('/region-service/tiJian/manage/importStudentKouQiang', data, {headers: {'X-Tenant-Id': regionId}}),
  export: (data: any, regionId: string) => request.download('/region-service/tiJian/manage/exportStudentKouQiang', data, {headers: {'X-Tenant-Id': regionId}}),
}
