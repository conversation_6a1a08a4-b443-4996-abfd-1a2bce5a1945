import request from "@/utils/request";

export default {
  getPageData: (data: any) => request.post('/shida-user-center-api/term/manage/getPageData', data),
  create: (data: any) => request.post('/shida-user-center-api/term/manage/create', data),
  update: (data: any) => request.post('/shida-user-center-api/term/manage/update', data),
  changeStatus: (termId: string, status: number) => request.post(`/shida-user-center-api/term/manage/changeStatus/${status}`, {key: termId}),
  guiDang: (id: string) => request.post(`/shida-user-center-api/term/manage/guiDang`, {key: id})
}
