import request from "@/utils/request";

export default {
  /**
   * 获取体质健康测试评分标准列表
   * @param data 搜索参数
   */
  getPageData: (data: any) =>
    request.post(`/shida-user-center-api/test-standard/manage/getPageData`, data),

  /**
   * 更新体质健康测试评分标准
   * @param data 标准数据
   */
  update: (data: any) =>
    request.post(`/shida-user-center-api/test-standard/manage/update`, data),


  /**
   * 下载导入模板
   * @param code 数据类型代码
   */
  downloadTemplate: (code: string) =>
    request.download(`/shida-user-center-api/test-standard/manage/downloadTemplate?code=${code}`, null),

  /**
   * 批量导入体质健康测试评分标准
   * @param file 文件
   * @param code 数据类型代码
   */
  import: (file: FormData, code: string) =>
    request.post(`/shida-user-center-api/test-standard/manage/import?code=${code}`, file, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
}

