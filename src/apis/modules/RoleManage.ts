import request from "@/utils/request";

export default {
  getPageData: (data: any) => request.post('/shida-user-center-api/role/manage/getPageData', data),
  changeStatus: (roleId: string, status: number) => request.post(`/shida-user-center-api/role/manage/changeStatus/${status}`, {key: roleId}),
  create: (data: any) => request.post('/shida-user-center-api/role/manage/create', data),
  update: (data: any) => request.post('/shida-user-center-api/role/manage/update', data),


}
