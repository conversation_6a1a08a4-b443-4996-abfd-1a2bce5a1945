import request from "@/utils/request";

export default {
  login: (data: any) => request.post('/shida-sso-center-api/account/admin/login', data),
  getSmsVerifyCode: (data: any) => request.post('/shida-sso-center-api/account/getSmsVerifyCode', data),
  logout: () => request.post('/shida-sso-center-api/account/logout'),
  getSelfInfo: () => request.post('/shida-sso-center-api/account/getSelfInfo'),
  changePassword: (data: any) => request.post('/shida-sso-center-api/account/changeSelfPassword', data),
  changeFieldValue: (data: any) => request.post('/shida-sso-center-api/account/changeFieldValue', data),
}
