import request from "@/utils/request";


export default {
  getPageData: (data: any) => request.post('/shida-user-center-api/grade/manage/getPageData', data),
  create: (data: any) => request.post('/shida-user-center-api/grade/manage/create', data),
  update: (data: any) => request.post('/shida-user-center-api/grade/manage/update', data),
  delete: (id: string) => request.post('/shida-user-center-api/grade/manage/delete', {key: id})
}
