import request from "@/utils/request";

export default {
  getPageData: (data: any, regionId: string) => request.post(`/region-service/school/manage/getPageData`, data, {headers: {'X-Tenant-Id': regionId}}),
  getDetail: (id: string, regionId: string) => request.post(`/region-service/school/manage/getDetail`, {key: id}, {headers: {'X-Tenant-Id': regionId}}),
  update: (data: any, regionId: string) => request.post(`/region-service/school/manage/update`, data, {headers: {'X-Tenant-Id': regionId}}),
  create: (data: any, regionId: string) => request.post(`/region-service/school/manage/create`, data, {headers: {'X-Tenant-Id': regionId}}),
  downloadAdminAccounts: (data: any, regionId: string) => request.download(`/region-service/school/manage/export/admin/account`, data, {headers: {'X-Tenant-Id': regionId}})
}
