import request from "@/utils/request";

export default {
  /**
   * 获取出勤列表
   * @param data 搜索参数
   * @param regionId 租户ID
   */
  getPageData: (data: any, regionId: string) =>
    request.post(`/region-service/sport-rate/manage/getPageData`, data, {headers: {'X-Tenant-Id': regionId}}),

  /**
   * 下载批量导入模板
   * @param termId   学期ID
   * @param regionId 租户ID【区县ID】
   */
  downloadBatchImportTemplate: (termId: string, regionId: string) =>
    request.download(`/region-service/sport-rate/manage/downloadBatchImportTemplate?termId=` + termId, null, {headers: {'X-Tenant-Id': regionId}}),

  /**
   * 批量导入
   * @param data 批量导入数据
   * @param termId 租户ID【区县ID】
   */
  batchImport: (data: FormData, termId: string, regionId: string) =>
    request.post(`/region-service/sport-rate/manage/batchImport?termId=` + termId, data, {headers: {'X-Tenant-Id': regionId}}),


  /**
   * 删除出勤记录
   * @param studentId 学生ID
   * @param termId  学期ID
   * @param regionId 租户ID【区县ID】
   */
  deleteData: (studentId: string, termId: string, regionId: string) =>
    request.post(`/region-service/sport-rate/manage/deleteData?studentId=` + studentId + "&termId=" + termId, null, {headers: {'X-Tenant-Id': regionId}}),

  /**
   * 导出记录
   * @param data 搜索参数
   * @param regionId 租户ID【区县ID】
   */
  export: (data: any, regionId: string) =>
    request.download(`/region-service/sport-rate/manage/export`, data, {headers: {'X-Tenant-Id': regionId}})
}
