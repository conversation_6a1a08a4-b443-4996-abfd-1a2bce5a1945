import request from "@/utils/request";

export default {
  /**
   * 获取课间操考勤列表
   * @param data 搜索参数
   * @param regionId 租户ID
   */
  getPageData: (data: any, regionId: string) =>
    request.post(`/region-service/interclass-exercise/manage/getPageData`, data, {headers: {'X-Tenant-Id': regionId}}),

  /**
   * 下载批量导入模板
   * @param termId   学期ID
   * @param regionId 租户ID【区县ID】
   */
  downloadBatchImportTemplate: (termId: string, regionId: string) =>
    request.download(`/region-service/interclass-exercise/manage/downloadBatchImportTemplate?termId=` + termId, null, {headers: {'X-Tenant-Id': regionId}}),

  /**
   * 批量导入
   * @param data 批量导入数据
   * @param termId 租户ID【区县ID】
   */
  batchImport: (data: FormData, termId: string, regionId: string) =>
    request.post(`/region-service/interclass-exercise/manage/batchImport?termId=` + termId, data, {headers: {'X-Tenant-Id': regionId}}),

  /**
   * 删除课间操考勤记录
   * @param studentId 学生ID
   * @param termId  学期ID
   * @param regionId 租户ID【区县ID】
   */
  deleteData: (studentId: string, termId: string, regionId: string) =>
    request.post(`/region-service/interclass-exercise/manage/deleteData?studentId=` + studentId + "&termId=" + termId, null, {headers: {'X-Tenant-Id': regionId}}),

  /**
   * 导出课间操考勤记录
   * @param data 搜索参数
   * @param regionId 租户ID【区县ID】
   */
  export: (data: any, regionId: string) =>
    request.download(`/region-service/interclass-exercise/manage/export`, data, {headers: {'X-Tenant-Id': regionId}})
}




