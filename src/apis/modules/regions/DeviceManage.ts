import request from "@/utils/request";

export default {
  getPageData: (data: any) => request.post('/region-service/device/manage/getPageData', data),
  create: (data: any) => request.post('/region-service/device/manage/create', data),
  update: (data: any) => request.post('/region-service/device/manage/update', data),
  delete: (id: string) => request.post('/region-service/device/manage/delete', {key: id}),
}
