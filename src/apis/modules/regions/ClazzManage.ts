import request from "@/utils/request";

export default {
  getPageData: (data: any, regionId: string) => request.post(`/region-service/clazz/manage/getPageData`, data, {headers: {'X-Tenant-Id': regionId}}),
  create: (data: any, regionId: string) => request.post(`/region-service/clazz/manage/create`, data, {headers: {'X-Tenant-Id': regionId}}),
  update: (data: any, regionId: string) => request.post(`/region-service/clazz/manage/update`, data, {headers: {'X-Tenant-Id': regionId}}),
  delete: (clazzId: string, regionId: string) => request.post(`/region-service/clazz/manage/delete`, {key: clazzId}, {headers: {'X-Tenant-Id': regionId}}),
}
