import request from '@/utils/request'

export default {
  getTeacherList: (data: any, regionId: string) => request.post('/region-service/teacher/manage/getPageData', data, {headers: {'X-Tenant-Id': regionId}}),
  getDetail: (id: string, regionId: string) => request.post('/region-service/teacher/manage/getDetail', {key:id}, {headers: {'X-Tenant-Id': regionId}}),
  save: (data: any, regionId: string) => request.post('/region-service/teacher/manage/add', data, {headers: {'X-Tenant-Id': regionId}}),
  delete: (id: string, regionId: string) => request.post('/region-service/teacher/manage/delete', {key:id}, {headers: {'X-Tenant-Id': regionId}}),
  update: (data: any, regionId: string) => request.post('/region-service/teacher/manage/edit', data, {headers: {'X-Tenant-Id': regionId}}),
  import: (data: any, regionId: string) => request.post('/region-service/teacher/manage/import', data, {headers: {'X-Tenant-Id': regionId}}),
  export: (data: any, regionId: string) => request.download('/region-service/teacher/manage/export', data, {headers: {'X-Tenant-Id': regionId}}),
  exportTemplate: (data: any, regionId: string) => request.download('/region-service/teacher/manage/downloadBatchImportTemplate', data, {headers: {'X-Tenant-Id': regionId}}),
}
