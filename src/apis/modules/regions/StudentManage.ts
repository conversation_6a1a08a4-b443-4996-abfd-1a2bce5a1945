import request from '@/utils/request'

export default {
  getPageData: (data: any, regionId: string) => request.post('/region-service/student/manage/getPageData', data, {headers: {'X-Tenant-Id': regionId}}),
  getDetail: (id: string, regionId: string) => request.post('/region-service/student/manage/detail', {key:id}, {headers: {'X-Tenant-Id': regionId}}),
  save: (data: any, regionId: string) => request.post('/region-service/student/manage/save', data, {headers: {'X-Tenant-Id': regionId}}),
  delete: (id: string, regionId: string) => request.post('/region-service/student/manage/delete', {key:id}, {headers: {'X-Tenant-Id': regionId}}),
  update: (data: any, regionId: string) => request.post('/region-service/student/manage/edit', data, {headers: {'X-Tenant-Id': regionId}}),
  import: (data: any, regionId: string) => request.post('/region-service/student/manage/import', data, {headers: {'X-Tenant-Id': regionId}}),
  export: (data: any, regionId: string) => request.download('/region-service/student/manage/export', data, {headers: {'X-Tenant-Id': regionId}}),
  exportTemplate: (data: any, regionId: string) => request.download('/region-service/student/manage/downloadBatchImportTemplate', data, {headers: {'X-Tenant-Id': regionId}}),
}
