import request from "@/utils/request";

export default {
  /**
   * 获取学生体质健康标准测试结果列表
   * @param data 搜索参数
   * @param regionId 租户ID
   */
  getPageData: (data: any, regionId: string) =>
    request.post(`/region-service/tizhiceshi/manage/getPageData`, data, {headers: {'X-Tenant-Id': regionId}}),

  /**
   * 获取体质测试项目列表
   * @param regionId 租户ID
   */
  getSportProjects: (regionId: string) =>
    request.get(`/region-service/tizhiceshi/manage/getSportProjects`, {headers: {'X-Tenant-Id': regionId}}),

  /**
   * 下载批量导入模板
   * @param termId 学期ID
   * @param regionId 租户ID
   */
  downloadBatchImportTemplate: (termId: string, regionId: string) =>
    request.download(`/region-service/tizhiceshi/manage/downloadBatchImportTemplate?termId=` + termId, null, {headers: {'X-Tenant-Id': regionId}}),

  /**
   * 批量导入体质测试结果
   * @param data 批量导入数据
   * @param termId 学期ID
   * @param regionId 租户ID
   */
  batchImport: (data: FormData, termId: string, regionId: string) =>
    request.post(`/region-service/tizhiceshi/manage/batchImport?termId=` + termId, data, {headers: {'X-Tenant-Id': regionId}}),

  /**
   * 导出体质测试结果
   * @param data 搜索参数
   * @param regionId 租户ID
   */
  export: (data: any, regionId: string) =>
    request.download(`/region-service/tizhiceshi/manage/export`, data, {headers: {'X-Tenant-Id': regionId}}),

  /**
   * 删除体质测试记录
   * @param studentId 学生ID
   * @param termId 学期ID
   * @param regionId 租户ID
   */
  deleteData: (studentId: string, termId: string, regionId: string) =>
    request.post(`/region-service/tizhiceshi/manage/deleteData?studentId=` + studentId + "&termId=" + termId, null, {headers: {'X-Tenant-Id': regionId}})
}
