import request from '@/utils/request'

export default {
  //内科相关Api
  getPageData: (data: any, regionId: string) => request.post('/region-service/tiJian/manage/getNeiKePageData', data, {headers: {'X-Tenant-Id': regionId}}),
  import: (data: any, regionId: string) => request.post('/region-service/tiJian/manage/neiKeBatchImport', data, {headers: {'X-Tenant-Id': regionId}}),
  export: (data: any, regionId: string) => request.download('/region-service/tiJian/manage/exportNeiKe', data, {headers: {'X-Tenant-Id': regionId}}),
  exportTemplate: (data: any, regionId: string) => request.download('/region-service/tiJian/manage/downloadNeiKeBatchImportTemplate', data, {headers: {'X-Tenant-Id': regionId}}),

  //外科相关Api
  waiKeGetPageData:  (data: any, regionId: string) => request.post('/region-service/tiJian/manage/getWaiKePageData', data, {headers: {'X-Tenant-Id': regionId}}),
  waiKeImport: (data: any, regionId: string) => request.post('/region-service/tiJian/manage/waiKeBatchImport', data, {headers: {'X-Tenant-Id': regionId}}),
  waiKeExport: (data: any, regionId: string) => request.download('/region-service/tiJian/manage/exportWaiKe', data, {headers: {'X-Tenant-Id': regionId}}),
  waiKeExportTemplate: (data: any, regionId: string) => request.download('/region-service/tiJian/manage/downloadWaiKeBatchImportTemplate', data, {headers: {'X-Tenant-Id': regionId}}),

  //耳鼻喉相关Api
  erBiHouGetPageData:  (data: any, regionId: string) => request.post('/region-service/tiJian/manage/getErBiHouPageData', data, {headers: {'X-Tenant-Id': regionId}}),
  erBiHouImport: (data: any, regionId: string) => request.post('/region-service/tiJian/manage/erBiHouBatchImport', data, {headers: {'X-Tenant-Id': regionId}}),
  erBiHouExport: (data: any, regionId: string) => request.download('/region-service/tiJian/manage/exportErBiHou', data, {headers: {'X-Tenant-Id': regionId}}),
  erBiHouExportTemplate: (data: any, regionId: string) => request.download('/region-service/tiJian/manage/downloadErBiHouBatchImportTemplate', data, {headers: {'X-Tenant-Id': regionId}}),


}
