import request from "@/utils/request";

export default {
  getPageData: (data: any, regionId: string) => request.post(`/region-service/student/adjust/getPageData`, data, {headers: {'X-Tenant-Id': regionId}}),
  create: (data: any, regionId: string) => request.post(`/region-service/student/adjust/create`, data, {headers: {'X-Tenant-Id': regionId}}),
  update: (data: any, regionId: string) => request.post(`/region-service/student/adjust/update`, data, {headers: {'X-Tenant-Id': regionId}}),
  getAdjustList: (data: any, regionId: string) => request.post(`/region-service/student/adjust/getAdjustList`, data, {headers: {'X-Tenant-Id': regionId}}),
  importClassAdjustRecord: (data: any, regionId: string) => request.post(`/region-service/student/adjust/import`, data, {headers: {'X-Tenant-Id': regionId}}),
  getClazzAdjustList: (data: any, regionId: string) => request.post(`/region-service/student/adjust/getClazzAdjustList`, data, {headers: {'X-Tenant-Id': regionId}}),

}
