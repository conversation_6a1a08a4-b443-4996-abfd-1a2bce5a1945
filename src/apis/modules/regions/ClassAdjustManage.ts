import request from "@/utils/request";

export default {
  getPageData: (data: any, regionId: string) => request.post(`/region-service/class-adjust/manage/getPageData`, data, {headers: {'X-Tenant-Id': regionId}}),
  create: (data: any, regionId: string) => request.post(`/region-service/class-tweak/manage/create`, data, {headers: {'X-Tenant-Id': regionId}}),
  update: (data: any, regionId: string) => request.post(`/region-service/class-tweak/manage/update`, data, {headers: {'X-Tenant-Id': regionId}}),
}
