import request from "@/utils/request";


export default {
  getPageData: (data: any) => request.post('/shida-user-center-api/region/manage/getRegionList', data),
  update: (data: any) => request.post('/shida-user-center-api/region/manage/update', data),
  downloadAccounts: (regionId: String) => request.download('/shida-user-center-api/region/manage/export/account', {key: regionId}),
  exportAllRegionAccounts: (data: any) => request.download('/shida-user-center-api/region/manage/export/all/account',data),
}
