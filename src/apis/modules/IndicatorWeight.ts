import request from "@/utils/request"

// 指标项接口
export interface IndicatorItem {
  id?: number
  name: string
  weight: number
  category: string
}

// 指标权重配置接口
export interface IndicatorWeightConfig {
  id?: number
  name: string
  code: string
  content: Array<{
    key: string
    value: string
    extra: string
  }>
  indicators?: IndicatorItem[]  // 转换后的格式
}

// 指标权重表单接口
export interface IndicatorWeightForm {
  indicators: IndicatorItem[]
}

export default {
  // 获取指标权重配置
  getIndicatorWeight() {
    return request.post<IndicatorWeightConfig>('/shida-user-center-api/zbqz/manage/getByCode', { key: 'ZBQZWH' })
  },

  // 更新指标权重配置
  updateIndicatorWeight(data: IndicatorWeightForm) {
    // 转换为现有接口期望的格式
    const formData = {
      code: 'ZBQZWH',
      name: '指标权重维护',
      orderNumber: 1,
      content: data.indicators.map(item => ({
        key: `${item.category}-${item.name}`,
        value: `${item.weight}%`,
        extra: item.id?.toString() || '0'
      }))
    }
    return request.post<string>('/shida-user-center-api/zbqz/manage/updateByCode', formData)
  },

  // 新增指标项 - 复用updateByCode接口
  addIndicator(data: IndicatorItem) {
    // 先获取现有配置，然后添加新项
    return this.getIndicatorWeight().then(response => {
      // 从response.data.content中提取现有数据，转换为IndicatorItem格式
      const existing = response.data?.content ? response.data.content.map((item: any) => {
        const [category, name] = item.key.split('-', 2)
        return {
          id: parseInt(item.extra) || 0,
          name: name || item.key,
          category: category || '未分类',
          weight: parseInt(item.value.replace('%', '')) || 0
        }
      }) : []

      const maxId = Math.max(...existing.map(item => item.id || 0), 0)
      const newItem = { ...data, id: maxId + 1 }

      return this.updateIndicatorWeight({ indicators: [...existing, newItem] })
    })
  },

  // 删除指标项
  deleteIndicator(id: number) {
    return request.post<string>('/shida-user-center-api/zbqz/manage/deleteIndicator', { key: id })
  }
}
