import request from "@/utils/request";

export default {
  getPageData: (data: any) => request.post('/shida-user-center-api/menu/manage/getPageData', data),
  create: (data: any) => request.post('/shida-user-center-api/menu/manage/create', data),
  update: (data: any) => request.post('/shida-user-center-api/menu/manage/update', data),
  delete: (id: string) => request.post('/shida-user-center-api/menu/manage/delete', {key: id}),
  getPermissionsByRoleId: (roleId: string) => request.post('/shida-user-center-api/menu/manage/getPermissionsByRoleId', {key: roleId}),
  changeRolePermissions: (data: any) => request.post(`/shida-user-center-api/menu/manage/changeRolePermissions`, data),
}
