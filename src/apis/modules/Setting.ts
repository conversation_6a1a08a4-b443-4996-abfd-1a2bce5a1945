import request from "@/utils/request"

// 设置项接口
export interface SettingItem {
  id?: number
  name: string
  code: string
  content: string
  regionId?: number
  schoolId?: number
  orderNumber?: number
}

// 键值对表单接口
export interface KeyForm<T> {
  key: T
}

// 通用设置更新表单接口
export interface SettingUpdateForm {
  code: string
  name: string
  content: string
  orderNumber?: number
}

export default {
  // 根据代码获取设置项
  getByCode(data: KeyForm<string>) {
    return request.post<any>('/shida-user-center-api/zbqz/manage/getByCode', data)
  },

  // 创建设置项
  create(data: SettingUpdateForm) {
    return request.post<string>('/shida-user-center-api/zbqz/manage/create', data)
  },

  // 根据代码更新设置项
  updateByCode(data: SettingUpdateForm) {
    return request.post<string>('/shida-user-center-api/zbqz/manage/updateByCode', data)
  },

  // 根据代码删除设置项
  deleteByCode(data: KeyForm<string>) {
    return request.post<string>('/shida-user-center-api/zbqz/manage/deleteByCode', data)
  }
}

