// src/request/Settings.ts
import type {AxiosInstance, AxiosRequestHeaders, InternalAxiosRequestConfig} from 'axios';
import axios from 'axios';
import {useUserStore} from '@/store';
import {ElLoading, ElMessageBox, ElNotification} from "element-plus";
import {Decrypt, Encrypt, RandomString} from '@/utils/secret'
import router from "@/router"
import {nextTick} from "vue";

let elMessageBoxIndex: any = null

export interface SdAxiosInstance extends AxiosInstance {
  download(url: string, data?: any, config?: any): Promise<any>;
}

const instance: SdAxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_PROXY_PREFIX, // 设置你的 API 基础 URL
  timeout: 10000, // 设置请求超时时间
}) as SdAxiosInstance;
// 不加密的接口
const notSecurityUrls = ['/shida-sso-center-api/account/getVerifyCodeImage']
// 检查regionId的接口
const checkRegionUrls: Array<string> = ['/region-service/**']

const securityStatus = sessionStorage.getItem('securityStatus') === 'true'

// URL匹配函数
function matchUrl(url: string, pattern: string): boolean {
  // 如果pattern以/**结尾，则进行前缀匹配
  if (pattern.endsWith('/**')) {
    const prefix = pattern.slice(0, -3);
    return url.startsWith(prefix);
  }
  // 精确匹配
  return url === pattern;
}

// 请求拦截器
instance.interceptors.request.use(
  async (config: InternalAxiosRequestConfig) => {
    // 获取 token
    const userStore = useUserStore();
    const token: string = userStore.token;

    // 如果有 token，则添加到请求头
    if (token) {
      config.headers = {
        ...config.headers,
        Authorization: `${token}`
      } as AxiosRequestHeaders;
      if (checkRegionUrls.some(pattern => matchUrl(config.url as string, pattern)) && !config.headers['X-Tenant-Id']) {
        const regionId = userStore.currentUserInfo?.cityAdminFlag ? config.data?.regionId : null;
        // 检查URL是否需要regionId
        if (!regionId && !config.headers['X-Tenant-Id']) {
          return Promise.reject({message: '区县标识不能为空', status: 410});
        } else if (!config.headers['X-Tenant-Id']) {
          config.headers['X-Tenant-Id'] = regionId
        }
      }

    }
    if (securityStatus && !notSecurityUrls.includes(config.url as string)) {
      let key = RandomString(16)
      config.headers['secret-key'] = key
      if (config.data && config.method == 'post') {
        const jsonData = JSON.stringify(config.data)
        const data = Encrypt(jsonData, key)
        config.data = {data}
        if (import.meta.env.MODE === 'development') {
          console.log(config.url, '\ndata===>', jsonData)
        }
      }
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
instance.interceptors.response.use(
  (response) => {
    if (response.status == 200 && response.data instanceof Blob) {
      return Promise.resolve(response)
    }
    if (response.status == 200) {
      const url = (response.config.url as string).split('?')[0]
      if (securityStatus && !notSecurityUrls.includes(url)) {
        const data = Decrypt(response.data, response.headers['secret-key'])
        let result = data ? JSON.parse(data) : null
        if (import.meta.env.MODE === 'development') {
          console.log(response.config.url, '\nresponse===>', result)
        }
        return Promise.resolve(result);
      } else {
        return Promise.resolve(response.data);
      }
    } else if (response.status == 401) {
      if (elMessageBoxIndex) {
        return Promise.reject(response.data)
      } else {
        elMessageBoxIndex = 1
        ElMessageBox.alert('token失效,请重新登录', '温馨提示', {type: "warning", showClose: false})
          .then(() => {
            elMessageBoxIndex = null
            useUserStore().logout()
            router.push('/login')
          })
        return Promise.reject(response.data)
      }
    } else if (response.status == 580) {
      const realData = response.data
      if (elMessageBoxIndex) {
        return Promise.reject(response.data)
      } else {
        elMessageBoxIndex = 1
        ElMessageBox.alert(realData.message, '温馨提示', {type: "warning", showClose: false})
          .then(() => {
            elMessageBoxIndex = null
            window.location.href = '/personal'
          })
        return Promise.reject(response.data)
      }
    } else {
      ElNotification({
        title: '温馨提示',
        message: response.data.message || '未知错误，请稍后重试',
        type: 'error',
      })
      return Promise.reject(response.data)
    }
  },
  (error) => {
    if (error.status == 401) {
      if (elMessageBoxIndex) {
        return Promise.reject(error.data || error)
      } else {
        elMessageBoxIndex = 1
        ElMessageBox.alert('token失效，请重新登录', '温馨提示', {type: "warning", showClose: false})
          .then(() => {
            elMessageBoxIndex = null
            router.push('/login')
          })
        return Promise.reject(error.data || error)
      }
    } else if (error.status == 404) {
      ElNotification({
        title: '温馨提示',
        message: '对不起，您请求的资源不存在。',
        type: 'error',
      })
    } else if (error.status == 410) {
      ElNotification({
        title: '温馨提示',
        message: error.response?.data?.message || error.message || '参数错误，请稍后重试',
        type: 'error',
      })
    } else if (error.status == 429) {
      ElNotification({
        title: '温馨提示',
        message: '您访问次数过多，请稍后重试。',
        type: 'error',
      })
    } else if (error.status == 500) {
      ElNotification({
        title: '温馨提示',
        message: error.response?.data?.message || '服务器错误，请稍后重试。',
        type: 'error',
      })
    } else if (error.status == 503) {
      ElNotification({
        title: '温馨提示',
        message: '服务繁忙，请稍后重试。',
        type: 'error',
      })
    } else {
      ElNotification({
        title: '温馨提示',
        message: error.response?.data?.message || '未知错误，请稍后重试',
        type: 'error',
      })
    }
    return Promise.reject(error?.response?.data || error?.response || error);
  }
);

instance.download = async (url: string, data: any, config: any) => {
  const loading = ElLoading.service({
    lock: true,
    fullscreen: true,
    background: 'rgba(0, 0, 0, 0.7)',
    text: '正在下载，请稍后...'
  })
  return instance.post(url, data, {
    ...config,
    responseType: 'blob'
  }).then((response) => {
    // 创建一个a标签
    const a = document.createElement('a')
    a.href = URL.createObjectURL(response.data)
    a.download = decodeURI(response.headers['content-disposition'].split('filename=')[1])
    document.body.appendChild(a);
    a.click(); //点击下载
    document.body.removeChild(a); //下载完成移除元素
    window.URL.revokeObjectURL(a.href); //释放掉blob对象
    ElNotification({message: "下载成功", type: 'success', title: '温馨提示'})
  }).finally(() => setTimeout(() => nextTick(() => loading.close()), 500))
}

export default instance;
