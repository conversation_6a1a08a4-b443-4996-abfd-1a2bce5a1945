/**
 * 判断是否为空
 * @param value 待判断的值
 */
export function isEmpty(value: unknown): boolean {
  // null 或 undefined
  if (value == null) return true;

  // 空字符串（包括纯空格）
  if (typeof value === "string" && value.trim() === "") return true;

  // 空数组
  if (Array.isArray(value) && value.length === 0) return true;

  // 空对象（排除 null，因为上面已判断）
  if (typeof value === "object" && !Array.isArray(value) && Object.keys(value).length === 0) return true;

  return false;
}

