import {createPinia} from 'pinia';
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate';

const pinia = createPinia();

// 持久化
pinia.use(piniaPluginPersistedstate);

export * from './modules/user';
export * from './modules/history';
export * from './modules/role';
export * from './modules/dept';
export * from './modules/userRouter';
export * from './modules/teacher';
export * from './modules/term';


export default pinia;
