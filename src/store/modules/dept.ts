import {defineStore} from 'pinia';
import request from "@/utils/request";
import type { KeyValue } from "@/commons/KeyValue";

export interface DeptState {
  topDept: DeptInfo | null;
  currentDept: DeptInfo | null;
  regionList: Array<KeyValue> | [];
  schoolList: Array<KeyValue> | [];
  gradeList: Array<KeyValue> | [];
}


export interface DeptInfo {
  code: string;
  name: string;
  parentId: string;
  floor: number;
  id: string;
}

export interface SchoolInfo {
  code: string;
  name: string;
  regionId: string;
  id: string;
}


export const useDeptStore = defineStore('dept', {
  state: (): DeptState => ({
    topDept: ({code: '130100', name: '石家庄市', parentId: '', floor: 1, id: '130100'}),
    currentDept: null,
    regionList: [],
    schoolList: [],
    gradeList: []
  }),
  actions: {
    async setCurrentDept(dept: DeptInfo) {
      this.currentDept = dept;
    },
    async setRegionList() {
      const res = await request.post('/shida-user-center-api/common/org/getRegionSelectList')
      this.regionList = res.data
    },
    async setSchoolList(regionId: string) {
      const res = await request.post('/shida-user-center-api/common/org/getSchoolSelectList', {key: regionId})
      this.schoolList = res.data
    },
    async setGradeList() {
      const res = await request.post('/shida-user-center-api/common/grade/getGradeSelectList')
      this.gradeList = res.data
    },
    clearDeptState() {
      this.currentDept = null;
      this.regionList = [];
      this.schoolList = [];
      this.gradeList = [];
    },
  },
  getters: {
    regionId: (state) => {
      if (state.currentDept?.code == state.topDept?.code) {
        return '111';
      } else {
        return state.currentDept?.id || "0";
      }
    }
  },
  persist: {
    key: 'sd-healthy.dept-store', // 持久化的 key
    storage: sessionStorage, // 存储方式
  }
});
