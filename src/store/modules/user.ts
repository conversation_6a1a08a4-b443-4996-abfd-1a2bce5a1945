import {defineStore} from 'pinia';
import {DeptInfo, useDeptStore, useHistoryStore, useUserRouterStore} from "@/store";
import router from "@/router";

export interface UserState {
  userInfo: UserInfo | null;
  token: string;
}

export interface UserInfo {
  defaultPasswordFlag: boolean,
  regionCode: string,
  deptFloor: number,
  regionId: string,
  regionName: string,
  email: string,
  id: string,
  mobile: string,
  nickname: string,
  parentDeptId: string,
  roles: Array<any>,
  permissions: Array<any>,
  menus: Array<any>,
  schoolCode: string,
  schoolId: string,
  schoolName: string,
  status: number,
  cityAdminFlag: boolean,
  schoolAdminFlag: boolean,
  regionAdminFlag: boolean,
  countyAdminFlag: boolean,
  token: string,
  username: string
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    userInfo: null,
    token: '',
  }),
  actions: {
    async setUserInfo(userInfo: UserInfo | any | null) {
      this.userInfo = userInfo;
      await useDeptStore().setCurrentDept(
        {
          id: userInfo.regionId,
          code: userInfo.regionCode,
          name: userInfo.regionName,
          floor: userInfo.deptFloor
        } as DeptInfo)
    },
    setToken(token: string) {
      this.token = token;
    },
    logout() {
      this.userInfo = null;
      this.token = '';
      useDeptStore().clearDeptState();
      const userRoutes = useUserRouterStore().userRoutes;
      for (const route of userRoutes) {
        route.name && router.removeRoute(route.name)
      }
      useUserRouterStore().clearRoutes()
      useHistoryStore().clearHistory()
    }
  },
  getters: {
    isAuthenticated: (state) => !!state.token,
    currentUserInfo: (state) => state.userInfo,
    role: (state) => state.userInfo?.roles[0].roleCode,
    regionId: (state) => state.userInfo?.regionId
  },
  persist: {
    key: 'sd-healthy.user-store', // 持久化的 key
    storage: sessionStorage, // 存储方式
  }
});
