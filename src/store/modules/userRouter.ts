import {defineStore} from 'pinia';
import {RouteRecordRaw} from "vue-router";

export interface UserRouterState {
  userRoutes: RouteRecordRaw[],
  initUserRoutesStatus: boolean
}

export const useUserRouterStore = defineStore('userRoutes', {
  state: (): UserRouterState => ({
    userRoutes: [],
    initUserRoutesStatus: false
  }),
  actions: {
    setUserRoutes(routes: RouteRecordRaw[]) {
      this.userRoutes = routes
      this.initUserRoutesStatus = true
    },
    clearRoutes() {
      this.userRoutes = []
      this.initUserRoutesStatus = false
    }
  }
})
