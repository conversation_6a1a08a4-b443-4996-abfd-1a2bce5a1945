// d:\IdeaProjects\ai-test\src\store\modules\user.ts
import {defineStore} from 'pinia';

export interface RoleState {
  roleList: Array<any> | null;
  roleEnum: any;
}


export const useRoleStore = defineStore('role', {
  state: (): RoleState => ({
    roleList: [{code: 'SUPER_ADMIN', name: '超级管理员'},
      {code: 'CITY_ADMIN', name: '市级管理员'},
      {code: 'COUNTY_ADMIN', name: '区县管理员'},
      {code: 'SCHOOL_ADMIN', name: '学校管理员'},
      {code: 'PARENT', name: '家长'},
      {code: 'TEACHER', name: '教师'},
      {code: 'STUDENT', name: '学生'}
    ],
    roleEnum: {
      SUPER_ADMIN: 'SUPER_ADMIN',
      CITY_ADMIN: 'CITY_ADMIN',
      COUNTY_ADMIN: 'COUNTY_ADMIN',
      SCHOOL_ADMIN: 'SCHOOL_ADMIN',
      PARENT: 'PARENT',
      TEACHER: 'TEACHER',
      STUDENT: 'STUDENT'
    }
  }),
  getters: {
    roleListArray: (state) => state.roleList,
    roleEnums: (state) => state.roleEnum,
    hasSchoolRoles: (state) => {
      return [state.roleEnum.SCHOOL_ADMIN, state.roleEnum.PARENT, state.roleEnum.TEACHER, state.roleEnum.STUDENT]
    }
  },
  persist: {
    key: 'sd-healthy.role-store', // 持久化的 key
    storage: sessionStorage, // 存储方式
  }
});
