import {defineStore} from 'pinia';
import request from "@/utils/request";
import type { KeyValue } from "@/commons/KeyValue";

export interface TermState {
  termList: Array<KeyValue> | [];
}

export const useTermStore = defineStore('term', {
  state: (): TermState => ({
    termList: []
  }),
  actions: {
    async setTermList() {
      const res = await request.post('/shida-user-center-api/common/term/getTermSelectList')
      this.termList = res.data
    },
    clearTermState() {
      this.termList = [];
    },
  },
  getters: {
  },
  persist: {
    key: 'sd-healthy.term-store', // 持久化的 key
    storage: sessionStorage, // 存储方式
  }
});
