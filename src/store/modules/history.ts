// d:\IdeaProjects\ai-test\src\store\modules\user.ts
import {defineStore} from 'pinia';

export interface HistoryItem {
  route: string | null;
  params: any | null;
}

export interface HistoryState {
  history: Array<HistoryItem> | null;
}


export const useHistoryStore = defineStore('history', {
  state: (): HistoryState => ({
    history: []
  }),
  actions: {
    setHistory(route: string | null, params: any | null) {
      let _history = this.history && this.history.find(item => item.route === route)
      if (!_history) {
        this.history && this.history.push({route, params})
      } else {
        _history = {route, params}
      }
    },
    removeHistory(route: string | null) {
      let _history = this.history && this.history.find(item => item.route === route)
      if (_history) {
        this.history && this.history.splice(this.history.indexOf(_history), 1)
      }
    },
    clearHistory() {
      this.history = []
    }
  },
  getters: {
    getHistory: (state) => (route: string | null) => {
      return state.history && state.history.find(item => item.route === route)?.params
    }
  },
  persist: {
    key: 'sd-healthy.history-store', // 持久化的 key
    storage: sessionStorage, // 存储方式
  }
});
