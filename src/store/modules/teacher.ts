import {defineStore} from 'pinia';
import type { KeyValue } from "@/commons/KeyValue";

export interface TeacherState {
  schoolTeacherList: Array<SchoolTeachers> | null;
}

export interface SchoolTeachers {
  schoolId: string;
  teachers: Array<KeyValue> | null;
}

export const useTeacherStore = defineStore('teacher', {
  state: (): TeacherState => ({
    schoolTeacherList: null
  }),
  getters: {},
  actions: {
    setSchoolTeachers(schoolId: string, teachers: Array<KeyValue>) {
      this.schoolTeacherList = [{schoolId, teachers}];
    }
  }
})
