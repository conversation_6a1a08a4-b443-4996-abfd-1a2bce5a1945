import {useUserStore} from "@/store";
import {Directive, DirectiveBinding} from "vue";


export const hasPerm: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const {value} = binding;
    const userStore = useUserStore();
    const perms = userStore.userInfo?.permissions.map(item => item.permission);
    if (value) {
      const requiredPerms = value; // DOM绑定需要的按钮权限标识
      const _hasPerm = perms?.some((perm) => {
        return requiredPerms.includes(perm);
      });

      if (!_hasPerm) {
        el.parentNode && el.parentNode.removeChild(el);
      }
    } else {
      throw new Error("need perms! Like v-has-perm=\"['sys:user:add','sys:user:edit']\"");
    }
  }
}
