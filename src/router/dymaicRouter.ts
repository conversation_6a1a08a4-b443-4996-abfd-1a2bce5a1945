import {useUserRouterStore, useUserStore} from "@/store";
import {Router, RouteRecordRaw} from "vue-router";
import Layout from "@/layout/Layout.vue";

// 预加载所有视图组件
const views = import.meta.glob('../views/**/*.vue')
// 生成动态路由
export const generateRoute = async (router: Router, routes: RouteRecordRaw[]): Promise<void> => {
  const userStore = useUserStore()
  const menus = userStore.userInfo?.menus || []
  const initRoute = (parent: any): RouteRecordRaw | null => {
    if (parent.type === 'F') {
      return null
    }
    const componentPath = parent.component
    const _route = {
      path: parent.router as string,
      name: parent.code as string,
      meta: {
        hidden: parent.visible === 0,
        title: parent.name,
        type: parent.type,
        orderNum: parent.orderNum,
        parent: parent.parentId ? parent.parentId : null
      },
      component: componentPath ? views[`../views/${componentPath}.vue`] : undefined,
      children: []
    } as RouteRecordRaw
    if (_route.meta?.parent === null) {
      _route.component = Layout
    }

    const childRoutes = parent.children || []
    for (let i = 0; i < childRoutes.length; i++) {
      const _child = childRoutes[i]
      if (_child.type === 'F') {
        continue;
      } else {
        const _childRoute = initRoute(_child)
        if (_childRoute) {
          _route.children?.push(_childRoute)
        }
      }
    }
    return _route;
  }
  const _routes: RouteRecordRaw[] = []
  for (let i = 0; i < menus.length; i++) {
    const parentRoute = menus[i]
    const _route = initRoute(parentRoute)
    if (_route) {
      _routes.push(_route)
    }
  }
  for (const _route of _routes) {
    try {
      router.addRoute(_route)
    } catch (e) {
      console.error(e, _route)
    }
  }
  useUserRouterStore().setUserRoutes(_routes);
  // 把个人中心放最后
  router.removeRoute('Personal')
  const personal = {
    path: '/personal',
    name: 'Personal',
    component: Layout,
    meta: {hidden: false, title: '个人中心'},
    redirect: '/personal/index',
    children: [
      {
        path: '/personal/index',
        name: 'PersonalIndex',
        component: () => import('@/views/Personal/Personal.vue'),
        meta: {hidden: false, title: '个人设置'},
      }
    ]
  } as RouteRecordRaw;
  router.addRoute(personal);
  const notMatchPath = {path: '/:pathMatch(.*)*', name: 'not-found', redirect: '/404'} as RouteRecordRaw
  router.addRoute(notMatchPath);
  console.log('动态路由生成完毕', router.getRoutes())
}
