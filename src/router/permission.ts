// 跳转路由时检查登录状态
import type {Router} from 'vue-router';
import NProgress from 'nprogress'
// 用户状态
import {useUserStore} from '@/store/modules/user'
import {generateRoute} from './dymaicRouter'
import {useUserRouterStore} from "@/store";


// 白名单
const whiteList = ['/login'];
export default function setupPermissionGuard(router: Router) {
  router.beforeEach(async (to, from, next) => {
    NProgress.start();
    if (whiteList.includes(to.path)) {
      next()
    } else {
      const userStore = useUserStore();
      // 假设 isAuthenticated 是一个 getter 来检查用户是否已登录
      const isAuthenticated = userStore.isAuthenticated;
      if (!isAuthenticated) {
        next({
          path: '/login',
          query: {redirect: to.fullPath}
        });
      } else {
        // await generateRoute(router, router.getRoutes());
        // next()
        if (useUserRouterStore().initUserRoutesStatus) {
          next()
        } else {
          await generateRoute(router, router.getRoutes());
          next({...to, replace: true})
        }
      }
    }
  });
  router.afterEach(() => {
    NProgress.done();
  });
}
