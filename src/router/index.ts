import {createRouter, createWebHashHistory} from 'vue-router';
// 引入permission
import setupPermissionGuard from '@/router/permission'
import Layout from "@/layout/Layout.vue";


const routes = [
  {
    path: '/',
    name: 'Home',
    component: Layout,
    meta: {hidden: false, title: '首页', orderNum: 0},
    redirect: '/home',
    children: [
      {
        path: '/home',
        name: 'HomePage',
        component: () => import('@/views/Home.vue'),
        meta: {hidden: false, title: '首页', orderNum: 0},
      }
    ]
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue') // 异步加载组件
  },
  {
    path: '/personal',
    name: 'Personal',
    component: Layout,
    meta: {hidden: false, title: '个人中心', orderNum: 300000},
    redirect: '/personal/index',
    children: [
      {
        path: '/personal/index',
        name: 'PersonalIndex',
        component: () => import('@/views/Personal/Personal.vue'),
        meta: {hidden: false, title: '个人设置', orderNum: 300000},
      }
    ]
  },
  {path: '/404', name: '404', component: () => import('../views/Error/404.vue')},
];

const router = createRouter({
  history: createWebHashHistory("/tz-admin"),
  routes
});

setupPermissionGuard(router);

export default router;
