<template>
  <router-view v-slot="{ Component }">
    <transition mode="out-in" name="fade-slide">
      <component :is="Component"/>
    </transition>
  </router-view>
</template>

<style lang="scss" scoped>
.fade-slide-enter-active {
  transition: opacity 0.3s ease-out;
}

.fade-slide-leave-active {
  transition: opacity 0.3s ease-out;
}

.fade-slide-enter-from {
  opacity: 0;
}

.fade-slide-leave-to {
  opacity: 0;
}
</style>
<script lang="ts" setup>
</script>
