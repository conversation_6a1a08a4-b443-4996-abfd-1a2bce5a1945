import {ElNotification} from "element-plus";

const showSuccessNotify = (message: string) => {
  ElNotification({
    type: 'success',
    title: '温馨提示',
    message: '操作成功'
  })
}

const showErrorNotify = (message: string) => {
  ElNotification({
    type: 'error',
    title: '温馨提示',
    message: message
  })
}

const showWarningNotify = (message: string) => {
  ElNotification({
    type: 'warning',
    title: '温馨提示',
    message: message
  })
}

export default {
  showSuccessNotify,
  showErrorNotify,
  showWarningNotify
}
