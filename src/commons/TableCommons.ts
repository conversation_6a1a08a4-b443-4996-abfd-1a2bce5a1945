import {computed, nextTick, ref} from "vue";
import type {TableData} from "./TableData.ts";
import {useHistoryStore, useUserStore} from "@/store";
import {useRoute} from "vue-router";
import {Ref} from "@vue/runtime-core";
import {createDialogModel, DialogMode} from "./DialogModel";

export function useTableCommons(search: Ref<any>, getTableData: Function) {
  /** 内部属性 start **/
  const route = useRoute()
  const historyStore = useHistoryStore()
  const userStore = useUserStore()
  const token = computed(() => {
    return userStore.token || ''
  })

  /**
   * 表格loading状态
   * */
  const loading = ref(false)
  /**
   * 表格数据
   */
  let tableData = ref<TableData>({
    records: [],
    total: 0,
    current: 1,
    size: 10,
    totalPage: 0
  })

  /**
   * 搜索提交
   */
  const searchSubmit = async () => {
    try {
      const path = route.path
      tableData.value.records = []
      if (historyStore.getHistory(path)) {
        const params = historyStore.getHistory(path)
        if (params) {
          search.value = params
        }
      }
      loading.value = true
      if (getTableData) {
        tableData.value = await getTableData()
      }
      historyStore.setHistory(route.path, search.value)
    } finally {
      loading.value = false
    }
  };
  /**
   * 分页改变
   * @param currentPage 当前页
   * @param pageSize 每页大小
   * */
  const pageOrSizeChange = async (currentPage: number, pageSize: number) => {
    search.value.pageNumber = currentPage
    search.value.pageSize = pageSize
    console.log('pageOrSizeChange', currentPage, pageSize)
    await searchSubmit()
  };

  /**
   * 表单对话框
   * */
  const dialogModel = ref(createDialogModel())

  /**
   * 重写保存完成事件
   * @param status
   */
  dialogModel.value.saveComplete = async (status: number) => {
    if (status === 1) {
      await nextTick(async () => {
        dialogModel.value.visible = false
        await searchSubmit()
      })
    }
    setTimeout(() => nextTick(() => dialogModel.value.saveLoading = false), 200)
  }

  /**
   * 重写关闭事件
   */
  dialogModel.value.closed = () => {
    // 重置所有属性
    dialogModel.value.title = '';
    dialogModel.value.visible = false;
    dialogModel.value.width = '600px';
    dialogModel.value.formName = '';
    dialogModel.value.mode = DialogMode.ADD;
    dialogModel.value.rowData = null;
    dialogModel.value.saveLoading = false;
  }

  /**
   * 显示表单对话框
   */
  const showFormDialog = (title: string = 'title',
                          formName: string = '',
                          rowData: any = {},
                          mode: DialogMode = DialogMode.ADD,
                          width: string = '600px') => {
    dialogModel.value.title = title
    dialogModel.value.formName = formName
    dialogModel.value.mode = mode
    dialogModel.value.rowData = rowData
    dialogModel.value.width = width
    nextTick(() => {
      dialogModel.value.visible = true
    })
  }

  return {
    token,
    loading,
    tableData,
    dialogModel,
    searchSubmit,
    pageOrSizeChange,
    showFormDialog
  };
}
