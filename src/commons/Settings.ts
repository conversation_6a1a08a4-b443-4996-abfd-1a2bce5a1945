import axios from "axios";

const settings = {
  siteTitle: '体质健康后台管理',
  securityStatus: false
}

export default {
  async install(Vue: any) {
    try {
      let url = import.meta.env.VITE_PROXY_PREFIX + '/shida-sso-center-api/securityStatus';
      const response = await axios.post(url)
      sessionStorage.setItem('securityStatus', response.data.data)
    } catch (error) {
      console.error(error);
    }
  },
  settings
}
