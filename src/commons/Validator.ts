export const validatorUsername = (rule: any, value: string, callback: any) => {
  if (!value) {
    return callback(new Error('用户名不能为空'))
  }
  const reg = /^[a-zA-Z0-9_-]{4,16}$/
  if (!reg.test(value)) {
    callback(new Error('用户名只能包含英文字母和数字，4-20个字符'))
  } else {
    callback()
  }
}

/**
 * 验证密码规则: 6-32位,必须包含字母、数字、与特殊符号,特殊符号包括: !@#$%^&*
 * @param rule
 * @param value
 * @param callback
 */
export const validatorPassword = (rule: any, value: string, callback: any) => {
  if (!value) {
    return callback(new Error('密码不能为空'))
  }
  const reg = /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[!@#$%^&*])[a-zA-Z\d!@#$%^&*]{6,32}$/
  if (!reg.test(value)) {
    callback(new Error('密码必须包含字母、数字、与特殊符号,特殊符号包括: !@#$%^&*'))
  } else {
    callback()
  }
}

/**
 * 电子邮箱验证规则
 * @param rule
 * @param value
 * @param callback
 */
export const validatorEmail = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback()
    return
  } else {
    const reg = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((\.[a-zA-Z0-9_-]{2,3}){1,2})$/
    if (!reg.test(value)) {
      callback(new Error('电子邮箱格式不正确'))
    } else {
      callback()
    }
  }
}

/**
 * 手机号码验证规则
 * @param rule
 * @param value
 * @param callback
 */
export const validatorMobile = (rule: any, value: string, callback: any) => {
  if (!value) {
    return callback()
  }
  const reg = /^1[3-9]\d{9}$/
  if (!reg.test(value)) {
    callback(new Error('手机号码格式不正确'))
  } else {
    callback()
  }
}

/**
 * 电话号码验证规则，包括座机与电话号码
 * @param rule
 * @param value
 * @param callback
 */
export const validateTelephone = (rule: any, value: string, callback: any) => {
  if (!value) {
    return callback()
  }
  // 更新正则表达式以支持座机和手机号码验证
  // 手机号码格式: 1XXXXXXXXXX (11位)
  // 座机号码格式: XXX-XXXXXXXX (3-4位区号 + 7-8位号码)
  const reg = /^1[3-9]\d{9}$|^0\d{2,3}-?\d{7,8}$/
  if (!reg.test(value)) {
    callback(new Error('电话号码格式不正确'))
  }
  callback()
}

/**
 * cron表达式验证规则
 * @param rule
 * @param value
 * @param callback
 */
export const validatorCron = (rule: any, value: string, callback: any) => {
  if (!value) {
    return callback()
  }
  const reg = /^(\*|([0-9]|1[0-9]|2[0-9]|3[0-9]|4[0-9]|5[0-9])|\*\/([0-9]|1[0-9]|2[0-9]|3[0-9]|4[0-9]|5[0-9])) (\*|([01]?\d|2[0-3])|\*\/([01]?\d|2[0-3])) (\*|([1-9]|[12][0-9]|3[01])|\*\/([1-9]|[12][0-9]|3[01])) (\*|([1-9]|1[0-2])|\*\/([1-9]|1[0-2])|(JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|OCT|NOV|DEC)) (\*|([0-7])|\*\/([0-7])|(SUN|MON|TUE|WED|THU|FRI|SAT))(\s+(\*|([19][0-9]{2})|([2][0-9]{3})))?$/;

  if (!reg.test(value)) {
    callback(new Error('cron表达式格式不正确'))
  } else {
    callback()
  }
}
export const validatorIdCard = (rule: any, value: string, callback: any) => {
  if (!value) {
    return callback()
  }
  if (value.length !== 18) {
    return callback(new Error('身份证号格式不正确'))
  }
  const reg = /^\d{17}[\dXx]|\d{15}$/
  if (!reg.test(value)) {
    callback(new Error('身份证号格式不正确'))
  } else {
    callback()
  }
  let codeArr = value.split(''); // 将身份证号码字符串转换为字符数组
  let factorArr = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]; // 加权因子
  let checkSumArr = [1, 0, 'X', 9, 8, 7, 6, 5, 4, 3, 2]; // 校验码对应值
  let sum = 0;
  for (let i = 0; i < factorArr.length; i++) {
    sum += parseInt(codeArr[i]) * factorArr[i]; // 加权求和
  }
  let checkDigit = checkSumArr[sum % 11]; // 计算校验码
  if (codeArr[17].toUpperCase() !== String(checkDigit).toUpperCase()) {
    callback(new Error('身份证号校验码错误'))
  }

}
