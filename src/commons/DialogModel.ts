export enum DialogMode {
  ADD = 'ADD',
  EDIT = 'EDIT',
  IMPORT = 'IMPORT',
  AUDIT = 'AUDIT',
  DETAIL = 'DETAIL'
}

export interface DialogModel {
  /**
   * 对话框的标题
   */
  title: string,

  /**
   * 对话框的可见性状态
   */
  visible: boolean,

  /**
   * 对话框的宽度
   */
  width: string,

  /**
   * 表单的名称
   */
  formName: string,

  /**
   * 对话框的模式（例如：'ADD', 'EDIT', 'IMPORT' ,'AUDIT'）
   */
  mode: DialogMode | null,

  /**
   * 行数据对象，包含要编辑的数据
   */
  rowData: object | null,

  /**
   * 保存操作的加载状态
   */
  saveLoading: boolean,

  /**
   * 保存操作完成后的回调函数
   */
  saveComplete: (status: number) => void,

  /**
   * 对话框关闭后的回调函数
   */
  closed: () => void
}

export const createDialogModel = () => {
  const result: DialogModel = ({
    title: '',
    visible: false,
    width: '600px',
    formName: '',
    mode: DialogMode.ADD,
    rowData: () => null,
    saveLoading: false,
    saveComplete: (status: number) => {
    },
    closed: () => {
      // 重置所有属性
      result.title = '';
      result.visible = false;
      result.width = '600px';
      result.formName = '';
      result.mode = DialogMode.ADD;
      result.rowData = null;
      result.saveLoading = false;
    }
  })
  return result
}
