<script lang="ts" setup>
import {computed, onMounted, ref} from "vue";
import {apis} from "@/apis";
import {useTableCommons} from "@/commons/TableCommons";
import {DialogMode} from "@/commons/DialogModel";
import DetailForm from "./components/OperateDetail.vue";
import Notification from "@/commons/Notification";

const search = ref({
  pageNumber: 1,
  pageSize: 10,
  keywords: "",
  operateType: "",
  startDateTime: "",
  endDateTime: ""
})

const operateTypeList = ref([
  {key: 301, value: '新增'},
  {key: 302, value: '删除'},
  {key: 303, value: '修改'},
  {key: 304, value: '查询'},
  {key: 305, value: '导入'},
  {key: 306, value: '导出'},
  {key: 307, value: '上传文件'},
  {key: 400, value: '未知'},
])

const operateTypeName = computed(() => {
  return (operateType: number) => {
    const operateTypeTarget = operateTypeList.value.find(item => item.key === operateType)
    return operateTypeTarget?.value
  }
})

const getTableData = async () => {
  const response = await apis.LogManage.getPageData(search.value)
  return response.data
}

const handleExport = async () => {
    await apis.LogManage.exportOperateLog(search.value)
}


let {
  loading,
  dialogModel,
  showFormDialog,
  tableData,
  searchSubmit,
  pageOrSizeChange
} = useTableCommons(search, getTableData)

onMounted(async () => {
  await searchSubmit()
})
</script>

<template>
  <div>
    <div class="lmt-operation-container">
      <div class="lmt-operation">
        <el-button v-has-perm="['operate:log:export']" icon="Download" type="info" @click="handleExport">导出</el-button>
      </div>
      <div class="lmt-search">
        <el-date-picker v-model="search.startDateTime" format="YYYY-MM-DD HH:mm:ss" placeholder="开始时间"
                        type="datetime"
                        value-format="YYYY-MM-DD HH:mm:ss"/>
        <el-date-picker v-model="search.endDateTime" format="YYYY-MM-DD HH:mm:ss" placeholder="结束时间" type="datetime"
                        value-format="YYYY-MM-DD HH:mm:ss"/>
        <el-select v-model="search.operateType" clearable placeholder="操作类别">
          <el-option v-for="item in operateTypeList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <el-input v-model="search.keywords" clearable placeholder="操作账号">
          <template #append>
            <el-button icon="Search" @click="searchSubmit"></el-button>
          </template>
        </el-input>
      </div>
    </div>
    <el-table v-loading="loading" :data="tableData.records" border
              max-height="calc(100vh - var(--lmt-header-height) - var(--lmt-main-padding) * 2 - 6rem)"
              stripe>
      <el-table-column align="center" label="序号" type="index" width="80">
        <template #default="scope">
          {{ (search.pageNumber - 1) * search.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="操作账号" min-width="140" prop="username"></el-table-column>
      <el-table-column label="角色" min-width="120" prop="roleName"></el-table-column>
      <el-table-column label="接口地址" min-width="220" prop="requestUrl" show-overflow-tooltip></el-table-column>
      <el-table-column label="所属区县" prop="deptName" width="120"></el-table-column>
      <el-table-column label="操作类别" prop="operateType" width="120">
        <template #default="{ row }">
          {{ operateTypeName(row.operateType) }}
        </template>
      </el-table-column>
      <el-table-column label="操作详细内容" min-width="120" prop="logContent"></el-table-column>
      <el-table-column label="操作ip" min-width="120" prop="ip"></el-table-column>
      <el-table-column label="时间" min-width="160" prop="createTime"></el-table-column>
      <el-table-column fixed="right" label="操作" min-width="100">
        <template #default="scope">
          <el-link icon="View" type="primary"
                   @click="showFormDialog('详情', 'DetailForm', scope.row, DialogMode.DETAIL, '800px')">详情
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-model:current-page="search.pageNumber" v-model:page-size="search.pageSize"
                   :page-sizes="[10, 20, 30, 40, 50, 100]" :total="Number(tableData.total)"
                   background
                   class="m-t-4 float-right" layout="total, prev, pager, next, sizes, ->" @change="pageOrSizeChange">
    </el-pagination>
    <el-dialog v-model="dialogModel.visible" :title="dialogModel.title" :width="dialogModel.width" destroy-on-close
               @closed="dialogModel.closed()">
      <DetailForm v-if="dialogModel.formName == 'DetailForm'" ref="detailForm" :mode="dialogModel.mode"
                  :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <template #footer>
        <el-button @click="dialogModel.visible = false">
          关 闭
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped></style>
