<script lang="ts" setup>
import {onMounted, ref} from 'vue';
import VueJsonPretty from 'vue-json-pretty';
import 'vue-json-pretty/lib/styles.css';

const formData = ref({
  requestParam: {},
  responseResult: {},
  requestUrl: ''
})

const props = defineProps({
  rowData: {
    type: [Object, null],
    default: () => {
    }
  },
  mode: {
    type: [String, null],
    default: 'DETAIL'
  }
})

const isJSON = (str: any) => {
  try {
    JSON.parse(str);
  } catch (e) {
    return false;
  }
  return true;
}

const isJSONObj = ref({
  requestParam: true,
  responseResult: true
})

onMounted(async () => {
  formData.value.requestUrl = props.rowData?.requestUrl
  if (props.rowData) {
    if (isJSON(props.rowData.requestParam)) {
      formData.value.requestParam = JSON.parse(props.rowData.requestParam)
      isJSONObj.value.requestParam = true
    } else {
      formData.value.requestParam = props.rowData.requestParam
      isJSONObj.value.requestParam = false
    }

    if (isJSON(props.rowData.responseResult)) {
      formData.value.responseResult = JSON.parse(props.rowData.responseResult)
      isJSONObj.value.responseResult = true
    } else {
      formData.value.responseResult = props.rowData.responseResult
      isJSONObj.value.responseResult = false
    }
  }
})

</script>

<template>
  <div class="form-container">
    <el-form ref="form" :model="formData" label-width="auto">
      <el-row :gutter="40">
        <el-col :span="24">
          <el-form-item label="请求地址：">
            <el-text type="primary">{{ formData.requestUrl }}</el-text>
          </el-form-item>
          <el-form-item label="请求参数：">
            <vue-json-pretty v-if="isJSONObj.requestParam" :data="formData.requestParam" :deep="3"/>
            <el-text v-else>{{ formData.requestParam }}</el-text>
          </el-form-item>
          <el-form-item label="返回参数：">
            <vue-json-pretty v-if="isJSONObj.responseResult" :data="formData.responseResult" :deep="3"/>
            <el-text v-else>{{ formData.responseResult }}</el-text>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>
.vjs-tree {
  padding-top: 6px;
}
</style>
