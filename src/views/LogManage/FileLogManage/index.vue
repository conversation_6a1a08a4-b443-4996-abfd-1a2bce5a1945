<script lang="ts" setup>
import {onMounted, ref} from "vue";
import {apis} from "@/apis";
import {useTableCommons} from "@/commons/TableCommons";

const search = ref({
  pageNumber: 1,
  pageSize: 10,
  username: "",
  filename: "",
  startDateTime: "",
  endDateTime: ""
})

const getTableData = async () => {
  const response = await apis.LogManage.getFileLogPageData(search.value)
  return response.data
}


let {
  loading,
  tableData,
  searchSubmit,
  pageOrSizeChange
} = useTableCommons(search, getTableData)

onMounted(async () => {
  await searchSubmit()
})


const handleExport = async () => {
  await apis.LogManage.exportOperateLog(search.value)
}

</script>

<template>
  <div>
    <div class="lmt-operation-container">
      <div class="lmt-operation">
        <el-button v-has-perm="['file:log:export']" icon="Download" type="info" @click="handleExport">导出</el-button>
      </div>
      <div class="lmt-search">
        <el-date-picker v-model="search.startDateTime" format="YYYY-MM-DD HH:mm:ss" placeholder="开始时间"
                        type="datetime"
                        value-format="YYYY-MM-DD HH:mm:ss"/>
        <el-date-picker v-model="search.endDateTime" format="YYYY-MM-DD HH:mm:ss" placeholder="结束时间" type="datetime"
                        value-format="YYYY-MM-DD HH:mm:ss"/>
        <el-input v-model="search.filename" clearable placeholder="文件名称"></el-input>
        <el-input v-model="search.username" clearable placeholder="操作账号">
          <template #append>
            <el-button icon="Search" @click="searchSubmit"></el-button>
          </template>
        </el-input>
      </div>
    </div>
    <el-table v-loading="loading" :data="tableData.records" border max-height="calc(100vh - var(--lmt-header-height) - var(--lmt-main-padding) * 2 - 6rem)"
              stripe>
      <el-table-column align="center" label="序号" type="index" width="80">
        <template #default="scope">
          {{ (search.pageNumber - 1) * search.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="文件id" min-width="180" prop="id"></el-table-column>
      <el-table-column label="原文件名称" min-width="100" prop="name" show-overflow-tooltip></el-table-column>
      <el-table-column label="服务器上文件名称" min-width="100" prop="serverFileName"
                       show-overflow-tooltip></el-table-column>
      <el-table-column label="url" min-width="160" prop="url" show-overflow-tooltip></el-table-column>
      <el-table-column label="上传时间" min-width="160" prop="createTime"></el-table-column>
      <el-table-column label="上传用户ID" min-width="100" prop="userId"></el-table-column>
      <el-table-column label="上传用户名称" min-width="130" prop="username" show-overflow-tooltip></el-table-column>
      <el-table-column label="部门编码" min-width="100" prop="deptCode"></el-table-column>
    </el-table>
    <el-pagination v-model:current-page="search.pageNumber" v-model:page-size="search.pageSize"
                   :page-sizes="[10, 20, 30, 40, 50, 100]" :total="Number(tableData.total)"
                   background
                   class="m-t-4 float-right" layout="total, prev, pager, next, sizes, ->" @change="pageOrSizeChange">
    </el-pagination>
  </div>
</template>

<style lang="scss" scoped></style>
