<script lang="ts" setup>
import {computed, onMounted, ref} from 'vue'
import {DialogMode} from "@/commons/DialogModel";
import {apis} from "@/apis";
import {ElMessageBox, ElNotification} from "element-plus";
import type { KeyValue } from "@/commons/KeyValue";

const formData = ref({
  id: '',
  userId: '',
  xm: '',
  userName: '',
  regionId: '',
  regionName: '',
  schoolId: '',
  sfzh: '',
  xjh: '',
  period: '',
  sjhm: '',
  xb: '',
  gradeId: '',
  gradeName: '',
  clazzId: '',
  clazzName: '',
  jtzz: '',
  schoolName: '',
  status: 1,
  zhzt:'',
  studentStatus: '',
  defaultPasswordFlag: false
})

const props = defineProps({
  rowData: {
    type: [Object, null],
    default: () => {
    }
  },
  mode: {
    type: [String, null],
    default: 'DETAIL'
  }
})

const schoolList = ref<Array<KeyValue>>([])

const studentStatusList = ref([
  {key: 1, value: '在读'},
  {key: 2, value: '毕业'},
  {key: 3, value: '休学'},
])
const SchoolPeriod = ref([
  {key: 1, value: '小学'},
  {key: 2, value: '初中'},
  {key: 3, value: '高中'},
  {key: 4, value: '九年一贯制'},
  {key: 5, value: '十二年一贯制'},
  {key: 6, value: '初高连读'}
])
onMounted(async () => {
  if (props.mode === DialogMode.DETAIL) {
    const {
      id,
      userId,
      xm,
      userName,
      regionId,
      regionName,
      schoolId,
      sfzh,
      xjh,
      period,
      sjhm,
      xb,
      gradeId,
      gradeName,
      clazzId,
      clazzName,
      jtzz,
      schoolName,
      status,
      zhzt,
      studentStatus,
      defaultPasswordFlag
    } = props.rowData as any
    formData.value = {id,
      userId,
      xm,
      userName,
      regionId,
      regionName,
      schoolId,
      sfzh,
      xjh,
      period,
      sjhm,
      xb,
      gradeId,
      gradeName,
      clazzId,
      clazzName,
      jtzz,
      schoolName,
      status,
      zhzt,
      studentStatus,
      defaultPasswordFlag}
    schoolList.value = (await apis.Commons.getSchoolSelectListCounty(formData.value.regionId)).data;
  }
})

// 定义组件可以发出的事件
// const emit = defineEmits(['closeDetail','searchSubmit'])

// const searchSubmit = async () => {
//   emit('searchSubmit')
//   closeDetail()
// }
// 定义其他按钮的方法（空实现，需要根据实际需求补充）
// const edit = (id:any,regionId:any) => {
//   toEdit(id,regionId)
// }

</script>

<template>
  <div class="form-container">
    <el-form ref="form" :model="formData" label-width="auto">
      <el-row :gutter="40">
        <el-col :span="10" :offset="2">
          <el-form-item label="学生姓名：" prop="xm">
            <el-text>{{ formData.xm }}</el-text>
          </el-form-item>
          <el-form-item label="学生账号：" prop="userName">
            <el-text>{{ formData.userName }}</el-text>
          </el-form-item>
          <el-form-item label="身份证号:" prop="sfzh">
            <el-text>{{ formData.sfzh }}</el-text>
          </el-form-item>
          <el-form-item label="学籍号：" prop="xjh">
            <el-text>{{ formData.xjh }}</el-text>
          </el-form-item>
          <el-form-item label="学段：" prop="peroid">
            <el-text>{{ SchoolPeriod.find(item => item.key == Number(formData.period))?.value }}</el-text>
          </el-form-item>
          <el-form-item label="性别：" prop="xb">
            <el-text v-if="formData.xb == '1'">男</el-text>
            <el-text v-else-if="formData.xb == '2'">女</el-text>
            <el-text v-else>未知</el-text>
          </el-form-item>
          <el-form-item label="家长手机号：" prop="sjhm">
            <el-text>{{ formData.sjhm }}</el-text>
          </el-form-item>
        </el-col>
          <el-col :span="10">
          <el-form-item label="年级：" prop="gradeName">
            <el-text>{{ formData.gradeName }}</el-text>
          </el-form-item>
          <el-form-item label="班级：" prop="className">
            <el-text>{{ formData.clazzName }}</el-text>
          </el-form-item>
          <el-form-item label="家庭住址：" prop="jtzz">
            <el-text>{{ formData.jtzz }}</el-text>
          </el-form-item>
          <el-form-item label="所属区县：" prop="regionName">
            <el-text>{{ formData.regionName }}</el-text>
          </el-form-item>
          <el-form-item label="所属学校：" prop="schoolName">
            <el-text>{{ formData.schoolName }}</el-text>
          </el-form-item>
          <el-form-item label="学生状态：" prop="studentStatus">
            <el-text>{{ studentStatusList.find(item => item.key === Number(formData.studentStatus))?.value || '未知' }}</el-text>
          </el-form-item>
          <el-form-item label="账号状态：" prop="status">
            <el-text v-if="formData.status">正常</el-text>
            <el-text v-else type="danger">冻结</el-text>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
