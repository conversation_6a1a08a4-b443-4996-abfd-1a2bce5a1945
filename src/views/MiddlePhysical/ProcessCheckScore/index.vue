<script lang="ts" setup>
import {onMounted, ref, watch} from "vue";
import {apis} from "@/apis";
import {useTableCommons} from "@/commons/TableCommons";
import {useDeptStore, useUserStore} from '@/store'
import {DialogMode} from "@/commons/DialogModel";
import SchoolPeriodSelector from "@/components/SchoolPeriodSelector/index.vue"
import DetailForm from "./components/Detail.vue";
import type { KeyValue } from "@/commons/KeyValue";


const userStore = useUserStore()
let currentRegionId = userStore.regionId || '130102'
const deptStore = useDeptStore()

if (deptStore.topDept?.id == deptStore.currentDept?.id) {
  currentRegionId = '130102'
}

const search = ref({
  pageNumber: 1,
  pageSize: 10,
  keywords: "",
  regionId: currentRegionId,
  schoolId: "",
  period: "",
  gradeId: "",
  clazzId: "",
  studentStatus: ""
})

const gradeList = ref<Array<KeyValue>>([])

// const studentStatusList = ref([
//   {key: 1, value: '在读'},
//   {key: 2, value: '毕业'},
//   {key: 3, value: '休学'},
// ])

const getTableData = async () => {
  const response = await apis.StudentManage.getPageData(search.value, search.value.regionId)
  for (let i = 0; i < response.data.records.length; i++) {
    const item = response.data.records[i];
    item.defaultPasswordView = "********";
    item.showPasswordView = true;
  }
  return response.data
}

const regionList = ref<any>(useDeptStore().regionList)
const schoolList = ref<Array<KeyValue>>([])
const clazzList = ref<Array<KeyValue>>([])

let {
  loading,
  dialogModel,
  showFormDialog,
  tableData,
  searchSubmit,
  pageOrSizeChange
} = useTableCommons(search, getTableData)

onMounted(async () => {
  if (userStore.userInfo?.cityAdminFlag) {
    await useDeptStore().setRegionList();
    regionList.value = useDeptStore().regionList;
  } else {
    regionList.value = [{key: currentRegionId, value: userStore.userInfo?.regionName}]
  }
  gradeList.value = (await apis.Commons.getGradeSelectList()).data;
  await regionChange()
  await searchSubmit()
})
watch(() => search.value.regionId,
  (newValues, oldValues) => {
    if (newValues != oldValues) {
      currentRegionId = newValues
      regionChange();
    }
  });

const regionChange = async () => {
  search.value.schoolId = ''
  schoolList.value = (await apis.Commons.getSchoolSelectListCounty(search.value.regionId)).data;
}

// 获取班级列表
const getClazzList = async () => {
  search.value.clazzId = ''
  clazzList.value = []
  if(search.value.schoolId && search.value.gradeId && search.value.period) {
    clazzList.value = (await apis.Commons.getClazzSelectList(search.value.schoolId, search.value.gradeId, search.value.period, search.value.regionId)).data;
  }
}

// 表单部分
const saveSubmit = async () => {
  dialogModel.value.saveLoading = true
}

const closeDetail = () => {
  dialogModel.value.visible = false
}
</script>

<template>
  <div>
    <div class="lmt-operation-container">
      <div class="lmt-operation">
        <el-button v-has-perm="['checkScore:export']" type="info" icon="Download" @click="apis.StudentManage.export(search, currentRegionId)">导出</el-button>
      </div>
      <div class="lmt-search">
        <el-select v-model="search.regionId" :clearable="false" placeholder="请选择所属区县" @change="regionChange"
                   v-if="!(userStore.userInfo?.countyAdminFlag || userStore.userInfo?.schoolAdminFlag)">
          <el-option v-for="item in regionList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <el-select v-model="search.schoolId" clearable placeholder="所属学校" @change="getClazzList"
                   v-if="!userStore.userInfo?.schoolAdminFlag">
          <el-option v-for="item in schoolList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <SchoolPeriodSelector v-model="search.period" show-type="select" from="student" @change="getClazzList"></SchoolPeriodSelector>
        <el-select v-model="search.gradeId" clearable placeholder="年级" @change="getClazzList">
          <el-option v-for="item in gradeList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <el-select v-model="search.clazzId" clearable placeholder="班级">
          <el-option v-for="item in clazzList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
<!--        <el-select v-model="search.studentStatus" clearable placeholder="学生状态">-->
<!--          <el-option v-for="item in studentStatusList" :key="item.key" :label="item.value"-->
<!--                     :value="item.key"></el-option>-->
<!--        </el-select>-->
        <el-input v-model="search.keywords" clearable placeholder="学生姓名/学籍号/身份证号" @clear="searchSubmit">
          <template #append>
            <el-button icon="Search" @click="searchSubmit"></el-button>
          </template>
        </el-input>
      </div>
    </div>
    <el-table v-loading="loading" :data="tableData.records" border max-height="calc(100vh - var(--lmt-header-height) - var(--lmt-main-padding) * 2 - 6rem)"
              stripe>
<!--      :cell-style="{ textAlign: 'center' }" :header-cell-style="{ textAlign: 'center' }"-->
      <el-table-column align="center" label="序号" type="index" width="80" fixed>
        <template #default="scope">
          {{ (search.pageNumber - 1) * search.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="姓名" min-width="160" prop="xm" fixed header-align="center"></el-table-column>
      <el-table-column label="学籍号" min-width="170" prop="xjh"></el-table-column>
      <el-table-column label="性别" min-width="60" prop="xb">
        <template #default="{row}">
          {{row.xb && row.xb == 1 ? '男' : row.xb == 2 ? '女' : ''}}
        </template>
      </el-table-column>
      <el-table-column label="所属班级" min-width="120" prop="clazzName"></el-table-column>
      <el-table-column label="所属学校" min-width="120" prop="schoolName"></el-table-column>
      <el-table-column label="所属区县" min-width="120" prop="regionName"></el-table-column>
      <el-table-column label="是否伤病残免试" min-width="130" prop="xb">
        <template #default="{row}">
          {{row.sbc && row.sbc == 1 ? '是' : row.sbc == 0 ? '否' : ''}}
        </template>
      </el-table-column>
      <el-table-column label="七年级下学期">
        <el-table-column label="日常体育锻炼" width="110" prop="7gradeRctydl"></el-table-column>
        <el-table-column label="体育与健康课" width="110" prop="7gradeTyyjkk"></el-table-column>
      </el-table-column>
      <el-table-column label="八年级上学期">
        <el-table-column label="日常体育锻炼" width="110" prop="8gradeRctydl"></el-table-column>
        <el-table-column label="体质健康标准测试" width="140" prop="8gradeTzjkbzcs"></el-table-column>
        <el-table-column label="体育与健康课" width="110" prop="8gradeTyyjkk"></el-table-column>
      </el-table-column>
      <el-table-column label="八年级下学期">
        <el-table-column label="日常体育锻炼" width="110" prop="8gradeRctydl"></el-table-column>
        <el-table-column label="体育与健康课" width="110" prop="8gradeTyyjkk"></el-table-column>
      </el-table-column>
      <el-table-column label="九年级上学期">
        <el-table-column label="日常体育锻炼" width="110" prop="9gradeRctydl"></el-table-column>
        <el-table-column label="体质健康标准测试" width="140" prop="9gradeTzjkbzcs"></el-table-column>
        <el-table-column label="体育与健康课" width="110" prop="9gradeTyyjkk"></el-table-column>
      </el-table-column>
      <el-table-column label="总分" min-width="120" prop="totalScore"></el-table-column>
      <el-table-column fixed="right" label="操作" min-width="150">
        <template #default="scope">
          <el-link icon="View" type="primary"
                   @click="showFormDialog('学生档案', 'DetailForm', scope.row, DialogMode.DETAIL, '800px')">查看档案
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-model:current-page="search.pageNumber" v-model:page-size="search.pageSize"
                   :page-sizes="[10, 20, 30, 40, 50, 100]" :total="Number(tableData.total)"
                   background
                   class="m-t-4 float-right" layout="total, prev, pager, next, sizes, ->" @change="pageOrSizeChange">
    </el-pagination>
    <el-dialog v-model="dialogModel.visible" :title="dialogModel.title" :width="dialogModel.width" destroy-on-close
               @closed="dialogModel.closed()">
      <DetailForm v-if="dialogModel.formName == 'DetailForm'" ref="detailForm" :mode="dialogModel.mode"
                  @closeDetail="closeDetail" :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <template #footer>
        <el-button @click="dialogModel.visible = false">
          关 闭
        </el-button>
        <el-button v-if="dialogModel.formName !== 'DetailForm'" :loading="dialogModel.saveLoading" type="primary"
                   @click="saveSubmit">确 认
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped></style>
