<script setup lang="ts">
import {ref, onMounted} from "vue"
import { policyTpList } from "@/views/MiddlePhysical/PolicyManage/dic"
import {apis} from "@/apis"
import SdWangEditor from "@/components/SdWangEditor/SdWangEditor.vue"
import {useUserStore} from '@/store'

const userStore = useUserStore()
let currentRegionId = userStore.regionId

const formData = ref({
  serialNum: "",
  title: "",
  policyTp: "",
  version: "",
  range: "",
  expireDate: "",
  content: "",
  status: "",
  createUser: "",
  updateTime: ""
})

// props
const props = defineProps({
  mode: {
    type: [String, null],
    default: 'EDIT'
  },
  rowData: {
    type: [Object, null],
    default: () => {}
  }
})

const formRef = ref()

// 表单验证规则
const rules = {
  serialNum: [{required: true, message: '请输入政策编号', trigger: 'blur'}],
  title: [{required: true, message: '请输入政策标题', trigger: 'blur'}],
}

// loading
const dataLoading = ref(false)

// 生命周期
onMounted(() => {
  // 新增
  if (props.mode === 'ADD') {

  } else if (props.mode === 'EDIT') {

  }
})

// 保存方法
const saveSubmit = async () => {

}

// 可用日期: 只能选择今天及以后
const availDate = (time: any) => {
  return time.getTime() < Date.now() - 8.64e7
}

defineExpose({
  saveSubmit
})
</script>

<template>
  <div class="policy-add">
    <el-form ref="formRef" :model="formData" v-loading="dataLoading" label-width="100px" :rules="rules">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item prop="serialNum" label="政策编号">
            <el-input v-model="formData.serialNum" placeholder="请输入政策编号" maxlength="20" show-word-limit></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="title" label="政策标题">
            <el-input v-model="formData.title" placeholder="请输入政策标题" maxlength="20" show-word-limit></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="policyTp" label="政策类别">
            <el-select v-model="formData.policyTp" clearable placeholder="请选择政策类别">
              <el-option v-for="item in policyTpList" :key="item.key" :label="item.value" :value="item.key"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item prop="version" label="版本号">
            <el-input v-model="formData.version" placeholder="请输入版本号"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="range" label="发布范围">
            <el-input v-model="formData.range" placeholder="请选择发布范围"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="expireDate" label="失效日期">
            <el-date-picker
              v-model="formData.expireDate"
              type="date"
              placeholder="请选择失效日期"
              value-format="YYYY-MM-DD"
              :disabled-date="availDate"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item style="height: 400px" label="正文">
        <SdWangEditor v-model="formData.content" :region-id="currentRegionId" service-url-prefix="/region-service" placeholder="请输入正文..." style="width: 100%" />
      </el-form-item>
    </el-form>
  </div>
</template>

<style scoped lang="scss">

</style>
