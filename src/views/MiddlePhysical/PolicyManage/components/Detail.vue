<script setup lang="ts">
import {ref, onMounted} from "vue"
import { policyStList } from "@/views/MiddlePhysical/PolicyManage/dic"

const policyDetail = ref({
  serialNum: "8943543895",
  title: "是是是",
  policyTp: "类型",
  version: "0.0.0",
  range: "全部",
  expireDate: "2025-08-08 12:31:21",
  content: "<h1>123123123</h1><p>123324</p>",
  status: "废止",
  createUser: "就等",
  updateTime: "2025-08-08 12:31:21"
})

// 生命周期
onMounted(async () => {

})
</script>

<template>
  <div class="policy-detail">
    <ul class="info-list">
      <li>
        <span class="desc-txt">政策编号：</span>
        <span class="result-txt">{{ policyDetail.serialNum }}</span>
      </li>
      <li>
        <span class="desc-txt">政策标题：</span>
        <span class="result-txt">{{ policyDetail.title }}</span>
      </li>
      <li>
        <span class="desc-txt">政策类别：</span>
        <span class="result-txt">{{ policyDetail.policyTp }}</span>
      </li>
      <li>
        <span class="desc-txt">版本号：</span>
        <span class="result-txt">{{ policyDetail.version }}</span>
      </li>
      <li>
        <span class="desc-txt">发布范围：</span>
        <span class="result-txt">{{ policyDetail.range }}</span>
      </li>
      <li>
        <span class="desc-txt">失效日期：</span>
        <span class="result-txt">{{ policyDetail.expireDate }}</span>
      </li>
    </ul>
    <el-divider border-style="dashed"><el-text type="info">正文</el-text></el-divider>
    <div class="content" v-html="policyDetail.content"></div>
    <el-divider border-style="dashed"></el-divider>
    <ul class="info-list">
      <li>
        <span class="desc-txt">状态：</span>
        <span class="result-txt">
          <el-tag type="primary" v-if="policyDetail.status == policyStList[0].key">{{ policyStList[0].value }}</el-tag>
          <el-tag type="success" v-else-if="policyDetail.status == policyStList[1].key">{{ policyStList[1].value }}</el-tag>
          <el-tag type="danger" v-else-if="policyDetail.status == policyStList[2].key">{{ policyStList[2].value }}</el-tag>
        </span>
      </li>
      <li>
        <span class="desc-txt">创建人：</span>
        <span class="result-txt">{{ policyDetail.createUser }}</span>
      </li>
      <li>
        <span class="desc-txt">更新时间：</span>
        <span class="result-txt">{{ policyDetail.updateTime }}</span>
      </li>
    </ul>
  </div>
</template>

<style scoped lang="scss">
.policy-detail {
  .content {
    margin: 15px 0;
  }
  .info-list {
    & > li {
      flex: 0 0 33.3333%
    }
    .desc-txt {
      flex: 0 0 70px;
      text-align: right;
    }
  }
}
</style>
