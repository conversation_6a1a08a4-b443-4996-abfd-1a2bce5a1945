<script setup lang="ts">
import {ref, onMounted} from "vue"
import AddForm from "@/views/MiddlePhysical/PolicyManage/components/Add.vue"
import DetailForm from "@/views/MiddlePhysical/PolicyManage/components/Detail.vue"
import {useTableCommons} from "@/commons/TableCommons"
import {DialogMode} from "@/commons/DialogModel"
import {apis} from "@/apis"
import { policyTpList, policyStList } from "@/views/MiddlePhysical/PolicyManage/dic"
import {ElMessageBox, ElNotification} from "element-plus"

// 主列表
const getTableData = async () => {
  const response = await apis.RegionManage.getPageData(search.value)
  return response.data
}

// 主列表搜索条件
const search = ref({
  pageNumber: 1,
  pageSize: 10,
  keywords: "",
  policyTp: "",
  status: ""
})

// 通用配置
let {
  loading,
  dialogModel,
  showFormDialog,
  tableData,
  searchSubmit,
  pageOrSizeChange
} = useTableCommons(search, getTableData)

// 弹窗内按钮方法
const addForm = ref()
const editForm = ref()
const saveSubmit = () => {
  dialogModel.value.saveLoading = true
  if (dialogModel.value.formName === 'AddForm') {
    addForm.value.saveSubmit()
  } else if (dialogModel.value.formName === 'EditForm') {
    editForm.value.saveSubmit()
  }
}

// 生命周期
onMounted( () => {
  searchSubmit()
})

// 发布
const pub = (row: any) => {
  console.log(row)
  ElMessageBox.confirm(`确认发布【${row.xm}】吗？`, "温馨提示",
    {confirmButtonText: '确定', cancelButtonText: '取消', type: 'error'})
    .then(async () => {
      await apis.TeacherManage.delete(row.id)
      ElNotification.success({message: "操作成功", title: "温馨提示"})
      await searchSubmit()
    })
}

// 删除
const del = (row: any) => {
  console.log(row)
  ElMessageBox.confirm(`确认删除【${row.xm}】吗？`, "温馨提示",
    {confirmButtonText: '确定', cancelButtonText: '取消', type: 'error'})
    .then(async () => {
      await apis.TeacherManage.delete(row.id)
      ElNotification.success({message: "操作成功", title: "温馨提示"})
      await searchSubmit()
    })
}

// 废止
const end = (row: any) => {
  console.log(row)
  ElMessageBox.confirm(`确认废止【${row.xm}】吗？`, "温馨提示",
    {confirmButtonText: '确定', cancelButtonText: '取消', type: 'error'})
    .then(async () => {
      await apis.TeacherManage.delete(row.id)
      ElNotification.success({message: "操作成功", title: "温馨提示"})
      await searchSubmit()
    })
}

</script>

<template>
  <div class="policy-manage">
    <div class="lmt-operation-container">
      <div class="lmt-operation">
        <el-button icon="Plus" type="primary"
                   @click="showFormDialog('编辑','AddForm', {  }, DialogMode.ADD, '1000px')">添加
        </el-button>
      </div>
      <div class="lmt-search">
        <el-select v-model="search.policyTp" clearable placeholder="政策类别">
          <el-option v-for="item in policyTpList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <el-select v-model="search.status" clearable placeholder="状态">
          <el-option v-for="item in policyStList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <el-input v-model="search.keywords" placeholder="政策标题" class="name-filter" :clearable="true">
          <template #append>
            <el-button icon="Search" @click="searchSubmit"></el-button>
          </template>
        </el-input>
      </div>
    </div>
    <el-table v-loading="loading" :data="tableData.records" border
              max-height="calc(100vh - var(--lmt-header-height) - var(--lmt-main-padding) * 2 - 3rem - var(--lmt-pager-height)"
              stripe>
      <el-table-column align="center" fixed="left" label="序号" type="index" width="80">
        <template #default="scope">
          {{ (search.pageNumber - 1) * search.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="政策编号" min-width="100" prop="name" show-overflow-tooltip></el-table-column>
      <el-table-column label="政策标题" min-width="100" prop="name" show-overflow-tooltip></el-table-column>
      <el-table-column label="政策类别" min-width="100" prop="name" show-overflow-tooltip></el-table-column>
      <el-table-column label="版本号" min-width="100" prop="name" show-overflow-tooltip></el-table-column>
      <el-table-column label="状态" min-width="100" prop="name" show-overflow-tooltip></el-table-column>
      <el-table-column label="发布范围" min-width="100" prop="name" show-overflow-tooltip></el-table-column>
      <el-table-column label="发布时间" min-width="100" prop="name" show-overflow-tooltip></el-table-column>
      <el-table-column label="失效日期" min-width="100" prop="name" show-overflow-tooltip></el-table-column>
      <el-table-column label="创建人" min-width="100" prop="name" show-overflow-tooltip></el-table-column>
      <el-table-column label="更新时间" min-width="100" prop="name" show-overflow-tooltip></el-table-column>
      <el-table-column label="操作" width="280">
        <template #default="scope">
          <el-link icon="InfoFilled" type="primary"
                   @click="showFormDialog('政策详情','DetailForm',scope.row,DialogMode.DETAIL, '1000px')">详情
          </el-link>
          <el-link icon="Check" type="success"
                   @click="pub(scope.row)">发布
          </el-link>
          <el-link icon="Close" type="danger"
                   @click="end(scope.row)">废止
          </el-link>
          <el-link icon="Edit" type="warning"
                   @click="showFormDialog('政策编辑','EditForm', scope.row, DialogMode.EDIT, '1000px')">编辑
          </el-link>
          <el-link icon="Delete" type="danger"
                   @click="del(scope.row)">删除
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-model:current-page="search.pageNumber" v-model:page-size="search.pageSize"
                   :page-sizes="[10, 20, 30, 40, 50, 100]" :total="Number(tableData.total)"
                   background
                   class="m-t-4 float-right" layout="total, prev, pager, next, sizes, ->" @change="pageOrSizeChange">
    </el-pagination>
    <el-dialog v-model="dialogModel.visible" :title="dialogModel.title" :width="dialogModel.width" destroy-on-close
               @closed="dialogModel.closed()" :close-on-click-modal="false">
      <AddForm v-if="dialogModel.formName == 'AddForm'" ref="addForm" :mode="dialogModel.mode"
               :row-data="{}" @save-complete="dialogModel.saveComplete"/>
      <EditForm v-if="dialogModel.formName == 'EditForm'" ref="editForm" :mode="dialogModel.mode"
                :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <DetailForm v-if="dialogModel.formName == 'DetailForm'" ref="detailForm" :mode="dialogModel.mode"
                  :row-data="dialogModel.rowData" />
      <template #footer>
        <el-button @click="dialogModel.visible = false">
          {{ dialogModel.formName !== 'DetailForm' ? '取 消' : '关 闭' }}
        </el-button>
        <el-button v-if="dialogModel.formName !== 'DetailForm'" :loading="dialogModel.saveLoading" type="primary"
                   @click="saveSubmit">确认
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">

</style>
