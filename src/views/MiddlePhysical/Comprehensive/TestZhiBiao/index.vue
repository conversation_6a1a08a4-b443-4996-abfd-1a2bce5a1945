<script setup lang="ts">
// 导入需要的模块和组件
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessageBox } from 'element-plus'
import Notification from '@/commons/Notification'
import { Refresh, Plus, Check, Edit, Delete } from '@element-plus/icons-vue'
import { apis } from '@/apis'
import type { IndicatorItem, IndicatorWeightConfig } from '@/apis/modules/IndicatorWeight'

// 响应式数据定义
const loading = ref(false) // 页面加载状态
const activeCategory = ref(1) // 当前激活的类别tab
const tabPosition = ref('left') // tab位置
const indicators = ref<IndicatorItem[]>([]) // 指标数据列表
const addDialogVisible = ref(false) // 添加/编辑对话框显示状态

// 新增/编辑表单数据
const addForm = reactive<IndicatorItem>({
  name: '',
  weight: 0,
  category: ''
})

// 表单验证规则
const addFormRules = {
  name: [
    { required: true, message: '请输入指标名称', trigger: 'blur' },
    { max: 100, message: '指标名称长度不能超过100字符', trigger: 'blur' }
  ],
  weight: [
    { required: true, message: '请输入权重', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '权重范围为0-100', trigger: 'blur' },
    {
      // 自定义验证规则：确保同一类别权重总和不超过100%
      validator: (rule: any, value: number, callback: any) => {
        if (!addForm.category) {
          callback()
          return
        }
        const validation = validateCategoryWithUniversal(addForm.category, isEditMode.value ? addForm.id : undefined)
        const newSum = validation.currentSum + (value || 0)
        if (newSum > 100) {
          if (addForm.category === '小学一年级至大学四年级') {
            callback(new Error(`通用类别权重不能超过100%。当前已有${validation.currentSum}%，再加上${value}%将超出限制`))
          } else {
            callback(new Error(`类别 "${addForm.category}" 总权重不能超过100%。通用权重: ${validation.universalWeight}% + 已有专用权重: ${validation.currentSum - validation.universalWeight}% + 新增权重: ${value}% = ${newSum}%`))
          }
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  category: [
    { required: true, message: '请选择适用类别', trigger: 'change' }
  ]
}

// 类别列表 - 统一使用数组格式
const tabs = ref([
  {key: 1, value: '小学一年级至大学四年级'},
  {key: 2, value: '小学一、二年级'},
  {key: 3, value: '小学三、四年级'},
  {key: 4, value: '小学五、六年级'},
  {key: 5, value: '初中、高中各年级'}
])

// 兼容性类别列表
const categories = [
  '小学一年级至大学四年级',
  '小学一、二年级',
  '小学三、四年级',
  '小学五、六年级',
  '初中、高中各年级'
]

// 获取当前选中的类别名称
const getCurrentCategoryName = computed(() => {
  const currentTab = tabs.value.find(tab => tab.key === activeCategory.value)
  return currentTab ? currentTab.value : '小学一、二年级'
})

// 计算属性：过滤当前类别的指标
const filteredIndicators = computed(() => {
  return indicators.value.filter(item => item.category === getCurrentCategoryName.value)
})

// 计算当前类别权重总和（包含通用权重）
const currentCategoryWeightSum = computed(() => {
  const categorySum = filteredIndicators.value.reduce((sum, item) => sum + (item.weight || 0), 0)
  // 如果不是通用类别，需要加上通用权重
  if (getCurrentCategoryName.value !== '小学一年级至大学四年级') {
    return universalWeight.value + categorySum
  }
  return categorySum
})

// 计算所有指标的权重总和
const totalWeightSum = computed(() => {
  return indicators.value.reduce((sum, item) => sum + (item.weight || 0), 0)
})

// 计算当前类别的已使用权重（包含通用权重）
const currentCategoryUsedWeight = computed(() => {
  if (!addForm.category) return 0
  const validation = validateCategoryWithUniversal(addForm.category, isEditMode.value ? addForm.id : undefined)
  return validation.currentSum
})

// 计算剩余可分配权重
const remainingWeight = computed(() => {
  if (!addForm.category) return 100
  const validation = validateCategoryWithUniversal(addForm.category, isEditMode.value ? addForm.id : undefined)
  return Math.max(0, 100 - validation.currentSum)
})

// 获取通用权重用于显示
const universalWeight = computed(() => {
  return getUniversalWeight()
})

// 加载数据方法
const loadData = async () => {
  try {
    loading.value = true
    const response = await apis.IndicatorWeight.getIndicatorWeight()
    if (response.data && response.data.content) {
      // 转换现有接口格式为指标格式
      indicators.value = response.data.content.map((item: any) => {
        const [category, name] = item.key.split('-', 2)
        return {
          id: parseInt(item.extra) || 0,
          name: name || item.key,
          category: category || '未分类',
          weight: parseInt(item.value.replace('%', '')) || 0
        }
      })
    }
  } catch (error) {
    Notification.showErrorNotify('加载数据失败，请检查网络连接')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 刷新数据
const handleRefresh = () => {
  loadData()
}

// 切换类别 - 统一使用数字key
const handleCategoryChange = (categoryKey: number) => {
  activeCategory.value = categoryKey
}

// 获取通用权重（小学一年级至大学四年级）
const getUniversalWeight = (excludeId?: number): number => {
  const universalIndicators = indicators.value.filter(item =>
    item.category === '小学一年级至大学四年级' && item.id !== excludeId
  )
  return universalIndicators.reduce((total, item) => total + (item.weight || 0), 0)
}

// 验证特定类别的权重总和（包含通用权重）
const validateCategoryWithUniversal = (categoryName: string, excludeId?: number): { isValid: boolean, currentSum: number, universalWeight: number } => {
  const universalWeight = getUniversalWeight(excludeId)

  if (categoryName === '小学一年级至大学四年级') {
    // 通用类别只验证自己
    return {
      isValid: universalWeight <= 100,
      currentSum: universalWeight,
      universalWeight: universalWeight
    }
  } else {
    // 其他类别需要加上通用权重
    const categoryIndicators = indicators.value.filter(item =>
      item.category === categoryName && item.id !== excludeId
    )
    const categoryWeight = categoryIndicators.reduce((total, item) => total + (item.weight || 0), 0)
    const totalWeight = universalWeight + categoryWeight

    return {
      isValid: totalWeight <= 100,
      currentSum: totalWeight,
      universalWeight: universalWeight
    }
  }
}

// 批量保存
const handleBatchSave = async () => {
  try {
    // 验证每个类别的权重（包含通用权重）不超过100%
    for (const category of categories) {
      const validation = validateCategoryWithUniversal(category)
      if (!validation.isValid) {
        if (category === '小学一年级至大学四年级') {
          Notification.showWarningNotify(`通用类别 "${category}" 的权重不能超过100%，当前为${validation.currentSum}%`)
        } else {
          Notification.showWarningNotify(`类别 "${category}" 的总权重不能超过100%。通用权重: ${validation.universalWeight}% + 专用权重: ${validation.currentSum - validation.universalWeight}% = ${validation.currentSum}%`)
        }
        return
      }
    }

    const response = await apis.IndicatorWeight.updateIndicatorWeight({
      indicators: indicators.value
    })

    if (response.data === 'success') {
      Notification.showSuccessNotify('保存成功')
      await loadData()
    } else {
      Notification.showErrorNotify('保存失败，请重试')
    }
  } catch (error) {
    Notification.showErrorNotify('保存失败，请检查网络连接')
    console.error(error)
  }
}

// 新增指标
const handleAddIndicator = () => {
  // 清空表单数据，确保新增模式
  addForm.id = undefined
  addForm.name = ''
  addForm.weight = 0
  addForm.category = getCurrentCategoryName.value
  addDialogVisible.value = true
}

// 新增/修改提交
const addFormRef = ref()
const isEditMode = computed(() => !!addForm.id)
const handleAddSubmit = async () => {
  try {
    await addFormRef.value?.validate()

    // 验证该类别的权重总和（包含通用权重，排除当前编辑的项）
    const validation = validateCategoryWithUniversal(addForm.category, isEditMode.value ? addForm.id : undefined)
    const newSum = validation.currentSum + (addForm.weight || 0)

    if (newSum > 100) {
      if (addForm.category === '小学一年级至大学四年级') {
        Notification.showWarningNotify(`通用类别权重不能超过100%。当前已有${validation.currentSum}%，再加上${addForm.weight}%将超出限制。`)
      } else {
        Notification.showWarningNotify(`类别 "${addForm.category}" 总权重不能超过100%。通用权重: ${validation.universalWeight}% + 已有专用权重: ${validation.currentSum - validation.universalWeight}% + 新增权重: ${addForm.weight}% = ${newSum}%`)
      }
      return
    }

    if (isEditMode.value) {
      // 修改模式：更新本地数据，保持原有顺序
      const targetIndex = indicators.value.findIndex(item => item.id === addForm.id)
      if (targetIndex !== -1) {
        indicators.value[targetIndex] = { ...addForm }
      }

      const response = await apis.IndicatorWeight.updateIndicatorWeight({ indicators: indicators.value })
      if (response.data === 'success') {
        Notification.showSuccessNotify('修改成功')
        addDialogVisible.value = false
        await loadData()
      } else {
        Notification.showErrorNotify(response.data || '修改失败，请重试')
        // 修改失败时恢复数据
        await loadData()
      }
    } else {
      // 新增模式：先调用新增API，成功后重新加载数据
      const response = await apis.IndicatorWeight.addIndicator(addForm)
      if (response.data === 'success') {
        Notification.showSuccessNotify('新增成功')
        addDialogVisible.value = false
        await loadData()
      } else {
        Notification.showErrorNotify(response.data || '新增失败，请重试')
      }
    }
  } catch (error) {
    Notification.showErrorNotify(isEditMode.value ? '修改失败' : '新增失败')
    console.error(error)
  }
}

// 修改指标
const handleEdit = (row: IndicatorItem) => {
  // 设置编辑模式的表单数据
  addForm.name = row.name
  addForm.weight = row.weight
  addForm.category = row.category
  addForm.id = row.id
  addDialogVisible.value = true
}

// 删除指标
const handleDelete = async (row: IndicatorItem) => {
  try {
    await ElMessageBox.confirm('确定要删除这个指标吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
      // 删除确认对话框
    })

    if (row.id) {
      const response = await apis.IndicatorWeight.deleteIndicator(row.id)
      if (response.data === 'success') {
        Notification.showSuccessNotify('删除成功')
        await loadData()
      } else {
        Notification.showErrorNotify(response.data || '删除失败，请重试')
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      Notification.showErrorNotify('删除失败，请检查网络连接')
      console.error(error)
    }
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<template>
  <div class="indicator-weight-manage">
    <div class="table-box">
      <!-- Tab切换区域 - 统一样式 -->
      <el-tabs v-model="activeCategory" :tab-position="tabPosition" style="height: 800px" @tab-change="handleCategoryChange">
        <el-tab-pane v-for="(item, index) in tabs" :label="item.value" :name="item.key" :key="item.key">
          <el-card>
            <!-- 新增指标按钮区域 -->
            <div class="tab-header">
              <div class="operation-buttons">
                <el-button icon="Plus" type="primary" @click="handleAddIndicator" v-has-perm="'setting:add'">新增</el-button>
              </div>
            </div>

            <el-table
              :data="filteredIndicators"
              border
              stripe
              v-loading="loading"
              style="width: 100%"
            >
              <el-table-column prop="id" label="ID" width="80" align="center" />
              <el-table-column prop="name" label="指标名称" min-width="200">
                <template #default="{ row }">
                  {{ row.name }}
                </template>
              </el-table-column>
              <el-table-column prop="weight" label="权重(%)" width="150" align="center">
                <template #default="{ row }">
                  {{ row.weight }}%
                </template>
              </el-table-column>
              <el-table-column prop="category" label="适用类别" min-width="180">
                <template #default="{ row }">
                  <el-tag type="info">{{ row.category }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="150" align="center" fixed="right">
                <template #default="{ row }">
                  <div class="operation-buttons">
                    <el-link
                      type="warning"
                      :underline="false"
                      @click="handleEdit(row)"
                      v-has-perm="'setting:update'"
                    >
                      <el-icon><Edit /></el-icon>
                      修改
                    </el-link>
                    <el-link
                      type="danger"
                      :underline="false"
                      @click="handleDelete(row)"
                      v-has-perm="'setting:delete'"
                    >
                      <el-icon><Delete /></el-icon>
                      删除
                    </el-link>
                  </div>
                </template>
              </el-table-column>
            </el-table>

            <div class="weight-summary" v-if="filteredIndicators.length > 0">
            </div>
          </el-card>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 新增/修改指标对话框 -->
    <el-dialog v-model="addDialogVisible" :title="isEditMode ? '修改指标' : '新增指标'" width="500px">
      <el-form
        ref="addFormRef"
        :model="addForm"
        :rules="addFormRules"
        label-width="100px"
      >
        <el-form-item label="指标名称" prop="name">
          <el-input v-model="addForm.name" placeholder="请输入指标名称" />
        </el-form-item>
        <el-form-item label="权重" prop="weight">
          <el-input-number
            v-model="addForm.weight"
            :min="0"
            :max="100"
            :precision="0"
            controls-position="right"
            style="width: 100%"
          />
          <span style="margin-left: 8px; color: #666;">%</span>
          <div style="color: #909399; font-size: 12px; margin-top: 4px;">
          </div>
        </el-form-item>
        <el-form-item label="适用类别" prop="category">
          <el-select
            v-model="addForm.category"
            placeholder="请选择适用类别"
            style="width: 100%"
            :disabled="isEditMode"
          >
            <el-option
              v-for="category in categories"
              :key="category"
              :label="category"
              :value="category"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleAddSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.indicator-weight-manage {
  padding: 20px;

  .search-box {
    margin-bottom: 20px;

    .search-form {
      display: flex;
      gap: 12px;
      align-items: center;
    }
  }

  .total-weight-summary {
    margin-bottom: 20px;
  }

  .table-box {
    .weight-summary {
      margin-top: 16px;
    }

    .tab-header {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      padding: 6px 0;
      border-bottom: 1px solid #e4e7ed;

      .add-indicator-btn {
        font-size: 15px;
        //padding: 8px 16px;
        transition: all 0.3s;

        &:hover {
          text-decoration: none;
        }

        .el-icon {
          font-size: 14px;
        }
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

.operation-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  align-items: center;

  .el-link {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 14px;

    .el-icon {
      font-size: 14px;
    }
  }
}

:deep(.el-table) {
  .el-input-number {
    width: 100%;
  }
}

:deep(.el-tabs__content) {
  padding-top: 20px;
}

// 移除Tab按钮选中时的背景变色
:deep(.el-tabs--left .el-tabs__item.is-active) {
  background-color: transparent !important;
  color: #409eff;
}

:deep(.el-tabs--left .el-tabs__item) {
  background-color: transparent !important;

  &:hover {
    background-color: transparent !important;
  }
}
</style>