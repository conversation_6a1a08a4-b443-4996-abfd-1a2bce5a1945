<script setup lang="ts">
// ----------------------------------------------------------------
// 导入区域 (Import Section)
// ----------------------------------------------------------------

// 从 'vue' 中导入核心的组合式API函数
import {onMounted, ref} from "vue"; // onMounted: 生命周期钩子，在组件挂载到DOM后执行。ref: 创建一个响应式的数据引用。

// 导入项目封装的API请求集合
import {apis} from "@/apis"; // 存放所有后端接口请求函数的地方

// 导入封装好的表格通用逻辑 (自定义Composable/Hook)
import {useTableCommons} from "@/commons/TableCommons"; // 这是一个自定义的组合式函数，简化了表格数据加载、搜索、分页等通用操作

// 导入 Pinia 状态管理库中定义的 store
import {useDeptStore, useUserStore} from '@/store' // Pinia是Vue的官方状态管理器。useUserStore用于获取用户信息，useDeptStore用于获取部门信息。

// 导入一个自定义的类型，用于定义键值对结构
import type { KeyValue } from "@/commons/KeyValue";

// 从 'element-plus/icons-vue' 导入图标组件
import { Upload, Download, Edit } from '@element-plus/icons-vue'; // 这些是Element Plus图标库里的图标，可以直接在模板中使用

// 导入项目封装的通知提示组件
import Notification from "@/commons/Notification"; // 一个统一处理成功、失败、警告等消息提示的工具

// 导入一个工具函数，用于判断值是否为空
import { isEmpty } from "@/utils/commonUtil";

// 导入子组件：测试标准的导入弹窗
import TestStandardImport from "@/views/MiddlePhysical/Comprehensive/TestStandard/components/TestStandardImport.vue";

// ----------------------------------------------------------------
// 初始化和状态管理 (Initialization & State Management)
// ----------------------------------------------------------------

// 获取用户状态实例
const userStore = useUserStore()
// 获取当前用户的区域ID，如果不存在则默认为 '130102'
let currentRegionId = userStore.regionId || '130102'

// 获取部门状态实例
const deptStore = useDeptStore()

// 如果当前部门是顶级部门，则强制将区域ID设置为 '130102' (可能是特定业务逻辑)
if (deptStore.topDept?.id == deptStore.currentDept?.id) {
  currentRegionId = '130102'
}

// 主列表的搜索条件对象，使用 ref() 使其成为响应式数据
const search = ref({
  code: "" // 这个code会根据用户选择的评分项目和性别动态改变，是请求后端数据的关键参数
})

// --- 编辑功能的状态管理 ---

// `editingCell` 用于跟踪当前正在编辑的单元格是哪一个。
// `ref<{rowIndex: number, grade: string} | null>` 定义了它的类型：
// - 可以是一个对象，包含 `rowIndex` (行索引) 和 `grade` (年级，也就是列的标识)
// - 也可以是 `null`，表示当前没有单元格处于编辑状态。
const editingCell = ref<{rowIndex: number, grade: string} | null>(null)

// `editingData` 用于双向绑定编辑弹窗(Popover)中的输入框的值。
const editingData = ref({
  minValue: '', // 最小值输入框的内容
  maxValue: ''  // 最大值输入框的内容
})

// --- UI状态管理 ---

// `popoverVisible` 用于控制每个可编辑单元格的Popover(弹出框)的显示和隐藏。
// 它是一个对象，键(key)由 `rowIndex-grade` 组成，值(value)为布尔值(true/false)。
// 例如: { '0-三年级': true, '1-四年级': false } 表示第0行"三年级"列的popover是显示的。
const popoverVisible = ref<{[key: string]: boolean}>({})


// --- 导入弹窗的状态管理 ---
const dialogModel = ref({
  visible: false, // 控制弹窗是否显示
  saveLoading: false, // 控制弹窗内"确认导入"按钮是否处于加载中状态，防止重复点击
  mode: '', // 弹窗模式 (在此代码中未使用，但通常用于区分"新增"或"编辑")
  // `saveComplete` 是一个回调函数，会传递给子组件(TestStandardImport)。
  // 当子组件完成保存操作后，会调用这个函数。
  saveComplete: (status: number) => {
    dialogModel.value.saveLoading = false; // 关闭按钮的加载状态
    if (status === 1) { // status === 1 通常表示保存成功
      dialogModel.value.visible = false; // 关闭弹窗
      searchSubmit(); // 调用 useTableCommons 提供的函数，重新加载表格数据
    }
  }
})

// 创建一个 ref 来获取导入子组件(TestStandardImport)的实例
// 这样在父组件中就可以通过 importForm.value 调用子组件暴露的方法，比如 saveSubmit()
const importForm = ref()


// ----------------------------------------------------------------
// 静态配置数据 (Static Configuration Data)
// ----------------------------------------------------------------

// 定义所有可选的评分项目。这是一个静态数组，用于生成左侧的菜单。
const scoringItems = ref([
  { key: 1, name: '体重指数单项评分', maleCode: '1TZZSDXPFB', femaleCode: '2TZZSDXPFB' },
  { key: 2, name: '肺活量单项评分', maleCode: '1FHLDXPFB', femaleCode: '2FHLDXPFB' },
  { key: 3, name: '50米跑单项评分', maleCode: '1WSMP', femaleCode: '2WSMP' },
  { key: 4, name: '坐位体前屈单项评分', maleCode: '1ZWTQQ', femaleCode: '2ZWTQQ' },
  { key: 5, name: '一分钟跳绳单项评分', maleCode: '1YFZTS', femaleCode: '2YFZTS' },
  { key: 6, name: '立定跳远单项评分', maleCode: '1LDTY', femaleCode: '2LDTY' },
  { key: 7, name: '男生一分钟仰卧起坐、引体向上单项评分', maleCode: '1YFZYWTZYTXS', femaleCode: '' }, // femaleCode为空，表示此项不支持女生
  { key: 8, name: '女生一分钟仰卧起坐单项评分', maleCode: '', femaleCode: '2YFZYWQZ' }, // maleCode为空，表示此项不支持男生
  { key: 9, name: '耐力跑单项评分', maleCode: '1NLT', femaleCode: '2NLT' }
])


// ----------------------------------------------------------------
// 响应式状态 (Reactive State)
// ----------------------------------------------------------------

// 当前用户在左侧菜单中选中的评分项目，用 key (数字) 来标识，默认为 1
const currentScoringItem = ref<number>(1)

// 当前激活的性别Tab，可以是 'male' (男生) 或 'female' (女生)，默认为 'male'
const activeTab = ref('male')

// 用于存储从后端获取的矩阵表格数据
const matrixData = ref<any[]>([])

// 用于存储动态生成的年级列名，例如 ['三年级', '四年级', ...]
const gradeColumns = ref<string[]>([])


// ----------------------------------------------------------------
// 辅助函数/计算属性 (Helper Functions / Computed-like)
// ----------------------------------------------------------------

/**
 * @description 获取当前选中的评分项目的完整配置对象
 * @returns {object | undefined} 评分项目对象
 */
const getCurrentItem = () => {
  // 从 scoringItems 数组中查找 key 与 currentScoringItem 的值匹配的项
  return scoringItems.value.find(item => item.key === currentScoringItem.value)
}

/**
 * @description 根据当前选中的项目和性别Tab，获取对应的后端查询code
 * @returns {string} 查询用的code
 */
const getCurrentCode = () => {
  const currentItem = getCurrentItem()
  if (!currentItem) return '' // 如果没找到项目，返回空字符串

  // 根据当前激活的Tab是 'male' 还是 'female' 返回对应的code
  if (activeTab.value === 'male') {
    return currentItem.maleCode
  } else {
    return currentItem.femaleCode
  }
}

/**
 * @description 检查当前选中的项目是否支持指定的性别
 * @param {'male' | 'female'} gender - 要检查的性别
 * @returns {boolean} 是否支持
 */
const isGenderSupported = (gender: 'male' | 'female') => {
  const currentItem = getCurrentItem()
  if (!currentItem) return false // 没找到项目，则不支持

  // 检查对应性别的code是否为空字符串
  if (gender === 'male') {
    return currentItem.maleCode !== ''
  } else {
    return currentItem.femaleCode !== ''
  }
}


// ----------------------------------------------------------------
// 核心业务逻辑 (Core Business Logic)
// ----------------------------------------------------------------

/**
 * @description 从后端获取矩阵表格数据
 */
const getMatrixData = async () => {
  // 1. 设置搜索参数：将当前应使用的code赋值给 search.value.code
  search.value.code = getCurrentCode()
  console.log('当前评分项目:', getCurrentItem()?.name, '性别Tab:', activeTab.value, '查询Code:', search.value.code)

  // 2. 如果code为空 (例如，某项目不支持当前选中的性别)，则直接清空数据并返回
  if (!search.value.code) {
    console.log('当前项目不支持该性别，清空数据')
    matrixData.value = []
    gradeColumns.value = []
    return { records: [], total: 0 } // 返回符合 useTableCommons 期望的格式
  }

  // 3. 调用API，传入搜索参数，发起网络请求
  const response = await apis.TestStandardManage.getMatrixData(search.value)

  // 4. 处理后端返回的数据
  if (response.data && response.data.length > 0) {
    // 将返回的数据赋值给 matrixData，用于渲染表格
    matrixData.value = response.data

    console.log('后端返回的数据:', response.data) // 调试日志
    console.log('第一行数据:', response.data[0]) // 调试日志

    // 动态生成列：从返回数据的第一行中提取所有年级的名称作为列标题
    const firstRow = response.data[0]
    if (firstRow && firstRow.gradeValues) {
      // Object.keys() 会返回对象 `gradeValues` 的所有键（也就是年级名称）组成的数组
      gradeColumns.value = Object.keys(firstRow.gradeValues)
    }
  } else {
    // 如果没有返回数据，则清空表格
    matrixData.value = []
    gradeColumns.value = []
  }

  // 5. 返回数据给 useTableCommons，它会自动处理 loading 状态和 tableData 的赋值
  return { records: response.data, total: response.data.length }
}

// 将 getMatrixData 函数作为获取表格数据的方法传递给 useTableCommons
const getTableData = async () => {
  return await getMatrixData()
}

// 使用自定义的 useTableCommons Hook
// 它会返回:
// - loading: 一个布尔值的 ref，表示数据是否正在加载
// - tableData: 存储表格数据的 ref，格式为 { records: [], total: 0 }
// - searchSubmit: 一个函数，调用它会触发 getTableData 重新获取数据
let {
  loading,
  tableData,
  searchSubmit
} = useTableCommons(search, getTableData)


// ----------------------------------------------------------------
// 事件处理器 (Event Handlers)
// ----------------------------------------------------------------

/**
 * @description 当左侧评分项目菜单项被点击时触发
 * @param {number} itemKey - 被点击项的key
 */
const handleScoringItemChange = async (itemKey: number) => {
  // 1. 更新当前选中的项目
  currentScoringItem.value = itemKey

  // 2. 检查切换项目后，原来选中的性别是否还被支持
  if (!isGenderSupported(activeTab.value as 'male' | 'female')) {
    // 如果不支持，就自动切换到这个项目支持的第一个性别
    const currentItem = getCurrentItem()
    if (currentItem) {
      if (currentItem.maleCode && !currentItem.femaleCode) { // 只支持男
        activeTab.value = 'male'
      } else if (currentItem.femaleCode && !currentItem.maleCode) { // 只支持女
        activeTab.value = 'female'
      }
      // 如果都支持，则保持不变
    }
  }

  // 3. 重新加载数据
  await searchSubmit()
}

/**
 * @description 当性别Tab(男生/女生)被点击时触发
 * @param {any} tab - Element Plus Tab组件传递的实例
 */
const handleTabClick = async (tab: any) => {
  const newGender = tab.props.name as 'male' | 'female'

  // 检查目标性别是否被当前项目支持。这是一个防御性检查，因为不支持的Tab已经被禁用了。
  if (!isGenderSupported(newGender)) {
    const currentItem = getCurrentItem()
    Notification.showWarningNotify(`${currentItem?.name || '当前项目'}不支持${newGender === 'male' ? '男生' : '女生'}数据`)
    return // 阻止后续操作
  }

  // 确保 activeTab 的值已更新，然后重新加载数据
  // 注意：v-model="activeTab" 已经处理了 activeTab.value 的更新，这里主要是为了触发数据加载
  activeTab.value = newGender
  await searchSubmit()
}

/**
 * @description 生成单元格的唯一键，用于popoverVisible对象
 * @param {number} rowIndex - 行索引
 * @param {string} grade - 年级 (列标识)
 * @returns {string} 格式为 "rowIndex-grade" 的字符串
 */
const getCellKey = (rowIndex: number, grade: string): string => {
  return `${rowIndex}-${grade}`
}

/**
 * @description 当用户点击单元格时，显示编辑用的Popover
 * @param {number} rowIndex - 行索引
 * @param {string} grade - 年级 (列标识)
 */
const showEditPopover = (rowIndex: number, grade: string) => {
  const cellKey = getCellKey(rowIndex, grade)

  // 为确保同一时间只有一个popover显示，先关闭所有其他的popover
  Object.keys(popoverVisible.value).forEach(key => {
    popoverVisible.value[key] = false
  })

  // 记录当前正在编辑的单元格
  editingCell.value = { rowIndex, grade }
  // 从表格数据中获取当前行的数据
  const row = tableData.value.records[rowIndex]

  console.log('编辑行数据:', row) // 调试日志
  console.log('gradeRawValues:', row.gradeRawValues) // 调试日志

  // 从后端返回的 `gradeRawValues` 字段中获取原始的、未格式化的 minValue 和 maxValue
  // 这样做可以避免从显示的 "≥100" 或 "80-90" 这样的字符串中反向解析数字
  if (row.gradeRawValues && row.gradeRawValues[grade]) {
    const rawData = row.gradeRawValues[grade]
    console.log('使用原始数值:', rawData) // 调试日志
    editingData.value = {
      // 将数字或null转换为字符串，用于输入框绑定。如果值为 null 或 undefined，则显示为空字符串。
      minValue: rawData.minValue !== null && rawData.minValue !== undefined ? rawData.minValue.toString() : '',
      maxValue: rawData.maxValue !== null && rawData.maxValue !== undefined ? rawData.maxValue.toString() : ''
    }
  } else {
    // 如果没有原始数据，则清空输入框
    console.log('没有原始数值数据，使用空值') // 调试日志
    editingData.value = {
      minValue: '',
      maxValue: ''
    }
  }

  // 最后，显示当前点击单元格的popover
  popoverVisible.value[cellKey] = true
}

/**
 * @description "取消"按钮的点击事件
 */
const cancelEdit = () => {
  // 如果有正在编辑的单元格
  if (editingCell.value) {
    // 根据正在编辑的单元格信息找到key，并关闭其popover
    const cellKey = getCellKey(editingCell.value.rowIndex, editingCell.value.grade)
    popoverVisible.value[cellKey] = false
  }

  // 重置编辑状态
  editingCell.value = null
  editingData.value = {
    minValue: '',
    maxValue: ''
  }
}

/**
 * @description "确定"按钮的点击事件，保存编辑后的数据
 */
const saveEdit = async () => {
  if (!editingCell.value) return // 如果没有单元格在编辑，则什么也不做

  const { rowIndex, grade } = editingCell.value // 解构出当前编辑的行和列
  const row = matrixData.value[rowIndex] // 获取原始数据行

  try {
    // 从原始数据中获取这条记录在数据库中的唯一ID
    const recordId = row.gradeRawValues?.[grade]?.id
    if (!recordId) {
      throw new Error('无法找到记录ID，请刷新页面重试')
    }

    let minValue: number | null = null
    let maxValue: number | null = null

    // 获取输入框的值并去除前后空格
    const minStr = editingData.value.minValue.trim()
    const maxStr = editingData.value.maxValue.trim()

    // --- 数据校验和处理 ---
    if (minStr === '' && maxStr === '') {
      // 1. 如果两个输入框都为空，表示用户想清空这个值
      minValue = null
      maxValue = null
    } else {
      // 2. 校验和转换最小值
      if (minStr !== '') {
        if (!isValidNumber(minStr)) { // 使用辅助函数校验格式
          throw new Error('最小值格式不正确，只允许整数或最多一位小数')
        }
        minValue = parseFloat(minStr) // 字符串转数字
      }

      // 3. 校验和转换最大值
      if (maxStr !== '') {
        if (!isValidNumber(maxStr)) {
          throw new Error('最大值格式不正确，只允许整数或最多一位小数')
        }
        maxValue = parseFloat(maxStr)
      }

      // 4. 逻辑校验：如果两个值都存在，最小值必须小于最大值
      if (minValue !== null && maxValue !== null) {
        if (minValue >= maxValue) {
          throw new Error('最小值必须小于最大值')
        }
      }

      // 5. 特殊业务逻辑处理：
      // 如果只填了最小值，表示一个开放区间 (例如: >= 100)
      // 在后端，我们用一个特殊的大数(999.9)来表示"无穷大"
      if (minValue !== null && maxValue === null) {
        maxValue = 999.9
      }
      // 如果只填了最大值，表示 (例如: <= 50)，minValue 保持为 null 即可
    }

    // --- 调用API更新 ---
    // 准备要发送给后端的数据包
    const updateData = {
      id: recordId,
      minValue: minValue,
      maxValue: maxValue
    }

    console.log('更新数据:', updateData) // 调试日志

    // 调用更新接口
    await apis.TestStandardManage.updateMatrixCell(updateData)

    // --- 更新本地UI ---
    // 更新成功后，为了避免重新请求整个列表，直接更新前端的本地数据，实现立即响应
    // a. 更新用于显示的格式化后的值
    row.gradeValues[grade] = formatValueRange(minValue, maxValue)

    // b. 同时更新用于下次编辑的原始数值
    if (row.gradeRawValues && row.gradeRawValues[grade]) {
      row.gradeRawValues[grade].minValue = minValue
      row.gradeRawValues[grade].maxValue = maxValue
      row.gradeRawValues[grade].displayValue = formatValueRange(minValue, maxValue) // displayValue可能在其他地方使用
    }

    // --- 清理状态 ---
    // 关闭popover
    const cellKey = getCellKey(rowIndex, grade)
    popoverVisible.value[cellKey] = false

    // 清除编辑状态
    editingCell.value = null
    editingData.value = {
      minValue: '',
      maxValue: ''
    }

    Notification.showSuccessNotify('更新成功')
  } catch (error: any) {
    // 如果上述任何一步(校验、API调用)出错，会进入catch块
    Notification.showErrorNotify(error.message || '更新失败，请重试')
  }
}

/**
 * @description 验证字符串是否为有效的数字（整数或一位小数）
 * @param {string} value - 要验证的字符串
 * @returns {boolean}
 */
const isValidNumber = (value: string): boolean => {
  // 正则表达式解释:
  // ^      - 字符串开始
  // -?     - 可选的负号
  // \d+    - 一位或多位数字
  // (\.\d{1})? - 一个可选的小数组，包含一个小数点和后面紧跟的一位数字
  // $      - 字符串结束
  const regex = /^-?\d+(\.\d{1})?$/
  const num = parseFloat(value)
  // 除了正则校验，还限制了数值的范围
  return regex.test(value) && num >= -10000 && num <= 10000
}


/**
 * @description 格式化数值范围用于显示
 * @param {number | null} minValue - 最小值
 * @param {number | null} maxValue - 最大值
 * @returns {string} 格式化后的字符串，如 "80-90", "≥100", "≤50", 或 "-"
 */
const formatValueRange = (minValue: number | null, maxValue: number | null): string => {
  if (minValue === null && maxValue === null) {
    return '-' // 都为空，显示横杠
  }

  if (minValue !== null && maxValue !== null) {
    // 如果最大值是约定的"无穷大"值 999.9，则显示为 "≥最小值"
    if (maxValue === 999.9) {
      return '≥' + minValue
    } else {
      // 否则显示为 "最小值-最大值"
      return minValue + '-' + maxValue
    }
  } else if (minValue !== null) {
    // 只有最小值 (此时maxValue必为null，但前面已处理了无穷大的情况，这里实际上不会走到)
    return '≥' + minValue
  } else { // 只有最大值
    return '≤' + maxValue
  }
}

/**
 * @description "导入"按钮点击事件处理函数
 */
const importData = async () => {
  // 控制导入弹窗的显示状态
  dialogModel.value.visible = true;
}

/**
 * @description "导出"按钮点击事件处理函数 (模板中没有导出按钮，但逻辑已写好)
 */
const handleExport = async () => {
  try {
    // 获取当前查询所需的code
    const currentCode = getCurrentCode()
    if (!currentCode) {
      Notification.showWarningNotify('当前项目不支持该性别，无法导出')
      return
    }

    // 调用下载模板的API，并将code传给后端，后端会生成对应的Excel模板文件
    await apis.TestStandardManage.downloadTemplate(currentCode);
    // 下载成功的提示由API请求的封装中统一处理
  } catch (error: any) {
    Notification.showErrorNotify('下载失败，请稍后重试');
  }
}

/**
 * @description 导入弹窗的"确认"按钮点击事件
 */
const saveSubmit = async () => {
  dialogModel.value.saveLoading = true; // 设置按钮为加载中状态，防止重复点击
  // 调用子组件 TestStandardImport 暴露的 saveSubmit 方法，让子组件处理文件上传和保存
  importForm.value.saveSubmit();
}

/**
 * @description 关闭弹窗的回调函数，会传递给子组件
 */
const closeDetail = () => {
  dialogModel.value.visible = false;
}

// ----------------------------------------------------------------
// 生命周期钩子 (Lifecycle Hooks)
// ----------------------------------------------------------------

/**
 * @description onMounted 是Vue的生命周期钩子，当组件第一次被创建并挂载到页面上时执行。
 */
onMounted(async () => {
  // 在组件加载完成后，立即调用 searchSubmit() 来获取并显示初始的表格数据。
  await searchSubmit()
})
</script>

<template>
  <div class="test-standard">
    <div class="test-standard-layout">
      <div class="left-sidebar">
        <div class="menu-list">
          <div
            v-for="item in scoringItems"
            :key="item.key"

            :class="['menu-item', { active: currentScoringItem === item.key }]"

            @click="handleScoringItemChange(item.key)">

            <span class="menu-text">{{ item.name }}</span>
          </div>
        </div>
      </div>

      <div class="right-content">
        <div class="lmt-operation-container">
          <div class="lmt-operation">
<!--            <div class="current-item-title">-->
<!--              <h3>{{ getCurrentItem()?.name || '请选择评分项目' }}</h3>-->
<!--            </div>-->
            <div class="operation-buttons">
              <el-button icon="Upload" type="primary" @click="importData" v-has-perm="'testStandard:import'">导入</el-button>
            </div>
          </div>
        </div>

        <el-tabs v-model="activeTab" @tab-click="handleTabClick">

          <el-tab-pane
            label="男生数据"
            name="male"
            :disabled="!isGenderSupported('male')">

            <div v-if="!isGenderSupported('male')" class="not-supported">
              <el-empty description="当前项目不支持男生数据" />
            </div>

            <el-table v-else v-loading="loading" :data="tableData.records" border stripe>
              <el-table-column label="等级" width="100" prop="level" fixed="left"></el-table-column>
              <el-table-column label="单项得分" width="100" prop="score" fixed="left"></el-table-column>

              <el-table-column
                v-for="grade in gradeColumns"
                :key="grade"
                :label="grade"
                min-width="100"
                align="center">

                <template #default="scope">
                  <div class="editable-cell">
                    <el-popover
                      :visible="popoverVisible[getCellKey(scope.$index, grade)]"
                      placement="top"
                      :width="280"
                      trigger="manual" >
                      <div class="edit-form">
                        <div class="input-area">
                          <el-input
                            v-model="editingData.minValue"
                            size="small"
                            placeholder="最小值"
                            @keyup.enter="saveEdit" @keyup.esc="cancelEdit"  />
                          <span class="range-separator">-</span>
                          <el-input
                            v-model="editingData.maxValue"
                            size="small"
                            placeholder="最大值"
                            @keyup.enter="saveEdit"
                            @keyup.esc="cancelEdit"
                          />
                        </div>
                        <div class="edit-actions">
                          <el-button size="small" @click="cancelEdit">取消</el-button>
                          <el-button type="primary" size="small" @click="saveEdit">确定</el-button>
                        </div>
                      </div>

                      <template #reference>
                        <div class="display-value" @click="showEditPopover(scope.$index, grade)">
                          {{ scope.row.gradeValues[grade] || '-' }}
                          <el-icon class="edit-icon" v-has-perm="'testStandard:update'">
                            <Edit />
                          </el-icon>
                        </div>
                      </template>
                    </el-popover>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>

          <el-tab-pane
            label="女生数据"
            name="female"
            :disabled="!isGenderSupported('female')">
            <div v-if="!isGenderSupported('female')" class="not-supported">
              <el-empty description="当前项目不支持女生数据" />
            </div>
            <el-table v-else v-loading="loading" :data="tableData.records" border stripe>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <el-dialog v-model="dialogModel.visible"
               :title="`导入${activeTab === 'male' ? '男生' : '女生'}${getCurrentItem()?.name || '评分标准'}`"
               width="800px">
      <TestStandardImport
        :code="getCurrentCode()" :gender-text="activeTab === 'male' ? '男生' : '女生'" ref="importForm" :close-on-click-modal="false" destroy-on-close @save-complete="dialogModel.saveComplete" @closeDetail="closeDetail"> </TestStandardImport>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDetail">取消</el-button>
          <el-button type="primary" @click="saveSubmit" :loading="dialogModel.saveLoading">
            确认导入
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>


<style lang="scss" scoped>
.test-standard {
  height: 100%;
}

.test-standard-layout {
  display: flex;
  height: 100%;
  gap: 0;
}

.left-sidebar {
  width: 200px;
  // background: #f8f9fa;
  border-right: 1px solid #e4e7ed;
  flex-shrink: 0;
}

.menu-title {
  padding: 16px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
}

.menu-list {
  padding: 8px 0;
}

.menu-item {
  padding: 12px 16px;
  cursor: pointer;
  color: #606266;
  transition: all 0.3s;
  border-right: 3px solid transparent;
  text-align: right;

  &:hover {
    // background: #ecf5ff;
    color: #409eff;
  }

  &.active {
    // background: #ecf5ff;
    color: #409eff;
    border-right-color: #409eff;
    font-weight: 500;
  }
}

.menu-text {
  font-size: 14px;
}

.right-content {
  // 加点外间距
  padding: 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.lmt-operation {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 16px;

  .operation-buttons {
    display: flex;
    gap: 8px;
  }
}

.current-item-title {
  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
    color: #303133;
  }
}

.not-supported {
  padding: 40px;
  text-align: center;
}

.editable-cell {
  position: relative;

  .display-value {
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s;

    &:hover {
      background-color: #f5f7fa;

      .edit-icon {
        opacity: 1;
      }
    }

    .edit-icon {
      opacity: 0;
      margin-left: 4px;
      font-size: 12px;
      color: #409eff;
      transition: opacity 0.2s;
    }
  }

  // Popover中的编辑表单样式
  .edit-form {
    display: flex;
    flex-direction: column;
    gap: 12px;
    align-items: center;
    text-align: center;

    .input-area {
      display: flex;
      align-items: center;
      gap: 8px;
      justify-content: center;

      .range-separator {
        margin: 0 4px;
        color: #606266;
        font-weight: bold;
      }
    }

    .edit-actions {
      display: flex;
      gap: 8px;
      justify-content: center;

      .el-button {
        --el-button-size: 28px;
      }
    }

    .help-text {
      font-size: 12px;
      color: #909399;
      text-align: center;
      margin-top: 4px;
      line-height: 1.4;
    }
  }
}

// 移除Tab按钮选中时的背景变色，并优化右侧Tab样式
:deep(.el-tabs--top .el-tabs__item.is-active) {
  background-color: transparent !important;
  color: #409eff;
}

:deep(.el-tabs--top .el-tabs__item) {
  background-color: transparent !important;

  &:hover {
    background-color: transparent !important;
  }
}

// 自定义Popover样式 - 完全居中
.centered-edit-popover {
  padding: 16px !important;
  text-align: center !important;

  .edit-form {
    width: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
    margin: 0 auto !important;

    .input-area {
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      gap: 8px !important;
      width: 100% !important;

      .el-input {
        text-align: center !important;

        .el-input__inner {
          text-align: center !important;
        }
      }

      .range-separator {
        display: inline-block !important;
        text-align: center !important;
        margin: 0 4px !important;
      }
    }

    .edit-actions {
      display: flex !important;
      justify-content: center !important;
      gap: 8px !important;
      width: 100% !important;
    }

    .help-text {
      text-align: center !important;
      width: 100% !important;
      margin: 0 auto !important;
    }
  }
}
</style>
