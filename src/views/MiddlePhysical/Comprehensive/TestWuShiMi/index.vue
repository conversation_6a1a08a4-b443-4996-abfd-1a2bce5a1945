<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessageBox } from 'element-plus'
import Notification from '@/commons/Notification'
import { Edit, Delete, Search, Refresh, Plus, Download } from '@element-plus/icons-vue'
import { apis } from '@/apis'

// 数据结构接口
interface ScoreSetting {
  level: string
  minValue: number
  maxValue: number
  score: number
}

interface WeightItem {
  id?: number
  name: string
  weight: number
  scoreSettings: ScoreSetting[]
}

interface WeightConfig {
  weightItems: WeightItem[]
}

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const tableData = ref<WeightItem[]>([])

// Tab切换相关
const activeTab = ref(1)
const tabPosition = ref('left')

// 标签页配置 - 统一使用数组格式
const tabs = ref([
  {key: 1, value: '日常参与体育锻炼', code: 'RCCYTYDL', description: '体育课出勤和课间操出勤率的权重总和必须等于100%'},
  {key: 2, value: '体质健康标准', code: 'TZJKBZ', description: '管理体质健康标准的分值设置'},
  {key: 3, value: '体育与健康课', code: 'TYYJKK', description: '管理体育与健康课程的分值设置'}
])

// 兼容性配置对象
const tabConfig = reactive({
  daily: {
    code: 'RCCYTYDL',
    name: '日常参与体育锻炼',
    description: '体育课出勤和课间操出勤率的权重总和必须等于100%'
  },
  health: {
    code: 'TZJKBZ',
    name: '体质健康标准',
    description: '管理体质健康标准的分值设置'
  },
  pe: {
    code: 'TYYJKK',
    name: '体育与健康课',
    description: '管理体育与健康课程的分值设置'
  }
})

// 搜索表单
const searchForm = reactive({
  keyword: ''
})

// 新增/修改表单
const addForm = reactive<WeightItem>({
  name: '',
  weight: 0,
  scoreSettings: [
    { level: '优秀', minValue: 90, maxValue: 100, score: 0 },
    { level: '良好', minValue: 80, maxValue: 89, score: 0 },
    { level: '不及格', minValue: 0, maxValue: 79, score: 0 }
  ]
})

// 表单引用
const addFormRef = ref()

// 表单验证规则
const addFormRules = {
  name: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    { min: 2, max: 50, message: '项目名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  weight: [
    { required: true, message: '请输入权重', trigger: 'blur' },
    { type: 'number', min: 1, max: 100, message: '权重必须在1-100之间', trigger: 'blur' }
  ]
}

// 计算属性
const totalWeight = computed(() => {
  return tableData.value.reduce((sum, item) => sum + (item.weight || 0), 0)
})

const remainingWeight = computed(() => {
  const currentUsed = tableData.value
    .filter(item => item.id !== addForm.id)
    .reduce((sum, item) => sum + (item.weight || 0), 0)
  return Math.max(0, 100 - currentUsed)
})

// 当前tab配置 - 根据activeTab数字找到对应配置
const currentTabConfig = computed(() => {
  const currentTab = tabs.value.find(tab => tab.key === activeTab.value)
  if (!currentTab) return tabConfig.daily // 默认返回第一个

  // 根据code映射到tabConfig
  const codeToKey: { [key: string]: keyof typeof tabConfig } = {
    'RCCYTYDL': 'daily',
    'TZJKBZ': 'health',
    'TYYJKK': 'pe'
  }

  const configKey = codeToKey[currentTab.code] || 'daily'
  return tabConfig[configKey]
})

// 方法
const loadData = async () => {
  try {
    loading.value = true
    const currentConfig = currentTabConfig.value
    const response = await apis.Setting.getByCode({ key: currentConfig.code })

    console.log('=== 数据加载调试 ===')
    console.log('当前tab:', activeTab.value)
    console.log('当前配置:', currentConfig)
    console.log('API响应:', response)

    if (!response || response.status !== 200) {
      throw new Error(`API响应失败: ${response?.status} - ${response?.message}`)
    }

    let contentData: any[] = []

    if (response.data && response.data.content) {
      const content = response.data.content
      console.log('Content数据:', content)
      console.log('Content类型:', typeof content)

      if (Array.isArray(content)) {
        // 根据当前tab类型处理不同的数据格式
        const currentConfig = currentTabConfig.value

        if (currentConfig.code === 'RCCYTYDL') {
          // 日常参与体育锻炼：权重设置格式
          contentData = content.map((item: any, index: number) => {
            let result: any = {
              id: parseInt(item.extra) || (index + 1),
              name: item.key || '',
              weight: parseInt((item.value || '0').replace('%', '')) || 0,
              scoreSettings: []
            }

            // 尝试解析extra中的完整数据
            if (item.extra && typeof item.extra === 'string') {
              try {
                const fullData = JSON.parse(item.extra)
                console.log(`项目${index}解析的完整数据:`, fullData)

                if (fullData.id) result.id = fullData.id
                if (fullData.name) result.name = fullData.name
                if (fullData.weight) result.weight = fullData.weight
                if (fullData.scoreSettings && Array.isArray(fullData.scoreSettings)) {
                  result.scoreSettings = fullData.scoreSettings
                }
              } catch (parseError) {
                console.warn(`项目${index}解析extra失败:`, parseError)
              }
            }

            return result
          })
        } else if (currentConfig.code === 'TZJKBZ' || currentConfig.code === 'TYYJKK') {
          // 体质健康标准/体育与健康课：分值设置格式
          // 需要将多个等级合并为一个项目
          const projectMap = new Map()

          content.forEach((item: any, index: number) => {
            if (item.extra && typeof item.extra === 'string') {
              try {
                const fullData = JSON.parse(item.extra)
                console.log(`等级${index}解析的完整数据:`, fullData)

                const projectKey = fullData.projectName || fullData.name || 'Unknown'

                if (!projectMap.has(projectKey)) {
                  projectMap.set(projectKey, {
                    id: fullData.id || (index + 1),
                    name: projectKey,
                    weight: 0, // 分值设置没有权重概念
                    scoreSettings: []
                  })
                }

                // 添加这个等级的分值设置
                const project = projectMap.get(projectKey)
                project.scoreSettings.push({
                  level: fullData.level || '',
                  minValue: fullData.minValue || 0,
                  maxValue: fullData.maxValue || 0,
                  score: fullData.score || 0
                })
              } catch (parseError) {
                console.warn(`等级${index}解析extra失败:`, parseError)
              }
            }
          })

          contentData = Array.from(projectMap.values())
          console.log('合并后的项目数据:', contentData)
        } else {
          // 其他情况使用默认解析
          contentData = []
        }
      } else if (typeof content === 'string') {
        // 处理字符串格式
        try {
          const config = JSON.parse(content) as WeightConfig
          contentData = config.weightItems || []
        } catch (parseError) {
          console.error('JSON解析失败:', parseError)
          contentData = []
        }
      } else if (typeof content === 'object' && content !== null) {
        // 处理对象格式
        if (content.weightItems) {
          contentData = content.weightItems
        } else {
          contentData = []
        }
      }
    }

    console.log('最终处理的数据:', contentData)
    tableData.value = contentData

    // 如果有数据，加载第一项到表单中以便编辑
    if (contentData.length > 0) {
      const firstItem = contentData[0]
      console.log('加载第一项数据到表单:', firstItem)

      addForm.name = firstItem.name || ''
      addForm.weight = firstItem.weight || 0
      addForm.id = firstItem.id

      // 直接使用后端返回的分值设置数据，不再使用写死的默认值
      if (firstItem.scoreSettings && Array.isArray(firstItem.scoreSettings) && firstItem.scoreSettings.length > 0) {
        addForm.scoreSettings = [...firstItem.scoreSettings]
        console.log('使用后端返回的分值设置:', firstItem.scoreSettings)
      }
    } else {
      // 没有数据时重置表单
      resetAddForm()
      Notification.showWarningNotify(`${currentConfig.name}暂无数据`)
    }
  } catch (error: any) {
     tableData.value = []
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  // 实现搜索逻辑
  loadData()
}

const handleRefresh = () => {
  searchForm.keyword = ''
  loadData()
}

// tab切换处理 - 统一使用数字key
const handleTabChange = (tabKey: number) => {
  console.log('切换到标签页:', tabKey)
  activeTab.value = tabKey
  searchForm.keyword = ''
  loadData()
}

// 项目切换处理
const handleProjectChange = (projectName: string) => {
  console.log('切换到项目:', projectName)

  // 从当前数据中找到对应项目的数据
  const projectData = tableData.value.find(item => item.name === projectName)
  if (projectData) {
    console.log('找到项目数据:', projectData)

    // 更新表单数据
    addForm.weight = projectData.weight || 0
    addForm.id = projectData.id

    // 更新分值设置
    if (projectData.scoreSettings && Array.isArray(projectData.scoreSettings) && projectData.scoreSettings.length > 0) {
      addForm.scoreSettings = [...projectData.scoreSettings]
      console.log('更新分值设置:', projectData.scoreSettings)
    } else {
      // 如果没有分值设置，使用默认值
      addForm.scoreSettings = [
        { level: '优秀', minValue: 90, maxValue: 100, score: 0 },
        { level: '良好', minValue: 80, maxValue: 89, score: 0 },
        { level: '不及格', minValue: 0, maxValue: 79, score: 0 }
      ]
    }
  } else {
    console.log('未找到项目数据，重置为默认值')
    // 如果没找到数据，重置为默认值
    addForm.weight = 0
    addForm.id = undefined
    addForm.scoreSettings = [
      { level: '优秀', minValue: 90, maxValue: 100, score: 0 },
      { level: '良好', minValue: 80, maxValue: 89, score: 0 },
      { level: '不及格', minValue: 0, maxValue: 79, score: 0 }
    ]
  }
}

// 编辑方法已移除，现在使用直接编辑的形式

const handleDelete = async (row: WeightItem) => {
  try {
    await ElMessageBox.confirm('确定要删除这个项目吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const newItems = tableData.value.filter(item => item.id !== row.id)
    const currentConfig = currentTabConfig.value

    // 根据不同tab类型准备不同格式的数据
    let contentData: any

    if (currentConfig.code === 'RCCYTYDL') {
      // 日常参与体育锻炼：转换为KeyValue格式
      contentData = newItems.map((item: any) => ({
        key: item.name,
        value: `${item.weight}%`,
        extra: JSON.stringify({
          id: item.id,
          name: item.name,
          weight: item.weight,
          scoreSettings: item.scoreSettings || []
        })
      }))
    } else if (currentConfig.code === 'TZJKBZ' || currentConfig.code === 'TYYJKK') {
      // 体质健康标准/体育与健康课：转换为KeyValue格式
      contentData = []
      newItems.forEach((item: any) => {
        if (item.scoreSettings && item.scoreSettings.length > 0) {
          item.scoreSettings.forEach((setting: any) => {
            contentData.push({
              key: `${item.name}(${setting.level})`,
              value: `${setting.minValue}-${setting.maxValue}=${setting.score}分`,
              extra: JSON.stringify({
                id: item.id,
                projectName: item.name,
                level: setting.level,
                minValue: setting.minValue,
                maxValue: setting.maxValue,
                score: setting.score
              })
            })
          })
        }
      })
    } else {
      // 其他情况使用原格式
      contentData = JSON.stringify({ weightItems: newItems })
    }

    const response = await apis.Setting.updateByCode({
      code: currentConfig.code,
      name: currentConfig.name,
      content: contentData,
      orderNumber: 1
    })

    if (response.data === 'success') {
      Notification.showSuccessNotify('删除成功')
      await loadData()
    } else {
      Notification.showErrorNotify(response.data || '删除失败，请重试')
    }
  } catch (error) {
    if (error !== 'cancel') {
      Notification.showErrorNotify('删除失败，请检查网络连接')
    }
  }
}

const handleAddSubmit = async () => {
  try {
    await addFormRef.value?.validate()

    // 验证权重总和
    const otherItemsWeight = tableData.value
      .filter(item => item.id !== addForm.id)
      .reduce((sum, item) => sum + (item.weight || 0), 0)

    if (otherItemsWeight + addForm.weight > 100) {
      Notification.showWarningNotify(`权重总和不能超过100%，当前已使用${otherItemsWeight}%，最多还能分配${100 - otherItemsWeight}%`)
      return
    }

    submitLoading.value = true

    let newItems = [...tableData.value]


    const index = newItems.findIndex(item => item.id === addForm.id)
    if (index !== -1) {
      newItems[index] = { ...addForm }
    } else {
      const maxId = Math.max(...newItems.map(item => item.id || 0), 0)
      newItems.push({ ...addForm, id: maxId + 1 })
    }

    const currentConfig = currentTabConfig.value

    // 根据不同tab类型准备不同格式的数据
    let contentData: any

    if (currentConfig.code === 'RCCYTYDL') {
      // 日常参与体育锻炼：转换为KeyValue格式
      contentData = newItems.map((item: any) => ({
        key: item.name,
        value: `${item.weight}%`,
        extra: JSON.stringify({
          id: item.id,
          name: item.name,
          weight: item.weight,
          scoreSettings: item.scoreSettings || []
        })
      }))
    } else if (currentConfig.code === 'TZJKBZ' || currentConfig.code === 'TYYJKK') {
      // 体质健康标准/体育与健康课：转换为KeyValue格式
      contentData = []
      newItems.forEach((item: any) => {
        if (item.scoreSettings && item.scoreSettings.length > 0) {
          item.scoreSettings.forEach((setting: any) => {
            contentData.push({
              key: `${item.name}(${setting.level})`,
              value: `${setting.minValue}-${setting.maxValue}=${setting.score}分`,
              extra: JSON.stringify({
                id: item.id,
                projectName: item.name,
                level: setting.level,
                minValue: setting.minValue,
                maxValue: setting.maxValue,
                score: setting.score
              })
            })
          })
        }
      })
    } else {
      // 其他情况使用原格式
      contentData = JSON.stringify({ weightItems: newItems })
    }

    const response = await apis.Setting.updateByCode({
      code: currentConfig.code,
      name: currentConfig.name,
      content: contentData,
      orderNumber: 1
    })

    if (response.data === 'success') {
      Notification.showSuccessNotify('保存成功')
      await loadData()
    } else {
      Notification.showErrorNotify(response.data || '保存失败，请重试')
    }
  } catch (error) {
    Notification.showErrorNotify('保存失败，请检查网络连接')
  } finally {
    submitLoading.value = false
  }
}

const handleExport = () => {
  Notification.showWarningNotify('导出功能正在开发中，敬请期待')
}

const resetAddForm = () => {
  addForm.id = undefined
  addForm.name = ''
  addForm.weight = 0
  addForm.scoreSettings = [
    { level: '优秀', minValue: 90, maxValue: 100, score: 0 },
    { level: '良好', minValue: 80, maxValue: 89, score: 0 },
    { level: '不及格', minValue: 0, maxValue: 79, score: 0 }
  ]
}

const addScoreSetting = () => {
  addForm.scoreSettings.push({ level: '', minValue: 0, maxValue: 0, score: 0 })
}

const removeScoreSetting = (index: number) => {
  addForm.scoreSettings.splice(index, 1)
}

const getTagType = (level: string) => {
  switch (level) {
    case '优秀':
    case '良好':
      return 'success'
    case '及格':
      return 'warning'
    case '不及格':
      return 'danger'
    default:
      return 'info'
  }
}


// 生命周期
onMounted(() => {
  loadData()
})
</script>
<template>
  <div class="score-setting-manage">
    <!-- Tab切换区域 - 统一样式 -->
    <el-tabs v-model="activeTab" :tab-position="tabPosition" style="height: 800px" @tab-change="handleTabChange">
      <el-tab-pane v-for="(item, index) in tabs" :label="item.value" :name="item.key" :key="item.key">
        <!-- 分值设置区域 -->
        <div class="score-setting-section">
          <el-card>
            <div class="section-header">
              <h3>{{ item.value }} - 分值设置</h3>
              <p class="description">{{ item.description }}</p>
            </div>

            <!-- 根据当前tab显示不同的内容 -->
            <div v-if="activeTab === 1 && index === activeTab - 1" class="setting-content">
          <!-- 日常参与体育锻炼的权重设置 -->
          <div class="weight-setting-area">
            <el-form :model="addForm" label-width="120px" class="setting-form">
              <el-form-item label="考核项目名称:">
                <el-select v-model="addForm.name" placeholder="请选择考核项目" style="width: 200px" @change="handleProjectChange">
                  <el-option label="体育课出勤" value="体育课出勤" />
                  <el-option label="课间操出勤率" value="课间操出勤率" />
                </el-select>
              </el-form-item>

              <el-form-item label="权重占比:">
                <el-input-number
                  v-model="addForm.weight"
                  :min="0"
                  :max="100"
                  style="width: 120px"
                />
                <span style="margin-left: 8px">%</span>
              </el-form-item>

              <el-form-item label="分值设置:">
                <div class="score-settings-grid">
                  <div v-for="(setting, index) in addForm.scoreSettings" :key="index" class="score-row">
                    <div class="score-item">
                      <label>最小值(%):</label>
                      <el-input-number v-model="setting.minValue" :min="0" :max="100" size="small" />
                    </div>
                    <div class="score-item">
                      <label>最大值(%):</label>
                      <el-input-number v-model="setting.maxValue" :min="0" :max="100" size="small" />
                    </div>
                    <div class="score-item">
                      <label>分值:</label>
                      <el-input-number v-model="setting.score" :min="0" :precision="2" size="small" />
                    </div>
                  </div>
                </div>
              </el-form-item>
            </el-form>

            <div class="form-actions">
              <el-button @click="resetAddForm">取消</el-button>
              <el-button type="primary" @click="handleAddSubmit" :loading="submitLoading">确定</el-button>
            </div>
          </div>
        </div>

            <div v-if="activeTab === 2 && index === activeTab - 1" class="setting-content">
              <!-- 体质健康标准 -->
              <div class="health-setting-area">
                <el-form :model="addForm" label-width="120px" class="setting-form">
                  <el-form-item label="项目名称:">
                    <el-input v-model="addForm.name" placeholder="请输入项目名称" style="width: 200px" readonly />
                  </el-form-item>

                  <el-form-item label="分值设置:">
                    <div class="score-settings-grid">
                      <div v-for="(setting, index) in addForm.scoreSettings" :key="index" class="score-row">
                        <div class="score-item">
                          <label>等级:</label>
                          <el-tag size="small">
                            {{ setting.level }}
                          </el-tag>
                        </div>
                        <div class="score-item">
                          <label>最小值(%):</label>
                          <el-input-number v-model="setting.minValue" :min="0" :max="100" size="small" />
                        </div>
                        <div class="score-item">
                          <label>最大值(%):</label>
                          <el-input-number v-model="setting.maxValue" :min="0" :max="100" size="small" />
                        </div>
                        <div class="score-item">
                          <label>分值:</label>
                          <el-input-number v-model="setting.score" :min="0" :precision="2" size="small" />
                        </div>
                      </div>
                    </div>
                  </el-form-item>
                </el-form>

                <div class="form-actions">
                  <el-button @click="resetAddForm">取消</el-button>
                  <el-button type="primary" @click="handleAddSubmit" :loading="submitLoading">确定</el-button>
                </div>
              </div>
            </div>

            <div v-if="activeTab === 3 && index === activeTab - 1" class="setting-content">
              <!-- 体育与健康课 -->
              <div class="pe-setting-area">
                <el-form :model="addForm" label-width="120px" class="setting-form">
                  <el-form-item label="课程项目:">
                    <el-input v-model="addForm.name" placeholder="请输入课程项目" style="width: 200px" readonly />
                  </el-form-item>

                  <el-form-item label="分值设置:">
                    <div class="score-settings-grid">
                      <div v-for="(setting, index) in addForm.scoreSettings" :key="index" class="score-row">
                        <div class="score-item">
                          <label>等级:</label>
                          <el-tag size="small">
                            {{ setting.level }}
                          </el-tag>
                        </div>
                        <div class="score-item">
                          <label>最小值(%):</label>
                          <el-input-number v-model="setting.minValue" :min="0" :max="100" size="small" />
                        </div>
                        <div class="score-item">
                          <label>最大值(%):</label>
                          <el-input-number v-model="setting.maxValue" :min="0" :max="100" size="small" />
                        </div>
                        <div class="score-item">
                          <label>分值:</label>
                          <el-input-number v-model="setting.score" :min="0" :precision="2" size="small" />
                        </div>
                      </div>
                    </div>
                  </el-form-item>
                </el-form>

                <div class="form-actions">
                  <el-button @click="resetAddForm">取消</el-button>
                  <el-button type="primary" @click="handleAddSubmit" :loading="submitLoading">确定</el-button>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>



<style scoped lang="scss">
.score-setting-manage {
  padding: 20px;

  .tabs-section, .score-setting-section, .weight-summary {
    margin-bottom: 20px;
  }

  .section-header {
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e4e7ed;

    h3 {
      margin: 0;
      color: #303133;
      font-size: 18px;
      font-weight: 500;
    }

    .description {
      margin: 8px 0 0 0;
      color: #909399;
      font-size: 14px;
      line-height: 1.5;
    }
  }

  .setting-content {
    .setting-form {
      background-color: #f8f9fa;
      padding: 24px;
      border-radius: 8px;
      border: 1px solid #e4e7ed;

      .el-form-item {
        margin-bottom: 24px;

        :deep(.el-form-item__label) {
          font-weight: 500;
          color: #606266;
        }
      }
    }

    // 新增等级设置样式
    .project-header {
      margin-bottom: 20px;
      padding: 16px;
      background-color: #f0f9ff;
      border-radius: 8px;
      border-left: 4px solid #409eff;

      h4 {
        margin: 0;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
      }
    }

    .levels-container {
      background-color: #f8f9fa;
      padding: 20px;
      border-radius: 8px;
      border: 1px solid #e4e7ed;
    }

    .level-setting-row {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      padding: 16px;
      background-color: #ffffff;
      border-radius: 6px;
      border: 1px solid #e4e7ed;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

      &:last-child {
        margin-bottom: 0;
      }

      .level-header {
        min-width: 100px;
        margin-right: 20px;
        display: flex;
        justify-content: center;
      }

      .level-inputs {
        display: flex;
        gap: 20px;
        flex: 1;

        .input-group {
          display: flex;
          flex-direction: column;
          gap: 8px;

          label {
            font-size: 14px;
            color: #606266;
            font-weight: 500;
          }
        }
      }
    }

    .score-settings-grid {
      border: 1px solid #dcdfe6;
      border-radius: 6px;
      padding: 16px;
      background-color: #ffffff;

      .score-row {
        display: grid;
        grid-template-columns: 120px 1fr 1fr 1fr; // 等级列固定宽度，其他列平分
        gap: 16px;
        margin-bottom: 16px;
        align-items: end; // 底部对齐

        &:last-child {
          margin-bottom: 0;
        }

        .score-item {
          display: flex;
          flex-direction: column;
          gap: 8px;

          label {
            font-size: 14px;
            color: #606266;
            font-weight: 500;
          }

          .el-input-number {
            width: 100%;
          }

          .el-tag {
            display: flex;
            justify-content: center;
            min-width: 60px;
          }
        }
      }
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      margin-top: 24px;
      padding-top: 20px;
      border-top: 1px solid #e4e7ed;
    }
  }

  .weight-summary {
    .current-weight {
      margin-top: 16px;
      text-align: center;
    }
  }

  :deep(.el-tabs__item) {
    padding: 0 24px;
    height: 48px;
    line-height: 48px;
    font-size: 15px;
    font-weight: 500;

    &.is-active {
      color: #409eff;
      background-color: #f0f9ff;
    }
  }

  :deep(.el-tabs__content) {
  padding: 0;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-alert) {
  border-radius: 6px;
}

:deep(.el-tag) {
  border-radius: 4px;
  font-weight: 500;
}

// 移除Tab按钮选中时的背景变色
:deep(.el-tabs--left .el-tabs__item.is-active) {
  background-color: transparent !important;
  color: #409eff;
}

:deep(.el-tabs--left .el-tabs__item) {
  background-color: transparent !important;

  &:hover {
    background-color: transparent !important;
  }
}
}
</style>
