<script setup lang="ts">
import {onMounted, ref} from "vue";
import {apis} from "@/apis";
import {useTableCommons} from "@/commons/TableCommons";
import {useDeptStore, useUserStore} from '@/store'
import type { KeyValue } from "@/commons/KeyValue";
import { ElMessage } from 'element-plus';
import { Edit, View, Delete, Upload, Download } from '@element-plus/icons-vue';
import Notification from "@/commons/Notification";
import { isEmpty } from "@/utils/commonUtil";
import TestStandardImport from "@/views/MiddlePhysical/Comprehensive/TestStandard/components/TestStandardImport.vue";

const userStore = useUserStore()
let currentRegionId = userStore.regionId || '130102'
const deptStore = useDeptStore()

if (deptStore.topDept?.id == deptStore.currentDept?.id) {
  currentRegionId = '130102'
}

// 主列表搜索条件
const search = ref({
  code: ""// 只保留性别条件，用于指定查询的code
})

// 编辑状态管理
const editingRows = ref(new Set<number>())
const editingData = ref<{[key: number]: any}>({})

// 导入弹窗相关
const dialogModel = ref({
  visible: false,
  saveLoading: false,
  mode: '',
  saveComplete: (status: number) => {
    dialogModel.value.saveLoading = false;
    if (status === 1) {
      dialogModel.value.visible = false;
      searchSubmit(); // 重新加载数据
    }
  }
})

// 创建一个 ref 来获取导入子组件的实例
const importForm = ref()

// 删除不再需要的选项

// 当前激活的Tab
const activeTab = ref('male')

// 主列表
const getTableData = async () => {
  // 根据当前Tab设置对应的code
  search.value.code = activeTab.value === 'male' ? '150MIPAO' : '250MIPAO'
  console.log('当前Tab:', activeTab.value, '查询Code:', search.value.code)
  const response = await apis.TestStandardManage.getPageData(search.value)
  return response.data
}

// 通用配置
let {
  loading,
  tableData,
  searchSubmit
} = useTableCommons(search, getTableData)

// 删除不再需要的性别转换函数

// Tab切换处理
const handleTabClick = async (tab: any) => {
  // 切换时清空编辑状态
  editingRows.value.clear()
  editingData.value = {}
  // 确保activeTab已更新到正确的值
  activeTab.value = tab.props.name
  await searchSubmit()
}

// 开始编辑
const startEdit = (index: number, row: any) => {
  editingRows.value.add(index)
  editingData.value[index] = {
    id: row.id,
    score: String(row.score ?? ''),       // 分数
    minValue: String(row.minValue ?? ''), // 使用 ?? 操作符，确保0值也能正确显示
    maxValue: String(row.maxValue ?? '')  // 使用 ?? 操作符，确保0值也能正确显示
  }
  console.log('开始编辑:', editingData.value[index]) // 调试日志
}

// 取消编辑
const cancelEdit = (index: number) => {
  editingRows.value.delete(index)
  delete editingData.value[index]
}

// 保存编辑
const saveEdit = async (index: number) => {
  const data = editingData.value[index]

  // 验证分数
  if (!validateScoreInput(data.score)) {
    return
  }

  // 验证数据
  if (!validateDecimalInput(data.minValue, '最小值') || !validateDecimalInput(data.maxValue, '最大值')) {
    return
  }

  // 验证最小值小于最大值
  if (data.minValue && data.maxValue && parseFloat(data.minValue) >= parseFloat(data.maxValue)) {
    ElMessage.error('最小值必须小于最大值')
    return
  }

  try {
    // 转换数据类型，确保后端能正确接收
    const updateData = {
      id: data.id,
      score: parseInt(data.score),
      minValue: parseFloat(data.minValue),
      maxValue: parseFloat(data.maxValue)
    }

    console.log('发送更新数据:', updateData) // 调试日志

    const response = await apis.TestStandardManage.update(updateData)

    console.log('更新响应:', response) // 调试日志

    if (response.data === true) {
      ElMessage.success('更新成功')
      editingRows.value.delete(index)
      delete editingData.value[index]
      await searchSubmit() // 重新加载数据
    } else {
      ElMessage.error('更新失败')
    }
  } catch (error: any) {
    console.error('更新错误:', error) // 调试日志
    ElMessage.error(error.response?.data?.message || error.message || '更新失败')
  }
}

// 验证小数输入（只允许一位小数）
const validateDecimalInput = (value: string, fieldName: string): boolean => {
  if (!value || value.trim() === '') {
    ElMessage.error(`${fieldName}不能为空`)
    return false
  }

  // 正则表达式：整数或最多一位小数
  const regex = /^\d+(\.\d{1})?$/
  if (!regex.test(value)) {
    ElMessage.error(`${fieldName}格式不正确，只允许整数或最多一位小数`)
    return false
  }

  const numValue = parseFloat(value)
  if (numValue < 0) {
    ElMessage.error(`${fieldName}不能为负数`)
    return false
  }



  return true
}

// 处理输入框的输入事件，确保只能输入有效格式
const handleInput = (value: any, field: string, index: number) => {
  // 只允许数字和一个小数点
  let cleanValue = value.replace(/[^\d.]/g, '')

  // 确保只有一个小数点
  const dotIndex = cleanValue.indexOf('.')
  if (dotIndex !== -1) {
    cleanValue = cleanValue.substring(0, dotIndex + 1) + cleanValue.substring(dotIndex + 1).replace(/\./g, '')
    // 限制小数点后只能有一位
    if (cleanValue.substring(dotIndex + 1).length > 1) {
      cleanValue = cleanValue.substring(0, dotIndex + 2)
    }
  }

  editingData.value[index][field] = cleanValue
}

// 验证分数输入（0-100的整数）
const validateScoreInput = (value: string): boolean => {
  if (!value || value.trim() === '') {
    ElMessage.error('分数不能为空')
    return false
  }

  const numValue = parseInt(value)
  if (isNaN(numValue)) {
    ElMessage.error('分数必须是整数')
    return false
  }

  if (numValue < 0 || numValue > 100) {
    ElMessage.error('分数必须在0-100之间')
    return false
  }

  return true
}

// 处理分数输入，只允许0-100的整数
const handleScoreInput = (value: any, index: number) => {
  // 只允许数字
  let cleanValue = String(value).replace(/[^\d]/g, '')

  // 限制在0-100范围内
  if (cleanValue !== '' && parseInt(cleanValue) > 100) {
    cleanValue = '100'
  }

  editingData.value[index].score = cleanValue
}

/**
 * @description "导入"按钮点击事件处理函数
 */
const importData = async () => {
  // 控制导入弹窗的显示状态
  dialogModel.value.visible = true;
}

/**
 * @description "导出"按钮点击事件处理函数
 */
const handleExport = async () => {
  try {
    // 调用导出 API，并将当前的搜索条件传递给后端
    await apis.TestStandardManage.export(search.value);
    const genderText = activeTab.value === 'male' ? '男生' : '女生'
    Notification.showSuccessNotify(`${genderText}体质健康测试评分标准导出成功`);
  } catch (error: any) {
    Notification.showErrorNotify('导出失败，请稍后重试');
  }
}

/**
 * @description 导入弹窗的"确认"按钮点击事件
 */
const saveSubmit = async () => {
  dialogModel.value.saveLoading = true; // 设置按钮为加载中状态，防止重复点击
  importForm.value.saveSubmit(); // 调用子组件 TestStandardImport 暴露的 saveSubmit 方法
}

/**
 * @description 关闭弹窗的回调函数，会传递给子组件
 */
const closeDetail = () => {
  dialogModel.value.visible = false;
}

onMounted(async () => {
  await searchSubmit()
})
</script>

<template>
  <div class="test-standard">
    <div class="lmt-operation-container">
      <div class="lmt-operation">
        <el-button icon="Upload" type="primary" @click="importData" v-has-perm="'testStandard:import'">导入</el-button>
<!--        <el-button icon="Download" type="info" @click="handleExport" v-has-perm="'testStandard:export'">批量导出</el-button>-->
      </div>
    </div>

    <!-- Tab切换 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="男生数据" name="male">
        <el-table v-loading="loading" :data="tableData.records" border
                  max-height="calc(100vh - var(--lmt-header-height) - var(--lmt-main-padding) * 2 - 3rem)"
                  stripe>
          <!--行号-->
          <el-table-column align="center" label="序号" type="index" width="80">
            <template #default="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
<!--          <el-table-column label="项目标题" min-width="200" prop="title" show-overflow-tooltip></el-table-column>-->
          <el-table-column label="年级" min-width="100" prop="gradeName" show-overflow-tooltip></el-table-column>
          <el-table-column label="等级" min-width="100" prop="level" show-overflow-tooltip></el-table-column>
          <el-table-column label="分数" min-width="120">
            <template #default="scope">
              <div v-if="editingRows.has(scope.$index)">
                <el-input
                  v-model="editingData[scope.$index].score"
                  @input="(value: any) => handleScoreInput(value, scope.$index)"
                  placeholder="0-100"
                  size="small"
                />
              </div>
              <span v-else>{{ scope.row.score }}</span>
            </template>
          </el-table-column>
<!--          <el-table-column label="最小值" min-width="150">-->
<!--            <template #default="scope">-->
<!--              <div v-if="editingRows.has(scope.$index)">-->
<!--                <el-input-->
<!--                  v-model="editingData[scope.$index].minValue"-->
<!--                  @input="(value: any) => handleInput(value, 'minValue', scope.$index)"-->
<!--                  placeholder="请输入最小值"-->
<!--                  size="small"-->
<!--                />-->
<!--              </div>-->
<!--              <span v-else>{{ scope.row.minValue }}</span>-->
<!--            </template>-->
<!--          </el-table-column>-->
          <el-table-column label="最大值" min-width="150">
            <template #default="scope">
              <div v-if="editingRows.has(scope.$index)">
                <el-input
                  v-model="editingData[scope.$index].maxValue"
                  @input="(value: any) => handleInput(value, 'maxValue', scope.$index)"
                  placeholder="请输入最大值"
                  size="small"
                />
              </div>
              <span v-else>{{ scope.row.maxValue }}</span>
            </template>
          </el-table-column>
          <el-table-column label="单位" min-width="100" prop="unit" show-overflow-tooltip></el-table-column>
          <el-table-column label="操作" min-width="120" fixed="right">
            <template #default="scope">
              <div v-if="editingRows.has(scope.$index)">
                <el-button type="primary" size="small" @click="saveEdit(scope.$index)">保存</el-button>
                <el-button size="small" @click="cancelEdit(scope.$index)">取消</el-button>
              </div>
              <div v-else>
                <el-link type="warning" :icon="Edit" @click="startEdit(scope.$index, scope.row)" v-has-perm="'testStandard:update'">
                  修改
                </el-link>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>

      <el-tab-pane label="女生数据" name="female">
        <el-table v-loading="loading" :data="tableData.records" border
                  max-height="calc(100vh - var(--lmt-header-height) - var(--lmt-main-padding) * 2 - 3rem)"
                  stripe>
          <!--行号-->
          <el-table-column align="center" label="序号" type="index" width="80">
            <template #default="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column label="年级" min-width="100" prop="gradeName" show-overflow-tooltip></el-table-column>
          <el-table-column label="等级" min-width="100" prop="level" show-overflow-tooltip></el-table-column>
          <el-table-column label="分数" min-width="120">
            <template #default="scope">
              <div v-if="editingRows.has(scope.$index)">
                <el-input
                  v-model="editingData[scope.$index].score"
                  @input="(value: any) => handleScoreInput(value, scope.$index)"
                  placeholder="0-100"
                  size="small"
                />
              </div>
              <span v-else>{{ scope.row.score }}</span>
            </template>
          </el-table-column>
<!--          <el-table-column label="最小值" min-width="150">-->
<!--            <template #default="scope">-->
<!--              <div v-if="editingRows.has(scope.$index)">-->
<!--                <el-input-->
<!--                  v-model="editingData[scope.$index].minValue"-->
<!--                  @input="(value: any) => handleInput(value, 'minValue', scope.$index)"-->
<!--                  placeholder="请输入最小值"-->
<!--                  size="small"-->
<!--                />-->
<!--              </div>-->
<!--              <span v-else>{{ scope.row.minValue }}</span>-->
<!--            </template>-->
<!--          </el-table-column>-->
          <el-table-column label="最大值" min-width="150">
            <template #default="scope">
              <div v-if="editingRows.has(scope.$index)">
                <el-input
                  v-model="editingData[scope.$index].maxValue"
                  @input="(value: any) => handleInput(value, 'maxValue', scope.$index)"
                  placeholder="请输入最大值"
                  size="small"
                />
              </div>
              <span v-else>{{ scope.row.maxValue }}</span>
            </template>
          </el-table-column>
          <el-table-column label="单位" min-width="100" prop="unit" show-overflow-tooltip></el-table-column>
          <el-table-column label="操作" min-width="120" fixed="right">
            <template #default="scope">
              <div v-if="editingRows.has(scope.$index)">
                <el-button type="primary" size="small" @click="saveEdit(scope.$index)">保存</el-button>
                <el-button size="small" @click="cancelEdit(scope.$index)">取消</el-button>
              </div>
              <div v-else>
                <el-link type="warning" :icon="Edit" @click="startEdit(scope.$index, scope.row)" v-has-perm="'testStandard:update'">
                  修改
                </el-link>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>

    <!-- 导入弹窗 -->
    <el-dialog v-model="dialogModel.visible"
               :title="`导入${activeTab === 'male' ? '男生' : '女生'}肺活量测试评分标准`"
               width="800px">
      <TestStandardImport
        :code="activeTab === 'male' ? '150MIPAO' : '250MIPAO'"
        :gender-text="activeTab === 'male' ? '男生' : '女生'"
        ref="importForm"
        :close-on-click-modal="false"
        destroy-on-close
        @save-complete="dialogModel.saveComplete"
        @closeDetail="closeDetail">
      </TestStandardImport>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDetail">取消</el-button>
          <el-button type="primary" @click="saveSubmit" :loading="dialogModel.saveLoading">
            确认导入
          </el-button>
    </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.test-standard {
  height: 100%;
}
</style>
