<script setup lang="ts">
import {ref, defineProps, watch} from "vue";
import {isEmpty} from "@/utils/commonUtil";
import Notification from "@/commons/Notification";
import {apis} from "@/apis";
import {ElNotification} from "element-plus";
import {UploadFilled} from '@element-plus/icons-vue'

const files = ref<any[]>([])
const errorMessages = ref<Array<{ rowIndex: number; message: string }>>([])
const fileList = ref<any[]>([])
const formData = ref({
  code: '',
})
const rules = {
  code: [{required: true, message: '请选择数据类型', trigger: 'change'}],
  uploadFile: [{required: true, message: '请上传文件', trigger: 'change'}],
}
const dataLoading = ref(false)
const form = ref()

const props = defineProps({
  code: {
    type: String,
    default: ''
  },
  genderText: {
    type: String,
    default: ''
  }
})

const importTemplateBtnLoading = ref(false);
// 下载现有数据
const importTemplateDownload = async () => {
  importTemplateBtnLoading.value = true;
  try {
    const {code} = formData.value;
    if (isEmpty(code)) {
      Notification.showErrorNotify("数据类型不能为空！");
      return;
    }
    await apis.TestStandardManage.downloadTemplate(code);
    Notification.showSuccessNotify("数据下载成功！");
  } catch (error) {
    // 捕获接口调用等可能出现的异常
    Notification.showErrorNotify("数据下载失败，请稍后重试！");
  } finally {
    importTemplateBtnLoading.value = false;
  }
}

const onRemove = (file: any, fileList: any) => {
  errorMessages.value = []
  // 清空文件列表
  fileList.value = []
}
const onExceed = (files: any, fileList: any) => {
  ElNotification.warning({message: `最多只能上传 1 个文件`, title: "温馨提示"})
}
const onChange = (file: any, fileList: any) => {
  let raw = file.raw;
  let fileTp =
    raw.type == "application/vnd.ms-excel" ||
    raw.type == "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
  if (!fileTp) {
    ElNotification.error({message: `只能上传excel格式文件`, title: "温馨提示"})
  } else {
    if (file.size > 5 * 1024 * 1024) {
      ElNotification.warning({message: `上传限制文件大小不能大于5M`, title: "温馨提示"});
    }
    // 更新文件列表
    files.value = [file.raw];
  }
}

const emit = defineEmits(['save-complete', 'closeDetail'])
const saveCompleteEmit = (status: number) => emit('save-complete', status)
// 保存
const saveSubmit = async () => {
  let {code} = formData.value;
  // 错误信息清空
  errorMessages.value = []
  if (isEmpty(code)) {
    Notification.showErrorNotify("数据类型不能为空！");
    saveCompleteEmit(0)
    return;
  }
  if (files.value.length === 0) {
    ElNotification.error({message: "请选择文件", title: "温馨提示"})
    saveCompleteEmit(0)
    return
  } else {
    const file = files.value[0]
    const formDates = new FormData()
    formDates.append('file', file.raw || file);
    await apis.TestStandardManage.import(formDates, code).then((res: any) => {
      if (res.data === "导入成功" || res.data === undefined) {
        ElNotification.success({message: "导入成功", title: "温馨提示"});
        saveCompleteEmit(1);
      } else if (res.data && res.data.length > 0) {
        errorMessages.value = res.data;
        saveCompleteEmit(0);
      }
    }).catch((error: any) => {
      const errorMessage = error.response?.data?.message || error.message || "导入失败";
      ElNotification.error({message: errorMessage, title: "温馨提示"})
      saveCompleteEmit(0)
    })
  }
}

// 监听 props 变化，自动设置 code
watch(() => props.code, (newCode) => {
  if (newCode) {
    formData.value.code = newCode;
  }
}, { immediate: true });

defineExpose({
  saveSubmit
})
</script>

<template>
  <div class="padding-20">
    <el-form ref="form" :model="formData" :rules="rules" v-loading="dataLoading" style="width: 700px"
             label-width="auto">
      <el-form-item label="数据类型：" prop="code">
        <el-input v-model="formData.code" readonly :placeholder="genderText + '肺活量测试评分标准'"></el-input>
      </el-form-item>
      <el-form-item label="参考数据：">
        <el-popconfirm width="220" confirm-button-text="确认" cancel-button-text="不需要" icon-color="#626AEF"
                       title="确定下载现有数据吗？"
                       @confirm="importTemplateDownload">
          <template #reference>
            <el-button icon="Download" type="info" :loading="importTemplateBtnLoading">
              下载现有数据
            </el-button>
          </template>
        </el-popconfirm>
<!--        <div class="tips">
          <p>说明：</p>
          <p>• 年级范围：一年级到高三</p>
          <p>• 等级类型：正常、低体重、超重、肥胖</p>
          <p>• 单位统一：千克</p>
          <p>• 分数范围：0-100</p>
        </div>-->
      </el-form-item>
      <el-form-item label="上传文件：" prop="uploadFile">
        <el-upload ref="upload" accept=".xlsx,.xls" :file-list="fileList" drag :auto-upload="false" :limit="1"
                   :on-remove="onRemove" :on-exceed="onExceed" :on-change="onChange" style="width: 100%">
          <el-icon class="el-icon--upload">
            <upload-filled/>
          </el-icon>
          <div class="el-upload__text">
            选择{{ genderText }}肺活量测试评分标准excel文件，<span style="color: red">多次上传会覆盖原数据</span>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              只能上传excel文件，且不超过5M
            </div>
          </template>
        </el-upload>
      </el-form-item>
      <el-form-item label="错误信息" v-if="errorMessages.length > 0">
        <div style="max-height: 300px; overflow-y: auto">
          <div v-for="(item, index) in errorMessages" :key="index">
            <div class="error-desc-text">
              {{ index + 1 }}、第{{ item.rowIndex }}行：{{ item.message }}
            </div>
          </div>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>
.padding-20 {
  padding: 20px;
}

.error-desc-text {
  color: #f56c6c;
  font-size: 12px;
  margin: 4px 0;
}

.tips {
  margin-top: 8px;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-size: 12px;
  color: #606266;

  p {
    margin: 4px 0;
  }
}
</style>
