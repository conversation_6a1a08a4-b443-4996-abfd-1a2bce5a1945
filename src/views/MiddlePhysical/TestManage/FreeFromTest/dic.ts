import type { KeyValue } from "@/commons/KeyValue"

// 申请类别字典
let applyTpList: Array<KeyValue> = [
  { key: 'disable', value: '残免' },
  { key: 'injury', value: '伤免' },
  { key: 'ill', value: '病免' },
  { key: 'illDelay', value: '病缓' },
  { key: 'optionProg', value: '选考项目免考' }
]

// 申请状态字典
let applyStList: Array<KeyValue> = [
  { key: 'county', value: '待区县审核' },
  { key: 'city', value: '待市级审核' },
  { key: 'pass', value: '通过' },
  { key: 'reject', value: '驳回' }
]

export { applyTpList, applyStList }
