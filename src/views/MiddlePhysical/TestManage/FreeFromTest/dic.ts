import type { KeyValue } from "@/commons/KeyValue"

// 申请类别字典
let applyTpList: Array<KeyValue> = [
  { key: 'can_mian', value: '残免' },
  { key: 'shang_mian', value: '伤免' },
  { key: 'bing_mian(', value: '病免' },
  { key: 'bing_huan', value: '病缓' },
  { key: 'mian_kao(', value: '选考项目免考' }
]

// 申请状态字典
let applyStList: Array<KeyValue> = [
  { key: 'waiting', value: '待审核' },
  { key: 'pass', value: '通过' },
  { key: 'reject', value: '驳回' }
]

export { applyTpList, applyStList }
