<script setup lang="ts">
import {ref, onMounted} from "vue"
import {useTableCommons} from "@/commons/TableCommons"
import {DialogMode} from "@/commons/DialogModel"
import {ElMessageBox, ElNotification} from "element-plus"
import {useDeptStore, useUserStore} from '@/store'
import {apis} from "@/apis"
import AddForm from "@/views/MiddlePhysical/TestManage/FreeFromTest/components/Add.vue"
import EditForm from "@/views/MiddlePhysical/TestManage/FreeFromTest/components/Add.vue"
import DetailForm from "@/views/MiddlePhysical/TestManage/FreeFromTest/components/Detail.vue"
import AuditForm from "@/views/MiddlePhysical/TestManage/FreeFromTest/components/Audit.vue"
// import type { KeyValue } from "@/commons/KeyValue"
import { applyTpList, applyStList } from "@/views/MiddlePhysical/TestManage/FreeFromTest/dic"

const userStore = useUserStore()
let currentRegionId = userStore.regionId
const deptStore = useDeptStore()

// 主列表
const getTableData = async () => {
  const response = await apis.RegionManage.getPageData(search.value)
  return response.data
}

// 主列表搜索条件
const search = ref({
  pageNumber: 1,
  pageSize: 10,
  keywords: "",
  regionId: currentRegionId,
  schoolId: "",
  grade: "",
  clazz: "",
  applyTp: "",
  prog: "",
  status: ""
})

// 通用配置
let {
  loading,
  dialogModel,
  showFormDialog,
  tableData,
  searchSubmit,
  pageOrSizeChange
} = useTableCommons(search, getTableData)

// 筛选条件
const regionList = ref<Array<any>>(deptStore.regionList)
const schoolList = ref<Array<any>>([])
const gradeList = ref<Array<any>>([])
const clazzList = ref<Array<any>>([])
const progList = ref<Array<any>>([{
  key: '1',
  value: 'sun'
}, {
  key: '2',
  value: 'is'
}])

// 弹窗内按钮方法
const addForm = ref()
const editForm = ref()
const auditForm = ref()
const saveSubmit = () => {
  dialogModel.value.saveLoading = true
  if (dialogModel.value.formName === 'AddForm') {
    addForm.value.saveSubmit()
  } else if (dialogModel.value.formName === 'EditForm') {
    editForm.value.saveSubmit()
  } else if (dialogModel.value.formName === 'AuditForm') {
    auditForm.value.saveSubmit()
  }
}

// 生命周期
onMounted(async () => {
  await deptStore.setRegionList()
  regionList.value = deptStore.regionList
  await searchSubmit()
})

// 筛选条件方法：区县变更
const regionChange = async () => {
  search.value.schoolId = ''
  schoolList.value = (await apis.Commons.getSchoolSelectListCounty(search.value.regionId)).data;
}

// 导出方法
const exportList = () => {

}

// 列表方法：删除item
const delItem = (row: any) => {
  console.log(row)
  ElMessageBox.confirm(`确认删除【${row.xm}】的申请吗？`, "温馨提示",
    {confirmButtonText: '确定', cancelButtonText: '取消', type: 'error'})
    .then(async () => {
      await apis.TeacherManage.delete(row.id)
      ElNotification.success({message: "操作成功", title: "温馨提示"})
      await searchSubmit()
    })
}

// 列表方法：通过item
const passItem = (row: any) => {
  console.log(row)
  ElMessageBox.confirm(`确认审核通过【${row.xm}】的申请吗？`, "温馨提示",
    {confirmButtonText: '确定', cancelButtonText: '取消', type: 'success'})
    .then(async () => {
      await apis.TeacherManage.delete(row.id)
      ElNotification.success({message: "操作成功", title: "温馨提示"})
      await searchSubmit()
    })
}
</script>

<template>
  <div class="free-from-test">
    <div class="lmt-operation-container">
      <div class="lmt-operation">
        <!--  v-has-perm="['region:export:account']" -->
        <el-button icon="Plus" type="primary"
                   @click="showFormDialog('免考缓考生信息','AddForm', {  }, DialogMode.ADD, '500px')">添加
        </el-button>
        <el-button icon="Download" type="info"
                   @click="exportList">导出
        </el-button>
      </div>
      <div class="lmt-search">
        <el-select v-model="search.regionId" clearable placeholder="请选择所属区县" @change="regionChange">
          <el-option v-for="item in regionList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <el-select v-model="search.schoolId" clearable placeholder="所属学校">
          <el-option v-for="item in schoolList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <el-select v-model="search.grade" clearable placeholder="请选择年级">
          <el-option v-for="item in gradeList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <el-select v-model="search.clazz" clearable placeholder="请选择班级">
          <el-option v-for="item in clazzList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <el-select v-model="search.applyTp" clearable placeholder="申请类别" class="apply-tp">
          <el-option v-for="item in applyTpList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <el-select v-model="search.prog" clearable placeholder="申请项目">
          <el-option v-for="item in progList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <el-select v-model="search.status" clearable placeholder="审核状态">
          <el-option v-for="item in applyStList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <el-input v-model="search.keywords" placeholder="学生姓名/学籍号/身份证" class="name-filter" :clearable="true">
          <template #append>
            <el-button icon="Search" @click="searchSubmit"></el-button>
          </template>
        </el-input>
      </div>
    </div>
    <el-table v-loading="loading" :data="tableData.records" border
              max-height="calc(100vh - var(--lmt-header-height) - var(--lmt-main-padding) * 2 - 3rem - var(--lmt-pager-height)"
              stripe>
      <!--行号-->
      <el-table-column align="center" fixed="left" label="序号" type="index" width="80">
        <template #default="scope">
          {{ (search.pageNumber - 1) * search.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="学生姓名" fixed="left" min-width="100" prop="name" show-overflow-tooltip></el-table-column>
      <el-table-column label="学籍号" fixed="left" width="180" prop="name"></el-table-column>
      <el-table-column label="性别" min-width="80" prop="name" show-overflow-tooltip></el-table-column>
      <el-table-column label="所在班级" min-width="100" prop="name" show-overflow-tooltip></el-table-column>
      <el-table-column label="所属学校" min-width="180" prop="name" show-overflow-tooltip></el-table-column>
      <el-table-column label="所属区县" min-width="100" prop="name" show-overflow-tooltip></el-table-column>
      <el-table-column label="申请类别" width="150" prop="name">
        <template #default="{ row }">
          <span>{{ row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="免考/缓考项目" min-width="200" prop="name" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="期限" min-width="200" prop="code" show-overflow-tooltip></el-table-column>
      <el-table-column label="审核状态" min-width="100" prop="contact" show-overflow-tooltip></el-table-column>
      <el-table-column label="审核人" min-width="100" prop="position" show-overflow-tooltip></el-table-column>
      <el-table-column label="审核时间" min-width="150" prop="telephone" show-overflow-tooltip></el-table-column>
      <!-- v-has-perm="['region:edit','region:export:account']" -->
      <el-table-column fixed="right" label="操作" width="280">
        <template #default="scope">
          <!--  v-has-perm="['region:edit']" -->
          <el-link icon="InfoFilled" type="primary"
                   @click="showFormDialog('免考缓考生信息','DetailForm',scope.row,DialogMode.DETAIL, '1000px')">详情
          </el-link>
          <el-link icon="Edit" type="warning"
                   @click="showFormDialog('修改','EditForm', scope.row, DialogMode.EDIT)">修改
          </el-link>
          <el-link icon="Check" type="success"
                   @click="passItem(scope.row)">通过
          </el-link>
          <el-link icon="Close" type="danger"
                   @click="showFormDialog('驳回申请','AuditForm', scope.row, DialogMode.AUDIT)">驳回
          </el-link>
          <el-link icon="Delete" type="danger"
                   @click="delItem(scope.row)">删除
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-model:current-page="search.pageNumber" v-model:page-size="search.pageSize"
                   :page-sizes="[10, 20, 30, 40, 50, 100]" :total="Number(tableData.total)"
                   background
                   class="m-t-4 float-right" layout="total, prev, pager, next, sizes, ->" @change="pageOrSizeChange">
    </el-pagination>
    <el-dialog v-model="dialogModel.visible" :title="dialogModel.title" :width="dialogModel.width" destroy-on-close
               @closed="dialogModel.closed()" :close-on-click-modal="false">
      <AddForm v-if="dialogModel.formName == 'AddForm'" ref="addForm" :mode="dialogModel.mode"
               :row-data="{}" v-model:progList="progList" @save-complete="dialogModel.saveComplete"/>
      <EditForm v-if="dialogModel.formName == 'EditForm'" ref="editForm" :mode="dialogModel.mode"
                :row-data="dialogModel.rowData" v-model:progList="progList" @save-complete="dialogModel.saveComplete"/>
      <DetailForm v-if="dialogModel.formName == 'DetailForm'" ref="detailForm" :mode="dialogModel.mode"
                  :row-data="dialogModel.rowData" v-model:progList="progList" @save-complete="dialogModel.saveComplete"/>
      <AuditForm v-if="dialogModel.formName == 'AuditForm'" ref="auditForm" :mode="dialogModel.mode"
                 @save-complete="dialogModel.saveComplete"/>
      <template #footer>
        <el-button @click="dialogModel.visible = false">
          {{ dialogModel.formName !== 'DetailForm' ? '取 消' : '关 闭' }}
        </el-button>
        <template v-if="dialogModel.formName === 'DetailForm'">
          <el-button type="success">通过</el-button>
          <el-button type="danger">驳回</el-button>
        </template>
        <el-button v-if="dialogModel.formName !== 'DetailForm'" :loading="dialogModel.saveLoading" type="primary"
                   @click="saveSubmit">确认
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.free-from-test {
  .apply-tp {
    min-width: 140px;
  }
  .name-filter {
    min-width: 250px;
  }
}
</style>
