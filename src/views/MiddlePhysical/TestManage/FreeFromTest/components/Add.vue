<script setup lang="ts">
import {ref, onMounted} from 'vue'
import {applyTpList} from "@/views/MiddlePhysical/TestManage/FreeFromTest/dic"
const progList = defineModel<any>('progList')
import TermSelector from "@/components/TermSelector/index.vue"

// 表单
const searchForm = ref({
  stuCode: "",
  stuInfo: {
    studentName: "",
    idCard: "",
    gender: "",
    clazz: "",
    school: "",
    county: ""
  },
  isHaveResult: false,
  isShowSearch: false
})

const formData = ref({
  applyTp: "",
  prog: <any>[],
  period: "",
  reason: "",
  proof: ""
})
const searchFormRef = ref()
const formRef = ref()

// 学期list
// const periodList = ref<Array<any>>([])

// 表单验证规则
const rules = {
  applyTp: [{required: true, message: '请选择年级', trigger: 'change'}],
  period: [{required: true, message: '请选择学校', trigger: 'change'}]
}

// props
const props = defineProps({
  mode: {
    type: [String, null],
    default: 'EDIT'
  },
  rowData: {
    type: [Object, null],
    default: () => {}
  }
})

// 根据学籍号搜索学生
const searchStu = () => {
  // searchForm.value.isHaveResult = true
  console.log(formData.value.period)
}

// 项目全选
const selectAll = () => {
  formData.value.prog = progList.value.map((item: any) => item.key)
}

// loading
const dataLoading = ref(false)

// 生命周期
onMounted(() => {
  // 新增
  if (props.mode === 'ADD') {
    // 展示搜索
    searchForm.value.isShowSearch = true
  } else if (props.mode === 'EDIT') {
    // 隐藏搜索
    searchForm.value.isShowSearch = false
  }

})
</script>

<template>
  <div class="free-from-test-add">
    <el-form ref="searchFormRef" :model="searchForm" label-width="120px" v-show="searchForm.isShowSearch">
      <el-form-item prop="stuCode" label="学籍号">
        <el-input v-model="searchForm.stuCode" placeholder="请输入学籍号" maxlength="19">
          <template #append>
            <el-button icon="Search" @click="searchStu"></el-button>
          </template>
        </el-input>
      </el-form-item>
    </el-form>
    <el-descriptions
      :column="1"
      size="default"
      border
      v-if="searchForm.isHaveResult"
    >
      <el-descriptions-item label-width="100" label="学生姓名">{{ searchForm.stuInfo.studentName }}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="身份证号">{{ searchForm.stuInfo.idCard }}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="性别">{{ searchForm.stuInfo.gender }}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="所在班级">{{ searchForm.stuInfo.clazz }}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="所属学校">{{ searchForm.stuInfo.school }}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="所属区县">{{ searchForm.stuInfo.county }}</el-descriptions-item>
    </el-descriptions>
    <el-form ref="formRef" :model="formData" v-loading="dataLoading" label-width="120px" :rules="rules">
      <el-form-item prop="applyTp" label="申请类别">
        <el-select v-model="formData.applyTp" clearable placeholder="申请类别">
          <el-option v-for="item in applyTpList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="prog" label="项目">
        <el-row :gutter="15">
          <el-col :span="20">
            <el-select v-model="formData.prog" clearable placeholder="项目" multiple collapse-tags style="min-width: 254px">
              <el-option v-for="item in progList" :key="item.key" :label="item.value" :value="item.key"></el-option>
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-button plain type="success" @click="selectAll">全选</el-button>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item prop="period" label="免考/缓考期限">
        <TermSelector v-model="formData.period" show-type="select" placeholder="请选择免考/缓考期限" style="min-width: 190px"></TermSelector>
      </el-form-item>
      <el-form-item prop="reason" label="类别情况说明">
        <el-input v-model="formData.reason" :rows="5" type="textarea" placeholder="类别情况说明" show-word-limit
                  maxlength="500"/>
      </el-form-item>
    </el-form>
  </div>
</template>

<style scoped lang="scss">

</style>
