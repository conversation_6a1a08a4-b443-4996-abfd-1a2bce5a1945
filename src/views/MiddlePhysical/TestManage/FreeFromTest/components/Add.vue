<script setup lang="ts">
import {ref, onMounted} from 'vue'
import {applyTpList} from "@/views/MiddlePhysical/TestManage/FreeFromTest/dic"
const progList = defineModel<any>('progList')
import TermSelector from "@/components/TermSelector/index.vue"
import {apis} from "@/apis"

const regionId = defineModel<string>('regionId')

// 表单
const searchForm = ref({
  stuCode: "",
  stuInfo: {
    studentName: "",
    idCard: "",
    gender: "",
    clazz: "",
    school: "",
    county: ""
  },
  isHaveResult: false,
  isShowSearch: false
})

const formData = ref({
  studentId: "123456",
  studentName: "李11",
  xjh: "G120102201011303215",
  sfzh: "120102201011303215",
  gradeId: "123456798",
  clazzId: "123456798",
  clazzName: "测试班级",
  schoolId: "1963423667933380610",
  schoolName: "测试学校",
  regionId: "130102",
  regionName: "测试区县",
  applyForCategary: "",
  categaryDescription: "情况说明",
  items: "123456",
  termLimit: "2025级上学期",
  zuozhengResource: ""
})
const searchFormRef = ref()
const formRef = ref()

// 表单验证规则
const rules = {
  applyForCategary: [{required: true, message: '请选择年级', trigger: 'change'}],
  termLimit: [{required: true, message: '请选择学校', trigger: 'change'}]
}

// props
const props = defineProps({
  mode: {
    type: [String, null],
    default: 'EDIT'
  },
  rowData: {
    type: [Object, null],
    default: () => {}
  }
})

// 根据学籍号搜索学生
const searchStu = async () => {
  // searchForm.value.isHaveResult = true
  // console.log(formData.value.termLimit)
  // const response = await apis.FreeFromTest.list(search.value)
  // response.data
  const response = await apis.Commons.jianSuoSuspend(searchForm.value.stuCode, regionId)
  console.log(response)
}

// 项目全选
const selectAll = () => {
  formData.value.items = progList.value.map((item: any) => item.key)
}

// loading
const dataLoading = ref(false)

// 生命周期
onMounted(() => {
  // 新增
  if (props.mode === 'ADD') {
    // 展示搜索
    searchForm.value.isShowSearch = true
  } else if (props.mode === 'EDIT') {
    // 隐藏搜索
    searchForm.value.isShowSearch = false
  }
})

// 保存方法
const saveSubmit = async () => {
  apis.FreeFromTest.addStu(formData.value, regionId.value).then((res: any) => {
    console.log(res)
  }).catch((err: any) => {
    console.log(err)
  })
}

defineExpose({
  saveSubmit
})
</script>

<template>
  <div class="free-from-test-add">
    <el-form ref="searchFormRef" :model="searchForm" label-width="120px" v-show="searchForm.isShowSearch">
      <el-form-item prop="stuCode" label="学籍号">
        <el-input v-model="searchForm.stuCode" placeholder="请输入学籍号" maxlength="19">
          <template #append>
            <el-button icon="Search" @click="searchStu"></el-button>
          </template>
        </el-input>
      </el-form-item>
    </el-form>
    <el-descriptions
      :column="1"
      size="default"
      border
      v-if="searchForm.isHaveResult"
    >
      <el-descriptions-item label-width="100" label="学生姓名">{{ searchForm.stuInfo.studentName }}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="身份证号">{{ searchForm.stuInfo.idCard }}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="性别">{{ searchForm.stuInfo.gender }}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="所在班级">{{ searchForm.stuInfo.clazz }}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="所属学校">{{ searchForm.stuInfo.school }}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="所属区县">{{ searchForm.stuInfo.county }}</el-descriptions-item>
    </el-descriptions>
    <el-form ref="formRef" :model="formData" v-loading="dataLoading" label-width="120px" :rules="rules">
      <el-form-item prop="applyForCategary" label="申请类别">
        <el-select v-model="formData.applyForCategary" clearable placeholder="申请类别">
          <el-option v-for="item in applyTpList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="items" label="项目">
        <el-row :gutter="15">
          <el-col :span="20">
            <el-select v-model="formData.items" clearable placeholder="项目" multiple collapse-tags style="min-width: 254px">
              <el-option v-for="item in progList" :key="item.key" :label="item.value" :value="item.key"></el-option>
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-button plain type="success" @click="selectAll">全选</el-button>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item prop="termLimit" label="免考/缓考期限">
        <TermSelector v-model="formData.termLimit" show-type="select" placeholder="请选择免考/缓考期限" style="min-width: 190px"></TermSelector>
      </el-form-item>
      <el-form-item prop="categaryDescription" label="类别情况说明">
        <el-input v-model="formData.categaryDescription" :rows="5" type="textarea" placeholder="类别情况说明" show-word-limit
                  maxlength="500"/>
      </el-form-item>
    </el-form>
  </div>
</template>

<style scoped lang="scss">

</style>
