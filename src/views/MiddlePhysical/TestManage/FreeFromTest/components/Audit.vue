<script setup lang="ts">
import {ref, onMounted} from 'vue'

const formData = ref({
  id: "",
  reason: "",
})

// 表单验证规则
const rules = {
  reason: [{required: true, message: '请输入驳回原因', trigger: 'blur'}]
}
const formRef = ref()

// loading
const dataLoading = ref(false)

// 生命周期
onMounted(() => {

})
</script>

<template>
  <div class="free-from-test-audit">
    <el-form ref="formRef" :model="formData" v-loading="dataLoading" label-width="120px" :rules="rules">
      <el-form-item prop="reason" label="驳回原因">
        <el-input v-model="formData.reason" :rows="5" type="textarea" placeholder="驳回原因" show-word-limit
                  maxlength="200"/>
      </el-form-item>
    </el-form>
  </div>
</template>

<style scoped lang="scss">

</style>
