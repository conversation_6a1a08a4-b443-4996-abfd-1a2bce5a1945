<script setup lang="ts">
import {ref, onMounted} from 'vue'

// 详情 - 基本信息
const applyDetailTable = ref({
  stuCode: "",
  studentName: "",
  idCard: "",
  gender: "",
  clazz: "",
  school: "",
  county: "",
  applyTp: "",
  applyProg: "",
  period: "",
  desc: "",
  proof: "",
  status: "",
  remark: ""
})

// 详情 - 审核信息
const auditInfo = ref({
  county: [],
  city: []
})

// 生命周期
onMounted(() => {

})
</script>

<template>
  <div class="free-from-test-detail">
    <el-row :gutter="20">
      <el-col :span="12">
        <div class="header">基本信息</div>
        <el-descriptions
          :column="1"
          size="default"
          border
        >
          <el-descriptions-item label-width="120px" label="学籍号">{{ applyDetailTable.stuCode }}</el-descriptions-item>
          <el-descriptions-item label-width="120px" label="姓名">{{ applyDetailTable.studentName }}</el-descriptions-item>
          <el-descriptions-item label-width="120px" label="身份证号">{{ applyDetailTable.idCard }}</el-descriptions-item>
          <el-descriptions-item label-width="120px" label="性别">{{ applyDetailTable.gender }}</el-descriptions-item>
          <el-descriptions-item label-width="120px" label="所在班级">{{ applyDetailTable.clazz }}</el-descriptions-item>
          <el-descriptions-item label-width="120px" label="所属学校">{{ applyDetailTable.school }}</el-descriptions-item>
          <el-descriptions-item label-width="120px" label="所属区县">{{ applyDetailTable.county }}</el-descriptions-item>
          <el-descriptions-item label-width="120px" label="申请类别">{{
              applyDetailTable.applyTp
            }}
          </el-descriptions-item>
          <el-descriptions-item label-width="120px" label="申请项目">{{
              applyDetailTable.applyProg
            }}
          </el-descriptions-item>
          <el-descriptions-item label-width="120px" label="免考缓考期限">{{
              applyDetailTable.period
            }}
          </el-descriptions-item>
          <el-descriptions-item label-width="120px" label="类别情况说明">{{
              applyDetailTable.desc
            }}
          </el-descriptions-item>
          <el-descriptions-item label-width="120px" label="佐证材料">{{ applyDetailTable.proof }}</el-descriptions-item>
          <el-descriptions-item label-width="120px" label="审核状态">{{
              applyDetailTable.status
            }}
          </el-descriptions-item>
          <el-descriptions-item label-width="120px" label="备注">{{ applyDetailTable.remark }}</el-descriptions-item>
        </el-descriptions>
      </el-col>
      <el-col :span="12">
        <div class="header">区县教育局审核</div>
        <!-- active的值是list长度减1 -->
        <template v-if="auditInfo.county.length > 0">
          <el-steps
            :space="20"
            :active="1"
            direction="vertical"
          >
            <el-step>
              <template #description>
                <div class="audit-step-wrap">
                  <div class="first-line">
                    <h2 class="auditor">旺旺</h2>
                    <el-tag type="primary" effect="dark">红旗飘飘</el-tag>
                  </div>
                  <div class="second-line">通过原因：最新报价在新农村</div>
                  <div class="third-line">2025-08-08 23:21:21</div>
                </div>
              </template>
            </el-step>
            <el-step>
              <template #description>
                <div class="audit-step-wrap">
                  <div class="first-line">
                    <h2 class="auditor">旺旺</h2>
                    <el-tag type="danger" effect="dark">红旗飘飘</el-tag>
                  </div>
                  <div class="second-line">通过原因：最新报价在新农村</div>
                  <div class="third-line">2025-08-08 23:21:21</div>
                </div>
              </template>
            </el-step>
          </el-steps>
        </template>
        <el-empty :image-size="100" description="暂无信息" v-else/>
        <div class="header">市教育局审核</div>
        <template v-if="auditInfo.city.length > 0">

        </template>
        <el-empty :image-size="100" description="暂无信息" v-else/>
      </el-col>
    </el-row>
  </div>
</template>

<style scoped lang="scss">
.free-from-test-detail {
  .header {
    border-left: 5px solid var(--el-color-primary);
    margin-bottom: 10px;
    padding-left: 10px;
  }

  .audit-step-wrap {
    margin-top: -5px;
    padding: 0 10px 20px;
    border-radius: 6px;

    .first-line {
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }

    .second-line {
      margin: 5px 0;
    }

    .third-line {
      color: var(--el-color-info);
    }

    .auditor {
      margin-right: 10px;
    }
  }
}
</style>
