<script setup lang="ts">
import {ref, onMounted} from "vue"
import {useTableCommons} from "@/commons/TableCommons"
import GradeSelector from "@/components/GradeSelector/index.vue"
import {DialogMode} from "@/commons/DialogModel"
import DetailForm from "@/views/MiddlePhysical/TestManage/ProjectSetting/components/DrawTestSetStep1.vue"
import AddForm from "@/views/MiddlePhysical/TestManage/ProjectSetting/components/DrawTestSetStep2.vue"

// import {ElMessageBox, ElNotification} from "element-plus"
import {apis} from "@/apis"

// 主列表
const getTableData = async () => {
  const response = await apis.RegionManage.getPageData(search.value)
  return response.data
}

// 可用的所有科目
const availProg = ref([
  { name: "风来时", score: "" },
  { name: "辽国", score: "" },
  { name: "过往的", score: "" },
  { name: "向着西北的", score: "" },
  { name: "啜泣者的", score: "" }
])

// 抽的个数
const drawNum = ref("0")
// 抽到的科目
const resultProg = ref([])

// 主列表搜索条件
const search = ref({
  pageNumber: 1,
  pageSize: 10,
  keywords: "",
  gradeId: ""
})

// 通用配置
let {
  loading,
  dialogModel,
  showFormDialog,
  tableData,
  searchSubmit,
  pageOrSizeChange
} = useTableCommons(search, getTableData)

onMounted(() => {
  searchSubmit()
})

// 保存
const addForm = ref()
const editForm = ref()
const saveSubmit = () => {
  // dialogModel.value.saveLoading = true
  // addForm.value.saveSubmit()
}

// 开始抽取
const startDraw = () => {
  // dialogModel.value.visible = false
  let shffuleList = JSON.parse(JSON.stringify(availProg.value))
  shffuleList.sort(() => 0.5 - Math.random())
  console.log(shffuleList)

  // showFormDialog('抽考科目','AddForm', {}, DialogMode.ADD, '500px')
}

// 重新抽取
const reDraw = () => {
  dialogModel.value.visible = false
  resultProg.value = []
  showFormDialog('抽考科目设定','DetailForm', {}, DialogMode.DETAIL, '500px')
}

</script>

<template>
  <div class="draw-test">
    <div class="lmt-operation-container">
      <div class="lmt-operation">

      </div>
      <div class="lmt-search">
        <GradeSelector v-model="search.gradeId" show-type="select"></GradeSelector>
        <el-button icon="Search" type="primary" @click="searchSubmit"></el-button>
      </div>
    </div>
    <el-table v-loading="loading" :data="tableData.records" border
              max-height="calc(100vh - var(--lmt-header-height) - var(--lmt-main-padding) * 2 - 3rem - var(--lmt-pager-height)"
              stripe>
      <el-table-column align="center" fixed="left" label="序号" type="index" width="80">
        <template #default="scope">
          {{ (search.pageNumber - 1) * search.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="年级" width="200" prop="name" show-overflow-tooltip></el-table-column>
      <el-table-column label="抽考科目（男生）" min-width="100" prop="name" show-overflow-tooltip></el-table-column>
      <el-table-column label="抽考科目（女生）" min-width="100" prop="name" show-overflow-tooltip></el-table-column>
      <el-table-column label="抽取状态" width="150" prop="name" show-overflow-tooltip></el-table-column>
      <el-table-column label="操作" width="100">
        <template #default="scope">
          <el-link icon="Operation" type="primary"
                   @click="showFormDialog('抽考科目设定','DetailForm',scope.row, DialogMode.ADD, '500px')">去设置
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-model:current-page="search.pageNumber" v-model:page-size="search.pageSize"
                   :page-sizes="[10, 20, 30, 40, 50, 100]" :total="Number(tableData.total)"
                   background
                   class="m-t-4 float-right" layout="total, prev, pager, next, sizes, ->" @change="pageOrSizeChange">
    </el-pagination>
    <el-dialog v-model="dialogModel.visible" :title="dialogModel.title" :width="dialogModel.width" destroy-on-close
               @closed="dialogModel.closed()" :close-on-click-modal="false">
      <DetailForm ref="detailForm" v-if="dialogModel.formName == 'DetailForm'" :mode="dialogModel.mode" v-model:availProg="availProg" :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete" />
      <AddForm ref="addForm" v-else-if="dialogModel.formName == 'AddForm'" :mode="dialogModel.mode"
               :row-data="dialogModel.rowData" v-model:resultProg="resultProg" @save-complete="dialogModel.saveComplete"/>
      <template #footer>
        <el-button @click="dialogModel.visible = false">取消</el-button>
        <el-button @click="startDraw">开始抽取</el-button>
        <el-button @click="reDraw">重新抽取</el-button>
        <el-button :loading="dialogModel.saveLoading" type="primary" @click="saveSubmit">确定科目</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">

</style>
