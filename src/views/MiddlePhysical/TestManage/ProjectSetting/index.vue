<script setup lang="ts">
import {ref, onMounted, readonly} from "vue"
import MustTest from "@/views/MiddlePhysical/TestManage/ProjectSetting/MustTest.vue"
import DrawTest from "@/views/MiddlePhysical/TestManage/ProjectSetting/DrawTest.vue"
import OptionTest from "@/views/MiddlePhysical/TestManage/ProjectSetting/OptionTest.vue"

// 菜单
const tabList = readonly([
  { key: 'mustTest', value: '必考科目' },
  { key: 'drawTest', value: '抽考科目' },
  { key: 'optionTest', value: '选考科目' }
])

// 当前选中tab
let curTab = ref('mustTest')

// tab切换
const tabChange = (tab: string) => {
  console.log(tab)
  curTab.value = tab
}

// 生命周期
onMounted(() => {

})
</script>

<template>
  <div class="proj-setting">
    <el-container class="sub-menu-in-page">
      <el-aside width="200px">
        <div class="menu-title">中考科目设定</div>
        <ul class="menu-list">
          <li
            v-for="item in tabList"
            :key="item.key"
            :class="['menu-item', { active: curTab === item.key }]"
            @click="tabChange(item.key)">
            <span class="menu-text">{{ item.value }}</span>
          </li>
        </ul>
      </el-aside>
      <el-main>
        <MustTest v-if="curTab == tabList[0].key" />
        <DrawTest v-else-if="curTab == tabList[1].key" />
        <OptionTest v-else-if="curTab == tabList[2].key" />
      </el-main>
    </el-container>
  </div>
</template>

<style scoped lang="scss">
.proj-setting {
  height: 100%;
}
</style>
