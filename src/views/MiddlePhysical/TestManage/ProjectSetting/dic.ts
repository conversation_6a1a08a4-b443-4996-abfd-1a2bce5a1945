import type { KeyValue } from "@/commons/KeyValue"

// 分值
let scoreList: Array<KeyValue> = [
  { key: "6", value: "6" },
  { key: "7", value: "7" },
  { key: "8", value: "8" },
  { key: "9", value: "9" },
  { key: "10", value: "10" },
  { key: "11", value: "11" },
  { key: "12", value: "12" },
  { key: "13", value: "13" },
  { key: "14", value: "14" },
  { key: "15", value: "15" }
]

export { scoreList }
