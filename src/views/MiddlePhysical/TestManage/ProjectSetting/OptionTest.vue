<script setup lang="ts">
import {ref, onMounted} from "vue"
import {useTableCommons} from "@/commons/TableCommons"
import {DialogMode} from "@/commons/DialogModel"
import {ElMessageBox, ElNotification} from "element-plus"
import {apis} from "@/apis"

// 主列表
const getTableData = async () => {
  const response = await apis.RegionManage.getPageData(search.value)
  return response.data
}

// 主列表搜索条件
const search = ref({
  pageNumber: 1,
  pageSize: 10,
  keywords: "",
  schoolId: "",
  grade: "",
  clazz: "",
  applyTp: "",
  prog: "",
  status: ""
})

// 通用配置
let {
  loading,
  dialogModel,
  showFormDialog,
  tableData,
  searchSubmit,
  pageOrSizeChange
} = useTableCommons(search, getTableData)

onMounted(() => {

})
</script>

<template>
  <div class="option-test">
    选考科目
  </div>
</template>

<style scoped lang="scss">

</style>
