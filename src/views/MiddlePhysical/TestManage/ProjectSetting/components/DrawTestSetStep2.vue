<script setup lang="ts">
import {ref, onMounted} from "vue"
import { scoreList } from "@/views/MiddlePhysical/TestManage/ProjectSetting/dic"
import {ElNotification} from "element-plus"

// 抽取到的科目
const progList = defineModel('resultProg')

onMounted(() => {

})

// 提交前验证
const beforeSubmit = () => {
  // 错误信息汇总
  let errorTips: string[] = []
  progList.value.forEach(item => {
    // 已勾选但分数为空的
    if (item.score == "") {
      errorTips.push(`请为科目 ‘${ item.name }’ 设定分数`)
    }
  })
  // 有错误才提示
  if (errorTips.length > 0) {
    let finalErrorTips = errorTips.join('<br />')
    ElNotification.success({message: `${ finalErrorTips }`, dangerouslyUseHTMLString: true, title: "温馨提示", duration: 0})
  } else {
    // 否则直接提交

  }
}
</script>

<template>
  <div class="draw-test-step2 modal-page">
    <div class="header">已抽取科目</div>
    <el-table :data="progList" border stripe >
      <el-table-column align="center" label="序号" type="index" width="80">
        <template #default="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="考试科目" prop="name" show-overflow-tooltip></el-table-column>
      <el-table-column align="center" label="分数设置" prop="score" show-overflow-tooltip>
        <template #default="scope">
          <el-select v-model="scope.row.score" placeholder="设定分数">
            <el-option v-for="item in scoreList" :key="item.key" :label="item.value" :value="item.key"></el-option>
          </el-select>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<style scoped lang="scss">

</style>
