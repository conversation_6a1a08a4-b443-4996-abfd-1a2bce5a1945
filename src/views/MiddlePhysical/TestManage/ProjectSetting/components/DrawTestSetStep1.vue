<script setup lang="ts">
import {ref, onMounted} from "vue"

// 全部科目
const allProgList = defineModel('availProg')
// 抽取数量
const drawForm = ref({
  num: ""
})

// 表单验证规则
const rules = {
  num: [{required: true, message: '请选择抽取数量', trigger: 'change'}]
}

onMounted(() => {

})

// 保存方法
const saveSubmit = async () => {
  /*apis.FreeFromTest.addStu(formData.value, regionId.value).then((res: any) => {
    console.log(res)
  }).catch((err: any) => {
    console.log(err)
  })*/
}

defineExpose({
  saveSubmit
})
</script>

<template>
  <div class="draw-test-step1 modal-page">
    <div class="header">抽考科目范围</div>
    <el-table :data="allProgList" border stripe >
      <el-table-column align="center" label="序号" type="index" width="80">
        <template #default="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="考试科目" prop="name" show-overflow-tooltip></el-table-column>
    </el-table>
    <el-form ref="formRef" :model="drawForm" label-width="120px" :rules="rules">
      <el-form-item prop="num" label="抽取科目数量">
        <el-select v-model="drawForm.num" placeholder="请设置数量">
          <el-option v-for="n in 3" :key="n" :value="n"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<style scoped lang="scss">

</style>
