<script setup lang="ts">
import {ref, onMounted} from "vue"
import { scoreList } from "@/views/MiddlePhysical/TestManage/ProjectSetting/dic"
import {ElNotification} from "element-plus"

// 男列表相关
const maleList = ref({
  // 待选列表
  list: [{
    name: '排球正面双手垫球',
    status: true,
    score: ""
  }, {
    name: '科目2',
    status: false,
    score: "6"
  }, {
    name: '羽毛球正反手击球',
    status: true,
    score: "9"
  }],
  // 当前选中
  cur: -1
})

// 女列表相关
const femaleList = ref({
  list: [{
    name: '500米轮滑',
    status: true,
    score: ""
  }, {
    name: '足球运球绕杆',
    status: false,
    score: "11"
  }, {
    name: '引体向上',
    status: true,
    score: "9"
  }],
  cur: -1
})

// 生命周期
onMounted(() => {

})

// 提交前的验证
const beforeSubmit = () => {
  // 错误信息汇总
  let errorTips: string[] = []
  // 筛出可用的行：已勾选 && 分数不为空
  let availListMale = maleList.value.list.filter(item => item.status && item.score !== "")
  let availListFemale = femaleList.value.list.filter(item => item.status && item.score !== "")
  console.log(availListMale, availListFemale)
  // 如果既没勾选也没设置分数
  if (availListMale.length == 0) {
    errorTips.push('请为男生选择必考科目')
  } else {
    maleList.value.list.forEach(item => {
      // 已勾选但分数为空的
      if (item.status && item.score == "") {
        errorTips.push(`请为男生所选科目 ‘${ item.name }’ 设定分数`)
      }
    })
  }
  if (availListFemale.length == 0) {
    errorTips.push('请为女生选择必考科目')
  } else {
    femaleList.value.list.forEach(item => {
      if (item.status && item.score == "") {
        errorTips.push(`请为女生所选科目 ‘${ item.name }’ 设定分数`)
      }
    })
  }
  // 有错误才提示
  if (errorTips.length > 0) {
    let finalErrorTips = errorTips.join('<br />')
    ElNotification.success({message: `${ finalErrorTips }`, dangerouslyUseHTMLString: true, title: "温馨提示", duration: 0})
  } else {
    // 否则直接提交

  }
}
</script>

<template>
  <div class="must-test-set modal-page">
    <div class="header">男生必考科目设定</div>
    <el-table :data="maleList.list" border stripe>
      <el-table-column align="center" label="序号" type="index" width="80">
        <template #default="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="考试科目" min-width="120" prop="name" show-overflow-tooltip></el-table-column>
      <el-table-column align="center" label="状态" width="120" prop="name">
        <template #default="scope">
          <el-checkbox v-model="scope.row.status" size="large" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="分数设置" width="150" prop="name">
        <template #default="scope">
          <el-select v-model="scope.row.score" placeholder="设定分数">
            <el-option v-for="item in scoreList" :key="item.key" :label="item.value" :value="item.key"></el-option>
          </el-select>
        </template>
      </el-table-column>
    </el-table>
    <div class="header">女生必考科目设定</div>
    <el-table :data="femaleList.list" border stripe>
      <el-table-column align="center" label="序号" type="index" width="80">
        <template #default="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="考试科目" min-width="120" prop="name" show-overflow-tooltip></el-table-column>
      <el-table-column align="center" label="状态" width="120" prop="name">
        <template #default="scope">
          <el-checkbox v-model="scope.row.status" size="large" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="分数设置" width="150" prop="name">
        <template #default="scope">
          <el-select v-model="scope.row.score" placeholder="设定分数">
            <el-option v-for="item in scoreList" :key="item.key" :label="item.value" :value="item.key"></el-option>
          </el-select>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<style scoped lang="scss">

</style>
