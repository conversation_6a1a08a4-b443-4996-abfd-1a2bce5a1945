<script setup lang="ts">
import {ref, onMounted} from "vue"
import {useTableCommons} from "@/commons/TableCommons"
import {apis} from "@/apis"
import {DialogMode} from "@/commons/DialogModel"
import {ElMessageBox, ElNotification} from "element-plus"
import { testTpList } from "@/views/MiddlePhysical/TestManage/ProjManage/dic"
import AddForm from "@/views/MiddlePhysical/TestManage/ProjManage/components/Add.vue"
import EditForm from "@/views/MiddlePhysical/TestManage/ProjManage/components/Add.vue"

// 主列表
const getTableData = async () => {
  const response = await apis.RegionManage.getPageData(search.value)
  return response.data
}

// 主列表搜索条件
const search = ref({
  pageNumber: 1,
  pageSize: 10,
  keywords: "",
  testTp: "",
  status: ""
})

// 通用配置
let {
  loading,
  dialogModel,
  showFormDialog,
  tableData,
  searchSubmit,
  pageOrSizeChange
} = useTableCommons(search, getTableData)

// 弹窗内按钮方法
const addForm = ref()
const editForm = ref()
const saveSubmit = () => {
  dialogModel.value.saveLoading = true
  if (dialogModel.value.formName === 'AddForm') {
    addForm.value.saveSubmit()
  } else if (dialogModel.value.formName === 'EditForm') {
    editForm.value.saveSubmit()
  }
}
// 生命周期
onMounted(() => {
  searchSubmit()
})

// 启用
const enable = (row: any) => {
  ElMessageBox.confirm(`确认删除【${row.xm}】的申请吗？`, "温馨提示",
    {confirmButtonText: '确定', cancelButtonText: '取消', type: 'error'})
    .then(async () => {
      await apis.TeacherManage.delete(row.id)
      ElNotification.success({message: "操作成功", title: "温馨提示"})
      await searchSubmit()
    })
}

// 禁用
const disable = (row: any) => {
  ElMessageBox.confirm(`确认删除【${row.xm}】的申请吗？`, "温馨提示",
    {confirmButtonText: '确定', cancelButtonText: '取消', type: 'error'})
    .then(async () => {
      await apis.TeacherManage.delete(row.id)
      ElNotification.success({message: "操作成功", title: "温馨提示"})
      await searchSubmit()
    })
}

// 删除
const del = (row: any) => {
  ElMessageBox.confirm(`确认删除【${row.xm}】的申请吗？`, "温馨提示",
    {confirmButtonText: '确定', cancelButtonText: '取消', type: 'error'})
    .then(async () => {
      await apis.TeacherManage.delete(row.id)
      ElNotification.success({message: "操作成功", title: "温馨提示"})
      await searchSubmit()
    })
}
</script>

<template>
  <div class="proj-manage">
    <div class="lmt-operation-container">
      <div class="lmt-operation">
        <el-button icon="Plus" type="primary"
                   @click="showFormDialog('添加科目','AddForm', {  }, DialogMode.ADD, '500px')">添加
        </el-button>
      </div>
      <div class="lmt-search">
        <el-select v-model="search.testTp" clearable placeholder="考试类别" class="test-tp">
          <el-option v-for="item in testTpList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <el-select v-model="search.status" clearable placeholder="审核状态">
          <el-option label="启用" value="1"></el-option>
          <el-option label="禁用" value="0"></el-option>
        </el-select>

      </div>
    </div>
    <el-table v-loading="loading" :data="tableData.records" border
              max-height="calc(100vh - var(--lmt-header-height) - var(--lmt-main-padding) * 2 - 3rem - var(--lmt-pager-height)"
              stripe>
      <el-table-column align="center" fixed="left" label="序号" type="index" width="80">
        <template #default="scope">
          {{ (search.pageNumber - 1) * search.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="科目名称" min-width="80" prop="name" show-overflow-tooltip></el-table-column>
      <el-table-column label="考试类别" min-width="150" prop="name" show-overflow-tooltip></el-table-column>
      <el-table-column label="适应人群" min-width="100" prop="name"></el-table-column>
      <el-table-column label="创建时间" min-width="80" prop="name"></el-table-column>
      <el-table-column label="状态" width="80" prop="name"></el-table-column>
      <el-table-column fixed="right" label="操作" width="220">
        <template #default="scope">
          <!--  v-has-perm="['region:edit']" -->
          <el-link icon="Edit" type="warning"
                   @click="showFormDialog('修改','EditForm', scope.row, DialogMode.EDIT)">编辑
          </el-link>
          <el-link icon="Check" type="success"
                   @click="enable(scope.row)">启用
          </el-link>
          <el-link icon="Close" type="danger"
                   @click="disable(scope.row)">禁用
          </el-link>
          <el-link icon="Delete" type="danger"
                   @click="del(scope.row)">删除
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-model:current-page="search.pageNumber" v-model:page-size="search.pageSize"
                   :page-sizes="[10, 30, 40, 50, 100]" :total="Number(tableData.total)"
                   background
                   class="m-t-4 float-right" layout="total, prev, pager, next, sizes, ->" @change="pageOrSizeChange">
    </el-pagination>
    <el-dialog v-model="dialogModel.visible" :title="dialogModel.title" :width="dialogModel.width" destroy-on-close
               @closed="dialogModel.closed()" :close-on-click-modal="false">
      <AddForm v-if="dialogModel.formName == 'AddForm'" ref="addForm" :mode="dialogModel.mode"
               :row-data="{}" @save-complete="dialogModel.saveComplete"/>
      <EditForm v-if="dialogModel.formName == 'EditForm'" ref="editForm" :mode="dialogModel.mode"
                :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <template #footer>
        <el-button @click="dialogModel.visible = false">
          {{ dialogModel.formName !== 'DetailForm' ? '取 消' : '关 闭' }}
        </el-button>
        <el-button v-if="dialogModel.formName !== 'DetailForm'" :loading="dialogModel.saveLoading" type="primary"
                   @click="saveSubmit">确认
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.proj-manage {
  .test-tp {
    min-width: 180px;
  }
}
</style>
