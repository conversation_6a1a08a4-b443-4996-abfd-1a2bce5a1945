<script setup lang="ts">
import {ref, onMounted} from 'vue'
import { testTpList } from "@/views/MiddlePhysical/TestManage/ProjManage/dic"

const formData = ref({
  projName: "",
  testTp: "",
  suit: "",
})
const formRef = ref()

// 表单验证规则
const rules = {
  projName: [{required: true, message: '请输入科目', trigger: 'blur'}],
  testTp: [{required: true, message: '请选择考试类别', trigger: 'change'}],
  suit: [{required: true, message: '请选择适应人群', trigger: 'change'}]
}

// props
const props = defineProps({
  mode: {
    type: [String, null],
    default: 'EDIT'
  },
  rowData: {
    type: [Object, null],
    default: () => {}
  }
})
// loading
const dataLoading = ref(false)

// 生命周期
onMounted(() => {
  // 新增
  if (props.mode === 'ADD') {

  } else if (props.mode === 'EDIT') {

  }
})
</script>

<template>
  <div class="add-proj">
    <el-form ref="formRef" :model="formData" v-loading="dataLoading" label-width="120px" :rules="rules">
      <el-form-item prop="reason" label="类别情况说明">
        <el-input v-model="formData.projName" placeholder="类别情况说明" show-word-limit
                  maxlength="50"/>
      </el-form-item>
      <el-form-item prop="applyTp" label="申请类别">
        <el-select v-model="formData.testTp" clearable placeholder="申请类别">
          <el-option v-for="item in testTpList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="period" label="适应人群">
        <el-select v-model="formData.testTp" clearable placeholder="请选择适应人群">
          <el-option v-for="item in testTpList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>

    </el-form>
  </div>
</template>

<style scoped lang="scss">

</style>
