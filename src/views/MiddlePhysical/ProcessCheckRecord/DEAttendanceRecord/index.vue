<script lang="ts" setup>
/**
 * @description 课间操出勤率记录管理页面
 *
 * 功能点：
 * 1. 具备多级权限管理（市、区、校），不同权限下筛选条件的可操作性不同。
 * 2. 支持按学期、区县、学校、班级、是否残疾/伤病、关键字（姓名/学籍号）进行复合查询。
 * 3. 表格数据支持分页展示。
 * 4. 表格的月份列是动态生成的，根据查询结果中包含的月份动态展示。
 * 5. 学校管理员具备导入、导出、删除学生记录的功能。
 * 6. 使用了自定义组合式函数(useTableCommons)来封装通用的表格逻辑，使组件代码更简洁。
 */

// =================================================================================================
// 导入依赖模块
// =================================================================================================

// SYNTAX: `import { ... } from "..."` 是 ES6 模块导入语法，用于从其他文件中导入变量、函数或类。
// SYNTAX: `{ onMounted, ref, watch }` 是解构赋值语法，表示从 "vue" 模块中只导入这三个具名导出的函数。
import { onMounted, ref, watch } from "vue";
// 导入封装好的所有后端接口请求函数集合
import { apis } from "@/apis";
// 导入一个自定义的组合式函数 (Composable)，它封装了表格页面的通用逻辑（如加载、分页、搜索等）
import { useTableCommons } from "@/commons/TableCommons";
// 从 Pinia 状态管理库中导入用户和部门相关的 store
import { useDeptStore, useUserStore } from "@/store";
// 导入自定义的通知提示组件
import Notification from "@/commons/Notification";
// 导入一个工具函数，用于判断变量是否为空
import { isEmpty } from "@/utils/commonUtil";
// SYNTAX: `import ComponentName from "..."` 是默认导入语法，通常用于导入一个组件。
import SportRateRecodeImport from "@/views/MiddlePhysical/ProcessCheckRecord/DEAttendanceRecord/components/SportRateRecodeImport.vue";

// =================================================================================================
// 初始化 Store 和用户信息
// =================================================================================================

// SYNTAX: `const` 用于声明一个块级作用域的常量，其引用不可被重新赋值。
const userStore = useUserStore(); // 初始化用户 store 实例
const deptStore = useDeptStore(); // 初始化部门/组织架构 store 实例
const userInfo = userStore.userInfo; // 从 userStore 中获取当前登录的用户信息，方便后续进行权限判断

// =================================================================================================
// 定义组件状态 (响应式变量)
// =================================================================================================

// SYNTAX: `ref()` 是 Vue 3 的核心 API，用于创建一个响应式的数据引用。对它的修改会触发视图更新。
// SYNTAX: 在 <script setup> 内部，必须通过 `.value` 属性来访问或修改 ref 的值。
const termId = ref();
// 定义一个 ref 来存储当前选中的学校 ID
const schoolId = ref();
// SYNTAX: `let` 用于声明一个块级作用域的变量，其值可以被修改。
// SYNTAX: `||` 是逻辑或操作符，常用于提供默认值。如果 `userStore.regionId` 是假值（如 null, undefined），则使用 '130102'。
let currentRegionId = userStore.regionId || '130102';

// SYNTAX: 使用 `ref` 包裹一个对象，该对象内的所有属性都将成为响应式的。
// 这个对象的值会与模板中的输入框、选择框进行双向绑定 (v-model)
const search = ref({
  pageNumber: 1,          // 当前页码
  pageSize: 10,           // 每页显示条数
  keywords: "",           // 搜索关键字 (学生姓名/学籍号)
  termId: termId,         // 学期ID (与上面的 termId ref 关联)
  region: currentRegionId, // 区县ID (初始值为 currentRegionId)
  schoolId: schoolId,     // 学校ID (与上面的 schoolId ref 关联)
  clazzId: "",            // 班级ID
  sfcj: "",               // 是否残疾 (sfcj: Shi Fou Can Ji)
  sfsb: "",               // 是否伤病 (sfsb: Shi Fou Shang Bing)
});

// =================================================================================================
// 权限处理逻辑：根据用户角色，在组件加载时预设一些查询条件
// =================================================================================================

// SYNTAX: `?.` 是可选链操作符。在访问 `cityAdminFlag` 前，会先检查 `userInfo` 是否为 null 或 undefined。如果是，则表达式直接返回 undefined，避免了"Cannot read properties of null"的错误。
if (userInfo?.cityAdminFlag) {
  // 如果是市级管理员，将当前区县ID默认设置为 '130102'。市级管理员可以自由切换区县。
  currentRegionId = '130102';
}
if (userInfo?.schoolAdminFlag) {
  // 如果是学校管理员，查询条件的学校ID直接锁定为当前管理员所在的学校ID
  // SYNTAX: 这里通过 `.value` 来修改 ref 的值。
  schoolId.value = userInfo.schoolId;
}
if (userInfo?.countyAdminFlag) {
  // 如果是区县管理员，查询条件的区县ID直接锁定为当前管理员所在的区县ID
  currentRegionId = userInfo.regionId;
}

// =================================================================================================
// 定义用于渲染 UI 的数据列表
// =================================================================================================

// SYNTAX: `<any[]>` 是 TypeScript 的类型注解，表示 `termList` 这个 ref 内部的值是一个数组，数组元素可以是任何(any)类型。
// 学期列表
const termList = ref<any>([])
// 区县列表
const regionList = ref<any>([])
// 学校列表
const schoolList = ref<any>([])
// 班级列表
const classList = ref<any>([])
// 动态月份数据
const dynamicMonth = ref<any>([])
// SYNTAX: `ref([...])` 初始化一个包含对象的数组。`key` 和 `value` 是对象的属性。
const operateTypeList = ref([{ key: false, value: "否" }, { key: true, value: "是" }]);

// =================================================================================================
// 辅助函数与业务逻辑函数
// =================================================================================================

/**
 * @description 在动态月份表格中，根据月份和字段名查找并返回值
 * @param {any[]} monthList - 某学生的所有月度数据，例如 [{month: '9', bjcs: 1}, {month: '10', bjcs: 0}]
 * @param {any} targetMonth - 当前要渲染的月份，例如 '9'
 * @param {string} field - 要获取的字段名，例如 'bjcs' (病假次数)
 * @returns {any | null} - 返回找到的值，如果找不到则返回 null
 */
// SYNTAX: `(param1: type, ...)` 是 TypeScript 中为函数参数添加类型注解的写法。
// SYNTAX: `=>` 是 ES6 的箭头函数语法，一种更简洁的函数写法。
const getMonthValue = (monthList: any[], targetMonth: any, field: string) => {
  // SYNTAX: `.find()` 是数组方法，返回数组中满足提供的测试函数的第一个元素的值。
  const monthData = monthList.find(item => item.month === targetMonth);
  // SYNTAX: `? :` 是三元运算符，是 `if...else` 语句的简洁形式。`condition ? value_if_true : value_if_false`
  return monthData ? monthData[field] : null;
}

/**
 * @description 异步获取学期列表数据
 */
// SYNTAX: `async` 关键字用于声明一个异步函数，该函数内部可以使用 `await`。
const getTremFunc = async () => {
  // SYNTAX: `await` 操作符用于等待一个 Promise 解析。它会暂停 `async` 函数的执行，直到 Promise 完成，然后返回 Promise 的结果。这使得异步代码看起来像同步代码。
  let termResponse = await apis.Commons.getTermSelectList();
  let tremData = termResponse.data;
  // 如果获取失败或数据为空，则显示错误通知并终止执行
  if (isEmpty(tremData)) {
    // SYNTAX: `return` 语句会终止函数的执行并返回值。
    return Notification.showErrorNotify("获取学期列表失败，请检查学期管理！");
  }
  // 将获取到的数据赋值给 termList，用于渲染下拉框
  termList.value = tremData;
  // SYNTAX: `.filter()` 是数组方法，创建一个新数组，其包含通过所提供函数实现的测试的所有元素。
  let startTrem = tremData.filter((item: any) => item.extra === '1');
  if (startTrem && startTrem.length > 0) {
    // 如果有启用的学期，默认选中第一个
    termId.value = startTrem[0].key;
  } else {
    // 如果没有启用的学期，默认选中列表中的第一个学期
    termId.value = tremData[0].key;
  }
}

/**
 * @description 异步获取学校列表数据
 */
const getSchoolFunc = async () => {
  // 调用 Pinia store 的 action，根据当前区县ID获取学校列表
  await deptStore.setSchoolList(currentRegionId);
  // 从 store 的 state 中获取学校列表并赋值给响应式变量
  schoolList.value = deptStore.schoolList;
  // 如果当前用户是学校管理员，schoolId 已经被预设，这里再次赋值以确保同步
  schoolId.value = userInfo?.schoolId;
}

/**
 * @description 异步获取区县列表数据
 */
const getRegionFunc = async () => {
  // 调用 Pinia store 的 action 获取所有区县列表
  await deptStore.setRegionList();
  // 从 store 中获取区县列表并赋值
  regionList.value = deptStore.regionList;
}

/**
 * @description 当区县下拉框选择变化时触发的事件处理函数 (联动效果)
 * @param {any} value - 新选中的区县 ID
 */
const regionChange = async (value: any) => {
  currentRegionId = value; // 更新当前区县ID
  schoolList.value = null; // 清空旧的学校列表
  await deptStore.setSchoolList(value); // 根据新的区县ID获取新的学校列表
  schoolList.value = deptStore.schoolList; // 更新学校列表
  search.value.schoolId = ''; // 清空已选中的学校ID
  search.value.clazzId = '';  // 清空已选中的班级ID
}

/**
 * @description 当学校下拉框选择变化时触发的事件处理函数 (联动效果)
 * @param {any} value - 新选中的学校 ID
 */
const schoolChange = async (value: any) => {
  search.value.schoolId = value; // 更新搜索条件中的学校ID
  classList.value = null; // 清空旧的班级列表
  search.value.clazzId = ''; // 清空已选中的班级ID
  // 调用 API，根据学校ID和区县ID获取班级列表
  let classResponse = await apis.regionCommons.getClazzSelectList(value, null, currentRegionId);
  classList.value = classResponse.data; // 更新班级列表
}

// SYNTAX: `watch()` 是 Vue 的 API，用于侦听响应式数据的变化并执行回调。
watch(
  // SYNTAX: 第一个参数是要侦听的数据源。这里使用一个数组，同时侦听多个源。
  // SYNTAX: `() => search.value.region` 是一种 getter 函数写法。当侦听响应式对象的深层属性时，推荐使用这种方式以确保响应性。
  [() => search.value.region, () => search.value.schoolId], // 侦听的数据源：搜索条件中的区县和学校ID
  // SYNTAX: 第二个参数是回调函数，`newValues` 和 `oldValues` 分别是新旧值的数组。
  (newValues, oldValues) => {
    // SYNTAX: `[var1, var2] = array` 是数组的解构赋值语法。
    const [newRegion, newSchool] = newValues;
    const [oldRegion, oldSchool] = oldValues;
    // SYNTAX: `!==` 是严格不等于运算符。
    if (newRegion !== oldRegion) {
      regionChange(newRegion); // 调用区县变化的处理函数
    }
    // 如果学校发生了变化，并且新值不为空
    if (newSchool !== oldSchool && newSchool) {
      schoolChange(newSchool); // 调用学校变化的处理函数
    }
  },
  // SYNTAX: 第三个参数是配置对象。`{ immediate: true }` 使 watch 在初始化时就立即执行一次回调。
  { immediate: true } // 这对于在页面加载时根据初始值加载学校和班级列表至关重要。
);

/**
 * @description "导入"按钮点击事件处理函数
 */
const importData = async () => {
  // 控制导入弹窗的显示状态
  dialogModel.value.visible = true;
}

/**
 * @description "导出"按钮点击事件处理函数
 */
const handleExport = async () => {
  // 调用导出 API，并将当前的所有搜索条件传递给后端
  let exportResponse = await apis.RecessExerciseRateManage.export(search.value, currentRegionId);
  // SYNTAX: `console.log()` 是一个用于调试的 Web API，向控制台输出信息。
  console.log(exportResponse);
}

/**
 * @description "删除"操作的处理函数
 * @param {string} studentId - 要删除记录的学生ID
 * @param {string} termId - 对应的学期ID
 */
const deleteData = async (studentId: string, termId: string) => {
  // 调用删除 API
  let deleteResponse = await apis.RecessExerciseRateManage.deleteData(studentId, termId, currentRegionId);
  if (deleteResponse.data) {
    // 如果后端返回成功
    Notification.showSuccessNotify("删除成功");
  } else {
    Notification.showErrorNotify("删除失败");
  }
  // 删除成功后，调用 searchSubmit() 重新查询并刷新表格数据
  await searchSubmit();
}

/**
 * @description 获取主表格数据的核心函数。它会被 `useTableCommons` 内部调用。
 */
const getTableData = async () => {
  // 前端校验：查询前必须选择学期和区县
  if (isEmpty(search.value.termId)) {
    return Notification.showErrorNotify("查询失败，查询学期条件不能为空！");
  }
  if (isEmpty(search.value.region)) {
    return Notification.showErrorNotify("查询失败，查询区县条件不能为空！");
  }
  // 数据同步，防止因权限等原因导致查询参数与实际不符
  if (search.value.region !== currentRegionId) {
    currentRegionId = search.value.region;
  }
  if (search.value.schoolId !== schoolId.value) {
    search.value.schoolId = schoolId.value;
  }
  console.log("search.value", search.value); // 调试用，打印最终的查询参数

  // 调用获取分页数据的 API
  const response = await apis.RecessExerciseRateManage.getPageData(search.value, currentRegionId);

  // 动态表头逻辑：如果查询结果不为空
  if (response.data.records.length > 0) {
    // 就从第一条记录中提取月份信息列表，赋值给 dynamicMonth
    // 假设所有学生的学期月份都是一样的，所以取第一个即可
    dynamicMonth.value = response.data.records[0].monthInterclassExerciseInfoList;
  }

  // 返回 API 响应的数据，useTableCommons 会接收这个返回值并更新 tableData
  return response.data;
}

// =================================================================================================
// 导入弹窗相关操作
// =================================================================================================

// 创建一个 ref 来获取导入子组件的实例，以便调用其内部方法
const importForm = ref();
/**
 * @description 导入弹窗的“确认”按钮点击事件
 */
const saveSubmit = async () => {
  dialogModel.value.saveLoading = true; // 设置按钮为加载中状态，防止重复点击
  importForm.value.saveSubmit(); // 调用子组件 SportRateRecodeImport 暴露的 saveSubmit 方法
}
/**
 * @description 关闭弹窗的回调函数，会传递给子组件
 */
const closeDetail = () => {
  dialogModel.value.visible = false; // 将弹窗的可见状态设为 false
}

// =================================================================================================
// 使用自定义 Composable `useTableCommons` 封装通用表格逻辑
// =================================================================================================

// SYNTAX: `let { ... } = ...` 是对象的解构赋值。它从 `useTableCommons` 返回的对象中提取出指定的属性，并创建为同名的局部变量。这是一种非常方便的获取和使用 Composable 返回值的方式。
let {
  loading,          // 表格的加载状态 (boolean)，用于 v-loading
  dialogModel,      // 控制弹窗的状态对象 (e.g., { visible, saveLoading })
  tableData,        // 存储表格数据的响应式对象 (e.g., { records, total })
  searchSubmit,     // 搜索提交函数。调用它会重置页码为1并执行 getTableData
  pageOrSizeChange  // 分页变化处理函数。当页码或每页条数改变时，调用它会执行 getTableData
} = useTableCommons(search, getTableData); // 传入搜索条件和获取数据的方法

// =================================================================================================
// 生命周期函数
// =================================================================================================

// SYNTAX: `onMounted()` 是 Vue 的生命周期钩子函数。传递给它的回调函数会在组件被挂载到 DOM 后执行。
onMounted(async () => {
  // 初始化页面所需的下拉框数据
  await getTremFunc();    // 1. 获取学期列表
  await getRegionFunc();  // 2. 获取区县列表
  await getSchoolFunc();  // 3. 获取学校列表

  // 在所有基础数据加载完毕后，执行第一次数据查询，填充表格
  await searchSubmit();
});

</script>

<template>
  <div>
    <div class="lmt-operation-container">
      <div class="lmt-operation">
        <el-button icon="Upload" type="primary" @click="importData" v-has-perm="['interclassExercise:import']">导入</el-button>
        <el-button icon="Download" type="info" @click="handleExport" v-has-perm="['interclassExercise:export']">批量导出
        </el-button>
      </div>

      <div class="lmt-search">
        <el-select v-model="search.termId" placeholder="学期选择">
          <el-option v-for="item in termList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>

        <el-select v-model="search.region" placeholder="所属县区"
                   :disabled="userInfo?.schoolAdminFlag || userInfo?.countyAdminFlag">
          <el-option v-for="item in regionList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>

        <el-select v-model="search.schoolId" clearable placeholder="所属学校" :disabled="userInfo?.schoolAdminFlag">
          <el-option v-for="item in schoolList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>

        <el-select v-model="search.clazzId" clearable placeholder="所属班级">
          <el-option v-for="item in classList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>

        <el-select v-model="search.sfcj" clearable placeholder="是否残疾免试">
          <el-option v-for="item in operateTypeList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>

        <el-select v-model="search.sfsb" clearable placeholder="是否伤病免试">
          <el-option v-for="item in operateTypeList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>

        <el-input v-model="search.keywords" clearable placeholder="学生姓名/学籍号">
          <template #append>
            <el-button icon="Search" @click="searchSubmit"></el-button>
          </template>
        </el-input>
      </div>
    </div>

    <el-table v-loading="loading" :data="tableData.records" border
              max-height="calc(100vh - var(--lmt-header-height) - var(--lmt-main-padding) * 2 - 6rem)" stripe>

      <el-table-column align="center" label="序号" type="index" width="80">
        <template #default="scope">
          {{ (search.pageNumber - 1) * search.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>

      <el-table-column label="姓名" prop="studentName" width="120"></el-table-column>
      <el-table-column label="学籍号" prop="xjh" width="120"></el-table-column>
      <el-table-column label="性别" prop="xb">
        <template #default="scope">
          <el-tag>{{ scope.row.xb == 1 ? '男' : '女' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="所属班级" prop="clazzName" width="120"></el-table-column>
      <el-table-column label="所属学校" prop="schoolName" width="120"></el-table-column>
      <el-table-column label="所属县区" prop="regionName" width="100"></el-table-column>
      <el-table-column label="是否残疾免试" prop="sfcj" width="120">
        <template #default="scope">
          <el-tag :type="scope.row.sfcj?'error':'primary'">{{ scope.row.sfcj ? '是' : '否' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="是否伤病免试" prop="sfsb" width="120">
        <template #default="scope">
          <el-tag :type="scope.row.sfsb?'error':'primary'">{{ scope.row.sfsb ? '是' : '否' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="所属学期" prop="termName" width="100"></el-table-column>
      <el-table-column label="学期总课时" prop="xqzks" width="100"></el-table-column>

      <el-table-column :label="item.month + `月份`" align="center" v-for="item in dynamicMonth">
        <el-table-column label="病假次数" width="100">
          <template #default="scope">
            <span>{{ getMonthValue(scope.row.monthInterclassExerciseInfoList, item.month, 'bjcs') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="事假次数" width="100">
          <template #default="scope">
            <span>{{ getMonthValue(scope.row.monthInterclassExerciseInfoList, item.month, 'sjcs') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="旷课次数" width="100">
          <template #default="scope">
            <span>{{ getMonthValue(scope.row.monthInterclassExerciseInfoList, item.month, 'qqcs') }}</span>
          </template>
        </el-table-column>
      </el-table-column>

      <el-table-column label="缺勤天数(天)" min-width="120" prop="qqs"></el-table-column>
      <el-table-column label="出勤率(%)" min-width="100">
        <template #default="scope">
          <span>{{ scope.row.cql + '%' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="课分值" min-width="100" prop="cqScore"></el-table-column>

      <el-table-column fixed="right" label="操作" width="120" align="center" v-has-perm="['interclassExercise:delete']">
        <template #default="scope">
          <el-popconfirm width="220" confirm-button-text="确认" cancel-button-text="取消" title="确认是否删除？"
                         @confirm="deleteData(scope.row.studentId, scope.row.termId)">
            <template #reference>
              <el-button link icon="Delete" type="danger" size="small">删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>


    </el-table>

    <el-pagination v-model:current-page="search.pageNumber" v-model:page-size="search.pageSize"
                   :page-sizes="[10, 20, 30, 40, 50, 100]" :total="Number(tableData.total)"
                   background
                   class="m-t-4 float-right" layout="total, prev, pager, next, sizes, ->" @change="pageOrSizeChange">
    </el-pagination>

    <el-dialog v-model="dialogModel.visible" title="导入课间操考勤数据" width="800">
      <SportRateRecodeImport :region-id="currentRegionId" :term-list="termList" ref="importForm"
                             :close-on-click-modal="false" destroy-on-close
                             @save-complete="dialogModel.saveComplete" :mode="dialogModel.mode"
                             @closeDetail="closeDetail"></SportRateRecodeImport>
      <template #footer>
        <el-button @click="dialogModel.visible=false">取 消</el-button>
        <el-button type="primary" :loading="dialogModel.saveLoading" @click="saveSubmit">确 认</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped></style>
