<script setup lang="ts">
import {ref, defineProps} from "vue";
import {isEmpty} from "@/utils/commonUtil";
import Notification from "@/commons/Notification";
import {apis} from "@/apis";
import {ElNotification} from "element-plus";
import {UploadFilled} from '@element-plus/icons-vue'

const files = ref<any[]>([])
const errorMessages = ref<Array<{ rowIndex: number; message: string }>>([])
const fileList = ref<any[]>([])
const formData = ref({
  termId: '',
})
const rules = {
  termId: [{required: true, message: '请选择学期', trigger: 'change'}],
  uploadFile: [{required: true, message: '请上传文件', trigger: 'change'}],
}
const dataLoading = ref(false)
const form = ref()

const props = defineProps({
  termList: {
    type: Array<any>,
    default: () => []
  },
  regionId: {
    type: String,
    default: ''
  }
})
const importTemplateBtnLoading = ref(false);
// 导入模板下载
const importTemplateDownload = async () => {
  importTemplateBtnLoading.value = true;
  try {
    const {termId} = formData.value;
    const {regionId} = props;
    if (isEmpty(termId)) {
      Notification.showErrorNotify("学期条件不能为空！");
      return;
    }
    // 验证区县ID
    if (isEmpty(regionId)) {
      Notification.showErrorNotify("区县不能为空！");
      return;
    }
    await apis.SportRateManage.downloadBatchImportTemplate(termId, regionId);
  } catch (error) {
    // 捕获接口调用等可能出现的异常
    console.error("模板下载失败:", error);
    Notification.showErrorNotify("模板下载失败，请稍后重试！");
  } finally {
    importTemplateBtnLoading.value = false;
  }
}

const onRemove = (file: any, fileList: any) => {
  errorMessages.value = []
  // 清空文件列表
  fileList.value = []
}
const onExceed = (files: any, fileList: any) => {
  ElNotification.warning({message: `最多只能上传 1 个文件`, title: "温馨提示"})
}
const onChange = (file: any, fileList: any) => {
  let raw = file.raw;
  let fileTp =
    raw.type == "application/vnd.ms-excel" ||
    raw.type == "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
  if (!fileTp) {
    ElNotification.error({message: `只能上传excel格式文件`, title: "温馨提示"})
  } else {
    if (file.size > 5 * 1024 * 1024) {
      ElNotification.warning({message: `上传限制文件大小不能大于5M`, title: "温馨提示"});
    }
    // 更新文件列表
    files.value = [file.raw];
  }
}

const emit = defineEmits(['save-complete', 'closeDetail'])
const saveCompleteEmit = (status: number) => emit('save-complete', status)
// 保存
const saveSubmit = async () => {
  let {termId} = formData.value;
  let {regionId} = props;
  // 错误信息清空
  errorMessages.value = []
  if (isEmpty(termId)) {
    Notification.showErrorNotify("学期条件不能为空！");
    saveCompleteEmit(0)
    return;
  }
  // 验证区县ID
  if (isEmpty(regionId)) {
    Notification.showErrorNotify("区县不能为空！");
    saveCompleteEmit(0)
    return;
  }
  if (files.value.length === 0) {
    ElNotification.error({message: "请选择文件", title: "温馨提示"})
    saveCompleteEmit(0)
    return
  } else {
    const file = files.value[0]
    const formDates = new FormData()
    formDates.append('file', file.raw || file);
    await apis.SportRateManage.batchImport(formDates, termId, regionId).then((res: any) => {
      if (res.data === undefined) {
        ElNotification.success({message: "导入成功", title: "温馨提示"});
        saveCompleteEmit(1);
      } else if (res.data.length > 0) {
        errorMessages.value = res.data;
        saveCompleteEmit(0);
      }
    }).catch(() => {
      ElNotification.error({message: "导入失败", title: "温馨提示"})
      saveCompleteEmit(0)
    })
  }
}

defineExpose({
  saveSubmit
})
</script>

<template>
  <div class="padding-20">
    <el-form ref="form" :model="formData" :rules="rules" v-loading="dataLoading" style="width: 700px"
             label-width="auto">
      <el-form-item label="选择学期：" prop="termId">
        <el-select placeholder="学期选择" v-model="formData.termId">
          <el-option v-for="item in termList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="模板文件：">
        <el-popconfirm width="220" confirm-button-text="确认" cancel-button-text="不需要" icon-color="#626AEF"
                       title="确定下载模板文件嘛？"
                       @confirm="importTemplateDownload">
          <template #reference>
            <el-button icon="Download" type="info" :loading="importTemplateBtnLoading">
              导入模板下载
            </el-button>
          </template>
        </el-popconfirm>

      </el-form-item>
      <el-form-item label="上传文件：" prop="uploadFile">
        <el-upload ref="upload" accept=".xlsx,.xls" :file-list="fileList" drag :auto-upload="false" :limit="1"
                   :on-remove="onRemove" :on-exceed="onExceed" :on-change="onChange" style="width: 100%">
          <el-icon class="el-icon--upload">
            <upload-filled/>
          </el-icon>
          <div class="el-upload__text">
            选择出勤记录excel文件，<span style="color: red">相同学籍号多次上传会覆盖原数据</span>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              只能上传excel文件，且不超过5M
            </div>
          </template>
        </el-upload>
      </el-form-item>
      <el-form-item label="错误信息" v-if="errorMessages.length > 0">
        <div style="max-height: 300px; overflow-y: auto">
          <div v-for="(item, index) in errorMessages" :key="index">
            <div class="error-desc-text">
              {{ index + 1 }}、第{{ item.rowIndex }}行：{{ item.message }}
            </div>
          </div>
        </div>
      </el-form-item>

    </el-form>
  </div>
</template>

