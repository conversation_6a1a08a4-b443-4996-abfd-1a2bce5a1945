<script lang="ts" setup>
/**
 * 权限划分需求如下：
 *
 * 市级管理员：可查看，可修改所属区县，所属学校，所属班级查询条件
 * 区县管理员：可查看，所属区县不可修改【只能当前区县】，可以修改所属学校，所属班级查询条件
 * 学校管理员：可查看，所属区县不可修改【只能当前区县】，所属学校不能修改【只能当前学校】，可以修改所属班级查询条件，可导入导出删除
 *
 * 查询条件：学期，是否残疾免试，是否伤病免试 均可使用
 */
import {onMounted, ref, watch} from "vue";
import {apis} from "@/apis";
import {useTableCommons} from "@/commons/TableCommons";
import {useDeptStore, useUserStore} from "@/store";
import Notification from "@/commons/Notification";
import {isEmpty} from "@/utils/commonUtil";
import TiZhiCeShiImport from "./components/TiZhiCeShiImport.vue";

const userStore = useUserStore()
const deptStore = useDeptStore()
const userInfo = userStore.userInfo;
// 学期ID
const termId = ref();
// 学校ID
const schoolId = ref();
// 区县ID
let currentRegionId = userStore.regionId || '130102';

const search = ref({
  pageNumber: 1,
  pageSize: 10,
  keywords: "",
  termId: termId,
  region: currentRegionId,
  schoolId: schoolId,
  clazzId: "",
  sfcj: "",
  sfsb: "",
});

// 权限处理
if (userInfo?.cityAdminFlag) {
  // 市级管理员，查询条件区县ID，默认使用长安区的编码'130102'
  currentRegionId = '130102';
}
if (userInfo?.schoolAdminFlag) {
  // 学校管理员，查询条件学校ID为当前学校且不可修改
  schoolId.value = userInfo.schoolId;
}
if (userInfo?.countyAdminFlag) {
  // 区县管理员，查询条件区县ID为当前区县且不可修改
  currentRegionId = userInfo.regionId;
}
// 学期列表
const termList = ref<any>([])
// 区县列表
const regionList = ref<any>([])
// 学校列表
const schoolList = ref<any>([])
// 班级列表
const classList = ref<any>([])
// 体育项目列表
const sportProjects = ref<any>([])
// 是否
const operateTypeList = ref([{key: false, value: "否"}, {key: true, value: "是"}])

// 获取项目测试结果
const getProjectResult = (projectResults: any, projectCode: string, field: string) => {
  if (!projectResults || !projectResults[projectCode]) {
    return '--';
  }
  return projectResults[projectCode][field] || '--';
}
// 获取学期列表
const getTremFunc = async () => {
  let termResponse = await apis.Commons.getTermSelectList();
  let tremData = termResponse.data
  if (isEmpty(tremData)) {
    return Notification.showErrorNotify("获取学期列表失败，请检查学期管理！");
  }
  termList.value = tremData;
  // 获取启用学期，如果获取，则默认使用该学期，否则默认使用第一个学期
  let startTrem = tremData.filter((item: any) => item.extra === '1');
  if (startTrem && startTrem.length > 0) {
    termId.value = startTrem[0].key;
  } else {
    termId.value = tremData[0].key
  }
}
// 获取学校列表
const getSchoolFunc = async () => {
  await deptStore.setSchoolList(currentRegionId);
  schoolList.value = deptStore.schoolList
  schoolId.value = userInfo?.schoolId;
}
// 获取区县列表
const getRegionFunc = async () => {
  await deptStore.setRegionList();
  regionList.value = deptStore.regionList
}
// 获取体育项目列表
const getSportProjectsFunc = async () => {
  try {
    let response = await apis.TiZhiCeShiManage.getSportProjects(currentRegionId);
    sportProjects.value = response.data || [];
  } catch (error) {
    console.error("获取体育项目列表失败:", error);
    sportProjects.value = [];
  }
}
// 切换区县
const regionChange = async (value: any) => {
  currentRegionId = value;
  schoolList.value = null;
  await deptStore.setSchoolList(value)
  schoolList.value = deptStore.schoolList
  search.value.schoolId = ''
  search.value.clazzId = ''
}
// 切换学校
const schoolChange = async (value: any) => {
  search.value.schoolId = value;
  classList.value = null;
  search.value.clazzId = ''
  let classResponse = await apis.regionCommons.getClazzSelectList(value, null, currentRegionId);
  classList.value = classResponse.data;
}
// 监控区县学校变化
watch([() => search.value.region, () => search.value.schoolId], (newValues, oldValues) => {
  const [newRegion, newSchool] = newValues;
  const [oldRegion, oldSchool] = oldValues;
  if (newRegion != oldRegion) {
    regionChange(newRegion);
  }
  if (newSchool != oldSchool && newSchool) {
    schoolChange(newSchool);
  }
}, {immediate: true});
// 导出
const handleExport = async () => {
  let exportResponse = await apis.TiZhiCeShiManage.export(search.value, currentRegionId);
  console.log(exportResponse);
}
// 删除学生体质测试记录
const deleteData = async (studentId: string, termId: string) => {
  let deleteResponse = await apis.TiZhiCeShiManage.deleteData(studentId, termId, currentRegionId);
  if (deleteResponse.data) {
    Notification.showSuccessNotify("删除成功")
  } else {
    Notification.showErrorNotify("删除失败")
  }
  await searchSubmit();
}
// 获取业务数据
const getTableData = async () => {
  if (isEmpty(search.value.termId)) {
    return Notification.showErrorNotify("查询失败，查询学期条件不能为空！");
  }
  if (isEmpty(search.value.region)) {
    return Notification.showErrorNotify("查询失败，查询区县条件不能为空！");
  }
  // 处理 查询条件与当前tenantId 不一致的问题，避免数据出错
  if (search.value.region != currentRegionId) {
    currentRegionId = search.value.region;
  }
  console.log(" schoolId.value", schoolId.value)
  if (search.value.schoolId != schoolId.value) {
    search.value.schoolId = schoolId.value;
  }
  console.log("search.value", search.value);
  const response = await apis.TiZhiCeShiManage.getPageData(search.value, currentRegionId);
  return response.data;
}
// 导入数据
const importData = async () => {
  dialogModel.value.visible = true;
}
// 导入表单相关操作
const importForm = ref()
// 提交
const saveSubmit = async () => {
  dialogModel.value.saveLoading = true
  importForm.value.saveSubmit()
}
// 关闭弹窗
const closeDetail = () => {
  dialogModel.value.visible = false
}

let {
  loading,
  dialogModel,
  tableData,
  searchSubmit,
  pageOrSizeChange
} = useTableCommons(search, getTableData)

// 组件挂载后执行
onMounted(async () => {
  // 初始化字典数据
  await getTremFunc();
  await getRegionFunc();
  await getSchoolFunc();
  await getSportProjectsFunc();
  // 获取数据
  await searchSubmit();
})

</script>

<template>
  <div>
    <div class="lmt-operation-container">
      <div class="lmt-operation">
        <el-button icon="Upload" type="primary" @click="importData" v-has-perm="['tizhiceshi:import']">导入</el-button>
        <el-button icon="Download" type="info" @click="handleExport" v-has-perm="['tizhiceshi:export']">批量导出
        </el-button>
      </div>
      <div class="lmt-search">
        <el-select v-model="search.termId" placeholder="学期选择">
          <el-option v-for="item in termList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <el-select v-model="search.region" placeholder="所属县区"
                   :disabled="userInfo?.schoolAdminFlag || userInfo?.countyAdminFlag">
          <el-option v-for="item in regionList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <el-select v-model="search.schoolId" clearable placeholder="所属学校" :disabled="userInfo?.schoolAdminFlag">
          <el-option v-for="item in schoolList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <el-select v-model="search.clazzId" clearable placeholder="所属班级">
          <el-option v-for="item in classList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <el-select v-model="search.sfcj" clearable placeholder="是否残疾免试">
          <el-option v-for="item in operateTypeList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <el-select v-model="search.sfsb" clearable placeholder="是否伤病免试">
          <el-option v-for="item in operateTypeList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <el-input v-model="search.keywords" clearable placeholder="学生姓名/学籍号">
          <template #append>
            <el-button icon="Search" @click="searchSubmit"></el-button>
          </template>
        </el-input>
      </div>
    </div>
    <el-table v-loading="loading" :data="tableData.records" border
              max-height="calc(100vh - var(--lmt-header-height) - var(--lmt-main-padding) * 2 - 6rem)" stripe>
      <el-table-column align="center" label="序号" type="index" width="80" fixed>
        <template #default="scope">
          {{ (search.pageNumber - 1) * search.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="姓名" prop="studentName" width="120" fixed></el-table-column>
      <el-table-column label="学籍号" prop="xjh" width="150"></el-table-column>
      <el-table-column label="性别" prop="xb" width="80">
        <template #default="scope">
          <el-tag>{{ scope.row.xb == 1 ? '男' : '女' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="所属班级" prop="clazzName" width="120"></el-table-column>
      <el-table-column label="所属学校" prop="schoolName" width="150"></el-table-column>
      <el-table-column label="所属县区" prop="regionName" width="120"></el-table-column>
      <el-table-column label="是否残疾免试" prop="sfcj" width="120">
        <template #default="scope">
          <el-tag :type="scope.row.sfcj?'error':'primary'">{{ scope.row.sfcj ? '是' : '否' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="是否伤病免试" prop="sfsb" width="120">
        <template #default="scope">
          <el-tag :type="scope.row.sfsb?'error':'primary'">{{ scope.row.sfsb ? '是' : '否' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="身高(cm)" prop="sg" width="100"></el-table-column>
      <el-table-column label="体重(kg)" prop="tz" width="100"></el-table-column>



      <!-- 动态显示体育项目 -->
      <el-table-column
        v-for="project in sportProjects"
        :key="project.code"
        :label="project.name"
        align="center"
        width="150">
        <el-table-column label="成绩" width="80">
          <template #default="scope">
            <span>{{ getProjectResult(scope.row.projectResults, project.code, 'result') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="分数" width="70">
          <template #default="scope">
            <span>{{ getProjectResult(scope.row.projectResults, project.code, 'score') }}</span>
          </template>
        </el-table-column>
      </el-table-column>

      <el-table-column label="总分" fixed="right" width="80" prop="totalScore"></el-table-column>
      <el-table-column label="等级" fixed="right" width="80" prop="grade"></el-table-column>
      <el-table-column fixed="right" label="操作" width="120" align="center" v-has-perm="['tizhiceshi:delete']">
        <template #default="scope">
          <el-popconfirm width="220" confirm-button-text="确认" cancel-button-text="取消" icon-color="#626AEF"
                         title="确认是否删除？"
                         @confirm="deleteData(scope.row.studentId, scope.row.termId)">
            <template #reference>
              <el-button link icon="Delete" type="danger" size="small">删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-model:current-page="search.pageNumber" v-model:page-size="search.pageSize"
                   :page-sizes="[10, 20, 30, 40, 50, 100]" :total="Number(tableData.total)"
                   background
                   class="m-t-4 float-right" layout="total, prev, pager, next, sizes, ->" @change="pageOrSizeChange">
    </el-pagination>

    <el-dialog v-model="dialogModel.visible" title="导入数据" width="800">
      <!-- 导入数据  -->
      <TiZhiCeShiImport :region-id="currentRegionId" :term-list="termList" ref="importForm"
                        :close-on-click-modal="false" destroy-on-close
                        @save-complete="dialogModel.saveComplete" :mode="dialogModel.mode"
                        @closeDetail="closeDetail"></TiZhiCeShiImport>
      <template #footer>
        <el-button @click="dialogModel.visible=false">取 消</el-button>
        <el-button type="primary" :loading="dialogModel.saveLoading" @click="saveSubmit">确 认</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped></style>
