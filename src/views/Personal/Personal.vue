<template>
  <div>
    <el-descriptions :column="12" border label-width="120px" title="个人中心">
      <el-descriptions-item :span="6" label="账户名" label-align="center">{{ userInfo.username }}</el-descriptions-item>
      <el-descriptions-item :span="6" label="角色" label-align="center">{{
          userInfo.roles.map((item: any) => item.roleName).join(",")
        }}
      </el-descriptions-item>
      <el-descriptions-item :span="6" label="角色代码" label-align="center">{{
          userInfo.roles.map((item: any) => item.roleCode).join(",")
        }}
      </el-descriptions-item>
      <el-descriptions-item :span="6" label="默认密码" label-align="center">
        <div class="flex justify-start">
          <div> {{ userInfo.defaultPasswordFlag ? '是' : '否' }}</div>
          <el-button class="m-l-2" icon="Edit" size="small" type="warning" @click="showDialog">修改密码</el-button>
        </div>
      </el-descriptions-item>
      <el-descriptions-item :span="6" label="真实姓名" label-align="center">
        {{ userInfo.nickname }}
        <el-link class="m-l-2" icon="Edit" size="small" type="warning"
                 @click="showEditFormDialog('真实姓名','nickname')">修改
        </el-link>
      </el-descriptions-item>
      <el-descriptions-item :span="6" label="手机号码" label-align="center">
        {{ userInfo.mobile }}
        <el-link class="m-l-2" icon="Edit" size="small" type="warning"
                 @click="showEditFormDialog('手机号码','mobile')">修改
        </el-link>
      </el-descriptions-item>
      <el-descriptions-item :span="12" label="电子邮箱" label-align="center">
        {{ userInfo.email }}
        <el-link class="m-l-2" icon="Edit" size="small" type="warning"
                 @click="showEditFormDialog('电子邮箱','email')">修改
        </el-link>
      </el-descriptions-item>
      <el-descriptions-item :span="6" label="区县代码" label-align="center">
        {{ userInfo.regionCode }}
      </el-descriptions-item>
      <el-descriptions-item :span="6" label="区县名称" label-align="center">
        {{ userInfo.regionName }}
      </el-descriptions-item>
      <el-descriptions-item :span="6" label="学校代码" label-align="center">
        {{ userInfo.schoolCode }}
      </el-descriptions-item>
      <el-descriptions-item :span="6" label="学校名称" label-align="center">
        {{ userInfo.schoolName }}
      </el-descriptions-item>
    </el-descriptions>
    <el-dialog v-model="dialogVisible" :close-on-click-modal="false" :title="dialogName==='changePasswordForm'?'修改密码':'修改'"
               destroy-on-close width="400px">
      <ChangePasswordForm v-if="dialogName==='changePasswordForm'" ref="changePassword" @save-complete="saveComplete"/>
      <EditForm v-if="dialogName==='editForm'"
                ref="editForm" :field-label="editFormData.labelName" :field-name="editFormData.fieldName" :row-data="userInfo"
                @save-complete="saveComplete"/>
      <template #footer>
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button :loading="saveLoading" type="primary" @click="saveSubmit">确 定</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import {useUserStore} from '@/store'
import ChangePasswordForm from "@/views/Personal/components/ChangePasswordForm.vue";
import {onMounted, ref} from "vue";
import EditForm from "./components/ChangeUserFieldForm.vue"
import {apis} from "@/apis";
import router from "@/router";

const userStore = useUserStore()

onMounted(async () => {
  const response = await apis.Account.getSelfInfo();
  userStore.setUserInfo(response.data);
})

let userInfo = ref(userStore.userInfo);
const dialogVisible = ref(false)
const saveLoading = ref(false)
const dialogName = ref('changePasswordForm')
const showDialog = () => {
  dialogName.value = 'changePasswordForm'
  dialogVisible.value = true
}

const changePassword = ref()
const editForm = ref()

const saveSubmit = async () => {
  saveLoading.value = true
  if (dialogName.value === 'changePasswordForm') {
    if (changePassword.value) {
      changePassword.value.saveSubmit && await changePassword.value.saveSubmit()
    }
  } else {
    if (editForm.value) {
      editForm.value.saveSubmit && await editForm.value.saveSubmit()
    }
  }
}

const editFormData = ref({
  labelName: '',
  fieldName: ''
})

const showEditFormDialog = (labelName: string, fieldName: string) => {
  editFormData.value = {
    labelName,
    fieldName
  }
  dialogName.value = 'editForm'
  dialogVisible.value = true
}

const saveComplete = async (status: number) => {
  if (status === 1) {
    dialogVisible.value = false
    if (dialogName.value === 'changePasswordForm') {
      await router.push('/login')
      return
    }
  }
  saveLoading.value = false
  const response = await apis.Account.getSelfInfo();
  userStore.setUserInfo(response.data);
  userInfo.value = response.data
}
</script>
<style lang="scss" scoped>

</style>
