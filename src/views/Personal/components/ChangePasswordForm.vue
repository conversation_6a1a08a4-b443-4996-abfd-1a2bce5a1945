<template>
  <div class="form-container">
    <el-form ref="form" :model="formData" :rules="rules" label-width="auto" @keyup.enter="saveSubmit">
      <el-form-item label="旧密码" prop="oldPassword">
        <el-input v-model="formData.oldPassword" maxlength="32" placeholder="请输入旧密码" type="password"></el-input>
      </el-form-item>
      <el-form-item label="新密码" prop="newPassword">
        <el-input v-model="formData.newPassword" maxlength="32" placeholder="请输入新密码" type="password"></el-input>
      </el-form-item>
      <el-form-item label="确认密码" prop="confirmPassword">
        <el-input v-model="formData.confirmPassword" maxlength="32" placeholder="请输入确认密码"
                  type="password"></el-input>
      </el-form-item>
    </el-form>
  </div>
</template>
<script lang="ts" setup>
import {ref} from 'vue'
import {validatorPassword} from "@/commons/Validator";
import {ElNotification, FormInstance} from "element-plus";
import router from "@/router";
import {useUserStore} from "@/store";
import {apis} from "@/apis";

const formData = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})


const rules = ref({
  oldPassword: [
    {required: true, message: '请输入旧密码', trigger: 'blur'}
  ],
  newPassword: [
    {required: true, message: '请输入新密码', trigger: 'blur'},
    {validator: validatorPassword, trigger: 'blur'}
  ],
  confirmPassword: [
    {required: true, message: '请输入确认密码', trigger: 'blur'},
    {validator: validatorPassword, trigger: 'blur'},
    {
      validator: (rule: any, value: string, callback: any) => {
        if (value !== formData.value.newPassword) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
})

const form = ref<FormInstance>()
const emit = defineEmits(['save-complete'])
const saveSubmit = async () => {
  form?.value?.validate(async valid => {
    if (valid) {
      try {
        await apis.Account.changePassword(formData.value)
        ElNotification.success('修改成功，请重新登录')
        useUserStore().logout()
        emit('save-complete', 1)
        await router.push('/login')
      } catch (e) {
        console.error(e);
        emit('save-complete', 0)
      }
    } else {
      emit('save-complete', 0)
    }
  })
}

// 暴露方法
defineExpose({
  saveSubmit
})
</script>

<style lang="scss" scoped>

</style>
