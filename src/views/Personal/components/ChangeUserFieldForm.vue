<script lang="ts" setup>
import {onMounted, ref} from "vue";
import {FormInstance} from "element-plus"
import {apis} from "@/apis";
import Notification from "@/commons/Notification";

const formData = ref({
  value: ''
})
const rules = ref({
  value: [{required: true, message: '请输入内容', trigger: 'blur'}]
})

const props = defineProps({
  rowData: {
    type: [Object, null],
    default: () => ({})
  },
  fieldName: {
    type: String,
    default: ''
  },
  fieldLabel: {
    type: String,
    default: ''
  },
  mode: {
    type: [String, null],
    default: "EDIT"
  }
})
onMounted(() => {
  formData.value = {
    value: props.rowData?.[props.fieldName] || '',
  }
})
const emit = defineEmits(['save-complete'])
const saveCompleteEmit = (status: number) => emit('save-complete', status)

const form = ref();
const saveSubmit = async () => {
  (form.value as FormInstance).validate(async (valid) => {
    if (valid) {
      try {
        await apis.Account.changeFieldValue({key: props.fieldName, value: formData.value.value})
        saveCompleteEmit(1)
        Notification.showSuccessNotify('操作成功')
      } catch (e) {
        saveCompleteEmit(0)
      }
    } else {
      saveCompleteEmit(0)
    }
  })
}

defineExpose({
  saveSubmit
})

</script>


<template>
  <div class="form-container">
    <el-form ref="form" :model="formData" :rules="rules" label-width="auto">
      <el-form-item :label="props.fieldLabel" prop="value">
        <el-input v-if="props.fieldName==='nickname'" v-model.trim="formData.value"
                  :placeholder="'请输入'+props.fieldLabel"
                  maxlength="32" show-word-limit></el-input>
        <el-input v-if="props.fieldName==='email'" v-model.trim="formData.value"
                  :placeholder="'请输入'+props.fieldLabel"
                  maxlength="64" show-word-limit></el-input>
        <el-input v-if="props.fieldName==='mobile'" v-model.trim="formData.value"
                  :placeholder="'请输入'+props.fieldLabel"
                  maxlength="11" show-word-limit></el-input>
      </el-form-item>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>

</style>
