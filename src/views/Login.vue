<template>
  <div class="flex justify-center items-center h-screen bg-gradient-to-r from-indigo-500 to-purple-500">
    <el-card class="w-120 bg-white backdrop-blur-sm shadow-lg rounded-lg">
      <template #header>
        <div class="flex justify-center items-center">
          <span class="text-2xl font-bold">{{ commons.settings.siteTitle }}</span>
        </div>
      </template>
      <div class="form-container">
        <el-form ref="loginFormRef" :model="loginForm" :rules="rules" class="p-4" label-width="auto"
                 @submit.prevent="handleLogin">
          <el-form-item label="用户名" prop="username">
            <el-input v-model="loginForm.username" autocomplete="off" class="w-full" placeholder="请输入账户名称"/>
          </el-form-item>
          <el-form-item label="密码" prop="password">
            <el-input v-model="loginForm.password" autocomplete="new-password" class="w-full" placeholder="请输入密码"
                      show-password type="password"/>
          </el-form-item>
          <el-form-item label="验证码" prop="verifyCode">
            <div class="flex items-center">
              <el-input v-model="loginForm.verifyCode" style="width: 100px"/>
              <el-button :disabled="verifyCode.disabled" class="m-l-2" style="width: 182px" @click="sendSmsVerifyCode"
                         :loading="verifyCode.loading">{{ verifyCode.text }}
              </el-button>
            </div>
          </el-form-item>
          <!--          <el-form-item label="验证码" prop="verifyCode">-->
          <!--            <div class="flex items-center">-->
          <!--              <el-image :src="captchaUrl" alt="验证码" class="ml-2 cursor-pointer h-8 w-28" @click="refreshCaptcha"/>-->
          <!--              <el-input v-model="loginForm.verifyCode" style="width: 100px"/>-->
          <!--            </div>-->
          <!--          </el-form-item>-->
          <el-form-item>
            <el-button :loading="submitLoading" class="w-full" native-type="submit" type="primary">登录</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import {nextTick, onMounted, reactive, ref} from 'vue';
import {ElMessageBox, ElNotification, FormInstance} from 'element-plus';
import {useUserStore} from '@/store/modules/user';
import router from "@/router";
import {apis} from "@/apis"
import commons from "@/commons/Settings";

const loginFormRef = ref<FormInstance>();

const loginForm = reactive({
  username: import.meta.env.DEV ? 'lcrsjadmin' : '',
  password: import.meta.env.DEV ? 'a12345678*' : '',
  verifyCode: import.meta.env.DEV ? '9999' : ''
});

const rules = reactive({
  username: [{required: true, message: '请输入用户名', trigger: 'blur'}],
  password: [{required: true, message: '请输入密码', trigger: 'blur'}],
  verifyCode: [{required: true, message: '请输入验证码', trigger: 'blur'}]
});

const verifyCode = ref({
  loading: false,
  disabled: false,
  text: '发送短信验证码'
});


const sendSmsVerifyCode = async () => {
  try {
    verifyCode.value.disabled = true
    verifyCode.value.loading = true
    verifyCode.value.text = '正在发送短信验证码...'
    await apis.Account.getSmsVerifyCode({username: loginForm.username, password: loginForm.password});
    ElNotification({
      title: '提示',
      message: '验证码已发送，请注意查收！',
      type: 'success'
    })
    verifyCode.value.text = '60秒后重新发送'
    let seconds = 60
    let timerIndex = setInterval(() => {
      if(seconds > 0){
        verifyCode.value.text = `${seconds}秒后重新发送`
        seconds--
      } else {
        clearInterval(timerIndex)
        verifyCode.value.disabled = false
        verifyCode.value.text = '发送短信验证码'
      }
    }, 1000)
  } catch (err) {
    console.error("err===>", err)
  } finally {
    verifyCode.value.loading = false
    verifyCode.value.text = '发送短信验证码'
  }
};


// 声明store
const userStore = useUserStore();

onMounted(async () => {
  userStore.logout()
})

const submitLoading = ref(false);

const handleLogin = () => {
  loginFormRef.value?.validate(async (valid) => {
    if (valid) {
      submitLoading.value = true
      try {
        const response = await apis.Account.login(loginForm);
        // 使用store中的actions--setUser方法更新用户信息
        userStore.setUserInfo(response.data);
        userStore.setToken(userStore?.currentUserInfo?.token || '');
        if (response.data.defaultPasswordFlag) {
          ElMessageBox.alert('您的密码为默认密码，请尽快修改密码！', '提示', {
            type: 'warning',
            confirmButtonText: '确定',
            callback: async () => {
              await router.push('/personal')
            }
          })
          return
        }
        await nextTick(async () => await router.push('/'))
      } catch (e: any) {
        ElNotification({
          title: '登录失败',
          message: e.message || '用户名或密码错误',
          type: 'error'
        })
        submitLoading.value = false
      }
    }
  });
};
</script>

<style lang="scss" scoped></style>
