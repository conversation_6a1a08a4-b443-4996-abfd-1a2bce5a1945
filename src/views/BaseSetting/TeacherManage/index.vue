<script setup lang="ts">
import {onMounted, ref,watch} from "vue";
import {apis} from "@/apis";
import {useDeptStore,useUserStore} from '@/store'
import {useTableCommons} from "@/commons/TableCommons";
import EditForm from "./components/TeacherForm.vue";
import AddForm from "./components/TeacherForm.vue";
import ImportForm from "./components/TeacherImport.vue";

import DetailForm from "./components/TeacherDetail.vue"
import {DialogMode} from "@/commons/DialogModel";
import {ElMessageBox, ElNotification} from "element-plus";
import type { KeyValue } from "@/commons/KeyValue";
const userStore = useUserStore()
let currentRegionId = userStore.regionId || '130102'
const deptStore = useDeptStore()

if (deptStore.topDept?.id == deptStore.currentDept?.id) {
  currentRegionId = '130102'
}
const search = ref({
  pageNumber: 1,
  pageSize: 10,
  keywords: '',
  regionId: currentRegionId,
  jslbSet: null,
  schoolId: null,
  period: null
})
const getTableData = async () => {
  if(!search.value.regionId){
    search.value.regionId = currentRegionId
  }
  const response = await apis.TeacherManage.getTeacherList(search.value, search.value.regionId)
  for (let i = 0; i < response.data.records.length; i++) {
    const item = response.data.records[i];
    item.defaultPasswordView = "********";
    item.showPasswordView = true;
  }
  console.log(response.data)
  return response.data
}
const regionList = ref<any>([])
const schoolList = ref<any>([])
const roleList = ref<Array<KeyValue>>([]);

let {
  loading,
  dialogModel,
  showFormDialog,
  tableData,
  searchSubmit,
  pageOrSizeChange
} = useTableCommons(search, getTableData)

onMounted(async () => {
  if (userStore.userInfo?.cityAdminFlag) {//市管理员
    await useDeptStore().setRegionList();
    regionList.value = useDeptStore().regionList;
  }
  if (userStore.userInfo?.cityAdminFlag || userStore.userInfo?.countyAdminFlag) {//县区管理员
   await deptStore.setSchoolList(currentRegionId)
    schoolList.value = deptStore.schoolList;
  }
  if (roleList.value.length == 0) {
    roleList.value = await apis.Commons.getRoleSelectList({key:'TEACHER'})
  }
  await searchSubmit()
})
const changeRegionId = async () => {
  schoolList.value = []
  await deptStore.setSchoolList(currentRegionId)
  schoolList.value = deptStore.schoolList
}
// 监控区县变化
watch(() => search.value.regionId,
  (newValues, oldValues) => {
    if (newValues != oldValues) {
      currentRegionId = newValues
      changeRegionId();
    }
  });
// 表单部分
const addForm = ref()
const editForm = ref()
const importForm = ref()

const saveSubmit = async () => {
  dialogModel.value.saveLoading = true
  if (dialogModel.value.formName === 'AddForm') {
    addForm.value.saveSubmit()
  } else if (dialogModel.value.formName === 'EditForm') {
    editForm.value.saveSubmit()
  } else if (dialogModel.value.formName === 'ImportForm') {
    importForm.value.saveSubmit()
  }
}
const resetPassword = async (row: any) => {
  ElMessageBox.confirm(`您确定重置账户【${row.jsxm}】的密码吗？`, "温馨提示",
    {type: "warning"})
    .then(async () => {
      await apis.UserManage.resetPassword(row.userId)
      ElNotification.success({message: "操作成功", title: "温馨提示"})
      await searchSubmit()
    })
}
const changeUserStatus = async (row: any, status: number) => {
  ElMessageBox.confirm(`您确定${status == 1 ? "启用" : "禁用"}账户【${row.jsxm}】吗？`, "温馨提示",
      {type: "warning"})
      .then(async () => {
        await apis.UserManage.changeUserStatus(row.userId, status)
        ElNotification.success({message: "操作成功", title: "温馨提示"})
        await searchSubmit()
      })

}
const deleteTeacher = async (row: any) => {
  ElMessageBox.confirm(`您确定删除姓名【${row.jsxm}】的教师吗？`, "温馨提示",
    {confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'})
      .then(async () => {
        await apis.TeacherManage.delete(row.id,currentRegionId)

        ElNotification.success({message: "操作成功", title: "温馨提示"})
        await searchSubmit()
      })

}
const toEdit = (id:any,regionId:any) => {
  dialogModel.value.visible = true
  showFormDialog('修改','EditForm',{id:id,regionId:regionId},DialogMode.EDIT,'1000px')

}

const closeDetail = () => {
  dialogModel.value.visible = false
}
const changePasswordView = (row:any)=> {
  if (row.showPasswordView) {
    row.defaultPasswordView = row.defaultPassword;
  } else {
    row.defaultPasswordView = "********";
  }
  row.showPasswordView = !row.showPasswordView;
}
</script>
<template>
  <div>
    <div class="lmt-operation-container">
      <div class="lmt-operation">
        <el-button type="primary" icon="Plus"
                   v-has-perm="['teacher:add']"
            @click="showFormDialog('新增','AddForm',{regionId: currentRegionId,schoolId:userStore.userInfo?.schoolId,roleList:roleList,},DialogMode.ADD,'1000px')">新增
        </el-button>
        <el-button v-has-perm="['teacher:import']" type="primary" icon="Upload" @click="showFormDialog('导入','ImportForm',{},DialogMode.IMPORT,'1000px')">批量导入</el-button>
        <el-button v-has-perm="['teacher:export']" type="info" icon="Download" @click="apis.TeacherManage.export(search,currentRegionId)">批量导出</el-button>
      </div>
      <div class="lmt-search">
        <el-select v-model="search.regionId" placeholder="所属区县" :clearable="false" v-if="userStore.userInfo?.cityAdminFlag" @change="changeRegionId">
          <el-option v-for="item in regionList" :key="item.key" :value="item.key" :label="item.value"></el-option>
        </el-select>
        <el-select v-model="search.schoolId" size="default" placeholder="所属学校" :clearable="true" v-if="userStore.userInfo?.cityAdminFlag || userStore.userInfo?.countyAdminFlag">
          <el-option v-for="item in schoolList" :key="item.key" :value="item.key" :label="item.value"></el-option>
        </el-select>
        <el-select v-model="search.jslbSet" size="default" placeholder="教师类别" multiple :clearable="true">
          <el-option label="班主任" value="1"></el-option>
          <el-option label="体育教师" value="2"></el-option>
        </el-select>
        <el-input placeholder="请输入教师姓名/身份证号" v-model="search.keywords" clearable @clear="searchSubmit">
          <template #append>
            <el-button @click="searchSubmit" icon="Search"></el-button>
          </template>
        </el-input>
      </div>
    </div>
    <el-table v-loading="loading" :data="tableData.records" stripe border
              max-height="calc(100vh - var(--lmt-header-height) - var(--lmt-main-padding) * 2 - 6rem)">
      <el-table-column type="index" label="序号" width="80" align="center">
        <template #default="scope">
          {{ (search.pageNumber - 1) * search.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="jsxm" label="教师姓名" min-width="160" show-overflow-tooltip></el-table-column>
      <el-table-column prop="jslbName" label="教师类别" min-width="160" show-overflow-tooltip></el-table-column>
      <el-table-column prop="userName" label="账号" min-width="120"></el-table-column>
      <el-table-column prop="defaultPasswordView" label="默认密码" width="120" >
        <template #default="{ row }">
          <span>{{ row.defaultPasswordView }}</span>
          <el-link
            @click="changePasswordView(row)"
            underline="hover"
            :icon="row.showPasswordView && 'View'"
          />
        </template>
      </el-table-column>
      <el-table-column prop="sfzh" label="身份证号" min-width="120"></el-table-column>
      <el-table-column prop="jsxb" label="性别" min-width="80">
        <template #default="{row}">
          {{row.jsxb == 1 ? '男' : '女'}}
        </template>
      </el-table-column>
      <el-table-column prop="schoolName" label="所属学校" min-width="120"></el-table-column>
      <el-table-column prop="roleName" label="教师角色" min-width="160" show-overflow-tooltip></el-table-column>
      <el-table-column prop="status" label="账号状态" min-width="120">
      <template #default="scope">
          <el-tag v-if="scope.row.status==1" type="success">正常</el-tag>
          <el-tag v-else type="danger">禁用</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="280" fixed="right">
        <template #default="scope">
          <el-link type="primary" icon="View"
                   @click="showFormDialog('详情','DetailForm',scope.row,DialogMode.DETAIL,'1000px')">详情
          </el-link>
          <el-link type="warning" icon="Edit" v-has-perm="['teacher:edit']"
                   @click="showFormDialog('修改','EditForm',scope.row,DialogMode.EDIT,'1000px')">修改
          </el-link>
          <el-link v-has-perm="['user:change:status']" v-if="scope.row.status==1" type="danger" icon="CircleClose" @click="changeUserStatus(scope.row,0)">禁用
          </el-link>
          <el-link v-has-perm="['user:change:status']" v-if="scope.row.status==0" type="success" icon="CircleCheck" @click="changeUserStatus(scope.row,1)">启用
          </el-link>
          <el-link v-has-perm="['user:reset:password']" type="warning" icon="RefreshLeft" :disabled="!scope.row.defaultPasswordFlag"
                   @click="resetPassword(scope.row)">重置密码
          </el-link>
          <el-link type="danger" icon="Delete" @click="deleteTeacher(scope.row)">删除
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination class="m-t-4 float-right" v-model:current-page="search.pageNumber"
                   v-model:page-size="search.pageSize" @change="pageOrSizeChange"
                   :page-sizes="[10, 20, 30, 40, 50, 100]"
                   :total="Number(tableData.total)" layout="total, prev, pager, next, sizes, ->" background>
    </el-pagination>
    <el-dialog :title="dialogModel.title" v-model="dialogModel.visible" :close-on-click-modal="false"
               destroy-on-close
               :width="dialogModel.width" @closed="dialogModel.closed()">
      <AddForm ref="addForm" v-if="dialogModel.formName == 'AddForm'" @save-complete="dialogModel.saveComplete"
               :row-data="dialogModel.rowData" :mode="dialogModel.mode"/>
      <EditForm ref="editForm" v-if="dialogModel.formName == 'EditForm'" @save-complete="dialogModel.saveComplete"
                :row-data="dialogModel.rowData" :mode="dialogModel.mode"/>
      <DetailForm ref="detailForm" v-if="dialogModel.formName == 'DetailForm'" @save-complete="dialogModel.saveComplete" @closeDetail="closeDetail" @searchSubmit="searchSubmit"
                  @toEdit="toEdit" :row-data="dialogModel.rowData" :mode="dialogModel.mode"/>
      <ImportForm ref="importForm" v-if="dialogModel.formName == 'ImportForm'" :regionId="currentRegionId" @save-complete="dialogModel.saveComplete" @closeDetail="closeDetail" :mode="dialogModel.mode"/>
      <template #footer>
        <el-button @click="dialogModel.visible=false">
          {{ dialogModel.formName !== 'DetailForm' ? '取 消' : '关 闭' }}
        </el-button>
        <el-button type="primary" :loading="dialogModel.saveLoading" @click="saveSubmit"
                   v-if="dialogModel.formName!=='DetailForm'">确 认
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">

</style>
