<script setup lang="ts">
import {ref, onMounted} from "vue";
import {apis} from "@/apis";
import {useDeptStore,useUserStore} from "@/store";
import {ElMessageBox, ElNotification} from "element-plus";

const userStore = useUserStore()
let currentRegionId = userStore.regionId || '130102'
const formData = ref({
   id:'',
   jslb:'',
   jsxm:'',
   userName:'',
   status:1,
   jsxb:'',
   sjhm:'',
   sfzh:'',
   nickName:'',
   regionId:'',
   userId:'',
   jstx:'',
   roleCode: '',
   roleName:'',
   createTime:'',
   zhzt:'',
   defaultPasswordFlag:false,
   schoolName: '',
   regionName:'',
  jslbName:''
})

// 定义组件可以发出的事件
const emit = defineEmits(['closeDetail','searchSubmit','toEdit'])


const props = defineProps({
  rowData: {
    type: [Object, null],
    default: () => {
    }
  },
  mode: {
    type: [String, null],
    default: 'EDIT'
  },
  visible: {
    type: [Boolean, null],
    default: false
  }
})

const dataLoading = ref(false)
const deptStore = useDeptStore()

// 定义关闭对话框的方法
const closeDetail = () => {
  // 发出close事件，通知父组件关闭对话框
  emit('closeDetail')

}
const searchSubmit = async () => {
  emit('searchSubmit')
  closeDetail()
}
const toEdit = async (id:any,regionId:any) => {
  emit('toEdit',id,regionId)
  closeDetail()
}


// 定义其他按钮的方法（空实现，需要根据实际需求补充）
const edit = (id:any,regionId:any) => {
  toEdit(id,regionId)
}

onMounted(async () => {
    dataLoading.value = true
    formData.value = props.rowData as any
    if(formData.value.jsxb){
      formData.value.jsxb='男'
    }else{
      formData.value.jsxb='女'
    }
    if(formData.value.status){
      formData.value.zhzt='正常'
    }else{
      formData.value.zhzt='禁用'
    }
  if (formData.value.jslb && formData.value.jslb == '1') {
    formData.value.jslb='班主任'
  }else{
    formData.value.jslb='体育老师'
  }

    dataLoading.value = false;

})
const resetPassword = async (row: any) => {
  ElMessageBox.confirm(`您确定重置账户【${formData.value.jsxm}】的密码吗？`, "温馨提示",
    {confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'})
    .then(async () => {
      await apis.UserManage.resetPassword(formData.value.userId)
      ElNotification.success({message: "操作成功", title: "温馨提示"})
    })
}
const changeUserStatus = async (row: any, status: number) => {
  ElMessageBox.confirm(`您确定${status == 1 ? "启用" : "禁用"}账户【${formData.value.jsxm}】吗？`, "温馨提示",
      {type: "warning"})
      .then(async () => {
        await apis.UserManage.changeUserStatus(formData.value.userId, status)
        formData.value.status = status
        formData.value.zhzt = status == 1 ? '正常' : '禁用'
        ElNotification.success({message: "操作成功", title: "温馨提示"})
      })
}
const deleteTeacher = async (row: any,regionId: string) => {
  ElMessageBox.confirm(`您确定删除姓名【${formData.value.jsxm}】的教师吗？`, "温馨提示",
    {confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'})
      .then(async () => {
        await apis.TeacherManage.delete(formData.value.id,regionId)
        ElNotification.success({message: "操作成功", title: "温馨提示"})
        await searchSubmit()
      }).catch(()=>{
        //ElNotification.error({message: "删除失败", title: "温馨提示"})
      })

}
</script>

<template>
  <div class="form-container">
    <el-form ref="form" :model="formData" label-width="auto">
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="教师姓名：" prop="jsxm">
            <el-text type="info">{{ formData.jsxm }}</el-text>
          </el-form-item>
          <el-form-item label="性别：" prop="jsxb">
            <el-text type="info">{{ formData.jsxb }}</el-text>
          </el-form-item>
           <el-form-item label="角色：" prop="roleName">
            <el-text type="info">{{ formData.roleName }}</el-text>
          </el-form-item>
          <el-form-item label="所属区县：" prop="regionId">
            <el-text type="info">{{formData.regionName}}</el-text>
          </el-form-item>
          <el-form-item label="教师类别：" prop="jslbName">
            <el-text type="info">{{ formData.jslbName }}</el-text>
          </el-form-item>
           <el-form-item label="创建时间：" prop="createTime">
            <el-text type="info">{{ formData.createTime }}</el-text>
          </el-form-item>

        </el-col>
        <el-col :span="12">
           <el-form-item label="身份证号：" prop="sfzh">
            <el-text type="info">{{ formData.sfzh }}</el-text>
          </el-form-item>
           <el-form-item label="手机号：" prop="sjhm">
            <el-text type="info">{{ formData.sjhm }}</el-text>
          </el-form-item>
          <el-form-item label="账号：" prop="userName">
            <el-text type="info">{{ formData.userName }}</el-text>
          </el-form-item>
          <el-form-item label="所属学校：" prop="regionId">
            <el-text type="info">{{formData.schoolName}}</el-text>
          </el-form-item>
           <el-form-item label="状态：" prop="status">
            <el-text type="info">{{ formData.zhzt }}</el-text>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="40">
        <div style="display: flex; justify-content: center; width: 100%; margin-top: 20px; gap: 10px;">
          <el-button type="button" label="返回" @click="closeDetail">返回</el-button>
          <el-button v-has-perm="['teacher:edit']" icon="Edit" type="warning" label="修改" @click="edit(formData.id,formData.regionId)">修改</el-button>
          <el-button v-has-perm="['user:change:status']" v-if="formData.status==0" icon="CircleCheck" type="success" label="启用"  @click="changeUserStatus(formData, 1)">启用</el-button>
          <el-button v-has-perm="['user:change:status']" v-if="formData.status==1" icon="CircleClose" type="danger" label="禁用"  @click="changeUserStatus(formData, 0)">禁用</el-button>
          <el-button v-has-perm="['teacher:delete']" icon="Delete" type="danger" label="删除"  @click="deleteTeacher(formData,formData.regionId)">删除</el-button>
          <el-button v-has-perm="['user:reset:password']" icon="RefreshLeft" type="warning" label="重置密码"  :disabled="!formData.defaultPasswordFlag" @click="resetPassword">重置密码</el-button>
        </div>
      </el-row>
    </el-form>
  </div>
</template>
