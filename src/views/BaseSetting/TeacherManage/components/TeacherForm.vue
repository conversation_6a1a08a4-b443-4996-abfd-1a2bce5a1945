<script setup lang="ts">
import {ref, onMounted, watch} from "vue";
import {DialogMode} from "@/commons/DialogModel";
import {validateTelephone,validatorIdCard} from "@/commons/Validator"
import type {FormInstance} from "element-plus";
import {apis} from "@/apis";
import {useDeptStore,useUserStore} from "@/store";
import type { KeyValue } from "@/commons/KeyValue";

import Notification from "@/commons/Notification";
const userStore = useUserStore()
let currentRegionId = userStore.regionId || '130102'
const deptStore = useDeptStore()

if (deptStore.topDept?.id == deptStore.currentDept?.id) {
  currentRegionId = '130102'
}
const formData = ref({
   id:'',
   jslb: '',
   jsxm:'',
   userName:'',
   status:1,
   jsxb:'' as string | number, // 支持字符串和数字类型，但在显示时会转换为字符串
   sjhm:'',
   sfzh:'',
   nickName:'',
   userId:'',
   jstx:'',
   roleCode: '',
   schoolId:'',
   regionName:'' as string,
   regionId:'',
   schoolName:'' as string,
   jslbList:[] as Array<string>,
})

const props = defineProps({
  rowData: {
    type: [Object, null],
    default: () => {
    }
  },
  mode: {
    type: [String, null],
    default: 'EDIT'
  },
  regionId: {
    type: [String, null],
    default: ''
  },
  schoolId: {
    type: [String, null],
    default: ''
  },
})
const roleList = ref<Array<KeyValue>>([])
const regionList = ref<any>([])
const schoolList = ref<any>([])
const rules = {
  jsxm: [
    {required: true, message: '请输入名称', trigger: 'blur'},
    {min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur'}
  ],
  userName: [
    {required: true, message: '请输入账号名称', trigger: 'blur'},
    {min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur'}
  ],
  sjhm: [{required: true, message: '请输入联系电话', trigger: 'blur'},
    {validator: validateTelephone, trigger: 'blur'}],
  sfzh: [{required: true, message: '请输入身份证号', trigger: 'blur'},
    {validator: validatorIdCard, trigger: 'blur'}
  ],
  jsxb: [{required: true, message: '请选择性别', trigger: 'blur'},],
  jslbList:[{required: true, message: '请选择教师类别', trigger: 'blur'},],
  regionId:[{required: true, message: '请选择区县', trigger: 'blur'},],
  schoolId:[{required: true, message: '请选择学校', trigger: 'blur'},],
  roleCode:[{required: true, message: '请选择角色', trigger: 'blur'},],
}
const dataLoading = ref(false)

onMounted(async () => {
  if (userStore.userInfo?.cityAdminFlag) {//市管理员
    await useDeptStore().setRegionList();
    regionList.value = useDeptStore().regionList;
  }else {
    regionList.value = [{key: currentRegionId, value: userStore.userInfo?.regionName}]
  }

  if (userStore.userInfo?.cityAdminFlag || userStore.userInfo?.countyAdminFlag) {//县区管理员
    await deptStore.setSchoolList(currentRegionId)
    schoolList.value = deptStore.schoolList
    if(props.regionId == userStore.regionId) {
      formData.value.regionId = props.regionId
    }
  }
   if(roleList.value.length == 0) {
    await apis.RoleManage.getRoleSelectList({key:'TEACHER'}).then((res: any) => {roleList.value =  res.data})
  }
  if (props.mode === DialogMode.EDIT) {
    // dataLoading.value = true
    const {
      id,jslb,jsxm,userName,status,jsxb,sjhm,sfzh,nickName,userId,jstx,roleCode,schoolId,regionName,regionId,schoolName,jslbList
    } = (await apis.TeacherManage.getDetail(props.rowData?.id, props.rowData?.regionId)).data as any
    formData.value = {id,jslb:String(jslb),jsxm,userName,status,jsxb:String(jsxb),sjhm,sfzh,nickName,userId,jstx,roleCode,schoolId,regionName,regionId,schoolName,jslbList}
    dataLoading.value = false
  } else {
    formData.value.regionId = currentRegionId
    formData.value.schoolId = userStore.userInfo?.schoolId as string
    formData.value.schoolName = userStore.userInfo?.schoolName as string
    formData.value.regionName = userStore.userInfo?.regionName as string
  }
})
const changeRegionId = async () => {
  schoolList.value = []
  await deptStore.setSchoolList(currentRegionId)
  schoolList.value = deptStore.schoolList
}
// 监控区县变化
watch(() => formData.value.regionId,
  (newValues, oldValues) => {
    if (newValues != oldValues) {
      currentRegionId = newValues
      changeRegionId();
    }
  });

const emit = defineEmits(['save-complete'])
const saveCompleteEmit = (status: number) => emit('save-complete', status)

const form = ref()
const saveSubmit = () => {
  (form.value as FormInstance).validate((valid) => {
    if (valid) {
      const fcn: Function = props.mode === DialogMode.ADD ? apis.TeacherManage.save : apis.TeacherManage.update;
      fcn(formData.value, formData.value.regionId).then(() => {
        saveCompleteEmit(1)
        Notification.showSuccessNotify('操作成功')
      }).catch(() => saveCompleteEmit(0))
    } else {
      saveCompleteEmit(0)
    }
  })
}

defineExpose({
  saveSubmit
})
</script>

<template>
  <div class="form-container">
    <el-form ref="form" :model="formData" :rules="rules" label-width="auto" v-loading="dataLoading">
       <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="教师姓名：" prop="jsxm" required>
             <el-input v-model.trim="formData.jsxm" placeholder="请输入教师姓名" maxlength="32" show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="教师类别：" prop="jslbList" required>
            <el-select v-model="formData.jslbList" placeholder="请选择教师类别" :clearable="false" multiple>
              <el-option label="班主任" value="1"></el-option>
              <el-option label="体育教师" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="手机号：" prop="sjhm" required>
             <el-input v-model.trim="formData.sjhm" placeholder="请输入联系电话" maxlength="11" show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="所属区县：" prop="regionId" required>
            <el-select v-model="formData.regionId" placeholder="请选择区县" :disabled="!userStore.userInfo?.cityAdminFlag || props.mode === DialogMode.EDIT" @change="changeRegionId">
              <el-option v-for="(item, index) in regionList" :key="item.key" :value="item.key" :label="item.value"></el-option>
            </el-select>
          </el-form-item>
           <el-form-item label="角色：" prop="roleCode" required>
            <el-select v-model="formData.roleCode" multiple placeholder="请选择角色">
              <el-option v-for="(item, index) in roleList" :key="index" :label="item.value" :value="item.key"></el-option>
            </el-select>
          </el-form-item>


          </el-col>
           <el-col :span="12">
            <el-form-item label="身份证号：" prop="sfzh" required>
             <el-input v-model.trim="formData.sfzh" placeholder="请输入身份证号" maxlength="18" show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="性别：" prop="jsxb" required>
             <el-select v-model="formData.jsxb" placeholder="请选择性别" :clearable="false">
              <el-option label="男" value="1"></el-option>
              <el-option label="女" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="账号名称" prop="userName" required>
            <el-input v-model.trim="formData.userName" placeholder="请输入账号名称" maxlength="32" show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="所属学校：" prop="schoolId" required>
             <el-select v-model="formData.schoolId" placeholder="请选择学校" :disabled="userStore.userInfo?.schoolAdminFlag || props.mode === DialogMode.EDIT">
              <el-option v-for="(item, index) in schoolList" :key="item.key" :value="item.key" :label="item.value"></el-option>
            </el-select>
          </el-form-item>
          </el-col>
        </el-row>
    </el-form>
  </div>
</template>

<style scoped lang="scss">

</style>
