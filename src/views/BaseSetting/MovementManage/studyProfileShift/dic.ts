import type { KeyValue } from "@/commons/KeyValue";

// 异动类型字典
let shiftTpList: Array<KeyValue> = [
  { key: '1', value: '休学' },
  { key: '2', value: '复学' },
  { key: '7', value: '留级' },
  { key: '8', value: '跳级' },
  { key: '3', value: '市内转入' },
  // { key: 'inCityTransferOut', value: '市内转出' },
  { key: '5', value: '市外转入' },
  { key: '6', value: '市外转出' },
 /* { key: 'newProfile', value: '新建学籍' },*/
  { key: '9', value: '失踪' },
  { key: '10', value: '死亡' }
]
let shiftTpList1: Array<KeyValue> = [
  { key: '1', value: '休学' },
 /* { key: 'restore', value: '复学' },*/
]
let shiftTpList2: Array<KeyValue> = [
  { key: '3', value: '市内转入' },
]
let shiftTpList3: Array<KeyValue> = [
  { key: '5', value: '市外转入' },
  { key: '6', value: '市外转出' },
]
let shiftTpList4: Array<KeyValue> = [
  { key: '8', value: '失踪' },
  { key: '9', value: '死亡' }
]

// 按学籍号查询到的学生详情结构
interface stuQryResult {
  id: string;
  studentName: string;
  idCard: string;
  studentCode: string;
  gender: string;
  belongClass: string;
  reason: string | null;
  proof: string | null;
}

export { shiftTpList, shiftTpList1, shiftTpList2, shiftTpList3, shiftTpList4, stuQryResult }
