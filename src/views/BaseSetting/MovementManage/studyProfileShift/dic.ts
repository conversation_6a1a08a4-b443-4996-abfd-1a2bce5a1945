import type { KeyValue } from "@/commons/KeyValue";

// 异动类型字典
let shiftTpList: Array<KeyValue> = [
  { key: 'suspend', value: '休学' },
  { key: 'restore', value: '复学' },
  { key: 'repeat', value: '留级' },
  { key: 'jump', value: '跳级' },
  { key: 'inCityTransferIn', value: '市内转入' },
  // { key: 'inCityTransferOut', value: '市内转出' },
  { key: 'outCityTransferIn', value: '市外转入' },
  { key: 'outCityTransferOut', value: '市外转出' },
  { key: 'newProfile', value: '新建学籍' },
  { key: 'disappear', value: '失踪' },
  { key: 'dead', value: '死亡' }
]

// 按学籍号查询到的学生详情结构
interface stuQryResult {
  id: string;
  studentName: string;
  idCard: string;
  studentCode: string;
  gender: string;
  belongClass: string;
  reason: string | null;
  proof: string | null;
}

export { shiftTpList, stuQryResult }
