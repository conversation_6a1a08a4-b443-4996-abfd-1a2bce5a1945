<script lang="ts" setup>
import {onMounted, ref} from 'vue'
import { Edit } from '@element-plus/icons-vue'
import {apis} from "@/apis";
import {useDeptStore, useUserStore} from "@/store";
const detailData = ref<any>({})
const userStore = useUserStore();
let currentRegionId = userStore.regionId || '130102'
const deptStore = useDeptStore()
if (deptStore.topDept?.id == deptStore.currentDept?.id) {
  currentRegionId = '130102'
}
const props = defineProps({
  rowData: {
    type: [Object, null],
    default: () => {}
  }
})

onMounted(async () => {
  console.log(props.rowData)
})
let dataList=ref([])
if(props.rowData.adjustType==3||props.rowData.adjustType==4){
let fn= apis.studyProfileShift.getShenHeNeiJiLu
  fn({key:props.rowData.id},currentRegionId).then(res=>{
    dataList.value=res.data
    console.log(res.data,"res")
  })
}
</script>

<template>
  <div class="form-container">
     <el-descriptions
      :column="3"
      size="default"
      border
      v-if="rowData.adjustType==1"
     >
<!--        <template #extra>
          <el-button type="primary" plain :icon="Edit" />
        </template>-->
       <el-descriptions-item label-width="100" label="异动类型">休学</el-descriptions-item>
       <el-descriptions-item label-width="100" label="学生姓名">{{ rowData?.studentName }}</el-descriptions-item>
       <el-descriptions-item label-width="100" label="身份证号">{{ rowData?.sfzh }}</el-descriptions-item>
       <el-descriptions-item label-width="100" label="学籍号">{{ rowData?.xjh }}</el-descriptions-item>
       <el-descriptions-item label-width="100" label="性别">{{ rowData?.xb==1?'男':'女' }}</el-descriptions-item>
       <el-descriptions-item label-width="100" label="所在班级">{{ rowData?.className }}</el-descriptions-item>
       <el-descriptions-item label-width="100" label="休学原因">{{ rowData?.suspendReason }}</el-descriptions-item>
       <el-descriptions-item label-width="100" label="休学开始时间">{{ rowData?.suspendBeginDate }}</el-descriptions-item>
     </el-descriptions>
    <el-descriptions
      :column="3"
      size="default"
      border
      v-if="rowData.adjustType==2"
    >
      <!--        <template #extra>
                <el-button type="primary" plain :icon="Edit" />
              </template>-->
      <el-descriptions-item label-width="100" label="异动类型">复学</el-descriptions-item>
      <el-descriptions-item label-width="100" label="学生姓名">{{ rowData?.studentName }}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="身份证号">{{ rowData?.sfzh }}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="学籍号">{{ rowData?.xjh }}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="性别">{{ rowData?.xb==1?'男':'女' }}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="所在班级">{{ rowData?.className }}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="休学原因">{{ rowData?.suspendReason }}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="休学开始时间">{{ rowData?.suspendBeginDate }}</el-descriptions-item>
    </el-descriptions>
      <el-descriptions
        :column="3"
        size="default"
        border
        v-if="rowData.adjustType==5"
      >
        <!--        <template #extra>
                  <el-button type="primary" plain :icon="Edit" />
                </template>-->
        <el-descriptions-item label-width="100" label="异动类型">市外转入</el-descriptions-item>
        <el-descriptions-item label-width="100" label="学生姓名">{{ rowData?.studentName }}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="身份证号">{{ rowData?.sfzh }}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="学籍号">{{ rowData?.xjh }}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="性别">{{ rowData?.xb==1?'男':'女' }}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="原学校">{{ rowData?.outSchoolName}}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="原年级">{{ rowData?.outGradeName }}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="原班级">{{ rowData?.outClazzName }}</el-descriptions-item>
<!--        <el-descriptions-item label-width="100" label="转入学校">{{ rowData?.className }}</el-descriptions-item>-->
        <el-descriptions-item label-width="100" label="转入年级">{{ rowData?.gradeName }}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="转入班级">{{ rowData?.clazzName}}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="申请原因">{{ rowData?.inReason}}</el-descriptions-item>
      </el-descriptions>

    <el-descriptions
      :column="3"
      size="default"
      border
      v-if="rowData.adjustType==6"
    >
      <!--        <template #extra>
                <el-button type="primary" plain :icon="Edit" />
              </template>-->
      <el-descriptions-item label-width="100" label="异动类型">市外转出</el-descriptions-item>
      <el-descriptions-item label-width="100" label="学生姓名">{{ rowData?.studentName }}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="身份证号">{{ rowData?.sfzh }}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="学籍号">{{ rowData?.xjh }}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="性别">{{ rowData?.xb==1?'男':'女' }}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="转入城市">{{ rowData?.intoCityName }}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="转入学校">{{ rowData?.intoSchoolName}}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="转出学期">{{ rowData?.outTermName }}</el-descriptions-item>
      <!--        <el-descriptions-item label-width="100" label="转入学校">{{ rowData?.className }}</el-descriptions-item>-->
      <el-descriptions-item label-width="100" label="转出年级">{{ rowData?.gradeName }}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="转出班级">{{ rowData?.clazzName}}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="申请原因">{{ rowData?.outReason }}</el-descriptions-item>
    </el-descriptions>
    <div    v-if="rowData.adjustType==3">
      <el-descriptions
        :column="3"
        size="default"
        border

      >
        <!--        <template #extra>
                  <el-button type="primary" plain :icon="Edit" />
                </template>-->
        <el-descriptions-item label-width="100" label="异动类型">市内转入</el-descriptions-item>
        <el-descriptions-item label-width="100" label="学生姓名">{{ rowData?.studentName}}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="身份证号">{{ rowData?.sfzh }}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="学籍号">{{ rowData?.xjh }}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="性别">{{ rowData?.xb ==1?'男':'女'}}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="原学校">{{ rowData?.outSchoolName }}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="原年级">{{ rowData?.outGradeName }}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="原班级">{{ rowData?.outClazzName}}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="转入学校">{{ rowData?.intoSchoolName }}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="转入年级">{{ rowData?.intoGradeName }}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="转入班级">{{ rowData?.intoClazzName }}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="申请原因">{{ rowData?.inReason}}</el-descriptions-item>
      </el-descriptions>
      <el-timeline style="max-width: 600px;margin-top: 20px" v-for="datas in dataList">
        <el-timeline-item :timestamp="datas.createTime" placement="top">
          <el-card>
            <p>审核人：{{datas.createUser}}</p>
           <p>审核状态:{{datas.status==1?'待审核':datas.status==2?'审核通过':datas.status==3?'审核不通过':''}}</p>
            <p v-if="datas.status==3">驳回原因:{{datas.reason }}</p>
            <p v-else>原因：无</p>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </div>
    <div    v-if="rowData.adjustType==4">
      <el-descriptions
        :column="3"
        size="default"
        border

      >
        <!--        <template #extra>
                  <el-button type="primary" plain :icon="Edit" />
                </template>-->
        <el-descriptions-item label-width="100" label="异动类型">市内转出</el-descriptions-item>
        <el-descriptions-item label-width="100" label="学生姓名">{{ rowData?.xm }}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="身份证号">{{ rowData?.sfzh }}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="学籍号">{{ rowData?.xjh }}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="性别">{{ rowData?.xb ==1?'男':'女'}}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="转入学校">{{ rowData?.intoSchoolName }}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="转入年级">{{ rowData?.intoGradeName }}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="转入班级">{{ rowData?.intoClazzName }}</el-descriptions-item>
<!--        <el-descriptions-item label-width="100" label="转出学校">{{ rowData?.schoolName}}</el-descriptions-item>-->
        <el-descriptions-item label-width="100" label="原年级">{{ rowData?.gradeName}}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="原班级">{{ rowData?.clazzName }}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="申请原因">{{ rowData?.outReason }}</el-descriptions-item>
      </el-descriptions>
      <el-timeline style="max-width: 600px;margin-top: 20px" v-for="datas in dataList">
        <el-timeline-item :timestamp="datas.createTime" placement="top">
          <el-card>
            <p>审核人：{{datas.createUser}}</p>
            <p>审核状态:{{datas.status==1?'待审核':datas.status==2?'审核通过':datas.status==3?'审核不通过':''}}</p>
            <p v-if="datas.status==3">驳回原因:{{datas.reason }}</p>
            <p v-else>原因：无</p>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </div>
    <el-descriptions
      :column="3"
      size="default"
      border
      v-if="rowData.adjustType==7"
    >
      <!--        <template #extra>
                <el-button type="primary" plain :icon="Edit" />
              </template>-->
      <el-descriptions-item label-width="100" label="异动类型">留级</el-descriptions-item>
      <el-descriptions-item label-width="100" label="学生姓名">{{ rowData?.xm}}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="身份证号">{{ rowData?.sfzh }}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="学籍号">{{ rowData?.xjh }}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="性别">{{ rowData?.xb==1?'男':'女' }}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="原年级">{{ rowData?.oldGradeName }}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="原班级">{{ rowData?.oldClassName }}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="留级年级">{{ rowData?.gradeName }}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="留级班级">{{ rowData?.clazzName }}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="审核时间">{{ rowData?.applyDate }}</el-descriptions-item>
    </el-descriptions>
    <el-descriptions
      :column="3"
      size="default"
      border
      v-if="rowData.adjustType==8"
    >
      <!--        <template #extra>
                <el-button type="primary" plain :icon="Edit" />
              </template>-->
      <el-descriptions-item label-width="100" label="异动类型">跳级</el-descriptions-item>
      <el-descriptions-item label-width="100" label="学生姓名">{{ rowData?.xm}}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="身份证号">{{ rowData?.sfzh }}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="学籍号">{{ rowData?.xjh }}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="性别">{{ rowData?.xb==1?'男':'女' }}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="原年级">{{ rowData?.oldGradeName }}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="原班级">{{ rowData?.oldClassName }}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="跳级年级">{{ rowData?.gradeName }}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="跳级班级">{{ rowData?.clazzName }}</el-descriptions-item>
      <el-descriptions-item label-width="100" label="审核时间">{{ rowData?.applyDate }}</el-descriptions-item>
    </el-descriptions>
  </div>
</template>
