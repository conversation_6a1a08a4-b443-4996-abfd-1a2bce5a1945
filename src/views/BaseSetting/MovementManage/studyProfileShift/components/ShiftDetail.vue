<script lang="ts" setup>
import {onMounted, ref} from 'vue'
import { Edit } from '@element-plus/icons-vue'

const detailData = ref<any>({})

const props = defineProps({
  rowData: {
    type: [Object, null],
    default: () => {}
  }
})

onMounted(async () => {
  console.log(props.rowData)
})

</script>

<template>
  <div class="form-container">
     <el-descriptions
      :column="3"
      size="default"
      border
     >
        <template #extra>
          <el-button type="primary" plain :icon="Edit" />
        </template>
        <el-descriptions-item label="异动类型">4324312</el-descriptions-item>
        <el-descriptions-item label="学籍号">123312321</el-descriptions-item>
        <el-descriptions-item label="学生姓名">123312321</el-descriptions-item>
        <el-descriptions-item label="身份证号">123312321</el-descriptions-item>
        <el-descriptions-item label="性别">123312321</el-descriptions-item>
        <el-descriptions-item label="原班级">123312321</el-descriptions-item>
        <el-descriptions-item label="学籍号">123312321</el-descriptions-item>
     </el-descriptions>
  </div>
</template>
