<script lang="ts" setup>
import {onMounted, ref} from 'vue'
import { Edit } from '@element-plus/icons-vue'

const detailData = ref<any>({})

const props = defineProps({
  rowData: {
    type: [Object, null],
    default: () => {}
  }
})

onMounted(async () => {
  console.log(props.rowData)
})

</script>

<template>
  <div class="form-container">
     <el-descriptions
      :column="3"
      size="default"
      border
     >
<!--        <template #extra>
          <el-button type="primary" plain :icon="Edit" />
        </template>-->
       <el-descriptions-item label-width="100" label="学生姓名">{{ rowData?.studentName }}</el-descriptions-item>
       <el-descriptions-item label-width="100" label="身份证号">{{ rowData?.sfzh }}</el-descriptions-item>
       <el-descriptions-item label-width="100" label="学籍号">{{ rowData?.xjh }}</el-descriptions-item>
       <el-descriptions-item label-width="100" label="性别">{{ rowData?.xb }}</el-descriptions-item>
       <el-descriptions-item label-width="100" label="所在班级">{{ rowData?.className }}</el-descriptions-item>
       <el-descriptions-item label-width="100" label="休学原因">{{ rowData?.suspendReason }}</el-descriptions-item>
       <el-descriptions-item label-width="100" label="休学开始时间">{{ rowData?.suspendBeginDate }}</el-descriptions-item>
     </el-descriptions>
  </div>
</template>
