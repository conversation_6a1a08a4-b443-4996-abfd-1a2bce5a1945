<script setup lang="ts">
import { ref, onMounted } from 'vue'
import {apis} from "@/apis"
import Notification from "@/commons/Notification";
import {useDeptStore, useUserStore} from "@/store";
const props = defineProps({
  rowData: {
    type: Object,
    required: true
  }
})
// loading
const dataLoading = ref(false)
let formData=ref({

})
formData.value.id=props.rowData.id
if(props.rowData.reason!=null){
  formData.value.status=props.rowData.status
}else {
  formData.value.status=2
}

formData.value.reason=props.rowData.reason

const rules=ref({

})
const userStore = useUserStore();
let currentRegionId = userStore.regionId || '130102'
const deptStore = useDeptStore()
if (deptStore.topDept?.id == deptStore.currentDept?.id) {
  currentRegionId = '130102'
}
const emit = defineEmits(['save-complete'])
const saveCompleteEmit = (status: number) => emit('save-complete', status)
const saveSubmit=async ()=>{
  let fcn =  apis.studyProfileShift.shenHeNeiOut
  fcn(formData.value,currentRegionId).then(res=>{
    saveCompleteEmit(1)
    Notification.showSuccessNotify('操作成功')
  }).catch(() => saveCompleteEmit(0))
}
onMounted(() => {
  console.log(props.rowData)
})
defineExpose({
  saveSubmit
})
</script>

<template>
  <div class="audit-shift">
    <el-form ref="suspendForm" v-loading="dataLoading" :model="formData" :rules="rules" label-width="170px">
      <el-form-item label="选择审核结果" prop="status">
        <el-select
          v-model="formData.status"
          placeholder="请选择审核结果"
        >
<!--          <el-option
            label="待审核"
            :value="1"
          ></el-option>-->
          <el-option
            label="审核通过"
            :value="2"
          ></el-option>
          <el-option
            label="审核不通过"
            :value="3"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="reason" label="驳回原因" v-if="formData.status==3">
        <el-input v-model="formData.reason" placeholder="请输入驳回原因" type="textarea" rows="10" show-word-limit
                  maxlength="500"></el-input>
      </el-form-item>
          </el-form>
  </div>
</template>

<style scoped lang="scss">

</style>
