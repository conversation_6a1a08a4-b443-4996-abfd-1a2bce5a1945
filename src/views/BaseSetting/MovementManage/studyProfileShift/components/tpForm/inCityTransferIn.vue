<script setup lang="ts">
import { ref, onMounted } from 'vue'
import type { stuQryResult } from "@/views/BaseSetting/MovementManage/studyProfileShift/dic"
import type { KeyValue } from "@/commons/KeyValue"
import {apis} from "@/apis";
import dayjs from "dayjs";
import Notification from "@/commons/Notification";
import SchoolPeriodSelector from "@/components/SchoolPeriodSelector/index.vue";
import {useDeptStore, useUserStore} from "@/store";
const stuInfoData = defineModel<stuQryResult>('qryResult')
const isHaveResult = defineModel<boolean>('isHaveResult')
/*const rowData=defineModel<any>('rowData')*/
// 表单
const formData = ref({
  suspendReason:"",
  inBeginDate:'',
  attachmentList: [],
  adjustType:3,
})

const userStore = useUserStore();
let currentRegionId = userStore.regionId || '130102'
const deptStore = useDeptStore()
if (deptStore.topDept?.id == deptStore.currentDept?.id) {
  currentRegionId = '130102'
}
const schoolId= userStore.userInfo?.schoolId
formData.value.intoschoolName=userStore.userInfo?.schoolName
// 表单验证规则
const rules = ref({

})
const emit = defineEmits(['save-complete'])
const saveCompleteEmit = (status: number) => emit('save-complete', status)
const saveSubmit=async ()=>{
  let fcn =  apis.studyProfileShift.addCityNeiIn
  let editfcn =  apis.studyProfileShift.editCityNeiIn
  let datas=ref(formData.value)
  datas.value.studentName=stuInfoData.value?.xm
  datas.value.studentId=stuInfoData.value?.studentId
  datas.value.oldRegionId=stuInfoData.value?.oldRegionId
  datas.value.clazzName=stuInfoData.value?.clazzName
  datas.value.schoolName=stuInfoData.value?.schoolName
  datas.value.clazzId=stuInfoData.value?.clazzId
  datas.value.gradeName=stuInfoData.value?.gradeName
  datas.value.gradeId=stuInfoData.value?.gradeId
  datas.value.xb=stuInfoData.value?.xb
  datas.value.sfzh=stuInfoData.value?.sfzh
  datas.value.xjh=stuInfoData.value?.xjh
  datas.value.suspendReason=stuInfoData.value?.suspendReason
  datas.value.id=stuInfoData.value?.id
  datas.value.adjustId=stuInfoData.value?.adjustId
  datas.value.inBeginDate=dayjs(formData.value.inBeginDate).format('YYYY-MM-DD')
  datas.value.studentId=stuInfoData.value?.studentId
  if(stuInfoData.value.adjustType==3){
    editfcn(datas.value,currentRegionId).then(res=>{
      saveCompleteEmit(1)
      Notification.showSuccessNotify('操作成功')
    }).catch(() => saveCompleteEmit(0))
  }else {
    fcn(datas.value,currentRegionId).then(res=>{
      saveCompleteEmit(1)
      Notification.showSuccessNotify('操作成功')
    }).catch(() => saveCompleteEmit(0))
  }

}
// loading
const dataLoading = ref(false)

// 可用年级、班级
let availGrade = ref<KeyValue[]>([])
let availClass = ref<KeyValue[]>([])

// 生命周期
onMounted(() => {

  getAvailGrade()
  /* getMatchClass()*/
})

// 可用的年级
const getAvailGrade =async () => {
  let datas= await apis.Commons.getGradeSelectList({})

  availGrade.value= datas.data
}

// 根据年级获取班级
const getMatchClass = async () => {
  let datas= await apis.studyProfileShift.getClazzSelectList(schoolId, formData.value.intoGradeId, currentRegionId)

  availClass.value= datas.data
}
if(stuInfoData.value.adjustType==3){
  formData.value.inBeginDate=stuInfoData.value?.inBeginDate
  formData.value.intoGradeId = stuInfoData.value?.intoGradeId

  getMatchClass()
  formData.value.intoClazzId = stuInfoData.value?.intoClazzId
  formData.value.adjustReason =stuInfoData.value?.adjustReason
  formData.value.period =stuInfoData.value?.period
}

defineExpose({
  saveSubmit
})
const periodList =
  [1, 2, 3]
</script>

<template>
  <div class="in-city-trans-in">
    <div class="shift-qry-result">
      <div class="fake-label">查询结果</div>
      <el-descriptions
        :column="1"
        size="default"
        border
        v-if="isHaveResult"
      >
        <el-descriptions-item label-width="100" label="学生姓名">{{ stuInfoData?.xm }}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="身份证号">{{ stuInfoData?.sfzh }}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="学籍号">{{ stuInfoData?.xjh }}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="性别">{{ stuInfoData?.xb ==1?'男':'女'}}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="原学校">{{ stuInfoData?.schoolName }}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="原年级">{{ stuInfoData?.gradeName }}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="原班级">{{ stuInfoData?.clazzName }}</el-descriptions-item>
      </el-descriptions>
      <span v-else class="search-tips">输入学籍号来搜索</span>
    </div>
    <el-form ref="suspendForm" v-loading="dataLoading" :model="formData" :rules="rules" label-width="70px">
      <el-form-item prop="schoolName" label="转入学校">
        <el-input v-model="formData.intoschoolName" placeholder="placeholder" disabled></el-input>
      </el-form-item>
      <el-form-item label="学段" prop="period">
        <SchoolPeriodSelector v-model="formData.period" :period-list="periodList" show-type="select" ></SchoolPeriodSelector>
      </el-form-item>
      <el-form-item prop="intoGradeId" label="转入年级">
        <el-select v-model="formData.intoGradeId" placeholder="请选择转入年级" @change="getMatchClass">
          <el-option v-for="item in availGrade" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="intoClazzId" label="转入班级">
        <el-select v-model="formData.intoClazzId" placeholder="请选择转入班级">
          <el-option v-for="item in availClass" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label-width="150px" prop="formData.inBeginDate"  label="转入申请时间">
        <el-date-picker
          v-model="formData.inBeginDate"
          format="YYYY年DD月MM日"
          date-format="YYYY年DD月MM日"
          time-format="HH:mm"
          disabled-minutes
          disabled-hours
          disabled-seconds
          type="datetime"
          placeholder="请输入转入申请时间"
        />
      </el-form-item>
      <el-form-item prop="adjustReason" label="转入原因">
        <el-input v-model="formData.adjustReason" placeholder="请输入转入原因" type="textarea" rows="10" show-word-limit
                  maxlength="500"></el-input>
      </el-form-item>
      <el-form-item prop="proof" label="附件">
        <el-input v-model="formData.proof"></el-input>
      </el-form-item>
    </el-form>
  </div>
</template>

<style scoped lang="scss">

</style>
