<script setup lang="ts">
import { ref, onMounted } from 'vue'
import type { stuQryResult } from "@/views/BaseSetting/MovementManage/studyProfileShift/dic"
import type { KeyValue } from "@/commons/KeyValue"
import {apis} from "@/apis";
import dayjs from "dayjs";
import Notification from "@/commons/Notification";
import {useDeptStore, useUserStore} from "@/store";
const stuInfoData = defineModel<stuQryResult>('qryResult')
const isHaveResult = defineModel<boolean>('isHaveResult')
/*const rowData=defineModel<any>('rowData')*/
// 表单
const formData = ref({
  suspendReason:"",
  suspendEndDate:'',
  attachmentList: [],
  adjustType:8,
})
const userStore = useUserStore();
let currentRegionId = userStore.regionId || '130102'
const deptStore = useDeptStore()
if (deptStore.topDept?.id == deptStore.currentDept?.id) {
  currentRegionId = '130102'
}
const schoolId= userStore.userInfo?.schoolId
// 表单验证规则
const rules = ref({

})
const emit = defineEmits(['save-complete'])
const saveCompleteEmit = (status: number) => emit('save-complete', status)
const saveSubmit=async ()=>{
  let fcn =  apis.studyProfileShift.addLiuOrTiao
  let datas=ref(formData.value)
  datas.value.xjh=stuInfoData.value?.xjh
  datas.value.suspendReason=stuInfoData.value?.suspendReason
  datas.value.id=stuInfoData.value?.id
  datas.value.clazzId=stuInfoData.value?.clazzId
  datas.value.gradeId=stuInfoData.value?.gradeId
  datas.value.adjustId=stuInfoData.value?.adjustId
  datas.value.adjustDate=dayjs(formData.value.adjustDate).format('YYYY-MM-DD')
  fcn(datas.value,currentRegionId).then(res=>{
    saveCompleteEmit(1)
    Notification.showSuccessNotify('操作成功')
  }).catch(() => saveCompleteEmit(0))
}
// loading
const dataLoading = ref(false)

// 可用年级、班级
let availGrade = ref<KeyValue[]>([])
let availClass = ref<KeyValue[]>([])

// 生命周期
onMounted(() => {

  getAvailGrade()
  /* getMatchClass()*/
})

// 可用的年级
const getAvailGrade =async () => {
  let datas= await apis.Commons.getGradeSelectList({})

  availGrade.value= datas.data
}

// 根据年级获取班级
const getMatchClass = async () => {
  let datas= await apis.studyProfileShift.getClazzSelectList(schoolId, formData.value.intoGradeId, currentRegionId)

  availClass.value= datas.data
}
defineExpose({
  saveSubmit
})
</script>

<template>
  <div class="repeat">
    <div class="shift-qry-result">
      <div class="fake-label">查询结果</div>
      <el-descriptions
        :column="1"
        size="default"
        border
        v-if="isHaveResult"
      >
        <el-descriptions-item label-width="100" label="学生姓名">{{ stuInfoData?.xm }}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="身份证号">{{ stuInfoData?.sfzh }}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="学籍号">{{ stuInfoData?.xjh }}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="性别">{{ stuInfoData?.xb==1?'男':'女' }}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="原年级">{{ stuInfoData?.gradeName }}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="原班级">{{ stuInfoData?.clazzName }}</el-descriptions-item>
      </el-descriptions>
      <span v-else class="search-tips">输入学籍号来搜索</span>
    </div>
    <el-form ref="suspendForm" v-loading="dataLoading" :model="formData" :rules="rules" label-width="70px">
<!--      <el-form-item prop="toGrade" label="原年级">
        <el-select v-model="formData.yuanGrade" placeholder="请选择原年级" @change="getMatchClass">
          <el-option v-for="item in availGrade" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="toClass" label="原班级">
        <el-select v-model="formData.toClass" placeholder="原班级">
          <el-option v-for="item in availClass" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>-->
      <el-form-item prop="intoGradeId" label="跳级年级">
        <el-select v-model="formData.intoGradeId" placeholder="请选择跳级年级" @change="getMatchClass">
          <el-option v-for="item in availGrade" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="intoClazzId" label="跳级班级">
        <el-select v-model="formData.intoClazzId" placeholder="请选择跳级班级">
          <el-option v-for="item in availClass" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label-width="150px" prop="formData.adjustDate"  label="跳级申请时间">
        <el-date-picker
          v-model="formData.adjustDate"
          format="YYYY年DD月MM日"
          date-format="YYYY年DD月MM日"
          time-format="HH:mm"
          disabled-minutes
          disabled-hours
          disabled-seconds
          type="datetime"
          placeholder="请输入跳级申请时间"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<style scoped lang="scss">

</style>
