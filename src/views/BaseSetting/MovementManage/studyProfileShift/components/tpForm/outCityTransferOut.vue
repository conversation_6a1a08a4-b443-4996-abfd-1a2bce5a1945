<script setup lang="ts">
//市外转入
import {ref, onMounted, computed} from 'vue'
import {apis} from "@/apis";
import { UploadFilled } from '@element-plus/icons-vue'
import type {stuQryResult} from "@/views/BaseSetting/MovementManage/studyProfileShift/dic"
import {useDeptStore, useUserStore} from "@/store";
import Notification from "@/commons/Notification";
import SchoolPeriodSelector from "@/components/SchoolPeriodSelector/index.vue";
import {DialogMode} from "@/commons/DialogModel";
import dayjs from "dayjs";
import {KeyValue} from "@/commons/KeyValue";
import {validateTelephone, validatorIdCard} from "@/commons/Validator";
import type {FormInstance} from "element-plus";
const stuInfoData = defineModel<stuQryResult>('qryResult')
/*const rowData=defineModel<any>('rowData')*/
const isHaveResult = defineModel<boolean>('isHaveResult')
const userStore = useUserStore();
const schoolId= userStore.userInfo?.schoolId
// 表单
let formData = ref({
  /* id: "",*/
  /* studentName: "",*/
  /* idCard: "",*/
  /*  suspendReason:"",*/
  outBeginDate:'',
  attachmentList: [],
  adjustType:6,
  cityName:'',
  schoolName:'',
  /*  studentCode: "",
    gender: "",
    belongClass: "",
    reason: "",
    proof: ""*/
})
formData.value.cityName="石家庄"

formData.value.schoolName=userStore?.userInfo?.schoolName




let currentRegionId = userStore.regionId || '130102'
const deptStore = useDeptStore()
if (deptStore.topDept?.id == deptStore.currentDept?.id) {
  currentRegionId = '130102'
}
// 获取 token
const token: string = userStore.token;
const headers={Authorization: `${token}`}
// 表单验证规则
const rules = ref({studentName: [
    {required: true, message: '请输入名称', trigger: 'blur'},
    {min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur'}
  ],
  sfzh: [{required: true, message: '请输入身份证号', trigger: 'blur'},
    {validator: validatorIdCard, trigger: 'blur'}
  ],
  xjh: [{required: true, message: '请输入学籍号', trigger: 'blur'}],
  xb: [{required: true, message: '请选择性别', trigger: 'blur'}],
  period: [{required: true, message: '请选择学段', trigger: 'change'}],
  adjustReason: [{required: true, message: '请输入审核原因', trigger: 'blur'}],},
)
const emit = defineEmits(['save-complete'])
const saveCompleteEmit = (status: number) => emit('save-complete', status)
const form = ref()
const saveSubmit= async ()=>{
  (form.value as FormInstance).validate((valid) => {
    if (valid) {
      let fcn =  apis.studyProfileShift.addCityOut
      let datas=ref(formData.value)
      datas.value.outBeginDate=dayjs(formData.value.outBeginDate).format('YYYY-MM-DD')
      fcn(datas.value,currentRegionId).then(res=>{
        saveCompleteEmit(1)
        Notification.showSuccessNotify('操作成功')
      }).catch(() => saveCompleteEmit(0))}
    else {
      saveCompleteEmit(0)
    }
  })
}
// 生命周期
onMounted(() => {

})
defineExpose({
  saveSubmit
})
// loading
const dataLoading = ref(false)
const schoolList = ref<any>([])
// 可用年级、班级
let availGrade = ref<KeyValue[]>([])
let availClass = ref<KeyValue[]>([])
let xueQiList=ref<any>()
// 生命周期
onMounted(() => {

  getAvailGrade()
  getxueqi()
  /* getMatchClass()*/
})

// 可用的年级
const getAvailGrade =async () => {
  let datas= await apis.Commons.getGradeSelectList({})

  availGrade.value= datas.data
}
// 获取学期
const getxueqi = async () => {
  let datas= await apis.studyProfileShift.getXueQIList({})

  xueQiList.value= datas
}
// 根据年级获取班级
const getMatchClass = async () => {
  let datas= await apis.studyProfileShift.getClazzSelectList(schoolId, formData.value.gradeId, currentRegionId)

  availClass.value= datas.data
}
/*if (userStore.userInfo?.schoolAdminFlag) {
  await deptStore.setSchoolList(currentRegionId)
  schoolList.value = deptStore.schoolList
/!*  if(props.regionId == userStore.regionId) {
    formData.value.regionId = props.regionId
  }*!/
}*/
const periodList =
  [1, 2, 3]

</script>

<template>
  <div class="suspend">
    <!--    <div class="shift-qry-result">
          <div class="fake-label">查询结果</div>
          <el-descriptions
            :column="1"
            size="default"
            border
            v-if="isHaveResult"
          >
            <el-descriptions-item label-width="100" label="学生姓名">{{ stuInfoData?.xm }}</el-descriptions-item>
            <el-descriptions-item label-width="100" label="身份证号">{{ stuInfoData?.sfzh }}</el-descriptions-item>
            <el-descriptions-item label-width="100" label="学籍号">{{ stuInfoData?.xjh }}</el-descriptions-item>
            <el-descriptions-item label-width="100" label="性别">{{ stuInfoData?.xb==1?'男':'女' }}</el-descriptions-item>
            <el-descriptions-item label-width="100" label="所在班级">{{ stuInfoData?.clazzName }}</el-descriptions-item>
          </el-descriptions>
          <span v-else class="search-tips">输入学籍号来搜索</span>
        </div>-->
    <el-form ref="form" v-loading="dataLoading" :model="formData" :rules="rules" label-width="90px">
      <el-form-item prop="xjh" label="学籍号">
        <el-input v-model="formData.xjh" placeholder="请输入学籍号"  rows="10" show-word-limit
        ></el-input>
      </el-form-item>
      <el-form-item prop="studentName" label="学生姓名">
        <el-input v-model="formData.studentName" placeholder="请输入学生姓名" rows="10" show-word-limit
        ></el-input>
      </el-form-item>
      <el-form-item prop="sfzh" label="身份证号">
        <el-input v-model="formData.sfzh" placeholder="请输入身份证号"  rows="10" show-word-limit
        ></el-input>
      </el-form-item>
      <el-form-item label="学段" prop="period">
        <SchoolPeriodSelector v-model="formData.period" :period-list="periodList" show-type="select" ></SchoolPeriodSelector>
      </el-form-item>
      <el-form-item label="性别" prop="xb">
        <el-select
          v-model="formData.xb"
          placeholder="请选择性别"
        >
          <el-option
            label="男"
            value="1"
          ></el-option>
          <el-option
            label="女"
            value="2"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="inCityName" label="转入城市">
        <el-input v-model="formData.inCityName" placeholder="请输入转入城市"  rows="10" show-word-limit
        ></el-input>
      </el-form-item>
      <el-form-item prop="inSchoolName" label="转入学校">
        <el-input v-model="formData.inSchoolName" placeholder="请输入转入学校"  rows="10" show-word-limit
        ></el-input>
      </el-form-item>
      <el-form-item prop="outTerm" label="转出学期">
        <el-select
          v-model="formData.outTerm"
          placeholder="请选择转出学期"
        >
          <el-option
            v-for="item in xueQiList"
            :key="item.id"
            :label="item.termName"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="gradeId" label="转出年级">
        <el-select v-model="formData.gradeId" placeholder="请选择转出年级" @change="getMatchClass">
          <el-option v-for="item in availGrade" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="toClass" label="转出班级">
        <el-select v-model="formData.clazzId" placeholder="请选择转出班级">
          <el-option v-for="item in availClass" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="adjustReason" label="申请原因">
        <el-input v-model="formData.adjustReason" placeholder="请输入申请原因" type="textarea" rows="10" show-word-limit
                  maxlength="500"></el-input>
      </el-form-item>
      <el-form-item label-width="150px" prop="formData.outBeginDate"  label="转入申请时间">
        <el-date-picker
          v-model="formData.outBeginDate"
          format="YYYY年DD月MM日"
          date-format="YYYY年DD月MM日"
          time-format="HH:mm"
          disabled-minutes
          disabled-hours
          disabled-seconds
          type="datetime"
          placeholder="请输入转入申请时间"
        />
      </el-form-item>
      <el-form-item prop="proof" label="附件">
        <el-upload
          class="upload-demo"
          drag
          action="/region-service/file/manage/upload"
          :headers="headers"
          accept=".zip,.rar"
          multiple
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            请上传附件
          </div>
          <template #tip>
            <div class="el-upload__tip">
              只能上传压缩包文件，且不超过5M
            </div>
          </template>
        </el-upload>
        <!--          <el-upload ref="upload" accept=".zip,.rar" action="" :file-list="fileList" :auto-upload="false" :limit="1"
                             :on-remove="onRemove" :on-exceed="onExceed" :on-change="onChange">

                    <el-button size="small" type="primary" icon="upload">选择文件</el-button>
                    <div slot="tip" class="warning-desc-text">
                      只能上传excel文件，且不超过5M
                    </div>
                  </el-upload>-->
        <!--        <el-input v-model="formData.proof"></el-input>-->
      </el-form-item>
    </el-form>
  </div>
</template>

<style scoped lang="scss">

</style>
