<script setup lang="ts">
import { ref, onMounted } from 'vue'
import type { stuQryResult } from "@/views/BaseSetting/MovementManage/studyProfileShift/dic"
import type { KeyValue } from "@/commons/KeyValue"
const stuInfoData = defineModel<stuQryResult>('qryResult')
const isHaveResult = defineModel<boolean>('isHaveResult')
// 表单
const formData = ref({
  id: "",
  studentName: "",
  idCard: "",
  studentCode: "",
  gender: "",
  belongClass: "",
  toGrade: "",
  toClass: "",
  reason: "",
  proof: ""
})

// 表单验证规则
const rules = ref({

})

// 生命周期
onMounted(() => {

})

// 可用年级、班级
const availGrade = ref<KeyValue[]>([])
const availClass = ref<KeyValue[]>([])

// 生命周期
onMounted(() => {
  getAvailGrade()
})

// 可用的年级
const getAvailGrade = () => {

}

// 根据年级获取班级
const getMatchClass = () => {

}

// loading
const dataLoading = ref(false)

</script>

<template>
  <div class="repeat">
    <div class="shift-qry-result">
      <div class="fake-label">查询结果</div>
      <el-descriptions
        :column="1"
        size="default"
        border
        v-if="isHaveResult"
      >
        <el-descriptions-item label-width="120" label="学生姓名">{{ stuInfoData?.studentName }}</el-descriptions-item>
        <el-descriptions-item label-width="120" label="身份证号">{{ stuInfoData?.idCard }}</el-descriptions-item>
        <el-descriptions-item label-width="120" label="学籍号">{{ stuInfoData?.studentCode }}</el-descriptions-item>
        <el-descriptions-item label-width="120" label="性别">{{ stuInfoData?.gender }}</el-descriptions-item>
        <el-descriptions-item label-width="120" label="所在班级">{{ stuInfoData?.belongClass }}</el-descriptions-item>
      </el-descriptions>
      <span v-else class="search-tips">输入学籍号来搜索</span>
    </div>
    <el-form ref="suspendForm" v-loading="dataLoading" :model="formData" :rules="rules" label-width="70px">
      <el-form-item prop="toGrade" label="留级年级">
        <el-select v-model="formData.toGrade" placeholder="请选择留级年级" @change="getMatchClass">
          <el-option v-for="item in availGrade" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="toClass" label="留级班级">
        <el-select v-model="formData.toClass" placeholder="请选择留级班级">
          <el-option v-for="item in availClass" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<style scoped lang="scss">

</style>
