<script setup lang="ts">
import {ref, onMounted} from 'vue'
import type {stuQryResult} from "@/views/BaseSetting/MovementManage/studyProfileShift/dic"
const stuInfoData = defineModel<stuQryResult>('qryResult')
const isHaveResult = defineModel<boolean>('isHaveResult')
// 表单
const formData = ref({
  id: "",
  studentName: "",
  idCard: "",
  studentCode: "",
  gender: "",
  belongClass: "",
  reason: "",
  proof: ""
})

// 表单验证规则
const rules = ref({})

// 生命周期
onMounted(() => {

})

// loading
const dataLoading = ref(false)

</script>

<template>
  <div class="suspend">
    <div class="shift-qry-result">
      <div class="fake-label">查询结果</div>
      <el-descriptions
        :column="1"
        size="default"
        border
        v-if="isHaveResult"
      >
        <el-descriptions-item label-width="100" label="学生姓名">{{ stuInfoData?.studentName }}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="身份证号">{{ stuInfoData?.idCard }}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="学籍号">{{ stuInfoData?.studentCode }}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="性别">{{ stuInfoData?.gender }}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="所在班级">{{ stuInfoData?.belongClass }}</el-descriptions-item>
      </el-descriptions>
      <span v-else class="search-tips">输入学籍号来搜索</span>
    </div>
    <el-form ref="suspendForm" v-loading="dataLoading" :model="formData" :rules="rules" label-width="70px">
      <el-form-item prop="reason" label="休学原因">
        <el-input v-model="formData.reason" placeholder="请输入休学原因" type="textarea" rows="10" show-word-limit
                  maxlength="500"></el-input>
      </el-form-item>
      <el-form-item prop="proof" label="附件">
        <el-input v-model="formData.proof"></el-input>
      </el-form-item>
    </el-form>
  </div>
</template>

<style scoped lang="scss">

</style>
