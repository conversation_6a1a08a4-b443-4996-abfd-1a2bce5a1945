<script setup lang="ts">
import {ref, onMounted} from 'vue'
import {apis} from "@/apis";
import { UploadFilled } from '@element-plus/icons-vue'
import type {stuQryResult} from "@/views/BaseSetting/MovementManage/studyProfileShift/dic"
import {useDeptStore, useUserStore} from "@/store";
import Notification from "@/commons/Notification";
import {DialogMode} from "@/commons/DialogModel";
import dayjs from "dayjs";
const stuInfoData = defineModel<stuQryResult>('qryResult')
/*const rowData=defineModel<any>('rowData')*/
const isHaveResult = defineModel<boolean>('isHaveResult')
// 表单
let formData = ref({
 /* id: "",*/
 /* studentName: "",*/
 /* idCard: "",*/
  suspendReason:"",
  suspendBeginDate:'',
  attachmentList: [],
  adjustType:1,
  /*  studentCode: "",
    gender: "",
    belongClass: "",
    reason: "",
    proof: ""*/
})




const userStore = useUserStore();
let currentRegionId = userStore.regionId || '130102'
const deptStore = useDeptStore()
if (deptStore.topDept?.id == deptStore.currentDept?.id) {
  currentRegionId = '130102'
}
// 获取 token
const token: string = userStore.token;
const headers={Authorization: `${token}`}
// 表单验证规则
const rules = ref({})
const emit = defineEmits(['save-complete'])
const saveCompleteEmit = (status: number) => emit('save-complete', status)
const saveSubmit=async ()=>{
  let fcn =  apis.studyProfileShift.addSuspend
  let datas=ref(formData.value)
  datas.value.xjh=stuInfoData.value?.xjh
  datas.value.suspendBeginDate=dayjs(formData.value.suspendBeginDate).format('YYYY-MM-DD')
  fcn(datas.value,currentRegionId).then(res=>{
    saveCompleteEmit(1)
    Notification.showSuccessNotify('操作成功')
  }).catch(() => saveCompleteEmit(0))
}
// 生命周期
onMounted(() => {

})
defineExpose({
  saveSubmit
})
// loading
const dataLoading = ref(false)

</script>

<template>
  <div class="suspend">
    <div class="shift-qry-result">
      <div class="fake-label">查询结果</div>
      <el-descriptions
        :column="1"
        size="default"
        border
        v-if="isHaveResult"
      >
        <el-descriptions-item label-width="100" label="学生姓名">{{ stuInfoData?.xm }}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="身份证号">{{ stuInfoData?.sfzh }}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="学籍号">{{ stuInfoData?.xjh }}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="性别">{{ stuInfoData?.xb==1?'男':'女' }}</el-descriptions-item>
        <el-descriptions-item label-width="100" label="所在班级">{{ stuInfoData?.clazzName }}</el-descriptions-item>
      </el-descriptions>
      <span v-else class="search-tips">输入学籍号来搜索</span>
    </div>
    <el-form ref="suspendForm" v-loading="dataLoading" :model="formData" :rules="rules" label-width="70px">
      <el-form-item prop="suspendReason" label="休学原因">
        <el-input v-model="formData.suspendReason" placeholder="请输入休学原因" type="textarea" rows="10" show-word-limit
                  maxlength="500"></el-input>
      </el-form-item>
      <el-form-item label-width="150px" prop="formData.suspendBeginDate"  label="休学申请时间">
        <el-date-picker
          v-model="formData.suspendBeginDate"
          format="YYYY年DD月MM日"
          date-format="YYYY年DD月MM日"
          time-format="HH:mm"
          disabled-minutes
          disabled-hours
          disabled-seconds
          type="datetime"
          placeholder="请输入休学申请时间"
        />
      </el-form-item>
      <el-form-item prop="proof" label="附件">
        <el-upload
          class="upload-demo"
          drag
          action="/region-service/file/manage/upload"
          :headers="headers"
          accept=".zip,.rar"
          multiple
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
           请上传附件
          </div>
          <template #tip>
            <div class="el-upload__tip">
              只能上传压缩包文件，且不超过5M
            </div>
          </template>
        </el-upload>
<!--          <el-upload ref="upload" accept=".zip,.rar" action="" :file-list="fileList" :auto-upload="false" :limit="1"
                     :on-remove="onRemove" :on-exceed="onExceed" :on-change="onChange">

            <el-button size="small" type="primary" icon="upload">选择文件</el-button>
            <div slot="tip" class="warning-desc-text">
              只能上传excel文件，且不超过5M
            </div>
          </el-upload>-->
<!--        <el-input v-model="formData.proof"></el-input>-->
      </el-form-item>
    </el-form>
  </div>
</template>

<style scoped lang="scss">

</style>
