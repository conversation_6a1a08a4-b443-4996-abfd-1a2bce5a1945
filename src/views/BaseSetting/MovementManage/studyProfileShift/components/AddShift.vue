<script setup lang="ts">
import {ref, onMounted} from 'vue'
// 休学
import SuspendForm from '@/views/BaseSetting/MovementManage/studyProfileShift/components/tpForm/suspend.vue'
// 复学
import RestoreForm from '@/views/BaseSetting/MovementManage/studyProfileShift/components/tpForm/restore.vue'
// 留级
import RepeatForm from '@/views/BaseSetting/MovementManage/studyProfileShift/components/tpForm/repeat.vue'
// 跳级
import JumpForm from '@/views/BaseSetting/MovementManage/studyProfileShift/components/tpForm/jump.vue'
// 市内转入
import InCityTransferIn
  from '@/views/BaseSetting/MovementManage/studyProfileShift/components/tpForm/inCityTransferIn.vue'
// 市外转入
import OutCityTransferIn
  from '@/views/BaseSetting/MovementManage/studyProfileShift/components/tpForm/outCityTransferIn.vue'
// 市外转出
import OutCityTransferOut
  from '@/views/BaseSetting/MovementManage/studyProfileShift/components/tpForm/outCityTransferOut.vue'
// 新增档案
import NewProfileForm from '@/views/BaseSetting/MovementManage/studyProfileShift/components/tpForm/newProfile.vue'
// 失踪
import DisappearForm from "@/views/BaseSetting/MovementManage/studyProfileShift/components/tpForm/disappear.vue"
// 死亡
import DeadForm from "@/views/BaseSetting/MovementManage/studyProfileShift/components/tpForm/dead.vue"
import type {stuQryResult} from "@/views/BaseSetting/MovementManage/studyProfileShift/dic"
import {shiftTpList} from '@/views/BaseSetting/MovementManage/studyProfileShift/dic'

// 弹窗顶部 - 异动类型
let addTpForm = ref({
  // 异动类型
  tp: '',
  // 学籍号
  stuCode: '',
  // 是否查询到了结果
  isHaveResult: false
})

// 查询到的学生详情
let stuInfoRes = ref<stuQryResult>({
  id: '',
  studentName: '',
  idCard: '',
  studentCode: '',
  gender: '',
  belongClass: '',
  reason: '',
  proof: ''
})

// props
const props = defineProps({
  rowData: {
    type: [Object, null],
    default: () => {
    }
  }
})

// 生命周期
onMounted(() => {
  // 指定了类型
  if (props?.rowData?.tp) {
    addTpForm.value.tp = props.rowData.tp
  }
  // 按学籍号查询
  if (props?.rowData?.stuCode) {
    searchStu()
  }
})

// 根据学籍号搜索学生
const searchStu = () => {
  // 模拟请求
  let lucky = Math.floor(Math.random() * 10);
  let nmList = ['张三', '嘎巴', '快机', '热风', '覆盖', '兴冲', '而过', '安装', '通过', '月月']
  let idList = ['140303201711022773', '140303201711021519', '140303201711028331', '140303201711020110', '140303201711023776', '140303201711022132', '140303201711028737', '140303201711020014', '140303201711025990', '140303201711026854']
  stuInfoRes.value.id = `${Math.random()}`
  stuInfoRes.value.studentName = nmList[lucky]
  stuInfoRes.value.idCard = idList[lucky]
  stuInfoRes.value.studentCode = `G${stuInfoRes.value.idCard}`
  stuInfoRes.value.gender = '男'
  stuInfoRes.value.belongClass = `2021级${lucky}班`
  addTpForm.value.isHaveResult = true
}

// 切换类型就清空查询结果
const clearQryRes = () => {
  stuInfoRes.value.id = ''
  stuInfoRes.value.studentName = ''
  stuInfoRes.value.idCard = ''
  stuInfoRes.value.studentCode = ''
  stuInfoRes.value.gender = ''
  stuInfoRes.value.belongClass = ''
  stuInfoRes.value.reason = ''
  stuInfoRes.value.proof = ''
  addTpForm.value.isHaveResult = false
}
</script>

<template>
  <div class="add-shift">
    <el-form ref="form" :model="addTpForm" label-width="70px">
      <el-form-item prop="tp" label="异动类型">
        <el-select v-model="addTpForm.tp" placeholder="请选择异动类型" @change="clearQryRes">
          <el-option v-for="item in shiftTpList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="stuCode" label="学籍号">
        <el-input v-model="addTpForm.stuCode" placeholder="请输入学籍号" maxlength="19">
          <template #append>
            <el-button icon="Search" @click="searchStu"></el-button>
          </template>
        </el-input>
      </el-form-item>
    </el-form>
    <div class="shift-form">
      <SuspendForm v-if="addTpForm.tp == 'suspend'" v-model:qryResult="stuInfoRes"
                   v-model:isHaveResult="addTpForm.isHaveResult"></SuspendForm>
      <RestoreForm v-else-if="addTpForm.tp == 'restore'" v-model:qryResult="stuInfoRes"
                   v-model:isHaveResult="addTpForm.isHaveResult"></RestoreForm>
      <RepeatForm v-else-if="addTpForm.tp == 'repeat'" v-model:qryResult="stuInfoRes"
                  v-model:isHaveResult="addTpForm.isHaveResult"></RepeatForm>
      <JumpForm v-else-if="addTpForm.tp == 'jump'" v-model:qryResult="stuInfoRes"
                v-model:isHaveResult="addTpForm.isHaveResult"></JumpForm>
      <InCityTransferIn v-else-if="addTpForm.tp == 'inCityTransferIn'"
                        v-model:qryResult="stuInfoRes"></InCityTransferIn>
      <OutCityTransferIn v-else-if="addTpForm.tp == 'outCityTransferIn'"
                         v-model:qryResult="stuInfoRes"></OutCityTransferIn>
      <OutCityTransferOut v-else-if="addTpForm.tp == 'outCityTransferOut'"
                          v-model:qryResult="stuInfoRes"></OutCityTransferOut>
      <NewProfileForm v-else-if="addTpForm.tp == 'newProfile'"></NewProfileForm>
      <DisappearForm v-else-if="addTpForm.tp == 'disappear'" v-model:qryResult="stuInfoRes"
                     v-model:isHaveResult="addTpForm.isHaveResult"></DisappearForm>
      <DeadForm v-else-if="addTpForm.tp == 'dead'" v-model:qryResult="stuInfoRes"
                v-model:isHaveResult="addTpForm.isHaveResult"></DeadForm>
    </div>
  </div>
</template>

<style scoped lang="scss">

</style>
