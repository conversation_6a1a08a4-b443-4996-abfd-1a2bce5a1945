<script setup lang="ts">
import {ref, onMounted} from 'vue'
import {apis} from "@/apis"
import { shiftTpList } from '@/views/BaseSetting/MovementManage/studyProfileShift/dic'
// 休学
import SuspendForm from '@/views/BaseSetting/MovementManage/studyProfileShift/components/tpForm/suspend.vue'
// 复学
import RestoreForm from '@/views/BaseSetting/MovementManage/studyProfileShift/components/tpForm/restore.vue'
// 留级
import RepeatForm from '@/views/BaseSetting/MovementManage/studyProfileShift/components/tpForm/repeat.vue'
// 跳级
import JumpForm from '@/views/BaseSetting/MovementManage/studyProfileShift/components/tpForm/jump.vue'
// 市内转入
import InCityTransferIn
  from '@/views/BaseSetting/MovementManage/studyProfileShift/components/tpForm/inCityTransferIn.vue'
// 市外转入
import OutCityTransferIn
  from '@/views/BaseSetting/MovementManage/studyProfileShift/components/tpForm/outCityTransferIn.vue'
// 市外转出
import OutCityTransferOut
  from '@/views/BaseSetting/MovementManage/studyProfileShift/components/tpForm/outCityTransferOut.vue'
// 失踪
import DisappearForm from "@/views/BaseSetting/MovementManage/studyProfileShift/components/tpForm/disappear.vue"
// 死亡
import DeadForm from "@/views/BaseSetting/MovementManage/studyProfileShift/components/tpForm/dead.vue"
import type {stuQryResult} from "@/views/BaseSetting/MovementManage/studyProfileShift/dic"
import {useDeptStore, useUserStore} from "@/store";
import Notification from "@/commons/Notification";
// 弹窗顶部 - 异动类型
let addTpForm = ref({
  // 异动类型
  tp: '',
  // 学籍号
  stuCode: '',
  // 是否查询到了结果
  isHaveResult: false
})
const userStore = useUserStore()
let currentRegionId = userStore.regionId || '130102'
const deptStore = useDeptStore()

if (deptStore.topDept?.id == deptStore.currentDept?.id) {
  currentRegionId = '130102'
}
// 查询到的学生详情
let stuInfoRes = ref<stuQryResult>({
  id: '',
  studentName: '',
  idCard: '',
  studentCode: '',
  gender: '',
  belongClass: '',
  reason: '',
  proof: ''
})

// props
const props = defineProps({
  rowData: {
    type: [Object, null],
    default: () => {
    }
  },

})
if(props.rowData.adjustType==3){
  addTpForm.value.isHaveResult=true
  console.log(props.rowData,"ddd")
  stuInfoRes.value.adjustType=props.rowData.adjustType
  stuInfoRes.value.id = props.rowData.id
  stuInfoRes.value.xm = props.rowData.studentName
  stuInfoRes.value.sfzh =props.rowData.sfzh
  stuInfoRes.value.xjh = props.rowData.xjh
  stuInfoRes.value.xb = props.rowData.xb
  stuInfoRes.value.clazzName = props.rowData.outClazzName
  stuInfoRes.value.clazzId= props.rowData.outClazzId
  stuInfoRes.value.gradeName = props.rowData.outGradeName
  stuInfoRes.value.gradeId= props.rowData.outGradeId
  stuInfoRes.value.schoolName = props.rowData.outSchoolName
  stuInfoRes.value.schoolId= props.rowData.outSchoolId
  stuInfoRes.value.oldRegionId=props.rowData.regionId
  stuInfoRes.value.studentId=props.rowData.studentId
  stuInfoRes.value.intoClazzId = props.rowData.intoClazzId
  stuInfoRes.value.intoGradeId = props.rowData.intoGradeId
  stuInfoRes.value.intoSchoolId=props.rowData.intoSchoolId
  stuInfoRes.value.adjustReason =props.rowData.inReason
  stuInfoRes.value.period =Number(props.rowData.intoPeriod)
  stuInfoRes.value.inBeginDate=props.rowData.applyDate
}
const dialogModel=ref()
const suspendForm = ref()
const restoreForm=ref()
const cityInForm=ref()
const cityOutForm=ref()
const cityShiNeiInForm=ref()
const liuJiForm=ref()
const tiaoJiForm=ref()
const emit = defineEmits(['save-complete'])
const saveCompleteEmit = (status: number) => emit('save-complete', status)
const saveSubmit=async ()=>{

  if(props.rowData.type='add'&&addTpForm.value.tp==1){
    suspendForm.value.saveSubmit()
  }
  if(props.rowData.type='add'&&addTpForm.value.tp==5){
    cityInForm.value.saveSubmit()
  }
  if(props.rowData.type='add'&&addTpForm.value.tp==6){
    cityOutForm.value.saveSubmit()
  }
  if(props.rowData.type='add'&&addTpForm.value.tp==7){
    liuJiForm.value.saveSubmit()
  }
  if(props.rowData.type='add'&&addTpForm.value.tp==8){
    tiaoJiForm.value.saveSubmit()
  }
  if(addTpForm.value.tp==3||props.rowData.adjustType==3){
    cityShiNeiInForm.value.saveSubmit()
  }
  if(props.rowData.adjustType==1){
    restoreForm.value.saveSubmit()
  }
}
// 生命周期
onMounted(() => {
  // 指定了类型
  if (props?.rowData?.tp) {
    addTpForm.value.tp = props.rowData.tp
  }
  // 按学籍号查询
  if (props?.rowData?.stuCode) {
    searchStu()
  }
})

// 根据学籍号搜索学生
const  searchStu =async () => {
 let fns=apis.studyProfileShift.jianSuoSuspend
  let shiNeiFns=apis.studyProfileShift.shiNeiSuoSuspend
  console.log(addTpForm.value.tp,"tp")
  if(addTpForm.value.tp!=3){
    fns({key:addTpForm.value.stuCode},currentRegionId).then(res=>{
      if(res){
        stuInfoRes.value.id = res.data.id
        stuInfoRes.value.xm = res.data.xm
        stuInfoRes.value.sfzh =res.data.sfzh
        stuInfoRes.value.xjh = res.data.xjh
        stuInfoRes.value.xb = res.data.xb
        stuInfoRes.value.clazzId = res.data.clazzId
        stuInfoRes.value.clazzName = res.data.clazzName
        stuInfoRes.value.gradeId = res.data.gradeId
        stuInfoRes.value.gradeName = res.data.gradeName

      }else {
        Notification.showErrorNotify('暂无此学生学籍信息')
      }
    })
  }else {
    shiNeiFns({key:addTpForm.value.stuCode},currentRegionId).then(res=>{
      console.log(res,"res")
      if(res){
        stuInfoRes.value.id = res.data.id
        stuInfoRes.value.xm = res.data.xm
        stuInfoRes.value.sfzh =res.data.sfzh
        stuInfoRes.value.xjh = res.data.xjh
        stuInfoRes.value.xb = res.data.xb
        stuInfoRes.value.clazzName = res.data.clazzName
        stuInfoRes.value.clazzId= res.data.clazzId
        stuInfoRes.value.gradeName = res.data.gradeName
        stuInfoRes.value.gradeId= res.data.gradeId
        stuInfoRes.value.schoolName = res.data.schoolName
        stuInfoRes.value.schoolId= res.data.schoolId
        stuInfoRes.value.oldRegionId=res.data.regionId
        stuInfoRes.value.studentId=res.data.id

      }else {
        Notification.showErrorNotify('暂无此学生学籍信息')
      }
    })
  }


  addTpForm.value.isHaveResult = true
}

// 切换类型就清空查询结果
const clearQryRes = () => {
  stuInfoRes.value.id = ''
  stuInfoRes.value.studentName = ''
  stuInfoRes.value.idCard = ''
  stuInfoRes.value.studentCode = ''
  stuInfoRes.value.gender = ''
  stuInfoRes.value.belongClass = ''
  stuInfoRes.value.reason = ''
  stuInfoRes.value.proof = ''
  addTpForm.value.isHaveResult = false
}
let saveComplete=(row)=>{
  saveCompleteEmit(row)
  console.log(row,"ddd")
}
defineExpose({
  saveSubmit
})
</script>

<template>
  <div class="add-shift">
    <el-form ref="form" :model="addTpForm" label-width="70px" v-if="rowData.type=='add'">
      <el-form-item prop="tp" label="异动类型">
        <el-select v-model="addTpForm.tp" placeholder="请选择异动类型" @change="clearQryRes" disabled>
          <el-option v-for="item in shiftTpList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="stuCode" label="学籍号" v-if="addTpForm.tp!=''&&addTpForm.tp!=5&&addTpForm.tp!=6">
        <el-input v-model="addTpForm.stuCode" placeholder="请输入学籍号" maxlength="19">
          <template #append>
            <el-button icon="Search" @click="searchStu"></el-button>
          </template>
        </el-input>
      </el-form-item>
    </el-form>
    <div class="shift-form">
      <SuspendForm v-if="addTpForm.tp == '1'" v-model:qryResult="stuInfoRes"
                   v-model:isHaveResult="addTpForm.isHaveResult" ref="suspendForm" @save-complete="saveComplete"></SuspendForm>
      <RestoreForm v-else-if="rowData.adjustType==1" v-model:qryResult="stuInfoRes" :rowData="rowData"
                   v-model:isHaveResult="addTpForm.isHaveResult" ref="restoreForm" @save-complete="saveComplete"></RestoreForm>
      <RepeatForm ref="liuJiForm" v-else-if="addTpForm.tp == '7'" v-model:qryResult="stuInfoRes"
                  v-model:isHaveResult="addTpForm.isHaveResult" @save-complete="saveComplete"></RepeatForm>
      <JumpForm ref="tiaoJiForm" v-else-if="addTpForm.tp == '8'" v-model:qryResult="stuInfoRes"
                v-model:isHaveResult="addTpForm.isHaveResult" @save-complete="saveComplete"></JumpForm>
      <InCityTransferIn ref="cityShiNeiInForm" v-else-if="addTpForm.tp == '3'||rowData.adjustType==3"  v-model:isHaveResult="addTpForm.isHaveResult"
                        v-model:qryResult="stuInfoRes" @save-complete="saveComplete"></InCityTransferIn>
      <OutCityTransferIn ref="cityInForm" v-else-if="addTpForm.tp == 5"
                         v-model:qryResult="stuInfoRes" @save-complete="saveComplete"></OutCityTransferIn>
      <OutCityTransferOut ref="cityOutForm" v-else-if="addTpForm.tp == '6'"
                          v-model:qryResult="stuInfoRes" @save-complete="saveComplete"></OutCityTransferOut>
<!--      <NewProfileForm v-else-if="addTpForm.tp == 'newProfile'"></NewProfileForm>-->
      <DisappearForm v-else-if="addTpForm.tp == '9'" v-model:qryResult="stuInfoRes"
                     v-model:isHaveResult="addTpForm.isHaveResult" @save-complete="saveComplete"></DisappearForm>
      <DeadForm v-else-if="addTpForm.tp == '10'" v-model:qryResult="stuInfoRes"
                v-model:isHaveResult="addTpForm.isHaveResult" @save-complete="saveComplete"></DeadForm>
    </div>
  </div>
</template>

<style scoped lang="scss">

</style>
