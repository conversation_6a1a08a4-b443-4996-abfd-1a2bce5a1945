<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { apis } from "@/apis";
import { useTableCommons } from "@/commons/TableCommons";
import {shiftTpList3} from "@/views/BaseSetting/MovementManage/studyProfileShift/dic"
// import { useUserStore } from '@/store';
import { DialogMode } from "@/commons/DialogModel";
// import { ElMessageBox } from "element-plus";
// import Notification from "@/commons/Notification";
import AddForm from "@/views/BaseSetting/MovementManage/studyProfileShift/components/AddShift.vue";
import EditForm from "@/views/BaseSetting/MovementManage/studyProfileShift/components/AddShift.vue";
import DetailForm from "@/views/BaseSetting/MovementManage/studyProfileShift/components/ShiftDetail.vue"
import Audit from "@/views/BaseSetting/MovementManage/studyProfileShift/components/AuditShift.vue"
import { shiftTpList } from '@/views/BaseSetting/MovementManage/studyProfileShift/dic'
import {useDeptStore, useUserStore} from "@/store";
import {KeyValue} from "@/commons/KeyValue";
const userStore = useUserStore()
let currentRegionId = userStore.regionId || '130102'
const deptStore = useDeptStore()

if (deptStore.topDept?.id == deptStore.currentDept?.id) {
  currentRegionId = '130102'
}
// 主列表搜索条件
const search = ref({
  pageNumber: 1,
  pageSize: 10,
  keywords: '',
  period: '',
  grade: '',
  masterOrSportTeacher: '',
  className: '',
  type:1
  /* regionId: currentRegionId*/
})
// store
// const userStore = useUserStore()

// 主列表
const getTableData = async () => {
  const response = await apis.studyProfileShift.getCityneiOutList(search.value, currentRegionId)
  return response.data
}

//
let {
  loading,
  dialogModel,
  showFormDialog,
  tableData,
  searchSubmit,
  pageOrSizeChange,
} = useTableCommons(search, getTableData)

// 生命周期
onMounted(async () => {
  await searchSubmit()
  await regionChange()
})

//
const addForm = ref()
const editForm = ref()
const AuditForm=ref()
const saveSubmit = async () => {
  dialogModel.value.saveLoading = true
  if (dialogModel.value.formName === 'AddForm') {
    addForm.value.saveSubmit()
  } else if (dialogModel.value.formName === 'EditForm') {
    editForm.value.saveSubmit()
  } else if (dialogModel.value.formName === 'AuditForm') {
    AuditForm.value.saveSubmit()
  }
}
const regionList = ref<any>(useDeptStore().regionList)
const schoolList = ref<Array<KeyValue>>([])
const regionChange = async () => {
  search.value.schoolId = ''
  schoolList.value = (await apis.Commons.getSchoolSelectListCounty(currentRegionId)).data;
}
</script>

<template>
  <div class="study-profile-shift">
    <div class="lmt-operation-container">
      <div class="lmt-operation">
<!--        <el-button v-has-perm="'clazz:add'" icon="Plus" type="primary"
                   @click="showFormDialog('添加学籍异动','AddForm',{  },DialogMode.ADD,'600px')">添加</el-button>-->
        <el-button v-has-perm="['student:export']" type="info" icon="Download"
                   @click="apis.studyProfileShift.outcityOutOrInexport(search, currentRegionId)">批量导出
        </el-button>
      </div>
      <div class="lmt-search">
        <el-select v-model="search.regionId" :clearable="false" placeholder="请选择所属区县" @change="regionChange"
                   v-if="!(userStore.userInfo?.countyAdminFlag || userStore.userInfo?.schoolAdminFlag)">
          <el-option v-for="item in regionList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <el-select v-model="search.schoolId" clearable placeholder="所属学校"
                   v-if="!userStore.userInfo?.schoolAdminFlag">
          <el-option v-for="item in schoolList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <el-select v-model="search.adjustType"
                   placeholder="请选择异动类型" class="filter-item" :clearable="true">
          <el-option v-for="item in shiftTpList1" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <el-date-picker v-model="search.suspendBeginDate" type="date" placeholder="申请日期" value-format="YYYY-MM-DD" size="default"   style="width: 180px;" :clearable="true" />
        <el-input v-model="search.keywords" placeholder="学生姓名" class="filter-item" :clearable="true"></el-input>
        <el-input v-model="search.xjh" placeholder="学籍号" class="filter-item" :clearable="true" @clear="searchSubmit">
          <template #append>
            <el-button icon="Search" @click="searchSubmit"></el-button>
          </template>
        </el-input>
      </div>
    </div>
    <el-table v-loading="loading" :data="tableData.records" border max-height="calc(100vh - var(--lmt-header-height) - var(--lmt-main-padding) * 2 - 6rem)"
              stripe>
      <el-table-column align="center" label="序号" type="index" width="80">
        <template #default="scope">
          {{ (search.pageNumber - 1) * search.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="申请ID" width="100" prop="adjustId"></el-table-column>
      <el-table-column label="学生姓名" min-width="120" prop="studentName" show-overflow-tooltip></el-table-column>
      <el-table-column label="学籍号" min-width="120" prop="xjh" show-overflow-tooltip></el-table-column>
      <el-table-column label="性别" min-width="120" prop="xb" show-overflow-tooltip>
        <template #default="scope">
          {{scope.row.xb==1?'男':'女'}}
        </template>
      </el-table-column>
      <el-table-column label="转入学校" min-width="120" prop="intoSchoolName" show-overflow-tooltip></el-table-column>
      <el-table-column label="转入年级" min-width="120" prop="intoGradeName" show-overflow-tooltip></el-table-column>
      <el-table-column label="转入班级" min-width="120" prop="intoClazzName" show-overflow-tooltip></el-table-column>
      <el-table-column label="当前年级" min-width="120" prop="gradeName" show-overflow-tooltip></el-table-column>
      <el-table-column label="当前班级" min-width="120" prop="clazzName" show-overflow-tooltip></el-table-column>
      <el-table-column label="申请日期" min-width="120" prop="applyDate" show-overflow-tooltip></el-table-column>
      <el-table-column label="审核状态" min-width="120" prop="status" show-overflow-tooltip>
        <template #default="scope">
          {{scope.row.status==1?'待审核':scope.row.status==2?'审核通过':scope.row.status==3?'审核不通过':''}}
        </template>
      </el-table-column>
      <el-table-column label="操作日期" min-width="120" prop="applyDate" show-overflow-tooltip></el-table-column>
      <el-table-column label="操作" min-width="120" fixed="right">
        <template #default="scope">
          <el-link icon="View" type="primary"
                   @click="showFormDialog('详情','DetailForm', scope.row, DialogMode.DETAIL,'800px')">详情
          </el-link>
          <el-link v-has-perm="['student:shiNeiIn:audit']" icon="AUDIT" type="warning"
                   @click="showFormDialog('审核','AuditForm',scope.row,DialogMode.AUDIT,'600px')">审核
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-model:current-page="search.pageNumber" v-model:page-size="search.pageSize"
                   :page-sizes="[10, 20, 30, 40, 50, 100]" :total="Number(tableData.total)"
                   background
                   class="m-t-4 float-right" layout="total, prev, pager, next, sizes, ->" @change="pageOrSizeChange">
    </el-pagination>
    <el-dialog v-model="dialogModel.visible" :close-on-click-modal="false" :title="dialogModel.title" :width="dialogModel.width"
               destroy-on-close @closed="dialogModel.closed()">
      <AddForm v-if="dialogModel.formName == 'AddForm'"  ref="addForm" :mode="dialogModel.mode"
               :row-data="{ type:'add',tp:'3' }" @save-complete="dialogModel.saveComplete"/>
      <EditForm v-if="dialogModel.formName == 'EditForm'" ref="editForm" :mode="dialogModel.mode"
                :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <DetailForm v-if="dialogModel.formName == 'DetailForm'" ref="detailForm" :mode="dialogModel.mode"
                  :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <Audit v-if="dialogModel.formName == 'AuditForm'" ref="AuditForm" :row-data="dialogModel.rowData" :mode="dialogModel.mode"  @save-complete="dialogModel.saveComplete"></Audit>
      <template #footer>
        <el-button @click="dialogModel.closed()">
          {{ dialogModel.formName !== 'DetailForm' ? '取 消' : '关 闭' }}
        </el-button>
        <el-button v-if="dialogModel.formName!=='DetailForm'" :loading="dialogModel.saveLoading" type="primary"
                   @click="saveSubmit">确 认
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.study-profile-shift {
  .filter-item {
    width: 180px;
  }
}
</style>
