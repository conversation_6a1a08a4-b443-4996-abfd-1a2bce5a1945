<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { apis } from "@/apis";
import { useTableCommons } from "@/commons/TableCommons";
// import { useUserStore } from '@/store';
import { DialogMode } from "@/commons/DialogModel";
// import { ElMessageBox } from "element-plus";
// import Notification from "@/commons/Notification";
import AddForm from "@/views/BaseSetting/MovementManage/studyProfileShift/components/AddShift.vue";
import EditForm from "@/views/BaseSetting/MovementManage/studyProfileShift/components/AddShift.vue";
import DetailForm from "@/views/BaseSetting/MovementManage/studyProfileShift/components/ShiftDetail.vue"
import { shiftTpList,shiftTpList1 } from '@/views/BaseSetting/MovementManage/studyProfileShift/dic'
import {useDeptStore, useUserStore} from "@/store";
import {KeyValue} from "@/commons/KeyValue";
const userStore = useUserStore()
let currentRegionId = userStore.regionId || '130102'
const deptStore = useDeptStore()

if (deptStore.topDept?.id == deptStore.currentDept?.id) {
  currentRegionId = '130102'
}
const regionList = ref<any>(useDeptStore().regionList)
const schoolList = ref<Array<KeyValue>>([])
// 主列表搜索条件
const search = ref({
  pageNumber: 1,
  pageSize: 10,
  keywords: '',
  period: '',
  grade: '',
  suspendBeginDate:'',
  masterOrSportTeacher: '',
  className: '',
  adjustType:7,
  regionId:  currentRegionId
})
// store
// const userStore = useUserStore()

// 主列表
const getTableData = async () => {
  search.value.adjustType=7
  const response = await apis.studyProfileShift.getLiuOrTiaoList(search.value, search.value.regionId)
  return response.data
}

//
let {
  loading,
  dialogModel,
  showFormDialog,
  tableData,
  searchSubmit,
  pageOrSizeChange,
} = useTableCommons(search, getTableData)

// 生命周期
onMounted( async () => {
  if (userStore.userInfo?.cityAdminFlag) {
    await useDeptStore().setRegionList();
    regionList.value = useDeptStore().regionList;
  } else {
    regionList.value = [{key: currentRegionId, value: userStore.userInfo?.regionName}]
  }
  await regionChange()
  await searchSubmit()
})

//
const addForm = ref()
const editForm = ref()
const saveSubmit = async () => {
  dialogModel.value.saveLoading = true
  if (dialogModel.value.formName === 'AddForm') {
    addForm.value.saveSubmit()
  } else if (dialogModel.value.formName === 'EditForm') {
    editForm.value.saveSubmit()
  }
}
const regionChange = async () => {
  search.value.schoolId = ''
  schoolList.value = (await apis.Commons.getSchoolSelectListCounty(search.value.regionId)).data;
}
</script>

<template>
  <div class="study-profile-shift">
    <div class="lmt-operation-container">
      <div class="lmt-operation">
        <el-button v-has-perm="['student:suspend:add']"  icon="Plus" type="primary"
                   @click="showFormDialog('添加学籍异动','AddForm',{  },DialogMode.ADD,'600px')">添加</el-button>
      </div>
      <el-button v-has-perm="['student:export']" type="info" icon="Download"
                 @click="apis.studyProfileShift.export(search, currentRegionId)">批量导出
      </el-button>
      <div class="lmt-search">
        <el-select v-model="search.regionId" :clearable="false" placeholder="请选择所属区县" @change="regionChange"
                   v-if="!(userStore.userInfo?.countyAdminFlag || userStore.userInfo?.schoolAdminFlag)">
          <el-option v-for="item in regionList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <el-select v-model="search.schoolId" clearable placeholder="所属学校"
                   v-if="!userStore.userInfo?.schoolAdminFlag">
          <el-option v-for="item in schoolList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <el-date-picker v-model="search.suspendBeginDate" type="date" placeholder="申请日期" value-format="YYYY-MM-DD" size="default"   style="width: 180px;" :clearable="true" />
        <el-input v-model="search.keywords" placeholder="学生姓名" class="filter-item" :clearable="true"></el-input>
        <el-input v-model="search.className" placeholder="学籍号" class="filter-item" :clearable="true" @clear="searchSubmit">
          <template #append>
            <el-button icon="Search" @click="searchSubmit"></el-button>
          </template>
        </el-input>
      </div>
    </div>
    <el-table v-loading="loading" :data="tableData.records" border max-height="calc(100vh - var(--lmt-header-height) - var(--lmt-main-padding) * 2 - 6rem)"
              stripe>
      <el-table-column align="center" label="序号" type="index" width="80">
        <template #default="scope">
          {{ (search.pageNumber - 1) * search.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="申请ID" width="100" prop="adjustId"></el-table-column>
      <el-table-column label="学生姓名" min-width="120" prop="xm" show-overflow-tooltip></el-table-column>
      <el-table-column label="学籍号" min-width="120" prop="xjh" show-overflow-tooltip></el-table-column>
      <el-table-column label="性别" min-width="120" prop="xb" show-overflow-tooltip>
        <template #default="scope">
          {{scope.row.xb==1?'男':'女'}}
        </template>
      </el-table-column>
      <el-table-column label="原年级" min-width="120" prop="oldGradeName" show-overflow-tooltip></el-table-column>
      <el-table-column label="原班级" min-width="120" prop="oldClassName" show-overflow-tooltip></el-table-column>
      <el-table-column label="留级年级" min-width="120" prop="gradeName" show-overflow-tooltip></el-table-column>
      <el-table-column label="留级班级" min-width="120" prop="clazzName" show-overflow-tooltip></el-table-column>
      <el-table-column label="申请日期" min-width="120" prop="applyDate" show-overflow-tooltip></el-table-column>
      <el-table-column label="操作" min-width="120" fixed="right">
        <template #default="scope">
          <el-link icon="View" type="primary"
                   @click="showFormDialog('详情','DetailForm', scope.row, DialogMode.DETAIL,'600px')">详情
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-model:current-page="search.pageNumber" v-model:page-size="search.pageSize"
                   :page-sizes="[10, 20, 30, 40, 50, 100]" :total="Number(tableData.total)"
                   background
                   class="m-t-4 float-right" layout="total, prev, pager, next, sizes, ->" @change="pageOrSizeChange">
    </el-pagination>
    <el-dialog v-model="dialogModel.visible" :close-on-click-modal="false" :title="dialogModel.title" :width="dialogModel.width"
               destroy-on-close @closed="dialogModel.closed()">
      <AddForm v-if="dialogModel.formName == 'AddForm'" :shiftTpList="shiftTpList" ref="addForm" :mode="dialogModel.mode"
               :row-data="{type:'add',tp:'7'  }" @save-complete="dialogModel.saveComplete"/>
      <EditForm v-if="dialogModel.formName == 'EditForm'" ref="editForm" :mode="dialogModel.mode"
                :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <DetailForm v-if="dialogModel.formName == 'DetailForm'" ref="detailForm" :mode="dialogModel.mode"
                  :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <template #footer>
        <el-button @click="dialogModel.closed()">
          {{ dialogModel.formName !== 'DetailForm' ? '取 消' : '关 闭' }}
        </el-button>
        <el-button v-if="dialogModel.formName!=='DetailForm'" :loading="dialogModel.saveLoading" type="primary"
                   @click="saveSubmit">确 认
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.study-profile-shift {
  .filter-item {
    width: 180px;
  }
}
</style>
