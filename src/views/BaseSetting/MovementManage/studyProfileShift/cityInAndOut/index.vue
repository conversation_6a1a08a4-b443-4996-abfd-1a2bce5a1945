<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { apis } from "@/apis";
import { useTableCommons } from "@/commons/TableCommons";
import type { TabsPaneContext } from 'element-plus'
const activeName = ref('first')

const handleClick = (tab: TabsPaneContext, event: Event) => {
  console.log(tab, event)
}
// import { useUserStore } from '@/store';
import { DialogMode } from "@/commons/DialogModel";
// import { ElMessageBox } from "element-plus";
// import Notification from "@/commons/Notification";
import AddForm from "@/views/BaseSetting/MovementManage/studyProfileShift/components/AddShift.vue";
import EditForm from "@/views/BaseSetting/MovementManage/studyProfileShift/components/AddShift.vue";
import DetailForm from "@/views/BaseSetting/MovementManage/studyProfileShift/components/ShiftDetail.vue"
import { shiftTpList } from '@/views/BaseSetting/MovementManage/studyProfileShift/dic'

// 主列表搜索条件
const search = ref({
  pageNumber: 1,
  pageSize: 10,
  keywords: '',
  period: '',
  grade: '',
  masterOrSportTeacher: '',
  className: '',
  regionId: ''
})
// store
// const userStore = useUserStore()

// 主列表
const getTableData = async () => {
  const response = await apis.ClazzManage.getPageData(search.value, search.value.regionId)
  return response.data
}

//
let {
  loading,
  dialogModel,
  showFormDialog,
  tableData,
  searchSubmit,
  pageOrSizeChange,
} = useTableCommons(search, getTableData)

// 生命周期
onMounted(() => {

})

//
const addForm = ref()
const editForm = ref()
const saveSubmit = async () => {
  dialogModel.value.saveLoading = true
  if (dialogModel.value.formName === 'AddForm') {
    addForm.value.saveSubmit()
  } else if (dialogModel.value.formName === 'EditForm') {
    editForm.value.saveSubmit()
  }
}
</script>

<template>
  <div class="study-profile-shift">
    <div class="lmt-operation-container">
      <div class="lmt-operation">
        <el-button v-has-perm="'clazz:add'" icon="Plus" type="primary"
                   @click="showFormDialog('添加学籍异动','AddForm',{  },DialogMode.ADD,'600px')">添加</el-button>
      </div>
      <div class="lmt-search">
        <el-select v-model="search.regionId"
                   placeholder="请选择异动类型" class="filter-item" :clearable="true">
          <el-option v-for="item in shiftTpList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <el-date-picker v-model="search.period" type="date" placeholder="申请日期" value-format="YYYY-MM-DD" size="default"   style="width: 180px;" :clearable="true" />
        <el-input v-model="search.keywords" placeholder="学生姓名" class="filter-item" :clearable="true"></el-input>
        <el-input v-model="search.className" placeholder="学籍号" class="filter-item" :clearable="true" @clear="searchSubmit">
          <template #append>
            <el-button icon="Search" @click="searchSubmit"></el-button>
          </template>
        </el-input>
      </div>
    </div>
    <el-tabs
      v-model="activeName"
      type="card"
      class="demo-tabs"
      @tab-click="handleClick"
    >
      <el-tab-pane label="转入学生" name="first">
        <el-table v-loading="loading" :data="tableData.records" border max-height="calc(100vh - var(--lmt-header-height) - var(--lmt-main-padding) * 2 - 6rem)"
                  stripe>
          <el-table-column align="center" label="序号" type="index" width="80">
            <template #default="scope">
              {{ (search.pageNumber - 1) * search.pageSize + scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column label="申请ID" width="100" prop="name"></el-table-column>
          <el-table-column label="学生姓名" min-width="120" prop="code" show-overflow-tooltip></el-table-column>
          <el-table-column label="学籍号" min-width="120" prop="code" show-overflow-tooltip></el-table-column>
          <el-table-column label="性别" min-width="120" prop="code" show-overflow-tooltip></el-table-column>
          <el-table-column label="当前年级" min-width="120" prop="code" show-overflow-tooltip></el-table-column>
          <el-table-column label="当前班级" min-width="120" prop="code" show-overflow-tooltip></el-table-column>
          <el-table-column label="异动类型" min-width="120" prop="code" show-overflow-tooltip></el-table-column>
          <el-table-column label="申请日期" min-width="120" prop="code" show-overflow-tooltip></el-table-column>
          <el-table-column label="预计结束日期" min-width="120" prop="code" show-overflow-tooltip></el-table-column>
          <el-table-column label="操作" min-width="120" fixed="right">
            <template #default="scope">
              <el-link icon="View" type="primary"
                       @click="showFormDialog('详情','DetailForm', scope.row, DialogMode.DETAIL,'600px')">详情
              </el-link>
              <!--          <el-link v-has-perm="'clazz:edit'" icon="Edit" type="warning"
                                 @click="showFormDialog('修改','EditForm',scope.row,DialogMode.EDIT,'600px')">修改
                        </el-link>-->
            </template>
          </el-table-column>
        </el-table>
        <el-pagination v-model:current-page="search.pageNumber" v-model:page-size="search.pageSize"
                       :page-sizes="[10, 20, 30, 40, 50, 100]" :total="Number(tableData.total)"
                       background
                       class="m-t-4 float-right" layout="total, prev, pager, next, sizes, ->" @change="pageOrSizeChange">
        </el-pagination>
      </el-tab-pane>
      <el-tab-pane label="转出学生" name="second">
        <el-table v-loading="loading" :data="tableData.records" border max-height="calc(100vh - var(--lmt-header-height) - var(--lmt-main-padding) * 2 - 6rem)"
                  stripe>
          <el-table-column align="center" label="序号" type="index" width="80">
            <template #default="scope">
              {{ (search.pageNumber - 1) * search.pageSize + scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column label="申请ID" width="100" prop="adjustId"></el-table-column>
          <el-table-column label="学生姓名" min-width="120" prop="studentName" show-overflow-tooltip></el-table-column>
          <el-table-column label="身份证号" min-width="120" prop="sfzh" show-overflow-tooltip></el-table-column>
          <el-table-column label="学籍号" min-width="120" prop="code" show-overflow-tooltip></el-table-column>
          <el-table-column label="性别" min-width="120" prop="xb" show-overflow-tooltip>
            <template #default="scope">
              {{scope.row.xb==1?'男':'女'}}
            </template>
          </el-table-column>
          <el-table-column label="当前年级" min-width="120" prop="gradeName" show-overflow-tooltip></el-table-column>
          <el-table-column label="当前班级" min-width="120" prop="className" show-overflow-tooltip></el-table-column>
          <el-table-column label="异动类型" min-width="120" prop="adjustType" show-overflow-tooltip></el-table-column>
          <el-table-column label="申请日期" min-width="120" prop="applyDate" show-overflow-tooltip></el-table-column>
          <el-table-column label="市外转出原因" min-width="120" prop="outReason" show-overflow-tooltip></el-table-column>
          <el-table-column label="操作用户名" min-width="120" prop="applyUserName" show-overflow-tooltip></el-table-column>
          <el-table-column label="操作" min-width="120" fixed="right">
            <template #default="scope">
              <el-link icon="View" type="primary"
                       @click="showFormDialog('详情','DetailForm', scope.row, DialogMode.DETAIL,'600px')">详情
              </el-link>
              <!--          <el-link v-has-perm="'clazz:edit'" icon="Edit" type="warning"
                                 @click="showFormDialog('修改','EditForm',scope.row,DialogMode.EDIT,'600px')">修改
                        </el-link>-->
            </template>
          </el-table-column>
        </el-table>
        <el-pagination v-model:current-page="search.pageNumber" v-model:page-size="search.pageSize"
                       :page-sizes="[10, 20, 30, 40, 50, 100]" :total="Number(tableData.total)"
                       background
                       class="m-t-4 float-right" layout="total, prev, pager, next, sizes, ->" @change="pageOrSizeChange">
        </el-pagination>
      </el-tab-pane>
      </el-tabs>

    <el-dialog v-model="dialogModel.visible" :close-on-click-modal="false" :title="dialogModel.title" :width="dialogModel.width"
               destroy-on-close @closed="dialogModel.closed()">
      <AddForm v-if="dialogModel.formName == 'AddForm'" ref="addForm" :mode="dialogModel.mode"
               :row-data="{  }" @save-complete="dialogModel.saveComplete"/>
      <EditForm v-if="dialogModel.formName == 'EditForm'" ref="editForm" :mode="dialogModel.mode"
                :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <DetailForm v-if="dialogModel.formName == 'DetailForm'" ref="detailForm" :mode="dialogModel.mode"
                  :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <template #footer>
        <el-button @click="dialogModel.closed()">
          {{ dialogModel.formName !== 'DetailForm' ? '取 消' : '关 闭' }}
        </el-button>
        <el-button v-if="dialogModel.formName!=='DetailForm'" :loading="dialogModel.saveLoading" type="primary"
                   @click="saveSubmit">确 认
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.study-profile-shift {
  .filter-item {
    width: 180px;
  }
}
</style>
