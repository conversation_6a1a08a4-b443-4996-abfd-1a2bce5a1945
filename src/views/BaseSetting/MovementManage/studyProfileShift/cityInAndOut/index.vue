<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { apis } from "@/apis";
import { useTableCommons } from "@/commons/TableCommons";
import type { TabsPaneContext } from 'element-plus'
const activeName = ref('first')

const handleClick = (tab: TabsPaneContext, event: Event) => {
  console.log(tab, event)
}
// import { useUserStore } from '@/store';
import { DialogMode } from "@/commons/DialogModel";
// import { ElMessageBox } from "element-plus";
// import Notification from "@/commons/Notification";
import AddForm from "@/views/BaseSetting/MovementManage/studyProfileShift/components/AddShift.vue";
import EditForm from "@/views/BaseSetting/MovementManage/studyProfileShift/components/AddShift.vue";
import DetailForm from "@/views/BaseSetting/MovementManage/studyProfileShift/components/ShiftDetail.vue"
import { shiftTpList } from '@/views/BaseSetting/MovementManage/studyProfileShift/dic'
import zhuanRuList from "@/views/BaseSetting/MovementManage/studyProfileShift/cityInAndOut/components/zhuanRuList.vue"
import zhuanChuList from "@/views/BaseSetting/MovementManage/studyProfileShift/cityInAndOut/components/zhuanChuList.vue"



//


// 生命周期
onMounted(() => {

})

</script>

<template>
  <div class="study-profile-shift">

    <el-tabs
      v-model="activeName"
      type="card"
      class="demo-tabs"
      @tab-click="handleClick"
    >
      <el-tab-pane label="转入学生" name="first">
        <zhuan-ru-list v-if="activeName=='first'"></zhuan-ru-list>

      </el-tab-pane>
      <el-tab-pane label="转出学生" name="second">
          <zhuan-chu-list v-if="activeName=='second'"></zhuan-chu-list>
      </el-tab-pane>
      </el-tabs>
  </div>
</template>

<style lang="scss" scoped>
.study-profile-shift {
  .filter-item {
    width: 180px;
  }
}
</style>
