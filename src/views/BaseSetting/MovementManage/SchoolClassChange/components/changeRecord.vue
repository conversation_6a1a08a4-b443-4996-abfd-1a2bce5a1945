
<script lang="ts" setup>
import {onMounted,ref} from "vue";
import {apis} from "@/apis";
import {useTableCommons} from "@/commons/TableCommons";
import {useDeptStore,useUserStore} from "@/store";
import {DialogMode} from "@/commons/DialogModel";
const userStore = useUserStore()
let currentRegionId = userStore.regionId || '130102'
const deptStore = useDeptStore()

if (deptStore.topDept?.id == deptStore.currentDept?.id) {
  currentRegionId = '130102'
}
const search = ref({
  pageNumber: 1,
  pageSize: 10,
  keywords: '',
  period: '',
  grade: '',
  masterOrSportTeacher: '',
  className: '',
  regionId: currentRegionId,
  schoolId: ''
})
const getTableData = async () => {

  console.log('search.value', search.value)
  const response = await apis.ClazzManage.getPageData(search.value, search.value.regionId)
  return response.data
}
let {
  loading,
  dialogModel,
  showFormDialog,
  tableData,
  searchSubmit,
  pageOrSizeChange,
} = useTableCommons(search, getTableData)
onMounted(async()=>{
  await searchSubmit()
})
</script>
<template>
  <div>
    <el-table
      v-loading="loading"
      :data="tableData.records"
      style="width: 100%"
    >
      <el-table-column
        width="80"
        label="序号"
        type="index"
        align="center"
      >
        <template #default="scope">
          {{ (search.pageNumber - 1) * search.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        prop="studentName"
        label="原班级"
        align="center"
        min-width="130px"
      >
      </el-table-column>
      <el-table-column
        prop="address"
        label="调入班级"
        align="center"
        min-width="180px"
      >
      </el-table-column>
      <el-table-column
        prop="address"
        label="调班时间"
        align="center"
        min-width="180px"
      >
      </el-table-column>

    </el-table>
  </div>
</template>
<style scoped>

</style>
