
<script lang="ts" setup>
import {onMounted,ref} from "vue";
import {apis} from "@/apis";
import {useTableCommons} from "@/commons/TableCommons";
import {useDeptStore,useUserStore} from "@/store";
import {DialogMode} from "@/commons/DialogModel";
const userStore = useUserStore()
let currentRegionId = userStore.regionId || '130102'
const deptStore = useDeptStore()
const props = defineProps({
  rowData: {
    type: [Object, null],
    default: () => {
    }
  },
  mode: {
    type: [String, null],
    default: 'DETAIL'
  }
})
if (deptStore.topDept?.id == deptStore.currentDept?.id) {
  currentRegionId = '130102'
}
const search = ref({
  pageNumber: 1,
  pageSize: 9999,
  keywords: '',
  period: '',
  grade: '',
  masterOrSportTeacher: '',
  className: '',
  studentId:'',
  regionId: currentRegionId,
  schoolId: ''
})
const formData = ref({
  id: '',
  userId: '',
  xm: '',
  userName: '',
  regionId: '',
  regionName: '',
  schoolId: '',
  sfzh: '',
  xjh: '',
  period: '',
  sjhm: '',
  xb: '',
  gradeId: '',
  gradeName: '',
  clazzId: '',
  clazzName: '',
  jtzz: '',
  schoolName: '',
  status: 1,
  zhzt:'',
  studentStatus: '',
  defaultPasswordFlag: false
})

const getTableData = async () => {
  console.log('search.value', search.value)
  const response = await apis.ClassAdjustManage.getAdjustList({key: formData.value.id}, search.value.regionId)
  console.log('response', response)
  return response
}
let {
  loading,
  dialogModel,
  showFormDialog,
  tableData,
  searchSubmit,
  pageOrSizeChange,
} = useTableCommons(search, getTableData)
onMounted(async()=>{
  if (props.mode === DialogMode.DETAIL) {
    const {
      id,
      userId,
      xm,
      userName,
      regionId,
      regionName,
      schoolId,
      sfzh,
      xjh,
      period,
      sjhm,
      xb,
      gradeId,
      gradeName,
      clazzId,
      clazzName,
      jtzz,
      schoolName,
      status,
      zhzt,
      studentStatus,
      defaultPasswordFlag
    } = props.rowData as any
    formData.value = {id,
      userId,
      xm,
      userName,
      regionId,
      regionName,
      schoolId,
      sfzh,
      xjh,
      period,
      sjhm,
      xb,
      gradeId,
      gradeName,
      clazzId,
      clazzName,
      jtzz,
      schoolName,
      status,
      zhzt,
      studentStatus,
      defaultPasswordFlag}
    console.log(formData.value, 'formData.value')
  }

  await searchSubmit()
})
</script>
<template>
  <div>

    <el-table
      v-loading="loading"
      :data="tableData.data"
      style="width: 100%"
    >
      <el-table-column
        width="80"
        label="序号"
        type="index"
        align="center"
      >
        <template #default="scope">
          {{ (search.pageNumber - 1) * search.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        prop="originalClazzName"
        label="原班级"
        align="center"
        min-width="130px"
      >
      </el-table-column>
      <el-table-column
        prop="clazzName"
        label="调入班级"
        align="center"
        min-width="180px"
      >
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="调班时间"
        align="center"
        min-width="180px"
      >
      </el-table-column>

    </el-table>
  </div>
</template>
<style scoped>

</style>
