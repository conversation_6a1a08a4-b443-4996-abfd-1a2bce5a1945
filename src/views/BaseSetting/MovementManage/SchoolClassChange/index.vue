<script lang="ts" setup>
import {onMounted, ref} from "vue";
import {apis} from "@/apis";
import {useTableCommons} from "@/commons/TableCommons";
import {useDeptStore, useUserStore} from "@/store";
import changeRecord from "./components/changeRecord.vue"
import schoolClassImport from "./components/schoolClassImport.vue"
import {DialogMode} from "@/commons/DialogModel";
import {ElNotification} from "element-plus";
import type {KeyValue} from "@/commons/KeyValue";

const userStore = useUserStore()
let currentRegionId = userStore.regionId || '130102'
let currentSchoolId = userStore.userInfo?.schoolId
const deptStore = useDeptStore()

if (deptStore.topDept?.id == deptStore.currentDept?.id) {
  currentRegionId = '130102'
}

const search = ref({
  pageNumber: 1,
  pageSize: 10,
  keywords: '',
  period: '',
  gradeId: '',
  isClassAdjust:'',
  masterOrSportTeacher: '',
  clazzId: '',
  regionId: currentRegionId,
  schoolId: currentSchoolId
})
const clazzAdjustSearch = ref({
  clazzName:'',
  clazzId:'',
  schoolId: currentSchoolId,
  studentIds:ref<any[]>([])
})
const gradeList = ref<any>(useDeptStore().gradeList)
const getTableData = async () => {

  const response = await apis.ClassAdjustManage.getPageData(search.value, search.value.regionId)


  return response.data
}
let {
  loading,
  dialogModel,
  showFormDialog,
  tableData,
  searchSubmit,
  pageOrSizeChange,
} = useTableCommons(search, getTableData)
let clazzList=ref<Array<KeyValue>>([])
let clazzForm=ref<Array<KeyValue>>([])
let tiaoBanShow=ref(false)
const rules={
  clazzId: [{required: true, message: '请选择班级', trigger: 'change'}],
}
onMounted(async()=>{
  await searchSubmit()
  clazzList.value = []
  clazzList.value = (await apis.Commons.getClazzSelectList(search.value.schoolId, search.value.gradeId || '', search.value.period || '', search.value.regionId)).data;
  if (gradeList.value.length == 0) {
    await useDeptStore().setGradeList()
    gradeList.value = useDeptStore().gradeList
  }
})
const selectChange=(e)=> {
  console.log(e,"e")

}
const piLiangClass=async ()=>{
  clazzAdjustSearch.value.clazzName = ''
  clazzAdjustSearch.value.clazzId = ''
  clazzAdjustSearch.value.studentIds = [] // 清空之前的IDs
  clazzAdjustSearch.value.studentIds = multipleSelection.value.map((item: any) => item.studentId);
  const clazzList = await apis.ClassAdjustManage.getClazzAdjustList(clazzAdjustSearch.value, search.value.regionId)
  clazzForm.value=clazzList.data
  tiaoBanShow.value=true
}
const tiaoClazz=async (id)=>{
  clazzAdjustSearch.value.clazzName = ''
  clazzAdjustSearch.value.clazzId = ''
  clazzAdjustSearch.value.studentIds = [] // 清空之前的IDs
  clazzAdjustSearch.value.studentIds.push(id)
  const clazzList = await apis.ClassAdjustManage.getClazzAdjustList(clazzAdjustSearch.value, search.value.regionId)
  clazzForm.value=clazzList.data
  tiaoBanShow.value=true
}
const multipleSelection = ref([])
const handleSelectionChange = (selection) => {
  multipleSelection.value = selection
}

const saveSubmit = async () => {
  // 根据选中的班级ID找到对应的班级名称
  const selectedClazz = clazzForm.value.find(
    (item: any) => item.key === clazzAdjustSearch.value.clazzId
  );
  if (selectedClazz) {
    clazzAdjustSearch.value.clazzName = selectedClazz.value.valueOf();
  }

  const response = await apis.ClassAdjustManage.update(
    clazzAdjustSearch.value,
    search.value.regionId
  );

  if (response.data) {
    tiaoBanShow.value = false;
    ElNotification.success({message: "操作成功", title: "温馨提示"});
    await searchSubmit(); // 刷新列表
  }
};
</script>
<template>
  <dvi>
    <div class="lmt-operation-container">
      <div class="lmt-operation">
        <el-button type="primary" icon="upload" @click="showFormDialog('导入','ImportForm',{},DialogMode.IMPORT,'1000px')">导入</el-button>
        <el-button type="primary" @click="piLiangClass">批量调班</el-button>
      </div>
      <div class="lmt-search">
        <el-select v-model="search.gradeId" clearable placeholder="请选择年级" style="width: 150px">
          <el-option v-for="item in gradeList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <el-select v-model="search.clazzId" clearable placeholder="请选择班级" style="width: 150px">
          <el-option v-for="item in clazzList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <el-select v-model="search.isClassAdjust" clearable placeholder="是否有调班记录" style="width: 150px">
          <el-option label="是" :value="1"></el-option>
          <el-option label="否" :value="0"></el-option>
        </el-select>
        <el-input v-model="search.keywords" clearable placeholder="学生姓名/学籍号" @clear="searchSubmit">
          <template #append>
            <el-button icon="Search" @click="searchSubmit"></el-button>
          </template>
        </el-input>
      </div>
    </div>
    <el-table
     v-loading="loading"
     :data="tableData.records"
      style="width: 100%"
     @selection-change="handleSelectionChange"
     border max-height="calc(100vh - var(--lmt-header-height) - var(--lmt-main-padding) * 2 - 6rem)"
     stripe
    >
      <el-table-column type="selection" width="55" />
      <el-table-column
        width="80"
        label="序号"
        type="index"
        align="center"
      >
        <template #default="scope">
          {{ (search.pageNumber - 1) * search.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        prop="xm"
        label="学生姓名"
        align="center"
        min-width="130px"
      >
      </el-table-column>
      <el-table-column
        prop="xjh"
        label="学籍号"
        align="center"
        min-width="180px"
      >
      </el-table-column>
      <el-table-column
        prop="xb"
        label="性别"
        align="center"
        min-width="180px"
      >
        <template #default="scope">
          {{ scope.row.xb == '1' ? '男' : scope.row.xb == '2' ? '女' : '未知' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="clazzName"
        label="所在班级"
        align="center"
      >
      </el-table-column>
      <el-table-column
        prop="isClassAdjust"
        label="是否有调班记录"
        align="center"
        min-width="180px"
      >
        <template #default="scope">
          {{ scope.row.isClassAdjust == '1' ? '有' : '无' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="adjustDate"
        label="最近调班日期"
        align="center"
        min-width="180px"
      >
      </el-table-column>
      <el-table-column
        prop="operator"
        label="操作人"
        align="center"
        min-width="180px"
      >
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="操作时间"
        align="center"
        min-width="180px"
      >
      </el-table-column>
      <el-table-column label="操作" min-width="180px" align="center">
        <template #default="scope">
          <el-link icow="edit" type="primary" @click="tiaoClazz(scope.row.studentId)">
            调班
          </el-link>
          <el-link icow="View" type="primary"   @click="showFormDialog('调班记录','DetailForm',{...scope.row, id: scope.row.studentId},DialogMode.DETAIL,'800px')">
            调班记录
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-model:current-page="search.pageNumber" v-model:page-size="search.pageSize"
                   :page-sizes="[10, 20, 30, 40, 50, 100]" :total="Number(tableData.total)"
                   background
                   class="m-t-4 float-right" layout="total, prev, pager, next, sizes, ->" @change="pageOrSizeChange">
    </el-pagination>
    <el-dialog v-model="dialogModel.visible" :close-on-click-modal="false" :title="dialogModel.title" :width="dialogModel.width"
               destroy-on-close
               draggable @closed="dialogModel.closed()">
      <school-class-import v-if="dialogModel.formName == 'ImportForm'" ref="importForm" :mode="dialogModel.mode"
                           :regionId="currentRegionId" @save-complete="dialogModel.saveComplete"  @close-detail="dialogModel.visible = false"></school-class-import>
      <changeRecord v-if="dialogModel.formName == 'DetailForm'" ref="detailForm" :mode="dialogModel.mode"
                    :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete" ></changeRecord>
    </el-dialog>
    <el-dialog v-model="tiaoBanShow" :close-on-click-modal="false" title="校内调班" :width="dialogModel.width"
               destroy-on-close
               draggable @closed="dialogModel.closed()">
      <el-form
        ref="form"
        :model="clazzAdjustSearch"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="调入班级" prop="clazzId">
          <el-select
            v-model="clazzAdjustSearch.clazzId"
            placeholder="请选择调入班级"
          >
            <el-option
              v-for="item in clazzForm"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-button type="primary" @click="saveSubmit">保存</el-button>
      </el-form>
    </el-dialog>
  </dvi>

</template>
<style>

</style>
