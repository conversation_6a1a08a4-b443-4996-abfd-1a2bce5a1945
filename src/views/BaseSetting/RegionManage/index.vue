<script lang="ts" setup>
import {onMounted, ref} from "vue";
import {apis} from "@/apis";
import {useTableCommons} from "@/commons/TableCommons";
import {DialogMode} from "@/commons/DialogModel";
import EditForm from "./components/RegionForm.vue";
import AddMasterForm from "./components/CountyMasterForm.vue";
import EditMasterForm from "./components/CountyMasterForm.vue";
import {ElMessageBox} from "element-plus";
import Notification from "@/commons/Notification";

const search = ref({
  pageNumber: 1,
  pageSize: 100,
  keywords: ''
})

const getTableData = async () => {
  const response = await apis.RegionManage.getPageData(search.value)
  return response.data
}

let {
  loading,
  dialogModel,
  showFormDialog,
  tableData,
  searchSubmit
} = useTableCommons(search, getTableData)

onMounted(async () => {
  await searchSubmit()
})
const editForm = ref()
const editMasterForm = ref()
const addMasterForm = ref()

const saveSubmit = async () => {
  dialogModel.value.saveLoading = true
  if (dialogModel.value.formName === 'EditForm') {
    editForm.value.saveSubmit()
  } else if (dialogModel.value.formName === 'EditMasterForm') {
    editMasterForm.value.saveSubmit()
  } else if (dialogModel.value.formName === 'AddMasterForm') {
    addMasterForm.value.saveSubmit()
  }
}

const showDeleteMasterDialog = async (item: any, status: number) => {
  ElMessageBox.confirm(`您确定要${status == 1 ? '启用' : '禁用'}账户【${item.nickname}】吗？`, "温馨提示", {type: "warning"})
    .then(async () => {
      await apis.UserManage.changeUserStatus(item.id, status)
      Notification.showSuccessNotify("操作成功")
      await searchSubmit()
    })
}
const showResetPasswordDialog = async (item: any) => {
  ElMessageBox.confirm(`您确定重置【${item.nickname}】的密码吗？`, "温馨提示", {type: "warning"})
    .then(async () => {
      await apis.UserManage.resetPassword(item.id)
      Notification.showSuccessNotify("操作成功")
    })
}

</script>

<template>
  <div>
    <div class="lmt-operation-container">
      <div class="lmt-operation">
        <el-button v-has-perm="['region:export:account']" icon="Download" type="info"
                   @click="apis.RegionManage.exportAllRegionAccounts(search)">导出所有区县账号
        </el-button>
      </div>
      <div class="lmt-search">
        <el-input v-model="search.keywords" clearable placeholder="请输入区县名称" @clear="searchSubmit">
          <template #append>
            <el-button icon="Search" @click="searchSubmit"></el-button>
          </template>
        </el-input>
      </div>
    </div>
    <el-table v-loading="loading" :data="tableData.records" border
              max-height="calc(100vh - var(--lmt-header-height) - var(--lmt-main-padding) * 2 - 3rem)"
              stripe>
      <!--行号-->
      <el-table-column align="center" label="序号" type="index" width="80">
        <template #default="scope">
          {{ (search.pageNumber - 1) * search.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="区县名称" min-width="100" prop="name" show-overflow-tooltip></el-table-column>
      <el-table-column label="区县代码" min-width="100" prop="code" show-overflow-tooltip></el-table-column>
      <el-table-column label="负责人" min-width="100" prop="contact" show-overflow-tooltip></el-table-column>
      <el-table-column label="职务" min-width="100" prop="position" show-overflow-tooltip></el-table-column>
      <el-table-column label="座机电话" min-width="120" prop="telephone" show-overflow-tooltip></el-table-column>
      <el-table-column label="手机号码" min-width="120" prop="mobile" show-overflow-tooltip></el-table-column>
      <el-table-column label="区县管理员" min-width="300" prop="masters" show-overflow-tooltip>
        <template #default="{row}">
          <ElSpace wrap>
            <el-text v-for="(item, index) in row.masters" v-if="row.masters&&row.masters.length" :key="index"
                     type="info">
              {{ item.nickname }}({{ item.username }})
              <el-link v-has-perm="'user:edit'" icon="Edit" type="warning"
                       @click="showFormDialog('修改管理员','EditMasterForm',item,DialogMode.EDIT)"></el-link>
              <el-link v-has-perm="'user:change:status'" :icon="item.status==1?'CircleClose':'CircleCheck'"
                       :type="item.status==1?'danger':'success'"
                       @click="showDeleteMasterDialog(item,item.status==1?0:1)"></el-link>
              <el-link v-has-perm="'user:reset:password'" icon="Refresh" type="warning"
                       @click="showResetPasswordDialog(item)"></el-link>
              <el-link v-if="index == row.masters.length - 1" v-has-perm="'user:add'" icon="Plus" type="primary"
                       @click="showFormDialog('新增管理员','AddMasterForm',row,DialogMode.ADD)"></el-link>
            </el-text>
            <el-text v-else>
              <el-link v-has-perm="'user:add'" icon="Plus" type="primary"
                       @click="showFormDialog('新增管理员','AddMasterForm',row,DialogMode.ADD)"></el-link>
            </el-text>
          </ElSpace>
        </template>
      </el-table-column>
      <el-table-column v-has-perm="['region:edit','region:export:account']" fixed="right" label="操作" min-width="150">
        <template #default="scope">
          <el-link v-has-perm="['region:edit']" icon="Edit" type="warning"
                   @click="showFormDialog('修改','EditForm',scope.row,DialogMode.EDIT)">修改
          </el-link>
          <el-link v-has-perm="['region:export:account']" icon="Download" type="info"
                   @click="apis.RegionManage.downloadAccounts(scope.row.id)">导出账号
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog v-model="dialogModel.visible" :close-on-click-modal="false" :title="dialogModel.title"
               :width="dialogModel.width"
               destroy-on-close @closed="dialogModel.closed">
      <EditForm v-if="dialogModel.formName == 'EditForm'" ref="editForm" :mode="dialogModel.mode"
                :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <AddMasterForm v-if="dialogModel.formName == 'AddMasterForm'" ref="addMasterForm"
                     :mode="dialogModel.mode"
                     :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <EditMasterForm v-if="dialogModel.formName == 'EditMasterForm'" ref="editMasterForm"
                      :mode="dialogModel.mode"
                      :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <template #footer>
        <el-button @click="dialogModel.visible=false">
          {{ dialogModel.formName !== 'DetailForm' ? '取 消' : '关 闭' }}
        </el-button>
        <el-button v-if="dialogModel.formName!=='DetailForm'" :loading="dialogModel.saveLoading" type="primary"
                   @click="saveSubmit">确 认
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>

</style>
