<script lang="ts" setup>
import {onMounted, ref} from 'vue'
import type {FormInstance} from "element-plus";
import {apis} from "@/apis";
import Notification from "@/commons/Notification";
import {validatorMobile} from "@/commons/Validator";

const props = defineProps({
  rowData: {
    type: [Object, null],
    default: () => ({})
  },
  mode: {
    type: [String, null],
    default: "EDIT"
  }
})

const formData = ref({
  name: '',
  code: '',
  id: '',
  contact: '',
  position: '',
  telephone: '',
  mobile: ''
})
const rules = ref({
  name: [{required: true, message: '请输入名称', trigger: 'blur'}],
  code: [{required: true, message: '请输入编码', trigger: 'blur'}],
  mobile: [{validator: validatorMobile, trigger: 'blur'}]
})

onMounted(async () => {
  const {id, name, code, contact, position, telephone, mobile} = props.rowData as any
  formData.value = {id, name, code, contact, position, telephone, mobile}
})

const emit = defineEmits(['save-complete'])

const saveCompleteEmit = (status: number) => emit('save-complete', status)

const form = ref()
const saveSubmit = () => {
  (form.value as FormInstance).validate((valid) => {
    if (valid) {
      apis.RegionManage.update(formData.value).then(() => {
        saveCompleteEmit(1)
        Notification.showSuccessNotify('操作成功')
      }).catch(() => saveCompleteEmit(0))
    } else {
      saveCompleteEmit(0)
    }
  })
}

defineExpose({
  saveSubmit
})


</script>

<template>
  <div class="form-container">
    <el-form ref="form" :model="formData" :rules="rules" label-width="auto">
      <el-form-item label="区县名称" prop="name">
        <el-input v-model="formData.name" maxlength="32" placeholder="请输入名称" show-word-limit></el-input>
      </el-form-item>
      <el-form-item label="区县编码" prop="code">
        <el-input v-model="formData.code" maxlength="32" placeholder="请输入编码" show-word-limit></el-input>
      </el-form-item>
      <el-form-item label="联系人" prop="contact">
        <el-input v-model="formData.contact" maxlength="32" placeholder="请输入联系人" show-word-limit></el-input>
      </el-form-item>
      <el-form-item label="职位" prop="position">
        <el-input v-model="formData.position" maxlength="32" placeholder="请输入职位" show-word-limit></el-input>
      </el-form-item>
      <el-form-item label="座机电话" prop="telephone">
        <el-input v-model="formData.telephone" maxlength="32" placeholder="请输入电话" show-word-limit></el-input>
      </el-form-item>
      <el-form-item label="手机号码" prop="mobile">
        <el-input v-model="formData.mobile" maxlength="11" placeholder="请输入手机" show-word-limit></el-input>
      </el-form-item>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>

</style>
