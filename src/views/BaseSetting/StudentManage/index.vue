<script lang="ts" setup>
import {onMounted, ref, watch} from "vue";
import {apis} from "@/apis";
import {useTableCommons} from "@/commons/TableCommons";
import {useDeptStore, useUserStore} from '@/store'
import {DialogMode} from "@/commons/DialogModel";
import SchoolPeriodSelector from "@/components/SchoolPeriodSelector/index.vue"
import GradeSelector from "@/components/GradeSelector/index.vue"
import {ElMessageBox, ElNotification} from "element-plus";
import AddForm from "./components/StudentForm.vue";
import EditForm from "./components/StudentForm.vue";
import DetailForm from "./components/StudentDetail.vue";
import ImportForm from "./components/StudentImport.vue";
import type { KeyValue } from "@/commons/KeyValue";


const userStore = useUserStore()
let currentRegionId = userStore.regionId || '130102'
const deptStore = useDeptStore()

if (deptStore.topDept?.id == deptStore.currentDept?.id) {
  currentRegionId = '130102'
}

const search = ref({
  pageNumber: 1,
  pageSize: 10,
  keywords: "",
  regionId: currentRegionId,
  schoolId: "",
  period: "",
  gradeId: "",
  clazzId: "",
  studentStatus: ""
})

const gradeList = ref<Array<KeyValue>>([])

const studentStatusList = ref([
  {key: 1, value: '在读'},
  {key: 2, value: '毕业'},
  {key: 3, value: '休学'},
])

const getTableData = async () => {
  const response = await apis.StudentManage.getPageData(search.value, search.value.regionId)
  for (let i = 0; i < response.data.records.length; i++) {
    const item = response.data.records[i];
    item.defaultPasswordView = "********";
    item.showPasswordView = true;
  }
  return response.data
}

const changePasswordView = (row:any)=> {
  if (row.showPasswordView) {
    row.defaultPasswordView = row.defaultPassword;
  } else {
    row.defaultPasswordView = "********";
  }
  row.showPasswordView = !row.showPasswordView;
}

const regionList = ref<any>(useDeptStore().regionList)
const schoolList = ref<Array<KeyValue>>([])
const clazzList = ref<Array<KeyValue>>([])

let {
  loading,
  dialogModel,
  showFormDialog,
  tableData,
  searchSubmit,
  pageOrSizeChange
} = useTableCommons(search, getTableData)

onMounted(async () => {
  if (userStore.userInfo?.cityAdminFlag) {
    await useDeptStore().setRegionList();
    regionList.value = useDeptStore().regionList;
  } else if (userStore.userInfo?.countyAdminFlag) {
    regionList.value = [{key: currentRegionId, value: userStore.userInfo?.regionName}]
  } else if (userStore.userInfo?.schoolAdminFlag) {
    search.value.schoolId = userStore.userInfo?.schoolId
  }
  gradeList.value = (await apis.Commons.getGradeSelectList()).data;
  await regionChange()
  await searchSubmit()
})
watch(() => search.value.regionId,
  (newValues, oldValues) => {
    if (newValues != oldValues) {
      currentRegionId = newValues
      regionChange();
    }
  });

const regionChange = async () => {
  if(userStore.userInfo?.cityAdminFlag || userStore.userInfo?.countyAdminFlag) {
    search.value.schoolId = ''
    schoolList.value = (await apis.Commons.getSchoolSelectListCounty(search.value.regionId)).data;
  }
}

// 获取班级列表
const getClazzList = async () => {
  search.value.clazzId = ''
  clazzList.value = []
  if(search.value.schoolId && search.value.gradeId && search.value.period) {
    clazzList.value = (await apis.Commons.getClazzSelectList(search.value.schoolId, search.value.gradeId, search.value.period, search.value.regionId)).data;
  }
}

// 表单部分
const addForm = ref()
const editForm = ref()
const importForm = ref()
const saveSubmit = async () => {
  dialogModel.value.saveLoading = true
  if (dialogModel.value.formName === 'AddForm') {
    addForm.value.saveSubmit()
  } else if (dialogModel.value.formName === 'EditForm') {
    editForm.value.saveSubmit()
  } else if (dialogModel.value.formName === 'ImportForm') {
    importForm.value.saveSubmit()
  }
}

const deleteStudent = async (row: any) => {
  ElMessageBox.confirm(`您确定删除姓名【${row.xm}】的学生吗？`, "温馨提示",
    {confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'})
    .then(async () => {
      await apis.TeacherManage.delete(row.id,currentRegionId)

      ElNotification.success({message: "操作成功", title: "温馨提示"})
      await searchSubmit()
    })

}
const resetPassword = async (row: any) => {
  ElMessageBox.confirm(`您确定重置账户【${row.xm}-${row.userName}】的密码吗？`, "温馨提示",
    {type: "warning"})
    .then(async () => {
      await apis.UserManage.resetPassword(row.userId)
      ElNotification.success({message: "操作成功", title: "温馨提示"})
      await searchSubmit()
    })
}
const changeUserStatus = async (row: any, status: number) => {
  ElMessageBox.confirm(`您确定${status == 1 ? "解冻" : "冻结"}账户【${row.xm}-${row.userName}】吗？`, "温馨提示",
    {type: "warning"})
    .then(async () => {
      await apis.UserManage.changeUserStatus(row.userId, status)
      ElNotification.success({message: "操作成功", title: "温馨提示"})
      await searchSubmit()
    })

}
const toEdit = (id:any,regionId:any) => {
  dialogModel.value.visible = true
  showFormDialog('修改','EditForm',{id:id,regionId:regionId},DialogMode.EDIT,'1000px')

}
const closeDetail = () => {
  dialogModel.value.visible = false
}
</script>

<template>
  <div>
    <div class="lmt-operation-container">
      <div class="lmt-operation">
        <el-button v-has-perm="['student:add']" icon="Plus" type="primary"
          @click="showFormDialog('新增','AddForm',{regionId: currentRegionId, schoolId:userStore.userInfo?.schoolId},DialogMode.ADD,'1000px')">新增
        </el-button>
        <el-button v-has-perm="['student:import']" type="primary" icon="Upload" @click="showFormDialog('导入','ImportForm',{},DialogMode.IMPORT,'1000px')">批量导入</el-button>
        <el-button v-has-perm="['student:export']" type="info" icon="Download" @click="apis.StudentManage.export(search, currentRegionId)">批量导出</el-button>
      </div>
      <div class="lmt-search">
        <el-select v-model="search.regionId" :clearable="false" placeholder="请选择所属区县" @change="regionChange"
                   v-if="!(userStore.userInfo?.countyAdminFlag || userStore.userInfo?.schoolAdminFlag)">
          <el-option v-for="item in regionList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <el-select v-model="search.schoolId" clearable placeholder="所属学校" @change="getClazzList"
                   v-if="!userStore.userInfo?.schoolAdminFlag">
          <el-option v-for="item in schoolList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <SchoolPeriodSelector v-model="search.period" show-type="select" from="student" @change="getClazzList"></SchoolPeriodSelector>
        <GradeSelector v-model="search.gradeId" show-type="select" @change="getClazzList"></GradeSelector>
        <el-select v-model="search.clazzId" clearable placeholder="班级">
          <el-option v-for="item in clazzList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <el-select v-model="search.studentStatus" clearable placeholder="学生状态">
          <el-option v-for="item in studentStatusList" :key="item.key" :label="item.value"
                     :value="item.key"></el-option>
        </el-select>
        <el-input v-model="search.keywords" clearable placeholder="学生姓名/学籍号/身份证号" @clear="searchSubmit">
          <template #append>
            <el-button icon="Search" @click="searchSubmit"></el-button>
          </template>
        </el-input>
      </div>
    </div>
    <el-table v-loading="loading" :data="tableData.records" border max-height="calc(100vh - var(--lmt-header-height) - var(--lmt-main-padding) * 2 - 6rem)"
              stripe>
      <el-table-column align="center" label="序号" type="index" width="80" fixed>
        <template #default="scope">
          {{ (search.pageNumber - 1) * search.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="学生姓名" min-width="160" prop="xm" fixed></el-table-column>
      <el-table-column label="学籍号" min-width="170" prop="xjh"></el-table-column>
      <el-table-column label="身份证号  " min-width="200" prop="sfzh"></el-table-column>
      <el-table-column label="学生账号" min-width="120" prop="userName"></el-table-column>
      <el-table-column label="性别" min-width="60" prop="xb">
        <template #default="{row}">
          {{row.xb && row.xb == 1 ? '男' : row.xb == 2 ? '女' : ''}}
        </template>
      </el-table-column>
      <el-table-column label="学段" min-width="60" prop="period">
        <template #default="scope">
          <SchoolPeriodSelector v-model="scope.row.period" show-type="label"></SchoolPeriodSelector>
        </template>
      </el-table-column>
      <el-table-column label="所在班级" min-width="100" prop="clazzName"></el-table-column>
      <el-table-column label="年级" min-width="120" prop="gradeName"></el-table-column>
      <el-table-column label="所属学校" min-width="120" prop="schoolName"></el-table-column>
      <el-table-column label="所属区县" min-width="120" prop="regionName"></el-table-column>
      <el-table-column label="默认密码" min-width="120" prop="defaultPasswordView">
        <template #default="{ row }">
          <span>{{ row.defaultPasswordView }}</span>
          <el-link
            @click="changePasswordView(row)"
            underline="hover"
            :icon="row.showPasswordView && 'View'"
          />
        </template>
      </el-table-column>
      <el-table-column label="学生状态" min-width="120" prop="studentStatus">
        <template #default="{ row }">
          {{ studentStatusList.find(item => item.key === row.studentStatus)?.value || '未知' }}
        </template>
      </el-table-column>
      <el-table-column label="学生账号状态" min-width="120" prop="status">
        <template #default="{ row }">
          <el-tag v-if="row.status == 1" type="success">正常</el-tag>
          <el-tag v-else type="danger">冻结</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="家长手机号" min-width="120" prop="sjhm"></el-table-column>
      <el-table-column fixed="right" label="操作" min-width="320">
        <template #default="scope">
          <el-link icon="View" type="primary"
                   @click="showFormDialog('详情', 'DetailForm', scope.row, DialogMode.DETAIL, '800px')">详情
          </el-link>
          <el-link v-has-perm="['student:edit']" icon="Edit" type="warning"
                   @click="showFormDialog('修改', 'EditForm', scope.row, DialogMode.EDIT, '800px')">修改
          </el-link>
          <el-link v-has-perm="['user:change:status']" v-if="scope.row.status==1" type="danger" icon="CircleClose" @click="changeUserStatus(scope.row,0)">账号冻结
          </el-link>
          <el-link v-has-perm="['user:change:status']" v-if="scope.row.status==0" type="success" icon="CircleCheck" @click="changeUserStatus(scope.row,1)">账号解冻
          </el-link>
          <el-link v-has-perm="['user:reset:password']"  type="warning" icon="RefreshLeft" :disabled="!scope.row.defaultPasswordFlag"
                   @click="resetPassword(scope.row)">重置密码
          </el-link>
          <el-link v-has-perm="['student:delete']" icon="Delete" type="danger" @click="deleteStudent(scope.row)">删除
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-model:current-page="search.pageNumber" v-model:page-size="search.pageSize"
                   :page-sizes="[10, 20, 30, 40, 50, 100]" :total="Number(tableData.total)"
                   background
                   class="m-t-4 float-right" layout="total, prev, pager, next, sizes, ->" @change="pageOrSizeChange">
    </el-pagination>
    <el-dialog v-model="dialogModel.visible" :title="dialogModel.title" :width="dialogModel.width" destroy-on-close
               @closed="dialogModel.closed()">
      <AddForm v-if="dialogModel.formName == 'AddForm'" ref="addForm" :mode="dialogModel.mode"
               :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <EditForm v-if="dialogModel.formName == 'EditForm'" ref="editForm" :mode="dialogModel.mode"
                :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <DetailForm v-if="dialogModel.formName == 'DetailForm'" ref="detailForm" :mode="dialogModel.mode"
                  @closeDetail="closeDetail"  @toEdit="toEdit" :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <ImportForm v-if="dialogModel.formName == 'ImportForm'" ref="importForm" :mode="dialogModel.mode"
                  :regionId="currentRegionId" @save-complete="dialogModel.saveComplete"/>
      <template #footer>
        <el-button @click="dialogModel.visible = false">
          {{ dialogModel.formName !== 'DetailForm' ? '取 消' : '关 闭' }}
        </el-button>
        <el-button v-if="dialogModel.formName !== 'DetailForm'" :loading="dialogModel.saveLoading" type="primary"
                   @click="saveSubmit">确 认
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped></style>
