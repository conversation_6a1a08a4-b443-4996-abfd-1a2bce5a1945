<script setup lang="ts">
import {ref, onMounted, watch, computed} from "vue";
import {DialogMode} from "@/commons/DialogModel";
import {validateTelephone,validatorIdCard} from "@/commons/Validator"
import type {FormInstance} from "element-plus";
import {apis} from "@/apis";
import {useDeptStore,useUserStore} from "@/store";
import Notification from "@/commons/Notification";
import SchoolPeriodSelector from "@/components/SchoolPeriodSelector/index.vue";
import type { KeyValue } from "@/commons/KeyValue";

const userStore = useUserStore()
let currentRegionId = userStore.regionId || '130102'
const deptStore = useDeptStore()

if (deptStore.topDept?.id == deptStore.currentDept?.id) {
  currentRegionId = '130102'
}

const formData = ref({
  id:'',
  xm:'',
  sfzh: '',
  xjh: '',
  xb:'' as string | number, // 支持字符串和数字类型，但在显示时会转换为字符串
  mz: '',
  sjhm: '',
  jtzz: '',
  period: '',
  regionId: '',
  schoolId: '',
  gradeId: '',
  clazzId: '',
})

const props = defineProps({
  rowData: {
    type: [Object, null],
    default: () => {
    }
  },
  mode: {
    type: [String, null],
    default: 'EDIT'
  },
  regionId: {
    type: [String, null],
    default: ''
  },
  schoolId: {
    type: [String, null],
    default: ''
  },
})

// const regionList = ref<any>([])
const schoolList = ref<any>([])
const gradeList = ref<Array<KeyValue>>([])
const clazzList = ref<Array<KeyValue>>([])
const nationList = ref<any>([])

const rules = {
  xm: [
    {required: true, message: '请输入名称', trigger: 'blur'},
    {min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur'}
  ],
  sfzh: [{required: true, message: '请输入身份证号', trigger: 'blur'},
    {validator: validatorIdCard, trigger: 'blur'}
  ],
  xjh: [{required: true, message: '请输入学籍号', trigger: 'blur'}],
  xb: [{required: true, message: '请选择性别', trigger: 'blur'}],
  mz: [{required: true, message: '请选择性别', trigger: 'change'}],
  sjhm: [
    {required: true, message: '请输入联系电话', trigger: 'blur'},
    {validator: validateTelephone, trigger: 'blur'}
  ],
  period: [{required: true, message: '请选择学段', trigger: 'change'}],
  gradeId: [{required: true, message: '请选择年级', trigger: 'change'}],
  clazzId: [{required: true, message: '请选择班级', trigger: 'change'}],
  jtzz: [{required: true, message: '请输入家庭住址', trigger: 'blur'}],
}
const dataLoading = ref(false)

onMounted(async () => {
  // if (userStore.userInfo?.cityAdminFlag) {
  //   await useDeptStore().setRegionList();
  //   regionList.value = useDeptStore().regionList;
  // } else {
  //   regionList.value = [{key: currentRegionId, value: userStore.userInfo?.regionName}]
  // }

  if (userStore.userInfo?.schoolAdminFlag) {
    await deptStore.setSchoolList(currentRegionId)
    schoolList.value = deptStore.schoolList
    if(props.regionId == userStore.regionId) {
      formData.value.regionId = props.regionId
    }
  }

  if (props.mode === DialogMode.EDIT) {
    const {
      id, xm, sfzh, xjh, xb, mz, sjhm, jtzz, period, regionId, schoolId, gradeId, clazzId
    } = (await apis.StudentManage.getDetail(props.rowData?.id, props.rowData?.regionId)).data as any
    formData.value = {id, xm, sfzh, xjh, xb, mz, sjhm, jtzz, period, regionId, schoolId, gradeId, clazzId}
    dataLoading.value = false
    clazzList.value = (await apis.Commons.getClazzSelectList(formData.value.schoolId, formData.value.gradeId, formData.value.period, formData.value.regionId)).data;
  } else {
    formData.value.regionId = currentRegionId
    formData.value.schoolId = userStore.userInfo?.schoolId as string
  }

  await getNationList()
  await getGradeSelectList()
  // await regionChange(currentRegionId)
})

const getNationList = async () => {
  nationList.value = (await apis.Commons.getNationList()).data;
}

const getGradeSelectList = async () => {
  gradeList.value = (await apis.Commons.getGradeSelectList()).data;
}

const gradeOrPeriodChange = async () => {
  formData.value.clazzId = ''
  clazzList.value = []
  if(formData.value.gradeId && formData.value.period) {
    clazzList.value = (await apis.Commons.getClazzSelectList(formData.value.schoolId, formData.value.gradeId, formData.value.period, formData.value.regionId)).data;
  }
}

const periodList = computed(() => {
  if (formData.value.schoolId) {
    const school = schoolList.value.find((item: any) => item.key === formData.value.schoolId)
    if (Number(school?.extra) === 1) {
      return [1]
    } else if (Number(school?.extra) === 2) {
      return [2]
    } else if (Number(school?.extra) === 3) {
      return [3]
    } else if (Number(school?.extra) === 4) {
      return [1, 2]
    } else if (Number(school?.extra) === 5) {
      return [1, 2, 3]
    } else if (Number(school?.extra) === 6) {
      return [2, 3]
    }
  } else {
    return [1, 2, 3]
  }
})

const emit = defineEmits(['save-complete'])
const saveCompleteEmit = (status: number) => emit('save-complete', status)

const form = ref()
const saveSubmit = () => {
  (form.value as FormInstance).validate((valid) => {
    if (valid) {
      const fcn: Function = props.mode === DialogMode.ADD ? apis.StudentManage.save : apis.StudentManage.update;
      fcn(formData.value, formData.value.regionId).then(() => {
        saveCompleteEmit(1)
        Notification.showSuccessNotify('操作成功')
      }).catch(() => saveCompleteEmit(0))
    } else {
      saveCompleteEmit(0)
    }
  })
}
// const regionChange = async (regionId: string) => {
//   if (regionId) {
//     await deptStore.setSchoolList(regionId)
//     schoolList.value = deptStore.schoolList
//   }
//   formData.value.schoolId = ''
//   formData.value.period = ''
// }
const schoolChange = async (schoolId: string) => {
  formData.value.period = ''
  formData.value.clazzId = ''
  clazzList.value = []
}
defineExpose({
  saveSubmit
})
</script>

<template>
  <div class="form-container">
    <el-form ref="form" :model="formData" :rules="rules" label-width="auto" v-loading="dataLoading">
      <el-row :gutter="40">
        <el-col :span="12">
<!--          <el-form-item v-if="userStore.userInfo?.cityAdminFlag || userStore.userInfo?.countyAdminFlag" label="所属区县：" prop="regionId">-->
<!--            <el-select v-model="formData.regionId" :clearable="false" placeholder="请选择所属区县" :disabled="userStore.userInfo?.countyAdminFlag" @change="regionChange">-->
<!--              <el-option v-for="item in regionList" :key="item.key" :label="item.value" :value="item.key"></el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
          <el-form-item label="学生姓名：" prop="xm">
            <el-input v-model.trim="formData.xm" placeholder="请输入学生姓名" maxlength="32" show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="学籍号：" prop="xjh">
            <el-input v-model.trim="formData.xjh" placeholder="请输入学籍号" maxlength="32" show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="民族：" prop="mz">
            <el-select v-model="formData.mz" placeholder="请选择民族">
              <el-option v-for="(item, index) in nationList" :key="item.key" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="学段：" prop="period">
            <SchoolPeriodSelector v-model="formData.period" :period-list="periodList" show-type="select" @change="gradeOrPeriodChange"></SchoolPeriodSelector>
          </el-form-item>
          <el-form-item label="班级：" prop="clazzId">
            <el-select v-model="formData.clazzId" clearable placeholder="班级">
              <el-option v-for="item in clazzList" :key="item.key" :value="item.key" :label="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
<!--          <el-form-item v-if="userStore.userInfo?.cityAdminFlag || userStore.userInfo?.countyAdminFlag" label="所属学校："-->
<!--                        prop="schoolId">-->
<!--            <el-select v-model="formData.schoolId" :clearable="true" placeholder="请选择所属学校" @change="schoolChange">-->
<!--              <el-option v-for="item in schoolList" :key="item.key" :label="item.value" :value="item.key"></el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
          <el-form-item label="身份证号：" prop="sfzh">
            <el-input v-model.trim="formData.sfzh" placeholder="请输入身份证号" maxlength="18" show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="性别：" prop="xb">
            <el-select v-model="formData.xb" placeholder="请选择性别" :clearable="false">
              <el-option label="男" :value="1"></el-option>
              <el-option label="女" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="家长手机号：" prop="sjhm">
            <el-input v-model.trim="formData.sjhm" placeholder="请输入联系电话" maxlength="11" show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="年级：" prop="gradeId">
            <el-select v-model="formData.gradeId" clearable placeholder="年级" @change="gradeOrPeriodChange">
              <el-option v-for="item in gradeList" :key="item.key" :label="item.value" :value="item.key"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="家庭住址" prop="jtzz">
            <el-input v-model.trim="formData.jtzz" placeholder="请输入家庭住址" maxlength="64" show-word-limit></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<style scoped lang="scss">

</style>
