<script lang="ts" setup>
import {computed, onMounted, ref} from 'vue'
import {DialogMode} from "@/commons/DialogModel";
import {apis} from "@/apis";
import {ElMessageBox, ElNotification} from "element-plus";
import type { KeyValue } from "@/commons/KeyValue";

const formData = ref({
  id: '',
  userId: '',
  xm: '',
  userName: '',
  regionId: '',
  regionName: '',
  schoolId: '',
  sfzh: '',
  xjh: '',
  period: '',
  sjhm: '',
  xb: '',
  gradeId: '',
  gradeName: '',
  clazzId: '',
  clazzName: '',
  jtzz: '',
  schoolName: '',
  status: 1,
  zhzt:'',
  studentStatus: '',
  defaultPasswordFlag: false
})

const props = defineProps({
  rowData: {
    type: [Object, null],
    default: () => {
    }
  },
  mode: {
    type: [String, null],
    default: 'DETAIL'
  }
})

const schoolList = ref<Array<KeyValue>>([])

const studentStatusList = ref([
  {key: 1, value: '在读'},
  {key: 2, value: '毕业'},
  {key: 3, value: '休学'},
])
const SchoolPeriod = ref([
  {key: 1, value: '小学'},
  {key: 2, value: '初中'},
  {key: 3, value: '高中'},
  {key: 4, value: '九年一贯制'},
  {key: 5, value: '十二年一贯制'},
  {key: 6, value: '初高连读'}
])
onMounted(async () => {
  if (props.mode === DialogMode.DETAIL) {
    const {
      id,
      userId,
      xm,
      userName,
      regionId,
      regionName,
      schoolId,
      sfzh,
      xjh,
      period,
      sjhm,
      xb,
      gradeId,
      gradeName,
      clazzId,
      clazzName,
      jtzz,
      schoolName,
      status,
      zhzt,
      studentStatus,
      defaultPasswordFlag
    } = props.rowData as any
    formData.value = {id,
      userId,
      xm,
      userName,
      regionId,
      regionName,
      schoolId,
      sfzh,
      xjh,
      period,
      sjhm,
      xb,
      gradeId,
      gradeName,
      clazzId,
      clazzName,
      jtzz,
      schoolName,
      status,
      zhzt,
      studentStatus,
      defaultPasswordFlag}
    schoolList.value = (await apis.Commons.getSchoolSelectListCounty(formData.value.regionId)).data;
  }
})
// 定义组件可以发出的事件
const emit = defineEmits(['closeDetail','searchSubmit','toEdit'])

const schoolName = computed(() => {
  return (schoolId: string) => {
    const target = schoolList.value.find(item => item.key === schoolId)
    return target?.value
  }
})
// 定义关闭对话框的方法
const closeDetail = () => {
  // 发出close事件，通知父组件关闭对话框
  emit('closeDetail')

}
const searchSubmit = async () => {
  emit('searchSubmit')
  closeDetail()
}
const toEdit = async (id:any,regionId:any) => {
  emit('toEdit',id,regionId)
  closeDetail()
}
// 定义其他按钮的方法（空实现，需要根据实际需求补充）
const edit = (id:any,regionId:any) => {
  toEdit(id,regionId)
}
const resetPassword = async (row: any) => {
  ElMessageBox.confirm(`您确定重置账户【${formData.value.xm}】的密码吗？`, "温馨提示",
    {confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'})
    .then(async () => {
      await apis.UserManage.resetPassword(formData.value.userId)
      ElNotification.success({message: "操作成功", title: "温馨提示"})
    })
}
const changeUserStatus = async (row: any, status: number) => {
  ElMessageBox.confirm(`您确定${status == 1 ? "解冻" : "冻结"}账户【${formData.value.xm}】吗？`, "温馨提示",
    {type: "warning"})
    .then(async () => {
      await apis.UserManage.changeUserStatus(formData.value.userId, status)
      formData.value.status = status
      ElNotification.success({message: "操作成功", title: "温馨提示"})
    })
}
const deleteStudent = async (row: any,regionId: string) => {
  ElMessageBox.confirm(`您确定删除姓名【${formData.value.xm}】的学生吗？`, "温馨提示",
    {confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'})
    .then(async () => {
      await apis.TeacherManage.delete(formData.value.id,regionId)
      ElNotification.success({message: "操作成功", title: "温馨提示"})
      await searchSubmit()
    }).catch(()=>{
    //ElNotification.error({message: "删除失败", title: "温馨提示"})
  })

}
</script>

<template>
  <div class="form-container">
    <el-form ref="form" :model="formData" label-width="auto">
      <el-row :gutter="40">
        <el-col :span="10" :offset="2">
          <el-form-item label="学生姓名：" prop="xm">
            <el-text>{{ formData.xm }}</el-text>
          </el-form-item>
          <el-form-item label="学生账号：" prop="userName">
            <el-text>{{ formData.userName }}</el-text>
          </el-form-item>
          <el-form-item label="身份证号:" prop="sfzh">
            <el-text>{{ formData.sfzh }}</el-text>
          </el-form-item>
          <el-form-item label="学籍号：" prop="xjh">
            <el-text>{{ formData.xjh }}</el-text>
          </el-form-item>
          <el-form-item label="学段：" prop="peroid">
            <el-text>{{ SchoolPeriod.find(item => item.key == Number(formData.period))?.value }}</el-text>
          </el-form-item>
          <el-form-item label="性别：" prop="xb">
            <el-text v-if="formData.xb == '1'">男</el-text>
            <el-text v-else-if="formData.xb == '2'">女</el-text>
            <el-text v-else>未知</el-text>
          </el-form-item>
          <el-form-item label="家长手机号：" prop="sjhm">
            <el-text>{{ formData.sjhm }}</el-text>
          </el-form-item>
        </el-col>
          <el-col :span="10">
          <el-form-item label="年级：" prop="gradeName">
            <el-text>{{ formData.gradeName }}</el-text>
          </el-form-item>
          <el-form-item label="班级：" prop="className">
            <el-text>{{ formData.clazzName }}</el-text>
          </el-form-item>
          <el-form-item label="家庭住址：" prop="jtzz">
            <el-text>{{ formData.jtzz }}</el-text>
          </el-form-item>
          <el-form-item label="所属区县：" prop="regionName">
            <el-text>{{ formData.regionName }}</el-text>
          </el-form-item>
          <el-form-item label="所属学校：" prop="schoolName">
            <el-text>{{ formData.schoolName }}</el-text>
          </el-form-item>
          <el-form-item label="学生状态：" prop="studentStatus">
            <el-text>{{ studentStatusList.find(item => item.key === Number(formData.studentStatus))?.value || '未知' }}</el-text>
          </el-form-item>
          <el-form-item label="账号状态：" prop="status">
            <el-text v-if="formData.status">正常</el-text>
            <el-text v-else type="danger">冻结</el-text>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="40">
        <div style="display: flex; justify-content: center; width: 100%; margin-top: 20px; gap: 10px;">
          <el-button type="default" label="返回" @click="closeDetail">返回</el-button>
          <el-button  v-has-perm="['student:edit']" icon="Edit" type="warning" label="修改" @click="edit(formData.id,formData.regionId)">修改</el-button>
          <el-button  v-has-perm="['user:change:status']" icon="CircleCheck" v-if="formData.status==0" type="success" label="账号解冻"  @click="changeUserStatus(formData, 1)">账号解冻</el-button>
          <el-button  v-has-perm="['user:change:status']" icon="CircleClose" v-if="formData.status==1" type="danger" label="账号冻结"  @click="changeUserStatus(formData, 0)">账号冻结</el-button>
<!--          <el-button v-has-perm="['student:delete']" type="danger" label="删除"  @click="deleteStudent(formData,formData.regionId)">删除</el-button>-->
          <el-button v-has-perm="['user:reset:password']" icon="RefreshLeft" type="warning" label="重置密码"  :disabled="!formData.defaultPasswordFlag" @click="resetPassword">重置密码</el-button>
        </div>
      </el-row>
    </el-form>
  </div>
</template>
