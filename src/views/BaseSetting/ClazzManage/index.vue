<script lang="ts" setup>

import {onMounted, ref} from "vue";
import {apis} from "@/apis";
import {useTableCommons} from "@/commons/TableCommons";
import {useDeptStore, useUserStore} from '@/store'
import {DialogMode} from "@/commons/DialogModel";
import SchoolPeriodSelector from "@/components/SchoolPeriodSelector/index.vue"
import SchoolTypeSelector from "@/components/SchoolTypeSelector/index.vue"
import EditForm from "./components/ClassForm.vue";
import AddForm from "./components/ClassForm.vue";
import DetailForm from "./components/ClassDetail.vue"

const masterOrSportList = ref([
  {key: 1, value: '班主任'},
  {key: 2, value: '体育老师'},
])

const userStore = useUserStore()
let currentRegionId = userStore.regionId || '130102'
const deptStore = useDeptStore()

if (deptStore.topDept?.id == deptStore.currentDept?.id) {
  currentRegionId = '130102'
}
let currentSchoolId = userStore.userInfo?.schoolId
const search = ref({
  pageNumber: 1,
  pageSize: 10,
  keywords: '',
  period: '',
  grade: '',
  masterOrSportTeacher: '',
  className: '',
  regionId: currentRegionId,
  schoolId: currentSchoolId
})
const getTableData = async () => {
  const response = await apis.ClazzManage.getPageData(search.value, search.value.regionId)
  return response.data
}

const gradeList = ref<any>(useDeptStore().gradeList)
const regionList = ref<any>(useDeptStore().regionList)
const schoolList = ref<any>(useDeptStore().schoolList)

let {
  loading,
  dialogModel,
  showFormDialog,
  tableData,
  searchSubmit,
  pageOrSizeChange,
} = useTableCommons(search, getTableData)


onMounted(async () => {
  if (regionList.value.length == 0) {
    await useDeptStore().setRegionList()
    regionList.value = useDeptStore().regionList
  }
  if (gradeList.value.length == 0) {
    await useDeptStore().setGradeList()
    gradeList.value = useDeptStore().gradeList
  }
  if (schoolList.value.length == 0) {
    await useDeptStore().setSchoolList(currentRegionId)
    schoolList.value = useDeptStore().schoolList
  }
  await searchSubmit();
})

const regionChange = async (regionId: string) => {
  if (regionId) {
    await useDeptStore().setSchoolList(regionId)
    schoolList.value = useDeptStore().schoolList
  }
  search.value.schoolId = ''
}


// 表单部分
const addForm = ref()
const editForm = ref()
const saveSubmit = async () => {
  dialogModel.value.saveLoading = true
  if (dialogModel.value.formName === 'AddForm') {
    addForm.value.saveSubmit()
  } else if (dialogModel.value.formName === 'EditForm') {
    editForm.value.saveSubmit()
  }
}
</script>

<template>
  <div>
    <div class="lmt-operation-container">
      <div class="lmt-operation">
        <el-button v-has-perm="'clazz:add'" icon="Plus" type="primary"
                   @click="showFormDialog('新增','AddForm',{regionId: search.regionId,schoolId: currentSchoolId},DialogMode.ADD,'600px')">新增
        </el-button>
      </div>
      <div class="lmt-search">
        <el-select v-if="userStore.userInfo?.cityAdminFlag" v-model="search.regionId" :clearable="false"
                   placeholder="请选择区县" @change="regionChange">
          <el-option v-for="item in regionList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <el-select v-if="userStore.userInfo?.cityAdminFlag"  v-model="search.schoolId" :clearable="true" placeholder="请选择学校">
          <el-option v-for="item in schoolList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <SchoolPeriodSelector v-model="search.period" show-type="select" style="width: 200px"></SchoolPeriodSelector>
        <el-select v-model="search.grade" clearable placeholder="请选择年级">
          <el-option v-for="item in gradeList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <el-input v-model="search.className" clearable placeholder="班级名称" @clear="searchSubmit">
          <template #append>
            <el-button icon="Search" @click="searchSubmit"></el-button>
          </template>
        </el-input>
      </div>
    </div>
    <el-table v-loading="loading" :data="tableData.records" border max-height="calc(100vh - var(--lmt-header-height) - var(--lmt-main-padding) * 2 - 6rem)"
              stripe>
      <!--行号-->
      <el-table-column align="center" label="序号" type="index" width="80">
        <template #default="scope">
          {{ (search.pageNumber - 1) * search.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="班级名称" min-width="100" prop="name" show-overflow-tooltip></el-table-column>
      <el-table-column label="班级代码" min-width="120" prop="code" show-overflow-tooltip></el-table-column>
      <el-table-column label="班级学段" min-width="100" prop="period" show-overflow-tooltip>
        <template #default="scope">
          <SchoolPeriodSelector v-model="scope.row.period" show-type="label"></SchoolPeriodSelector>
        </template>
      </el-table-column>
      <template v-if="userStore.userInfo?.cityAdminFlag||userStore.userInfo?.regionAdminFlag">
        <el-table-column label="所属学校" min-width="120" prop="schoolName" show-overflow-tooltip></el-table-column>
        <el-table-column label="学校性质" min-width="120" prop="schoolType" show-overflow-tooltip>
          <template #default="scope">
            <SchoolTypeSelector v-model="scope.row.schoolType" show-type="label"></SchoolTypeSelector>
          </template>
        </el-table-column>
        <el-table-column label="学校学段" min-width="120" prop="schoolPeriod" show-overflow-tooltip>
          <template #default="scope">
            <SchoolPeriodSelector v-model="scope.row.schoolPeriod" show-type="label"></SchoolPeriodSelector>
          </template>
        </el-table-column>
      </template>
      <el-table-column label="年级" min-width="100" prop="gradeName" show-overflow-tooltip></el-table-column>

      <el-table-column label="班主任" min-width="100" prop="masterTeacherName" show-overflow-tooltip></el-table-column>
      <el-table-column label="体育老师" min-width="100" prop="sportTeacherName"
                       show-overflow-tooltip></el-table-column>
      <el-table-column label="班级学生数" min-width="100" prop="studentCount" show-overflow-tooltip></el-table-column>
      <el-table-column label="班级状态" min-width="100" prop="status" show-overflow-tooltip>
        <template #default="scope">
          <el-text v-if="scope.row.status == 1" type="success">正常</el-text>
          <el-text v-else-if="scope.row.status == 0" type="danger">禁用</el-text>
          <el-text v-else-if="scope.row.status == 2" type="info">已毕业</el-text>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" min-width="120" v-has-perm="['clazz:detail', 'clazz:edit']">
        <template #default="scope">
          <el-link v-has-perm="'clazz:detail'" icon="View" type="primary"
                   @click="showFormDialog('详情','DetailForm',scope.row,DialogMode.DETAIL,'600px')">详情
          </el-link>
          <el-link v-has-perm="'clazz:edit'" icon="Edit" type="warning"
                   @click="showFormDialog('修改','EditForm',scope.row,DialogMode.EDIT,'600px')">修改
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-model:current-page="search.pageNumber" v-model:page-size="search.pageSize"
                   :page-sizes="[10, 20, 30, 40, 50, 100]" :total="Number(tableData.total)"
                   background
                   class="m-t-4 float-right" layout="total, prev, pager, next, sizes, ->" @change="pageOrSizeChange">
    </el-pagination>
    <el-dialog v-model="dialogModel.visible" :close-on-click-modal="false" :title="dialogModel.title" :width="dialogModel.width"
               destroy-on-close
               draggable @closed="dialogModel.closed()">
      <AddForm v-if="dialogModel.formName == 'AddForm'" ref="addForm" :mode="dialogModel.mode"
               :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <EditForm v-if="dialogModel.formName == 'EditForm'" ref="editForm" :mode="dialogModel.mode"
                :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <DetailForm v-if="dialogModel.formName == 'DetailForm'" ref="detailForm" :mode="dialogModel.mode"
                  :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <template #footer>
        <el-button @click="dialogModel.visible=false">
          {{ dialogModel.formName !== 'DetailForm' ? '取 消' : '关 闭' }}
        </el-button>
        <el-button v-if="dialogModel.formName!=='DetailForm'" :loading="dialogModel.saveLoading" type="primary"
                   @click="saveSubmit">确 认
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>

</style>
