<script lang="ts" setup>
import {computed, onMounted, ref} from "vue";
import {DialogMode} from "@/commons/DialogModel";
import SchoolPeriodSelector from "@/components/SchoolPeriodSelector/index.vue"
import type {FormInstance} from "element-plus";
import {apis} from "@/apis";
import Notification from "@/commons/Notification";
import {useDeptStore, useUserStore} from "@/store";
import TeacherSelector from "@/components/TeacherSelector/index.vue"
import type { KeyValue } from "@/commons/KeyValue";

const formData = ref({
  id: "",
  name: "",
  code: "",
  gradeId: "",
  schoolId: "",
  period: "",
  schoolPeriod: "",
  masterTeacherId: "",
  sportTeacherId: "",
  regionId: ""
})

const gradeList = ref<any>(useDeptStore().gradeList)

const props = defineProps({
  rowData: {
    type: [Object, null],
    default: () => {
    }
  },
  mode: {
    type: [String, null],
    default: 'EDIT'
  }
})

const rules = {
  name: [{required: true, message: '请输入班级名称', trigger: 'blur'}],
  code: [{required: true, message: '请输入班级代码', trigger: 'blur'}],
  gradeId: [{required: true, message: '请选择年级', trigger: 'change'}],
  schoolId: [{required: true, message: '请选择学校', trigger: 'change'}],
  period: [{required: true, message: '请选择学段', trigger: 'change'}],
  masterTeacherId: [{required: true, message: '请选择班主任', trigger: 'change'}],
  sportTeacherId: [{required: true, message: '请选择体育老师', trigger: 'change'}],
  regionId: [{required: true, message: '请选择区县', trigger: 'change'}]
}

// const regionList = ref<Array<KeyValue>>([])

const dataLoading = ref(false)
const deptStore = useDeptStore()
const userStore = useUserStore()
const regionList = ref<Array<KeyValue>>(deptStore.regionList)
const schoolList = ref<Array<KeyValue>>([])

onMounted(async () => {
  if (props.mode === DialogMode.EDIT) {
    dataLoading.value = true
    const {
      id, name, code, gradeId, schoolId, period, schoolPeriod,
      masterTeacherId, sportTeacherId, regionId
    } = props.rowData as any
    formData.value = {
      id, name, code, gradeId, schoolId, period, schoolPeriod,
      masterTeacherId, sportTeacherId, regionId
    }
    dataLoading.value = false
  } else {
    if (userStore.userInfo?.regionAdminFlag || userStore.userInfo?.schoolAdminFlag) {
      formData.value.regionId = userStore.userInfo?.regionId
    }
  }
  if (regionList.value.length == 0) {
    await deptStore.setRegionList()
    regionList.value = deptStore.regionList
  }
  if (props.rowData && props.rowData.schoolId) {
    formData.value.schoolId = props.rowData.schoolId
  }
})


const regionChange = async (regionId: string) => {
  if (regionId) {
    await deptStore.setSchoolList(regionId)
    schoolList.value = deptStore.schoolList
  }
  formData.value.schoolId = ''
  formData.value.period = ''
  formData.value.masterTeacherId = ''
  formData.value.sportTeacherId = ''
}
const schoolChange = async (schoolId: string) => {
  formData.value.period = ''
  formData.value.masterTeacherId = ''
  formData.value.sportTeacherId = ''
}

const periodList = computed(() => {
  if (formData.value.schoolId) {
    const school = schoolList.value.find(item => item.key === formData.value.schoolId)
    if (Number(school?.extra) === 1) {
      return [1]
    } else if (Number(school?.extra) === 2) {
      return [2]
    } else if (Number(school?.extra) === 3) {
      return [3]
    } else if (Number(school?.extra) === 4) {
      return [1, 2]
    } else if (Number(school?.extra) === 5) {
      return [1, 2, 3]
    } else if (Number(school?.extra) === 6) {
      return [2, 3]
    }
  } else {
    return [1, 2, 3]
  }
})

const emit = defineEmits(['save-complete'])
const saveCompleteEmit = (status: number) => emit('save-complete', status)

const form = ref()
const saveSubmit = () => {
  (form.value as FormInstance).validate((valid) => {
    if (valid) {
      const fcn: Function = props.mode === DialogMode.ADD ? apis.ClazzManage.create : apis.ClazzManage.update;
      fcn(formData.value, formData.value.regionId).then(() => {
        saveCompleteEmit(1)
        Notification.showSuccessNotify('操作成功')
      }).catch(() => saveCompleteEmit(0))
    } else {
      saveCompleteEmit(0)
    }
  })
}

defineExpose({
  saveSubmit
})
</script>

<template>
  <div class="form-container">
    <el-form ref="form" v-loading="dataLoading" :model="formData" :rules="rules" label-width="auto">
      <el-form-item v-if="userStore.userInfo?.cityAdminFlag" label="所属区县：" prop="regionId">
        <el-select v-model="formData.regionId" :clearable="false" placeholder="请选择所属区县" @change="regionChange">
          <el-option v-for="item in regionList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="userStore.userInfo?.countyAdminFlag||userStore.userInfo?.cityAdminFlag" label="所属学校："
                    prop="schoolId">
        <el-select v-model="formData.schoolId" :clearable="true" placeholder="请选择所属学校" @change="schoolChange">
          <el-option v-for="item in schoolList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="班级代码：" prop="code">
        <el-input v-model.trim="formData.code" placeholder="请输入班级代码，入学年份+班号" maxlength="32" show-word-limit></el-input>
      </el-form-item>
      <el-form-item label="班级名称：" prop="name">
        <el-input v-model.trim="formData.name" placeholder="请输入班级名称" maxlength="32" show-word-limit></el-input>
      </el-form-item>
      <el-form-item label="年级：" prop="gradeId">
        <el-select v-model="formData.gradeId" :clearable="false" placeholder="请选择年级">
          <el-option v-for="item in gradeList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="学段：" prop="period">
        <SchoolPeriodSelector v-model="formData.period" :period-list="periodList"
                              show-type="select"></SchoolPeriodSelector>
      </el-form-item>
      <el-form-item label="班主任：" prop="masterTeacherId">
        <TeacherSelector v-model="formData.masterTeacherId" :region-id="formData.regionId"
                         :school-id="formData.schoolId" show-type="select"></TeacherSelector>
      </el-form-item>
      <el-form-item label="体育老师：" prop="sportTeacherId">
        <TeacherSelector v-model="formData.sportTeacherId" :region-id="formData.regionId"
                         :school-id="formData.schoolId" show-type="select"></TeacherSelector>
      </el-form-item>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>

</style>
