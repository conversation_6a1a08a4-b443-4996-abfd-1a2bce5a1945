<script lang="ts" setup>
import {onMounted, ref} from 'vue'
import {DialogMode} from "@/commons/DialogModel";
import {useUserStore} from "@/store";

const userStore = useUserStore()

const formData = ref<any>({})

const props = defineProps({
  rowData: {
    type: [Object, null],
    default: () => {
    }
  },
  mode: {
    type: [String, null],
    default: 'DETAIL'
  }
})

const dataLoading = ref(false)

onMounted(async () => {
  if (props.mode === DialogMode.DETAIL) {
    dataLoading.value = true
    formData.value = props.rowData as any
    dataLoading.value = false
  } else {
    if (userStore.userInfo?.regionAdminFlag || userStore.userInfo?.schoolAdminFlag) {
      formData.value.regionId = userStore.userInfo?.regionId
    }
  }
})

</script>

<template>
  <div class="form-container">
    <el-form ref="form" :model="formData" label-width="auto">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="班级名称：">
            <el-text type="info">{{ formData.name }}</el-text>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="班级代码：">
            <el-text type="info">{{ formData.code }}</el-text>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="userStore.userInfo?.cityAdminFlag" :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属学校：">
            <el-text type="info">{{ formData.schoolName }}</el-text>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属区域：">
            <el-text type="info">{{ formData.regionName }}</el-text>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="年级：">
            <el-text type="info">{{ formData.gradeName }}</el-text>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="学生数量：">
            <el-text type="info">{{ formData.studentCount }}</el-text>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="班主任：">
            <el-text type="info">{{ `${formData.masterTeacherName}(${formData.masterTeacherPhone})` }}</el-text>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="体育老师：">
            <el-text type="info">{{ `${formData.sportTeacherName}(${formData.sportTeacherPhone})` }}</el-text>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
