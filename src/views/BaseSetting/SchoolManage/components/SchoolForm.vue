<script lang="ts" setup>
import {onMounted, ref} from "vue";
import {DialogMode} from "@/commons/DialogModel";
import {validateTelephone} from "@/commons/Validator"
import SchoolPeriodSelector from "@/components/SchoolPeriodSelector/index.vue"
import SchoolTypeSelector from "@/components/SchoolTypeSelector/index.vue"
import SdWangEditor from "@/components/SdWangEditor/SdWangEditor.vue";
import type {FormInstance} from "element-plus";
import {apis} from "@/apis";
import Notification from "@/commons/Notification";
import {useDeptStore} from "@/store";
import type { KeyValue } from "@/commons/KeyValue";

import {useUserStore} from '@/store/modules/user'

const userStore = useUserStore()
const currentRole: String = userStore.role
const isCounty = ref(false)
if (currentRole == 'COUNTY_ADMIN') {
  isCounty.value = true
}

const formData = ref({
  id: '',
  name: '',
  code: '',
  regionId: '',
  regionName: '',
  period: 1,
  schoolType: 1,
  contacts: '',
  telephone: '',
  description: ''
})

const props = defineProps({
  rowData: {
    type: [Object, null],
    default: () => {
    }
  },
  mode: {
    type: [String, null],
    default: 'EDIT'
  }
})

const rules = {
  name: [
    {required: true, message: '请输入名称', trigger: 'blur'},
    {min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur'}
  ],
  code: [
    {required: true, message: '请输入代码', trigger: 'blur'},
    {min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur'}
  ],
  period: [{required: true, message: '请选择学段', trigger: 'change'}],
  schoolType: [{required: true, message: '请选择学校类型', trigger: 'change'}],
  contacts: [{required: true, message: '请输入联系人', trigger: 'blur'}],
  telephone: [{required: true, message: '请输入联系电话', trigger: 'blur'},
    {validator: validateTelephone, trigger: 'blur'}]
}

const regionList = ref<Array<KeyValue>>([])

const dataLoading = ref(false)

onMounted(async () => {
  if (props.mode === DialogMode.EDIT) {
    dataLoading.value = true
    const {
      id, name, code, regionId, regionName, period,
      schoolType, contacts, telephone, description
    } = (await apis.SchoolManage.getDetail(props.rowData?.id, props.rowData?.regionId)).data as any
    formData.value = {id, name, code, regionId, regionName, period, schoolType, contacts, telephone, description}
    dataLoading.value = false
  } else {
    regionList.value = useDeptStore().regionList
    formData.value.regionId = props.rowData?.regionId
  }
})

const emit = defineEmits(['save-complete'])
const saveCompleteEmit = (status: number) => emit('save-complete', status)

const form = ref()
const saveSubmit = () => {
  (form.value as FormInstance).validate((valid) => {
    if (valid) {
      const fcn: Function = props.mode === DialogMode.ADD ? apis.SchoolManage.create : apis.SchoolManage.update;
      fcn(formData.value, formData.value.regionId).then(() => {
        saveCompleteEmit(1)
        Notification.showSuccessNotify('操作成功')
      }).catch(() => saveCompleteEmit(0))
    } else {
      saveCompleteEmit(0)
    }
  })
}

defineExpose({
  saveSubmit
})
</script>

<template>
  <div class="form-container">
    <el-form ref="form" v-loading="dataLoading" :model="formData" :rules="rules" label-width="auto">
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="学校名称：" prop="name">
            <el-input v-model.trim="formData.name" maxlength="64" placeholder="请输入学校名称"
                      show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="学校代码：" prop="code">
            <el-input v-model.trim="formData.code" maxlength="32" placeholder="请输入学校代码"
                      show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="学段：" prop="period">
            <SchoolPeriodSelector v-model="formData.period" show-type="select"></SchoolPeriodSelector>
          </el-form-item>
          <template v-if="props.mode === DialogMode.EDIT">
            <el-form-item label="所属区县：" prop="regionId">
              <el-text type="info">{{ formData.regionName }}</el-text>
            </el-form-item>
          </template>
          <template v-if="props.mode === DialogMode.ADD">
            <el-form-item label="所属区县：" prop="regionId">
              <el-select v-model="formData.regionId" :disabled="isCounty" placeholder="请选择所属区县"
                         style="width: 100%">
                <el-option v-for="item in regionList" :key="item.key" :label="item.value" :value="item.key"></el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-col>
        <el-col :span="12">
          <el-form-item label="办学性质：" prop="schoolType">
            <SchoolTypeSelector v-model="formData.schoolType" show-type="select"></SchoolTypeSelector>
          </el-form-item>
          <el-form-item label="联系人：" prop="contacts">
            <el-input v-model.trim="formData.contacts" maxlength="32" placeholder="请输入联系人" show-word-limit/>
          </el-form-item>
          <el-form-item label="联系电话：" prop="telephone">
            <el-input v-model.trim="formData.telephone" maxlength="32" placeholder="请输入联系电话" show-word-limit/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="学校简介：" style="height: 400px">
            <SdWangEditor v-model="formData.description" :region-id="formData.regionId" service-url-prefix="/region-service" placeholder="请输入学校简介..." style="width: 100%"/>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>

</style>
