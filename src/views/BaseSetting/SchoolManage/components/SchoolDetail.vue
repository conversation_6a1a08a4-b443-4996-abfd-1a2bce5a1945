<script lang="ts" setup>
import {onMounted, ref} from 'vue'
import {DialogMode} from "@/commons/DialogModel";
import {apis} from "@/apis";
import SchoolPeriodSelector from "@/components/SchoolPeriodSelector/index.vue";
import SchoolTypeSelector from "@/components/SchoolTypeSelector/index.vue";


const formData = ref({
  id: '',
  name: '',
  code: '',
  regionId: '',
  regionName: '',
  period: 1,
  schoolType: 1,
  contacts: '',
  telephone: '',
  description: ''
})

const props = defineProps({
  rowData: {
    type: [Object, null],
    default: () => {
    }
  },
  mode: {
    type: [String, null],
    default: 'EDIT'
  }
})

const dataLoading = ref(false)

onMounted(async () => {
  if (props.mode === DialogMode.DETAIL) {
    dataLoading.value = true
    const {
      id, name, code, regionId, regionName, period,
      schoolType, contacts, telephone, description
    } = (await apis.SchoolManage.getDetail(props.rowData?.id, props.rowData?.regionId)).data as any
    formData.value = {
      id, name, code, regionId, regionName, period,
      schoolType, contacts, telephone, description
    }
    dataLoading.value = false
  }
})

</script>

<template>
  <div class="form-container">
    <el-form ref="form" :model="formData" label-width="auto">
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="学校名称：" prop="name">
            <el-text type="info">{{ formData.name }}</el-text>
          </el-form-item>
          <el-form-item label="学校代码：" prop="code">
            <el-text type="info">{{ formData.code }}</el-text>
          </el-form-item>
          <el-form-item label="学段：" prop="period">
            <SchoolPeriodSelector v-model="formData.period" show-type="label"></SchoolPeriodSelector>
          </el-form-item>
          <el-form-item label="所属区县：" prop="regionName">
            <el-text type="info">{{ formData.regionName }}</el-text>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="学校性质：" prop="schoolType">
            <SchoolTypeSelector v-model="formData.schoolType" show-type="label"></SchoolTypeSelector>
          </el-form-item>
          <el-form-item label="联系人：" prop="contacts">
            <el-text type="info">{{ formData.contacts }}</el-text>
          </el-form-item>
          <el-form-item label="联系电话：" prop="telephone">
            <el-text type="info">{{ formData.telephone }}</el-text>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="40">
        <el-col :span="24">
          <el-form-item label="学校简介：" prop="description">
            <div class="lmt-html-viewer" style="width: 100%;min-height: 300px;" v-html="formData.description"></div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
