<script lang="ts" setup>
import {onMounted, ref} from "vue";
import {validatorEmail, validatorMobile, validatorUsername} from "@/commons/Validator";
import {useRoleStore} from "@/store";
import {apis} from "@/apis";
import {DialogMode} from "@/commons/DialogModel";
import type {FormInstance} from "element-plus";
import Notification from "@/commons/Notification";

const props = defineProps({
  rowData: {
    type: [Object, null],
    default: () => {
    }
  },
  mode: {
    type: [String, null],
    default: 'EDIT'
  }
})

const formData = ref({
  id: '',
  username: '',
  nickname: '',
  mobile: '',
  email: '',
  regionId: '',
  schoolId: '',
  roleCodes: [] as string[],
  status: 1,
  defaultPasswordFlag: true,
  defaultPassword: ''
})

const rules = ref({
  username: [{required: true, message: '请输入用户名', trigger: 'blur'},
    {validator: validatorUsername, trigger: 'blur'}],
  nickname: [{required: true, message: '请输入昵称', trigger: 'blur'}],
  mobile: [{required: true, message: '请输入手机号', trigger: 'blur'},
    {validator: validatorMobile, trigger: 'blur'}],
  email: [{validator: validatorEmail, trigger: 'blur'}]
})

onMounted(async () => {
  const roleStore = useRoleStore()
  formData.value.roleCodes = [roleStore.roleEnum.SCHOOL_ADMIN]
  formData.value.regionId = props.rowData?.regionId
  formData.value.schoolId = props.rowData?.id
  if (props.mode === DialogMode.EDIT) {
    const {
      id, username, nickname, mobile, email, status, regionId,
      schoolId, roleCodes, defaultPasswordFlag, defaultPassword
    } = props.rowData as any
    formData.value = {
      id, username, nickname, mobile, regionId,
      email, status, regionId, schoolId, roleCodes, defaultPasswordFlag,
      defaultPassword
    }
  }
})

const form = ref()

const emit = defineEmits(['save-complete'])
const saveCompleteEmit = (status: number) => emit('save-complete', status)

const saveSubmit = () => {
  (form.value as FormInstance).validate((valid) => {
    if (valid) {
      const fcn: Function = props.mode === DialogMode.ADD ? apis.UserManage.create : apis.UserManage.update;
      fcn(formData.value).then(() => {
        saveCompleteEmit(1)
        Notification.showSuccessNotify('操作成功')
      }).catch(() => saveCompleteEmit(0))
    } else {
      saveCompleteEmit(0)
    }
  })
}

defineExpose({
  saveSubmit
})

</script>

<template>
  <div class="form-container">
    <el-form ref="form" :model="formData" :rules="rules" label-width="auto">
      <el-form-item label="账户：" prop="username">
        <el-input v-model="formData.username" maxlength="32" placeholder="请输入用户名" show-word-limit></el-input>
      </el-form-item>
      <el-form-item label="真实姓名：" prop="nickname">
        <el-input v-model="formData.nickname" maxlength="32" placeholder="请输入昵称" show-word-limit></el-input>
      </el-form-item>
      <el-form-item label="手机号：" prop="mobile">
        <el-input v-model="formData.mobile" maxlength="11" placeholder="请输入手机号" show-word-limit></el-input>
      </el-form-item>
      <el-form-item label="电子邮箱：" prop="email">
        <el-input v-model="formData.email" maxlength="64" placeholder="请输入邮箱" show-word-limit></el-input>
      </el-form-item>
      <el-form-item label="状态：">
        <ElRadioGroup v-model="formData.status">
          <ElRadio :value="1">启用</ElRadio>
          <ElRadio :value="0">禁用</ElRadio>
        </ElRadioGroup>
      </el-form-item>
      <template v-if="DialogMode.EDIT===props.mode">
        <el-form-item label="默认密码：">
          <el-text type="info">{{ formData.defaultPassword }}</el-text>
        </el-form-item>
        <el-form-item label="默认密码标识：">
          <el-text :type="formData.defaultPasswordFlag?'primary':'warning'">
            {{ formData.defaultPasswordFlag ? '是' : '否' }}
          </el-text>
        </el-form-item>
      </template>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>

</style>
