<script lang="ts" setup>

import {onMounted, ref} from "vue";
import {apis} from "@/apis";
import {useTableCommons} from "@/commons/TableCommons";
import {useDeptStore, useUserStore} from '@/store'
import {DialogMode} from "@/commons/DialogModel";
import SchoolPeriodSelector from "@/components/SchoolPeriodSelector/index.vue"
import SchoolTypeSelector from "@/components/SchoolTypeSelector/index.vue"
import EditForm from "./components/SchoolForm.vue";
import AddForm from "./components/SchoolForm.vue";
import DetailForm from "./components/SchoolDetail.vue"
import AddMasterForm from "./components/SchoolMasterForm.vue";
import EditMasterForm from "./components/SchoolMasterForm.vue";
import {ElMessageBox} from "element-plus";
import Notification from "@/commons/Notification";

const userStore = useUserStore()
let currentRegionId = userStore.regionId || '130102'
const deptStore = useDeptStore()

if (deptStore.topDept?.id == deptStore.currentDept?.id) {
  currentRegionId = '130102'
}


const search = ref({
  pageNumber: 1,
  pageSize: 10,
  keywords: '',
  regionId: currentRegionId,
  schoolId: '',
  schoolType: null,
  schoolPeriod: null
})

const getTableData = async () => {
  console.log('search.value', search.value)
  const response = await apis.SchoolManage.getPageData(search.value, search.value.regionId)
  return response.data
}

const regionList = ref<any>(useDeptStore().regionList)

let {
  loading,
  dialogModel,
  showFormDialog,
  tableData,
  searchSubmit,
  pageOrSizeChange
} = useTableCommons(search, getTableData)

onMounted(async () => {
  if (regionList.value.length == 0) {
    await useDeptStore().setRegionList()
    regionList.value = useDeptStore().regionList
  }
  await searchSubmit()
})

// 表单部分
const addForm = ref()
const editForm = ref()
const editMasterForm = ref()
const addMasterForm = ref()
const saveSubmit = async () => {
  dialogModel.value.saveLoading = true
  if (dialogModel.value.formName === 'AddForm') {
    addForm.value.saveSubmit()
  } else if (dialogModel.value.formName === 'EditForm') {
    editForm.value.saveSubmit()
  } else if (dialogModel.value.formName === 'EditMasterForm') {
    editMasterForm.value.saveSubmit()
  } else if (dialogModel.value.formName === 'AddMasterForm') {
    addMasterForm.value.saveSubmit()
  }
}

const showDeleteDialog = async (master: any) => {
  ElMessageBox.confirm(`您确定删除【${master.nickname}】吗？`, "温馨提示", {type: "warning"})
    .then(async () => {
      await apis.UserManage.delete(master.id)
      await searchSubmit()
      Notification.showSuccessNotify("操作成功")
    })
}

const showResetPasswordDialog = async (master: any) => {
  ElMessageBox.confirm(`您确定重置账户【${master.nickname}】的密码吗？`, "温馨提示",
    {type: "warning"})
    .then(async () => {
      await apis.UserManage.resetPassword(master.id)
      Notification.showSuccessNotify("操作成功")
    })
}

</script>

<template>
  <div>
    <div class="lmt-operation-container">
      <div class="lmt-operation">
        <el-button v-has-perm="['school:add']" icon="Plus" type="primary"
                   @click="showFormDialog('新增','AddForm',{regionId: search.regionId},DialogMode.ADD,'1000px')">新增
        </el-button>
        <el-button v-has-perm="['school:export:account']" icon="Download" type="info" @click="apis.SchoolManage.downloadAdminAccounts(search,currentRegionId)">
          导出学校管理员账号
        </el-button>
      </div>
      <div class="lmt-search">
        <el-select v-if="userStore.userInfo?.cityAdminFlag" v-model="search.regionId" :clearable="false"
                   placeholder="请选择所属区县" style="width: 140px">
          <el-option v-for="item in regionList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <SchoolTypeSelector v-model="search.schoolType" show-type="select"
                            style="width: 140px!important;"></SchoolTypeSelector>
        <SchoolPeriodSelector v-model="search.schoolPeriod" show-type="select"
                              style="width: 140px!important;"></SchoolPeriodSelector>
        <el-input v-model="search.keywords" clearable placeholder="学校名称/学校代码" @clear="searchSubmit">
          <template #append>
            <el-button icon="Search" @click="searchSubmit"></el-button>
          </template>
        </el-input>
      </div>
    </div>
    <el-table v-loading="loading" :data="tableData.records" border max-height="calc(100vh - var(--lmt-header-height) - var(--lmt-main-padding) * 2 - 6rem)"
              stripe>
      <!--行号-->
      <el-table-column align="center" label="序号" type="index" width="80">
        <template #default="scope">
          {{ (search.pageNumber - 1) * search.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="学校名称" min-width="160" prop="name" show-overflow-tooltip></el-table-column>
      <el-table-column label="学校代码" min-width="160" prop="code" show-overflow-tooltip></el-table-column>
      <el-table-column label="联系人" min-width="120" prop="contacts"></el-table-column>
      <el-table-column label="联系电话" prop="telephone" width="120"></el-table-column>
      <el-table-column label="所属区县" min-width="100" prop="regionName" show-overflow-tooltip></el-table-column>
      <el-table-column label="学段" prop="period" show-overflow-tooltip width="120">
        <template #default="{row}">
          <SchoolPeriodSelector v-model="row.period" showType="label"></SchoolPeriodSelector>
        </template>
      </el-table-column>
      <el-table-column label="办学性质" min-width="100" prop="schoolType" show-overflow-tooltip>
        <template #default="{row}">
          <SchoolTypeSelector v-model="row.schoolType" showType="label"></SchoolTypeSelector>
        </template>
      </el-table-column>
      <el-table-column label="管理员" min-width="250" prop="masterList" show-overflow-tooltip>
        <template #default="{row}">
          <ElSpace wrap>
            <el-text v-for="(item, index) in row.masterList" v-if="row.masterList.length" :key="index" type="info">
              {{ item.nickname }}({{ item.username }})
              <el-link v-has-perm="['user:edit']" icon="Edit" type="warning"
                       @click="showFormDialog('修改管理员','EditMasterForm',item,DialogMode.EDIT)"></el-link>
              <el-link icon="Delete" type="danger" @click="showDeleteDialog(item)"></el-link>
              <el-link icon="Refresh" type="warning" @click="showResetPasswordDialog(item)"></el-link>
              <el-link v-if="index == row.masterList.length - 1" icon="Plus" type="primary"
                       @click="showFormDialog('新增管理员','AddMasterForm',row,DialogMode.ADD)"></el-link>
            </el-text>
            <el-text v-else>
              <el-link icon="Plus" type="primary"
                       @click="showFormDialog('新增管理员','AddMasterForm',row,DialogMode.ADD)"></el-link>
            </el-text>
          </ElSpace>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" min-width="210">
        <template #default="scope">
          <el-link icon="View" type="primary"
                   @click="showFormDialog('详情','DetailForm',scope.row,DialogMode.DETAIL,'1000px')">详情
          </el-link>
          <el-link v-has-perm="['school:edit']" icon="Edit" type="warning"
                   @click="showFormDialog('修改','EditForm',scope.row,DialogMode.EDIT,'1000px')">修改
          </el-link>
          <el-link v-has-perm="['school:export:account']" icon="Download" type="info"
                   @click="apis.SchoolManage.downloadAdminAccounts({schoolId: scope.row.id},scope.row.regionId)">导出管理员账号
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-model:current-page="search.pageNumber" v-model:page-size="search.pageSize"
                   :page-sizes="[10, 20, 30, 40, 50, 100]" :total="Number(tableData.total)"
                   background
                   class="m-t-4 float-right" layout="total, prev, pager, next, sizes, ->" @change="pageOrSizeChange">
    </el-pagination>
    <el-dialog v-model="dialogModel.visible" :close-on-click-modal="false" :title="dialogModel.title"
               :width="dialogModel.width"
               destroy-on-close @closed="dialogModel.closed()">
      <AddForm v-if="dialogModel.formName == 'AddForm'" ref="addForm" :mode="dialogModel.mode"
               :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <EditForm v-if="dialogModel.formName == 'EditForm'" ref="editForm" :mode="dialogModel.mode"
                :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <DetailForm v-if="dialogModel.formName == 'DetailForm'" ref="detailForm" :mode="dialogModel.mode"
                  :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <EditMasterForm v-if="dialogModel.formName == 'EditMasterForm'" ref="editMasterForm"
                      :mode="dialogModel.mode"
                      :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <AddMasterForm v-if="dialogModel.formName == 'AddMasterForm'" ref="addMasterForm"
                     :mode="dialogModel.mode"
                     :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <template #footer>
        <el-button @click="dialogModel.visible=false">
          {{ dialogModel.formName !== 'DetailForm' ? '取 消' : '关 闭' }}
        </el-button>
        <el-button v-if="dialogModel.formName!=='DetailForm'" :loading="dialogModel.saveLoading" type="primary"
                   @click="saveSubmit">确 认
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>

</style>
