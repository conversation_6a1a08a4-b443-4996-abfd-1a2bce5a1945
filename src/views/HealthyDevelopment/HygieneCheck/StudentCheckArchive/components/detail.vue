<script setup lang="ts">
import {onMounted, ref} from "vue"
import type {FormInstance} from "element-plus";
import {DialogMode} from "@/commons/DialogModel";
import {apis} from "@/apis";
import Notification from "@/commons/Notification";

const activeNames = ref(['0'])
onMounted(() => {

})
const handleChange = () => {

}
const termlist = ref([
  {
    key: 0,
    name: '2018-2019学年第一学期',
    date: '2018/02/25'
  },
  {
    key: 1,
    name: '2018-2019学年第二学期',
    date: '2018/07/25'
  },
])
const saveSubmit = () => {
  apis.StudentManage.export()
}
</script>

<template>
  <div class="form-container">
    <div class="mx-5 border-rd-lg overflow-hidden">
      <div class="flex-center py-5 font-size-5 font-bold bg-#ddd">
        学生体检档案
      </div>
      <div>
        <div class="p-t-4 p-b-2 font-bold">基本信息</div>
        <table border class="border w-full border-collapse text-align-center">
          <tr class="w-full h-15">
            <td class="font-bold">姓名</td>
            <td>123</td>
            <td class="font-bold">身份证号</td>
            <td>456</td>
            <td class="font-bold">学籍号</td>
            <td>789</td>
          </tr>
          <tr class="w-full h-15">
            <td class="font-bold">性别</td>
            <td>123</td>
            <td class="font-bold">出生日期</td>
            <td>456</td>
            <td class="font-bold">民族</td>
            <td>789</td>
          </tr>
          <tr class="w-full h-15">
            <td class="font-bold">所在学校</td>
            <td>123</td>
            <td class="font-bold">出所在班级</td>
            <td>456</td>
            <td class="font-bold">监护人</td>
            <td>789</td>
          </tr>
          <tr class="w-full h-15">
            <td class="font-bold">联系电话</td>
            <td>123</td>
            <td class="font-bold">家庭住址</td>
            <td colspan="3">789</td>
          </tr>
        </table>
      </div>
      <div>
        <div class="p-t-4 p-b-2 font-bold">体检信息</div>
        <div class="info">
          <el-collapse v-model="activeNames" @change="handleChange">
            <el-collapse-item v-for="(item, index) in termlist" :title="item.name+'（归档日期：'+item.date+'）'" :name="index">
              <table border class="border w-full border-collapse text-align-center m-t-2 color-#606266">
<!--                视力-->
                <tr class="w-full h-15">
                  <td class="w-10% font-bold" rowspan="6">眼科</td>
                  <td class="w-25% font-bold">左眼裸眼视力</td>
                  <td>456</td>
                  <td class="w-25% font-bold">右眼裸眼视力</td>
                  <td>456</td>
                </tr>
                <tr class="w-full h-15">
                  <td class="w-25% font-bold">左眼球镜S</td>
                  <td>456</td>
                  <td class="w-25% font-bold">右眼球镜S</td>
                  <td>456</td>
                </tr>
                <tr class="w-full h-15">
                  <td class="w-25% font-bold">左眼柱镜C</td>
                  <td>456</td>
                  <td class="w-25% font-bold">右眼柱镜C</td>
                  <td>456</td>
                </tr>
                <tr class="w-full h-15">
                  <td class="w-25% font-bold">左眼轴位A</td>
                  <td>456</td>
                  <td class="w-25% font-bold">右眼轴位A</td>
                  <td>456</td>
                </tr>
                <tr class="w-full h-15">
                  <td class="w-25% font-bold">左眼屈光度</td>
                  <td>456</td>
                  <td class="w-25% font-bold">右眼屈光度</td>
                  <td>456</td>
                </tr>
                <tr class="w-full h-15">
                  <td class="font-bold">是否为角膜塑形镜（OK镜）佩戴者</td>
                  <td>456</td>
                </tr>
<!--                形态指标-->
                <tr class="w-full h-15">
                  <td class="w-10% font-bold" rowspan="2">形态指标</td>
                  <td class="w-25% font-bold">身高（cm）</td>
                  <td>456</td>
                  <td class="w-25% font-bold">体重（kg）</td>
                  <td>456</td>
                </tr>
                <tr class="w-full h-15">
                  <td class="w-25% font-bold">腰围（cm）</td>
                  <td>456</td>
                  <td class="w-25% font-bold">臀围（cm）</td>
                  <td>456</td>
                </tr>
<!--                内科-->
                <tr class="w-full h-15">
                  <td class="w-10% font-bold" rowspan="3">内科</td>
                  <td class="w-25% font-bold">心</td>
                  <td>456</td>
                  <td class="w-25% font-bold">肺</td>
                  <td>456</td>
                </tr>
                <tr class="w-full h-15">
                  <td class="w-25% font-bold">肝</td>
                  <td>456</td>
                  <td class="w-25% font-bold">脾</td>
                  <td>456</td>
                </tr>
                <tr class="w-full h-15">
                  <td class="w-25% font-bold">血压</td>
                  <td>456</td>
                  <td class="w-25% font-bold">肺活量</td>
                  <td>456</td>
                </tr>
<!--                外科-->
                <tr class="w-full h-15">
                  <td class="w-10% font-bold" rowspan="3">外科</td>
                  <td class="w-25% font-bold">头部</td>
                  <td>456</td>
                  <td class="w-25% font-bold">颈部</td>
                  <td>456</td>
                </tr>
                <tr class="w-full h-15">
                  <td class="w-25% font-bold">脊柱</td>
                  <td>456</td>
                  <td class="w-25% font-bold">四肢</td>
                  <td>456</td>
                </tr>
                <tr class="w-full h-15">
                  <td class="w-25% font-bold">皮肤</td>
                  <td>456</td>
                  <td class="w-25% font-bold">淋巴结</td>
                  <td>456</td>
                </tr>
<!--                耳鼻喉-->
                <tr class="w-full h-15">
                  <td class="w-10% font-bold" rowspan="3">耳鼻喉</td>
                  <td class="w-25% font-bold">听力</td>
                  <td>456</td>
                  <td class="w-25% font-bold">外耳道与鼓膜</td>
                  <td>456</td>
                </tr>
                <tr class="w-full h-15">
                  <td class="w-25% font-bold">外鼻</td>
                  <td>456</td>
                  <td class="w-25% font-bold">嗅觉</td>
                  <td>456</td>
                </tr>
                <tr class="w-full h-15">
                  <td class="w-25% font-bold">扁桃体</td>
                  <td>456</td>
                </tr>
<!--                口腔-->
                <tr class="w-full h-15">
                  <td class="w-10% font-bold" rowspan="1">口腔</td>
                  <td class="w-25% font-bold">牙齿</td>
                  <td>456</td>
                  <td class="w-25% font-bold">牙周</td>
                  <td>456</td>
                </tr>
<!--                结核病检查-->
                <tr class="w-full h-15">
                  <td class="w-10% font-bold" rowspan="1">结核病检查</td>
                  <td class="w-25% font-bold">是否检查</td>
                  <td>456</td>
                  <td class="w-25% font-bold">是否发病</td>
                  <td>456</td>
                </tr>
<!--                其他-->
                <tr class="w-full h-15">
                  <td class="w-10% font-bold" rowspan="2">其他</td>
                  <td class="w-25% font-bold">丙氨酸氨基转移酶</td>
                  <td>456</td>
                  <td class="w-25% font-bold">结合分支杆菌</td>
                  <td>456</td>
                </tr>
              </table>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.info {
  color: #000;
  ::v-deep .el-collapse-item__header {
    background-color: #ddd;
    box-sizing: border-box;
    padding-left: 1rem;
  }
}
</style>
