<script lang="ts" setup>
import {onMounted, ref} from "vue"
import {apis} from "@/apis"
import {useTableCommons} from "@/commons/TableCommons"
import {useDeptStore, useUserStore} from '@/store'
import GradeSelector from "@/components/GradeSelector/index.vue"
import TermSelector from "@/components/TermSelector/index.vue"

const userStore = useUserStore()
let currentRegionId = userStore.regionId || '130102'
const deptStore = useDeptStore()

if (deptStore.topDept?.id == deptStore.currentDept?.id) {
  currentRegionId = '130102'
}

const search = ref({
  pageNumber: 1,
  pageSize: 10,
  keywords: "",
  regionId: currentRegionId,
  gradeId: "",
  termId: "",
  id: "" // 传参，以id为例
})

const props = defineProps({
  rowData: {
    type: [Object, null],
    default: () => {}
  }
})

const getTableData = async () => {
  const response = await apis.StudentManage.getPageData(search.value, search.value.regionId)
  return response.data
}

const regionList = ref<any>(useDeptStore().regionList)

let {
  loading,
  dialogModel,
  showFormDialog,
  tableData,
  searchSubmit,
  pageOrSizeChange
} = useTableCommons(search, getTableData)

onMounted(async () => {
  search.value.id = props.rowData?.id // 获取传参，以id为例
  if (userStore.userInfo?.cityAdminFlag) {
    await useDeptStore().setRegionList();
    regionList.value = useDeptStore().regionList;
  } else if (userStore.userInfo?.countyAdminFlag) {
    regionList.value = [{key: currentRegionId, value: userStore.userInfo?.regionName}]
  }
  await searchSubmit()
})
const emit = defineEmits(['handleBack'])
const handleBack = () => {
  emit('handleBack')
}
</script>

<template>
  <div>
    <div class="lmt-operation-container">
      <div class="lmt-operation">
        <el-button type="default" icon="Back"
                   @click="handleBack">返回
        </el-button>
        <el-button v-has-perm="['xingTai:export']" type="info" icon="Download"
                   @click="apis.StudentManage.export(search, currentRegionId)">导出
        </el-button>
      </div>
      <div class="lmt-search">
        <TermSelector v-model="search.termId" show-type="select" style="min-width: 190px"></TermSelector>
        <el-select v-model="search.regionId" :clearable="false" placeholder="所属区县"
                   v-if="userStore.userInfo?.cityAdminFlag">
          <el-option v-for="item in regionList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <GradeSelector v-model="search.gradeId" show-type="select"></GradeSelector>
        <el-input v-model="search.keywords" clearable placeholder="学校名称" @clear="searchSubmit">
          <template #append>
            <el-button icon="Search" @click="searchSubmit"></el-button>
          </template>
        </el-input>
      </div>
    </div>
    <el-table v-loading="loading" :data="tableData.records" border
              max-height="calc(100vh - var(--lmt-header-height) - var(--lmt-main-padding) * 2 - 6rem)"
              stripe>
      <el-table-column align="center" label="序号" type="index" width="80" fixed>
        <template #default="scope">
          {{ (search.pageNumber - 1) * search.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="学校名称" min-width="120" prop="schoolName" show-overflow-tooltip></el-table-column>
      <el-table-column label="学段" min-width="120" prop="period" show-overflow-tooltip></el-table-column>
      <el-table-column label="所属区县" min-width="120" prop="regionName" show-overflow-tooltip></el-table-column>
      <el-table-column label="年级" min-width="120" prop="gradeName" show-overflow-tooltip></el-table-column>
      <el-table-column label="学生人数" min-width="90" prop="xb"></el-table-column>
      <el-table-column label="男生平均身高" min-width="90" prop="xb"></el-table-column>
      <el-table-column label="男生肥胖率" min-width="90" prop="xb"></el-table-column>
      <el-table-column label="女生平均身高" min-width="90" prop="xb"></el-table-column>
      <el-table-column label="女生肥胖率" min-width="90" prop="xb"></el-table-column>
      <el-table-column label="所属学期" min-width="190" prop="xb"></el-table-column>
    </el-table>
    <el-pagination v-model:current-page="search.pageNumber" v-model:page-size="search.pageSize"
                   :page-sizes="[10, 20, 30, 40, 50, 100]" :total="Number(tableData.total)"
                   background
                   class="m-t-4 float-right" layout="total, prev, pager, next, sizes, ->" @change="pageOrSizeChange">
    </el-pagination>
  </div>
</template>

<style lang="scss" scoped></style>
