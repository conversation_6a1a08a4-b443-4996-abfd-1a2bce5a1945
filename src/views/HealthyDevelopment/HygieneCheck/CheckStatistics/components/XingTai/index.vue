<script lang="ts" setup>
import {onMounted, ref} from "vue"
import {apis} from "@/apis"
import {useTableCommons} from "@/commons/TableCommons"
import {useDeptStore, useUserStore} from '@/store'
import GradeSelector from "@/components/GradeSelector/index.vue"
import TermSelector from "@/components/TermSelector/index.vue"

const userStore = useUserStore()
let currentRegionId = userStore.regionId || '130102'
const deptStore = useDeptStore()

if (deptStore.topDept?.id == deptStore.currentDept?.id) {
  currentRegionId = '130102'
}

const search = ref({
  pageNumber: 1,
  pageSize: 10,
  regionId: currentRegionId,
  gradeId: "",
  termId: ""
})

const getTableData = async () => {
  const response = await apis.StudentManage.getPageData(search.value, search.value.regionId)
  return response.data
}

const regionList = ref<any>(useDeptStore().regionList)

let {
  loading,
  dialogModel,
  showFormDialog,
  tableData,
  searchSubmit,
  pageOrSizeChange
} = useTableCommons(search, getTableData)

onMounted(async () => {
  if (userStore.userInfo?.cityAdminFlag) {
    await useDeptStore().setRegionList();
    regionList.value = useDeptStore().regionList;
  } else if (userStore.userInfo?.countyAdminFlag) {
    regionList.value = [{key: currentRegionId, value: userStore.userInfo?.regionName}]
  }
  await searchSubmit()
})
const emit = defineEmits(['handleDetail'])
const handleDetail = (id: any) => {
  emit('handleDetail', { id })
}
</script>

<template>
  <div>
    <div class="lmt-operation-container">
      <div class="lmt-operation">
        <el-button v-has-perm="['xingTai:export']" type="info" icon="Download"
                   @click="apis.StudentManage.export(search, currentRegionId)">导出
        </el-button>
      </div>
      <div class="lmt-search">
        <TermSelector v-model="search.termId" show-type="select" style="min-width: 190px"></TermSelector>
        <el-select v-model="search.regionId" :clearable="false" placeholder="所属区县"
                   v-if="userStore.userInfo?.cityAdminFlag">
          <el-option v-for="item in regionList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <GradeSelector v-model="search.gradeId" show-type="select"></GradeSelector>
        <el-button icon="Search" type="primary" @click="searchSubmit"></el-button>
      </div>
    </div>
    <el-table v-loading="loading" :data="tableData.records" border
              max-height="calc(100vh - var(--lmt-header-height) - var(--lmt-main-padding) * 2 - 6rem)"
              stripe>
      <el-table-column align="center" label="序号" type="index" width="80" fixed>
        <template #default="scope">
          {{ (search.pageNumber - 1) * search.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="市/区县" min-width="120" prop="xm" fixed></el-table-column>
      <el-table-column label="年级" min-width="120" prop="xjh"></el-table-column>
      <el-table-column label="小学学生视力统计分析" min-width="600">
        <el-table-column label="小学学校数量" min-width="90" prop="xb"></el-table-column>
        <el-table-column label="学生人数" min-width="90" prop="xb"></el-table-column>
        <el-table-column label="男生平均身高" min-width="90" prop="xb"></el-table-column>
        <el-table-column label="男生肥胖率" min-width="90" prop="xb"></el-table-column>
        <el-table-column label="女生平均身高" min-width="90" prop="xb"></el-table-column>
        <el-table-column label="女生肥胖率" min-width="90" prop="xb"></el-table-column>
      </el-table-column>
      <el-table-column label="初中学生视力统计分析" min-width="600">
        <el-table-column label="初中学校数量" min-width="90" prop="xb"></el-table-column>
        <el-table-column label="学生人数" min-width="90" prop="xb"></el-table-column>
        <el-table-column label="男生平均身高" min-width="90" prop="xb"></el-table-column>
        <el-table-column label="男生肥胖率" min-width="90" prop="xb"></el-table-column>
        <el-table-column label="女生平均身高" min-width="90" prop="xb"></el-table-column>
        <el-table-column label="女生肥胖率" min-width="90" prop="xb"></el-table-column>
      </el-table-column>
      <el-table-column label="高中学生视力统计分析" min-width="600">
        <el-table-column label="高中学校数量" min-width="90" prop="xb"></el-table-column>
        <el-table-column label="学生人数" min-width="90" prop="xb"></el-table-column>
        <el-table-column label="男生平均身高" min-width="90" prop="xb"></el-table-column>
        <el-table-column label="男生肥胖率" min-width="90" prop="xb"></el-table-column>
        <el-table-column label="女生平均身高" min-width="90" prop="xb"></el-table-column>
        <el-table-column label="女生肥胖率" min-width="90" prop="xb"></el-table-column>
      </el-table-column>
      <el-table-column label="学生总数" min-width="90" prop="xb"></el-table-column>
      <el-table-column label="男生平均身高" min-width="90" prop="xb"></el-table-column>
      <el-table-column label="男生肥胖率" min-width="90" prop="xb"></el-table-column>
      <el-table-column label="女生平均身高" min-width="90" prop="xb"></el-table-column>
      <el-table-column label="女生肥胖率" min-width="90" prop="xb"></el-table-column>
      <el-table-column label="所属学期" min-width="190" prop="xb"></el-table-column>
      <el-table-column fixed="right" label="操作" min-width="100">
        <template #default="{ row }">
          <el-link icon="View" type="primary"
                   @click="handleDetail(row.id)">详情
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-model:current-page="search.pageNumber" v-model:page-size="search.pageSize"
                   :page-sizes="[10, 20, 30, 40, 50, 100]" :total="Number(tableData.total)"
                   background
                   class="m-t-4 float-right" layout="total, prev, pager, next, sizes, ->" @change="pageOrSizeChange">
    </el-pagination>
  </div>
</template>

<style lang="scss" scoped></style>
