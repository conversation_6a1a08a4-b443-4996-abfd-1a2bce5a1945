<script setup lang="ts">
import {ref} from "vue";
import ShiLi from './components/ShiLi/index.vue'
import ShiLiDetail from './components/ShiLi/detail.vue'
import XingTai from './components/XingTai/index.vue'
import XingTaiDetail from './components/XingTai/detail.vue'
import type {TabsInstance} from 'element-plus'

const tabPosition = ref<TabsInstance['tabPosition']>('left')
const tabs = ref([
  {key: 1, value: '视力统计'},
  {key: 2, value: '形态统计'},
  {key: 3, value: '学生口腔健康情况统计'},
  {key: 4, value: '学生耳鼻喉健康情况统计'},
  {key: 5, value: '学生结核病检查情况统计'},
])
const currentTab = ref<number>(1)
const detailFlag = ref<boolean>(false)
const rowData = ref<object>({})

// 详情
const handleDetail = (params: object) => {
  detailFlag.value = true
  rowData.value = params
}
// 返回
const handleBack = () => {
  detailFlag.value = false
}
// tab点击
const tabClick = () => {
  detailFlag.value = false
}
</script>

<template>
  <div>
    <el-tabs v-model="currentTab" :tab-position="tabPosition" @tab-click="tabClick" style="height: 800px">
      <el-tab-pane v-for="(item, index) in tabs" :label="item.value" :name="item.key" :key="item.key">
        <ShiLi v-if="currentTab === 1 && index === currentTab - 1 && !detailFlag"
                @handleDetail="handleDetail"/>
        <ShiLiDetail v-if="currentTab === 1 && index === currentTab - 1 && detailFlag" :rowData="rowData"
                      @handleBack="handleBack"/>
        <XingTai v-if="currentTab === 2 && index === currentTab - 1 && !detailFlag" @handleDetail="handleDetail"/>
        <XingTaiDetail v-if="currentTab === 2 && index === currentTab - 1 && detailFlag" :rowData="rowData"
                         @handleBack="handleBack"/>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<style lang="scss" scoped>
</style>
