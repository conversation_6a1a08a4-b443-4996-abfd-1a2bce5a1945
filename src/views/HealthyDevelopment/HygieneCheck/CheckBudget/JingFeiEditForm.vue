<script lang="ts" setup>
import {onMounted, ref} from "vue";
import {DialogMode} from "@/commons/DialogModel";
import type {FormInstance} from "element-plus";
import {apis} from "@/apis";
import Notification from "@/commons/Notification";
import type {KeyValue} from "@/commons/KeyValue";

import {useUserStore} from '@/store/modules/user'


const userStore = useUserStore()
const currentRole: String = userStore.role
const isCounty = ref(false)
if (currentRole == 'COUNTY_ADMIN') {
  isCounty.value = true
}

let currentRegionId = userStore.regionId || '130102'


const formData = ref({
  id: '',
  termId: '',
  termName: '',
  studentCount: '',
  zjf: '',
  rjjf: '',
  operator: '',
  operatorTime: ''
})

const props = defineProps({
  rowData: {
    type: [Object, null],
    default: () => {
    }
  },
  mode: {
    type: [String, null],
    default: 'EDIT'
  }
})

const isNumber = (value: string) => /^\d*\.?\d*$/.test(value);

const rules = {
  zjf: [{required: true, message: '请输入总经费', trigger: 'blur'}],
  rjjf: [{required: true, message: '请输入人均经费', trigger: 'blur'}],
  studentCount: [{required: true, message: '请输入学生总数', trigger: 'blur'}]
}

const regionList = ref<Array<KeyValue>>([])

const dataLoading = ref(false)

onMounted(async () => {
  if (props.mode === DialogMode.EDIT) {
    dataLoading.value = true
    formData.value = props.rowData as any
    dataLoading.value = false
  }
})

const emit = defineEmits(['save-complete'])
const saveCompleteEmit = (status: number) => emit('save-complete', status)

const form = ref()
const saveSubmit = () => {
  (form.value as FormInstance).validate((valid) => {
    if (valid) {
      const fcn: Function = apis.JingFei.update;
      fcn(formData.value, currentRegionId).then(() => {
        saveCompleteEmit(1)
        Notification.showSuccessNotify('操作成功')
      }).catch(() => saveCompleteEmit(0))
    } else {
      saveCompleteEmit(0)
    }
  })
}

defineExpose({
  saveSubmit
})
</script>

<template>
  <div class="form-container">
    <el-form ref="form" v-loading="dataLoading" :model="formData" :rules="rules" label-width="auto">
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="学期：">
            {{ formData.termName }}
          </el-form-item>
          <el-form-item label="学生总人数：" prop="studentCount">
            <el-input v-model.trim="formData.studentCount" type="number" maxlength="64" placeholder="请输入学生总人数"
                      show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="总经费：" prop="zjf">
            <el-input v-model.trim="formData.zjf" type="number" maxlength="64" placeholder="请输入总经费"
                      show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="人均经费：" prop="rjjf">
            <el-input v-model.trim="formData.rjjf" type="number" maxlength="32" placeholder="请输入人均经费"
                      show-word-limit></el-input>
          </el-form-item>

        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>

</style>
