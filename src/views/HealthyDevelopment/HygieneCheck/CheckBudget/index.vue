<script lang="ts" setup>
import {onMounted, ref, watch} from "vue";
import {apis} from "@/apis";
import {useTableCommons} from "@/commons/TableCommons";
import {useDeptStore, useUserStore} from '@/store'
import {DialogMode} from "@/commons/DialogModel";
import SchoolPeriodSelector from "@/components/SchoolPeriodSelector/index.vue"
import type { KeyValue } from "@/commons/KeyValue";
import TermSelector from "@/components/TermSelector/index.vue";
import EditForm from "./JingFeiEditForm.vue";


const userStore = useUserStore()
const deptStore = useDeptStore()
const userInfo = userStore.userInfo;
let currentRegionId = userStore.regionId || '130102'

if (deptStore.topDept?.id == deptStore.currentDept?.id) {
  currentRegionId = '130102'
}

const search = ref({
  pageNumber: 1,
  pageSize: 10,
  keywords: "",
  termId: "",
  regionId: currentRegionId,
  schoolId: "",
  period: "",
  gradeId: "",
  clazzId: ""
})

const getTableData = async () => {
  const response = await apis.JingFei.getPageData(search.value, search.value.regionId)
  for (let i = 0; i < response.data.records.length; i++) {
    const item = response.data.records[i];
    item.defaultPasswordView = "********";
    item.showPasswordView = true;
  }
  return response.data
}

// 区县列表
const regionList = ref<any>(useDeptStore().regionList)
//学校列表
const schoolList = ref<Array<KeyValue>>([])
//年纪列表
const gradeList = ref<Array<KeyValue>>([])
// 学期列表
const termList = ref<any>([])
// 班级列表
const clazzList = ref<any>([])


let {
  loading,
  dialogModel,
  showFormDialog,
  tableData,
  searchSubmit,
  pageOrSizeChange
} = useTableCommons(search, getTableData)

onMounted(async () => {
  if (userStore.userInfo?.cityAdminFlag) {
    await useDeptStore().setRegionList();
    regionList.value = useDeptStore().regionList;
  } else if (userStore.userInfo?.countyAdminFlag) {
    regionList.value = [{key: currentRegionId, value: userStore.userInfo?.regionName}]
  } else if (userStore.userInfo?.schoolAdminFlag) {
    search.value.schoolId = userStore.userInfo?.schoolId
  }
  gradeList.value = (await apis.Commons.getGradeSelectList()).data;
  await regionChange()
  await searchSubmit()
})
watch(() => search.value.regionId,
  (newValues, oldValues) => {
    if (newValues != oldValues) {
      currentRegionId = newValues
      regionChange();
    }
  });

const regionChange = async () => {
  if(userStore.userInfo?.cityAdminFlag || userStore.userInfo?.countyAdminFlag) {
    search.value.schoolId = ''
    schoolList.value = (await apis.Commons.getSchoolSelectListCounty(search.value.regionId)).data;
  }
}

// 表单部分
const addForm = ref()
const editForm = ref()
const editMasterForm = ref()
const addMasterForm = ref()
const saveSubmit = async () => {
  dialogModel.value.saveLoading = true
  if (dialogModel.value.formName === 'AddForm') {
    addForm.value.saveSubmit()
  } else if (dialogModel.value.formName === 'EditForm') {
    editForm.value.saveSubmit()
  } else if (dialogModel.value.formName === 'EditMasterForm') {
    editMasterForm.value.saveSubmit()
  } else if (dialogModel.value.formName === 'AddMasterForm') {
    addMasterForm.value.saveSubmit()
  }
}

</script>

<template>
  <div>
    <div class="lmt-operation-container">
      <div class="lmt-search">
        <TermSelector v-model="search.termId" show-type="select" style="min-width: 190px"></TermSelector>
        <el-button icon="Search" @click="searchSubmit"></el-button>
      </div>
    </div>
    <el-table v-loading="loading" :data="tableData.records" border max-height="calc(100vh - var(--lmt-header-height) - var(--lmt-main-padding) * 2 - 6rem)"
              stripe>
      <el-table-column align="center" label="序号" type="index" width="80" fixed>
        <template #default="scope">
          {{ (search.pageNumber - 1) * search.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="所属学期" min-width="180" prop="termName" fixed header-align="center"></el-table-column>
      <el-table-column label="学生人数(人)" min-width="180" prop="studentCount"></el-table-column>
      <el-table-column label="体检总经费(元)" min-width="120" prop="zjf"></el-table-column>
      <el-table-column label="人均体检费用" min-width="120" prop="rjjf"></el-table-column>
      <el-table-column label="操作人" min-width="120" prop="operator"></el-table-column>
      <el-table-column label="操作时间" min-width="120" prop="operatorTime"></el-table-column>
      <el-table-column label="操作" min-width="120" prop="jb">
        <template #default="scope">
          <el-link icon="Edit" type="warning"
                   @click="showFormDialog('修改','EditForm',scope.row,DialogMode.EDIT,'1000px')">编辑
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-model:current-page="search.pageNumber" v-model:page-size="search.pageSize"
                   :page-sizes="[10, 20, 30, 40, 50, 100]" :total="Number(tableData.total)"
                   background
                   class="m-t-4 float-right" layout="total, prev, pager, next, sizes, ->" @change="pageOrSizeChange">
    </el-pagination>
    <el-dialog v-model="dialogModel.visible" :close-on-click-modal="false" :title="dialogModel.title"
               :width="dialogModel.width"
               destroy-on-close @closed="dialogModel.closed()">
      <EditForm v-if="dialogModel.formName == 'EditForm'" ref="editForm" :mode="dialogModel.mode"
                :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <template #footer>
        <el-button @click="dialogModel.visible=false">
          {{ dialogModel.formName !== 'DetailForm' ? '取 消' : '关 闭' }}
        </el-button>
        <el-button v-if="dialogModel.formName!=='DetailForm'" :loading="dialogModel.saveLoading" type="primary"
                   @click="saveSubmit">确 认
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped></style>
