<script setup lang="ts">
import {onMounted, ref} from "vue";
// import {apis} from "@/apis";
// import {useTableCommons} from "@/commons/TableCommons";
// import {useDeptStore, useUserStore} from '@/store'
// import {DialogMode} from "@/commons/DialogModel";
// import SchoolPeriodSelector from "@/components/SchoolPeriodSelector/index.vue"
// import {ElMessageBox, ElNotification} from "element-plus";
// import type { KeyValue } from "@/commons/KeyValue";
import ShiLi from './components/ShiLi/index.vue'
import XingTai from './components/XingTai/index.vue'
import QiTa from './components/QiTa/index.vue'
import KouQiang from './components/KouQiang/index.vue'
import JieHeBing from './components/JieHeBing/index.vue'
import NeiKe from './components/NeiKe/index.vue'
import WaiKe from './components/Waike/index.vue'
import ErBiHou from './components/ErBiHou/index.vue'
import type { TabsInstance } from 'element-plus'
const tabPosition = ref<TabsInstance['tabPosition']>('left')
const tabs = ref([
  {key: 1, value: '视力'},
  {key: 2, value: '形态指标'},
  {key: 3, value: '内科'},
  {key: 4, value: '外科'},
  {key: 5, value: '耳鼻喉'},
  {key: 6, value: '口腔'},
  {key: 7, value: '结核病检查情况'},
  {key: 8, value: '其他'},
])
const currentTab = ref<number>(1)

onMounted(async () => {
  console.log(currentTab.value)
})
</script>

<template>
  <div>
    <el-tabs v-model="currentTab" :tab-position="tabPosition" style="height: 800px">
      <el-tab-pane v-for="(item, index) in tabs" :label="item.value" :name="item.key" :key="item.key">
        <ShiLi v-if="currentTab === 1 && index === currentTab - 1"></ShiLi>
        <XingTai v-if="currentTab === 2 && index === currentTab - 1"></XingTai>
        <NeiKe v-if="currentTab === 3 && index === currentTab - 1"></NeiKe>
        <WaiKe v-if="currentTab === 4 && index === currentTab - 1"></WaiKe>
        <ErBiHou v-if="currentTab === 5 && index === currentTab -1"></ErBiHou>
        <KouQiang v-if="currentTab === 6 && index === currentTab - 1"></KouQiang>
        <JieHeBing v-if="currentTab === 7 && index === currentTab - 1"></JieHeBing>
        <QiTa v-if="currentTab === 8 && index === currentTab - 1"></QiTa>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<style lang="scss" scoped>
</style>
