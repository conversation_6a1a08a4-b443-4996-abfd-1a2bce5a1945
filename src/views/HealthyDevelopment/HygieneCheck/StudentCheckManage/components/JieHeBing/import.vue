<script setup lang="ts">
import { ref ,onMounted} from 'vue'
import { apis } from '@/apis'
import {ElMessageBox, ElNotification} from "element-plus";
const fileSize = ref<any[]>([])
const errorMessages = ref<Array<{rowIndex: number; message: string}>>([])
const props = defineProps({
  regionId: {
    type: [String, null],
    default: ''
  },
  mode: {
    type: [String, null],
    default: 'import'
  }
})
const fileList = ref<any[]>([])
const downloadTemplateFile = async () => {
  await apis.StudentJieHeBing.exportTemplate(null,String(props.regionId)).then(() => {
     // ElNotification.success({message: "下载成功", title: "温馨提示"})
  }).catch(()=>{
    ElNotification.error({message: "下载失败", title: "温馨提示"})
  })
}

// 定义关闭对话框的方法
const closeDetail = async () => {
  // 发出close事件，通知父组件关闭对话框
  emit('closeDetail')
}
const emit = defineEmits(['save-complete','closeDetail'])
const saveCompleteEmit = (status: number) => emit('save-complete', status)


const saveSubmit = async () => {
   if (fileSize.value.length === 0) {
    ElNotification.error({message: "请选择文件", title: "温馨提示"})
     saveCompleteEmit(0)
    return
  }else{
    const file = fileSize.value[0]
    const formData = new FormData()
    formData.append('file', file.raw || file)
    await apis.StudentJieHeBing.import(formData, props.regionId).then((res: any) => {
        if(res.data.length>0){
            errorMessages.value = res.data
          saveCompleteEmit(0)
        }else{
            ElNotification.success({message: "导入成功", title: "温馨提示"})
             saveCompleteEmit(1)
        }
    }).catch(()=>{
      ElNotification.error({message: "导入失败", title: "温馨提示"})
    })
  }

}
const onRemove = (file: any, fileList: any) => {
    errorMessages.value = []
    // 清空文件列表
    fileList.value = []
}
const onExceed = (files: any, fileList: any) => {
    ElNotification.warning({message: `最多只能上传 1 个文件`, title: "温馨提示"})
}
const onChange = (file: any, fileList: any) => {
  let raw = file.raw;
    let fileTp =
        raw.type == "application/vnd.ms-excel" ||
        raw.type == "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    if (!fileTp) {
    ElNotification.error({message: `只能上传excel格式文件`, title: "温馨提示"})
    }else {
        if (file.size > 5 * 1024 * 1024) {
            ElNotification.warning({message: `上传限制文件大小不能大于5M`, title: "温馨提示"});
        }
        // 更新文件列表
        fileSize.value = [file.raw];
    }
}

defineExpose({
  saveSubmit
})
</script>
<template>
  <div class="form-container">
    <el-form label-width="auto">
        <el-form-item label="模板文件">
            <el-button type="info" @click="downloadTemplateFile" icon="download" size="small">下载模板</el-button>
        </el-form-item>
        <el-form-item label="选择文件" prop="file">
            <el-upload ref="upload" accept=".xlsx,.xls" action="" :file-list="fileList" :auto-upload="false" :limit="1"
                :on-remove="onRemove" :on-exceed="onExceed" :on-change="onChange">

                <el-button size="small" type="primary" icon="upload">选择文件</el-button>
                <div slot="tip" class="warning-desc-text">
                    只能上传excel文件，且不超过5M
                </div>
            </el-upload>
        </el-form-item>
        <el-form-item label="错误信息"  v-if= "errorMessages.length > 0">
            <div style="max-height: 300px; overflow-y: auto">
                <div v-for="(item, index) in errorMessages" :key="index">
                    <div class="error-desc-text">
                        {{ index + 1 }}、第{{ item.rowIndex }}行：{{ item.message }}
                    </div>
                </div>
            </div>
        </el-form-item>
    </el-form>
  </div>
</template>
