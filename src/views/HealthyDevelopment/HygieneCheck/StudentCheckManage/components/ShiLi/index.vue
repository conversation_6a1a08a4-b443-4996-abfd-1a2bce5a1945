<script lang="ts" setup>
import {onMounted, ref, watch} from "vue"
import {apis} from "@/apis"
import {useTableCommons} from "@/commons/TableCommons"
import {useDeptStore, useUserStore} from '@/store'
import {DialogMode} from "@/commons/DialogModel"
import SchoolPeriodSelector from "@/components/SchoolPeriodSelector/index.vue"
import GradeSelector from "@/components/GradeSelector/index.vue"
import TermSelector from "@/components/TermSelector/index.vue"
import ImportForm from "./import.vue"
import type {KeyValue} from "@/commons/KeyValue"


const userStore = useUserStore()
let currentRegionId = userStore.regionId || '130102'
const deptStore = useDeptStore()

if (deptStore.topDept?.id == deptStore.currentDept?.id) {
  currentRegionId = '130102'
}

const search = ref({
  pageNumber: 1,
  pageSize: 10,
  keywords: "",
  regionId: currentRegionId,
  schoolId: "",
  period: "",
  gradeId: "",
  clazzId: "",
  termId: ""
})

const getTableData = async () => {
  const response = await apis.TiJianShiLi.getPageData(search.value, search.value.regionId)
  return response.data
}

const regionList = ref<any>(useDeptStore().regionList)
const schoolList = ref<Array<KeyValue>>([])
const clazzList = ref<Array<KeyValue>>([])

let {
  loading,
  dialogModel,
  showFormDialog,
  tableData,
  searchSubmit,
  pageOrSizeChange
} = useTableCommons(search, getTableData)

onMounted(async () => {
  if (userStore.userInfo?.cityAdminFlag) {
    await useDeptStore().setRegionList();
    regionList.value = useDeptStore().regionList;
  } else if (userStore.userInfo?.countyAdminFlag) {
    regionList.value = [{key: currentRegionId, value: userStore.userInfo?.regionName}]
  } else if (userStore.userInfo?.schoolAdminFlag) {
    search.value.schoolId = userStore.userInfo?.schoolId
  }
  await regionChange()
  await searchSubmit()
  console.log(search.value)
})
watch(() => search.value.regionId,
  (newValues, oldValues) => {
    if (newValues != oldValues) {
      currentRegionId = newValues
      regionChange();
    }
  });

const regionChange = async () => {
  if (userStore.userInfo?.cityAdminFlag || userStore.userInfo?.countyAdminFlag) {
    search.value.schoolId = ''
    schoolList.value = (await apis.Commons.getSchoolSelectListCounty(search.value.regionId)).data;
  }
}

// 获取班级列表
const getClazzList = async () => {
  search.value.clazzId = ''
  clazzList.value = []
  if (search.value.schoolId && search.value.gradeId && search.value.period) {
    clazzList.value = (await apis.Commons.getClazzSelectList(search.value.schoolId, search.value.gradeId, search.value.period, search.value.regionId)).data;
  }
}

// 表单部分
const importForm = ref()
const saveSubmit = async () => {
  dialogModel.value.saveLoading = true
  if (dialogModel.value.formName === 'ImportForm') {
    importForm.value.saveSubmit()
  }
}
</script>

<template>
  <div>
    <div class="lmt-operation-container">
      <div class="lmt-operation">
        <el-button v-has-perm="['shiLi:import']" type="primary" icon="Upload"
                   @click="showFormDialog('导入','ImportForm',{},DialogMode.IMPORT,'1000px')">导入
        </el-button>
        <el-button v-has-perm="['shiLi:export']" type="info" icon="Download"
                   @click="apis.TiJianShiLi.export(search, currentRegionId)">导出
        </el-button>
      </div>
      <div class="lmt-search">
        <TermSelector v-model="search.termId" show-type="select" style="min-width: 190px"></TermSelector>
        <el-select v-model="search.regionId" :clearable="false" placeholder="所属区县" @change="regionChange"
                   v-if="!(userStore.userInfo?.countyAdminFlag || userStore.userInfo?.schoolAdminFlag)">
          <el-option v-for="item in regionList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <el-select v-model="search.schoolId" clearable placeholder="所属学校" @change="getClazzList"
                   v-if="!userStore.userInfo?.schoolAdminFlag">
          <el-option v-for="item in schoolList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <SchoolPeriodSelector v-model="search.period" show-type="select" from="student"
                              @change="getClazzList"></SchoolPeriodSelector>
        <GradeSelector v-model="search.gradeId" show-type="select" @change="getClazzList"></GradeSelector>
        <el-select v-model="search.clazzId" clearable placeholder="班级">
          <el-option v-for="item in clazzList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <el-input v-model="search.keywords" clearable placeholder="学生姓名/学籍号" @clear="searchSubmit">
          <template #append>
            <el-button icon="Search" @click="searchSubmit"></el-button>
          </template>
        </el-input>
      </div>
    </div>
    <el-table v-loading="loading" :data="tableData.records" border
              max-height="calc(100vh - var(--lmt-header-height) - var(--lmt-main-padding) * 2 - 6rem)"
              stripe>
      <el-table-column align="center" label="序号" type="index" width="80" fixed>
        <template #default="scope">
          {{ (search.pageNumber - 1) * search.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="姓名" min-width="120" prop="xm" fixed header-align="center"></el-table-column>
      <el-table-column label="学籍号" min-width="180" prop="xjh"></el-table-column>
      <el-table-column label="性别" min-width="60" prop="xb">
        <template #default="{row}">
          {{ row.xb && row.xb == 1 ? '男' : row.xb == 2 ? '女' : '' }}
        </template>
      </el-table-column>
      <el-table-column label="所属班级" min-width="120" prop="clazzName" show-overflow-tooltip></el-table-column>
      <el-table-column label="所属学校" min-width="120" prop="schoolName" show-overflow-tooltip></el-table-column>
      <el-table-column label="所属区县" min-width="120" prop="regionName" show-overflow-tooltip></el-table-column>
      <el-table-column label="右眼裸眼视力" min-width="80" prop="xb"></el-table-column>
      <el-table-column label="左眼裸眼视力" min-width="80" prop="xb"></el-table-column>
      <el-table-column label="右眼球镜S" min-width="80" prop="xb"></el-table-column>
      <el-table-column label="右眼柱镜C" min-width="80" prop="xb"></el-table-column>
      <el-table-column label="右眼轴位A" min-width="80" prop="xb"></el-table-column>
      <el-table-column label="右眼屈光度" min-width="80" prop="xb"></el-table-column>
      <el-table-column label="左眼球镜S" min-width="80" prop="xb"></el-table-column>
      <el-table-column label="左眼柱镜C" min-width="80" prop="xb"></el-table-column>
      <el-table-column label="左眼轴位A" min-width="80" prop="xb"></el-table-column>
      <el-table-column label="左眼屈光度" min-width="80" prop="xb"></el-table-column>
      <el-table-column label="是否为角膜塑形镜（OK镜）佩戴者" min-width="140" prop="xb"></el-table-column>
      <el-table-column label="所属学期" min-width="100" prop="xb"></el-table-column>
    </el-table>
    <el-pagination v-model:current-page="search.pageNumber" v-model:page-size="search.pageSize"
                   :page-sizes="[10, 20, 30, 40, 50, 100]" :total="Number(tableData.total)"
                   background
                   class="m-t-4 float-right" layout="total, prev, pager, next, sizes, ->" @change="pageOrSizeChange">
    </el-pagination>
    <el-dialog v-model="dialogModel.visible" :title="dialogModel.title" :width="dialogModel.width" destroy-on-close
               @closed="dialogModel.closed()">
      <ImportForm v-if="dialogModel.formName == 'ImportForm'" ref="importForm" :mode="dialogModel.mode"
                  :regionId="currentRegionId" @save-complete="dialogModel.saveComplete"/>
      <template #footer>
        <el-button @click="dialogModel.visible = false">关 闭</el-button>
        <el-button :loading="dialogModel.saveLoading" type="primary" @click="saveSubmit">确 认</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped></style>
