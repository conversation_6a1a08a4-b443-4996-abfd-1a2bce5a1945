<script lang="ts" setup>
import {onMounted, ref} from "vue";
import {DialogMode} from "@/commons/DialogModel";
import CheckProjectSelector from "@/components/CheckProjectSelector/index.vue"
import type {FormInstance} from "element-plus";
import {apis} from "@/apis";
import Notification from "@/commons/Notification";
import {useDeptStore} from "@/store";
import type { KeyValue } from "@/commons/KeyValue";

const formData = ref({
  id: '',
  deviceId: '',
  deviceName: '',
  regionId: '',
  regionName: '',
  schoolId: '',
  projectName: '',
  location: ''
})

const props = defineProps({
  rowData: {
    type: [Object, null],
    default: () => {
    }
  },
  mode: {
    type: [String, null],
    default: 'EDIT'
  }
})

const rules = {
  deviceId: [
    {required: true, message: '请输入ID', trigger: 'blur'},
    {min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur'}
  ],
  deviceName: [
    {required: true, message: '请输入名称', trigger: 'blur'},
    {min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur'}
  ],
  regionId: [{required: true, message: '请选择区县', trigger: 'change'}],
  schoolId: [{required: true, message: '请选择所属单位', trigger: 'change'}],
  projectName: [{required: true, message: '请选择检测项目', trigger: 'change'}],
  location: [{required: true, message: '请输入放置位置', trigger: 'blur'}],
}

const regionList = ref<Array<KeyValue>>([])

const schoolList = ref<Array<KeyValue>>([])

const dataLoading = ref(false)

onMounted(async () => {
  if (props.mode === DialogMode.EDIT) {
    dataLoading.value = true
    const {
      id, deviceId, deviceName, regionId, regionName, schoolId, projectName, location
    } = props.rowData as any
    formData.value = {id, deviceId, deviceName, regionId, regionName, schoolId, projectName, location}
    schoolList.value = (await apis.Commons.getSchoolSelectListCounty(formData.value.regionId)).data;
    dataLoading.value = false
  } else {
    regionList.value = useDeptStore().regionList
  }
})

const regionChange = async () => {
  formData.value.schoolId = ''
  schoolList.value = (await apis.Commons.getSchoolSelectListCounty(formData.value.regionId)).data;
}

const emit = defineEmits(['save-complete'])
const saveCompleteEmit = (status: number) => emit('save-complete', status)

const form = ref()
const saveSubmit = () => {
  (form.value as FormInstance).validate((valid) => {
    if (valid) {
      const fcn: Function = props.mode === DialogMode.ADD ? apis.DeviceManage.create : apis.DeviceManage.update;
      fcn(formData.value).then(() => {
        saveCompleteEmit(1)
        Notification.showSuccessNotify('操作成功')
      }).catch(() => saveCompleteEmit(0))
    } else {
      saveCompleteEmit(0)
    }
  })
}

defineExpose({
  saveSubmit
})
</script>

<template>
  <div class="form-container">
    <el-form ref="form" v-loading="dataLoading" :model="formData" :rules="rules" label-width="auto">
      <el-row :gutter="40">
        <el-col :offset="6" :span="12">
          <el-form-item label="设备ID：" prop="deviceId">
            <el-input v-model.trim="formData.deviceId" placeholder="请输入设备ID" maxlength="32" show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="设备名称：" prop="deviceName">
            <el-input v-model.trim="formData.deviceName" placeholder="请输入设备名称" maxlength="32" show-word-limit></el-input>
          </el-form-item>
          <template v-if="props.mode === DialogMode.EDIT">
            <el-form-item label="所属区县：" prop="regionId">
              <el-text type="info">{{ formData.regionName }}</el-text>
            </el-form-item>
          </template>
          <template v-if="props.mode === DialogMode.ADD">
            <el-form-item label="所属区县：" prop="regionId">
              <el-select v-model="formData.regionId" clearable placeholder="请选择所属区县" style="width: 100%"
                         @change="regionChange">
                <el-option v-for="item in regionList" :key="item.key" :label="item.value" :value="item.key"></el-option>
              </el-select>
            </el-form-item>
          </template>
          <el-form-item label="所属单位：" prop="schoolId">
            <el-select v-model="formData.schoolId" clearable placeholder="请选择所属单位" style="width: 100%">
              <el-option v-for="item in schoolList" :key="item.key" :label="item.value" :value="item.key"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="检测项目：" prop="projectName">
            <CheckProjectSelector v-model="formData.projectName" show-type="select"></CheckProjectSelector>
          </el-form-item>
          <el-form-item label="放置位置：" prop="location">
            <el-input v-model.trim="formData.location" placeholder="请输入设备名称" maxlength="32" show-word-limit></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<style lang="scss" scoped></style>
