<script lang="ts" setup>
import {computed, onMounted, ref} from 'vue'
import {DialogMode} from "@/commons/DialogModel";
import CheckProjectSelector from "@/components/CheckProjectSelector/index.vue"
import {apis} from "@/apis";
import type { KeyValue } from "@/commons/KeyValue";

const formData = ref({
  id: '',
  deviceId: '',
  deviceName: '',
  regionId: '',
  regionName: '',
  schoolId: '',
  projectName: '',
  location: '',
  onlineStatus: null
})

const props = defineProps({
  rowData: {
    type: [Object, null],
    default: () => {
    }
  },
  mode: {
    type: [String, null],
    default: 'DETAIL'
  }
})

const schoolList = ref<Array<KeyValue>>([])


onMounted(async () => {
  if (props.mode === DialogMode.DETAIL) {
    const {
      id, deviceId, deviceName, regionId, regionName, schoolId, projectName, location, onlineStatus
    } = props.rowData as any
    formData.value = {id, deviceId, deviceName, regionId, regionName, schoolId, projectName, location, onlineStatus}
    schoolList.value = (await apis.Commons.getSchoolSelectListCounty(formData.value.regionId)).data;
  }
})

const schoolName = computed(() => {
  return (schoolId: string) => {
    const target = schoolList.value.find(item => item.key === schoolId)
    return target?.value
  }
})

</script>

<template>
  <div class="form-container">
    <el-form ref="form" :model="formData" label-width="auto">
      <el-row :gutter="40">
        <el-col :offset="6" :span="12">
          <el-form-item label="设备ID：" prop="deviceId">
            <el-text type="info">{{ formData.deviceId }}</el-text>
          </el-form-item>
          <el-form-item label="设备名称：" prop="deviceName">
            <el-text type="info">{{ formData.deviceName }}</el-text>
          </el-form-item>
          <el-form-item label="所属区县：" prop="regionName">
            <el-text type="info">{{ formData.regionName }}</el-text>
          </el-form-item>
          <el-form-item label="所属单位：" prop="schoolId">
            <el-text type="info">{{ schoolName(formData.schoolId) }}</el-text>
          </el-form-item>
          <el-form-item label="检测项目：" prop="projectName">
            <CheckProjectSelector v-model="formData.projectName" show-type="label"></CheckProjectSelector>
          </el-form-item>
          <el-form-item label="放置位置：" prop="location">
            <el-text type="info">{{ formData.location }}</el-text>
          </el-form-item>
          <el-form-item label="在线状态：" prop="onlineStatus">
            <el-text type="info">{{ formData.onlineStatus ? '在线' : '离线' }}</el-text>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
