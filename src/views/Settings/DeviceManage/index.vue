<script lang="ts" setup>
import {onMounted, ref} from "vue";
import {apis} from "@/apis";
import {useTableCommons} from "@/commons/TableCommons";
import {useDeptStore, useUserStore} from '@/store'
import {DialogMode} from "@/commons/DialogModel";
import {ElMessageBox} from "element-plus";
import Notification from "@/commons/Notification";
import AddForm from "./components/DeviceForm.vue";
import EditForm from "./components/DeviceForm.vue";
import DetailForm from "./components/DeviceDetail.vue";
import type { KeyValue } from "@/commons/KeyValue";


const userStore = useUserStore()
let currentRegionId = userStore.regionId || '130102'
const deptStore = useDeptStore()

if (deptStore.topDept?.id == deptStore.currentDept?.id) {
  currentRegionId = '130102'
}

const search = ref({
  pageNumber: 1,
  pageSize: 10,
  keywords: "",
  regionId: currentRegionId,
  schoolId: "",
  sportProjectCode: "",
  onlineStatus: null
})

const projectList = ref<Array<KeyValue>>([])

const onlineStatusList = ref([
  {key: 1, value: '在线', onlineStatus: true},
  {key: 2, value: '离线', onlineStatus: false},
])

const getTableData = async () => {
  const response = await apis.DeviceManage.getPageData(search.value)
  return response.data
}

const regionList = ref<any>(useDeptStore().regionList)

const schoolList = ref<Array<KeyValue>>([])

let {
  loading,
  dialogModel,
  showFormDialog,
  tableData,
  searchSubmit,
  pageOrSizeChange
} = useTableCommons(search, getTableData)

onMounted(async () => {
  if (regionList.value.length == 0) {
    await useDeptStore().setRegionList()
    regionList.value = useDeptStore().regionList
  }
  projectList.value = (await apis.Commons.getSportProjectSelectList()).data;
  await searchSubmit()
})

const projectNameText = (projectCode: String) => {
  const target = projectList.value.find(item => item.key === projectCode)
  return target?.value
}

const regionChange = async () => {
  search.value.schoolId = ''
  schoolList.value = (await apis.Commons.getSchoolSelectListCounty(search.value.regionId)).data;
}

// 表单部分
const addForm = ref()
const editForm = ref()
const saveSubmit = async () => {
  dialogModel.value.saveLoading = true
  if (dialogModel.value.formName === 'AddForm') {
    addForm.value.saveSubmit()
  } else if (dialogModel.value.formName === 'EditForm') {
    editForm.value.saveSubmit()
  }
}

const showDeleteDialog = (rowData: any) => {
  ElMessageBox.confirm(`您确定删除设备【${rowData.deviceName}】吗？`, "温馨提示", {type: "warning"})
    .then(async () => {
      await apis.DeviceManage.delete(rowData.id)
      await searchSubmit()
      Notification.showSuccessNotify("操作成功")
    })
}
</script>

<template>
  <div>
    <div class="lmt-operation-container">
      <div class="lmt-operation">
        <el-button v-has-perm="['device:add']" icon="Plus" type="primary"
                   @click="showFormDialog('新增', 'AddForm', { regionId: search.regionId }, DialogMode.ADD, '800px')">新增
        </el-button>
      </div>
      <div class="lmt-search">
        <el-select v-model="search.regionId" clearable placeholder="请选择所属区县" @change="regionChange">
          <el-option v-for="item in regionList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <el-select v-model="search.schoolId" clearable placeholder="所属单位">
          <el-option v-for="item in schoolList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <el-select v-model="search.sportProjectCode" clearable placeholder="检测项目">
          <el-option v-for="item in projectList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
        <el-select v-model="search.onlineStatus" clearable placeholder="在线状态">
          <el-option v-for="item in onlineStatusList" :key="item.key" :label="item.value"
                     :value="item.onlineStatus"></el-option>
        </el-select>
        <el-input v-model="search.keywords" clearable placeholder="设备名称/ID" @clear="searchSubmit">
          <template #append>
            <el-button icon="Search" @click="searchSubmit"></el-button>
          </template>
        </el-input>
      </div>
    </div>
    <el-table v-loading="loading" :data="tableData.records" border max-height="calc(100vh - var(--lmt-header-height) - var(--lmt-main-padding) * 2 - 6rem)"
              stripe>
      <el-table-column align="center" label="序号" type="index" width="80">
        <template #default="scope">
          {{ (search.pageNumber - 1) * search.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="设备ID" min-width="160" prop="deviceId"></el-table-column>
      <el-table-column label="设备名称" min-width="160" prop="deviceName"></el-table-column>
      <el-table-column label="检测项目" min-width="200" prop="projectName" show-overflow-tooltip>
        <template #default="{ row }">
          {{ projectNameText(row.projectName) }}
        </template>
      </el-table-column>
      <el-table-column label="所属单位" min-width="120" prop="schoolName"></el-table-column>
      <el-table-column label="所属区县" min-width="120" prop="regionName"></el-table-column>
      <el-table-column label="放置位置" min-width="200" prop="location" show-overflow-tooltip></el-table-column>
      <el-table-column label="创建时间" min-width="160" prop="createTime"></el-table-column>
      <el-table-column label="在线状态" min-width="120" prop="onlineStatus">
        <template #default="{ row }">
          {{ row.onlineStatus ? '在线' : '离线' }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" min-width="180">
        <template #default="scope">
          <el-link icon="View" type="primary"
                   @click="showFormDialog('详情', 'DetailForm', scope.row, DialogMode.DETAIL, '800px')">详情
          </el-link>
          <el-link v-has-perm="['device:edit']" icon="Edit" type="warning"
                   @click="showFormDialog('修改', 'EditForm', scope.row, DialogMode.EDIT, '800px')">修改
          </el-link>
          <el-link v-has-perm="['device:delete']" icon="Delete" type="danger" @click="showDeleteDialog(scope.row)">删除
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-model:current-page="search.pageNumber" v-model:page-size="search.pageSize"
                   :page-sizes="[10, 20, 30, 40, 50, 100]" :total="Number(tableData.total)"
                   background
                   class="m-t-4 float-right" layout="total, prev, pager, next, sizes, ->" @change="pageOrSizeChange">
    </el-pagination>
    <el-dialog v-model="dialogModel.visible" :title="dialogModel.title" :width="dialogModel.width" destroy-on-close
               @closed="dialogModel.closed()">
      <AddForm v-if="dialogModel.formName == 'AddForm'" ref="addForm" :mode="dialogModel.mode"
               :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <EditForm v-if="dialogModel.formName == 'EditForm'" ref="editForm" :mode="dialogModel.mode"
                :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <DetailForm v-if="dialogModel.formName == 'DetailForm'" ref="detailForm" :mode="dialogModel.mode"
                  :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <template #footer>
        <el-button @click="dialogModel.visible = false">
          {{ dialogModel.formName !== 'DetailForm' ? '取 消' : '关 闭' }}
        </el-button>
        <el-button v-if="dialogModel.formName !== 'DetailForm'" :loading="dialogModel.saveLoading" type="primary"
                   @click="saveSubmit">确 认
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped></style>
