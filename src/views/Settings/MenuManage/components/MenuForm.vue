<script lang="ts" setup>
import {computed, onMounted, ref} from "vue";
import {DialogMode} from "@/commons/DialogModel";
import {apis} from "@/apis";
import Notification from "@/commons/Notification";

const props = defineProps({
  rowData: {
    type: [Object, null],
    default: () => ({})
  },
  mode: {
    type: [String, null],
    default: "ADD"
  }
})

const formData = ref({
  id: '',
  parentId: null,
  name: '',
  code: '',
  type: '',
  permission: '',
  router: '',
  component: '',
  visible: 1,
  orderNum: ''
})

const rules = computed(() => {
  const result = {
    name: [{required: true, message: '请输入名称', trigger: 'blur'}],
    code: [{required: true, message: '请输入编码', trigger: 'blur'}],
    type: [{required: true, message: '请选择类型', trigger: 'change'}],
    permission: [{required: true, message: '请输入权限标识', trigger: 'blur'}],
    router: [{required: true, message: '请输入路由地址', trigger: 'blur'}],
    component: [{required: true, message: '请输入组件地址', trigger: 'blur'}],
    orderNum: [{required: true, message: '请输入排序字段', trigger: 'blur'}]
  }
  if (formData.value.type === 'F') {
    result.router = []
    result.component = []
  }
  return result
})

const menuList = ref([])
const dataLoading = ref(false)
onMounted(async () => {
  dataLoading.value = true
  if (props.mode === DialogMode.EDIT) {
    formData.value = {...props.rowData} as any
  } else {
    formData.value.parentId = props.rowData?.id || null
    formData.value.type = props.rowData?.type === 'M' ? 'C' : props.rowData?.type === 'C' ? 'F' : 'M'
  }
  menuList.value = (await apis.Commons.getMenuTreeList()).data
  dataLoading.value = true
})

const emit = defineEmits(['save-complete'])
const saveCompleteEmit = (status: number) => emit('save-complete', status)

const form = ref()
const saveSubmit = () => {
  form.value.validate().then(async (valid: boolean) => {
    if (valid) {
      const fn = props.mode === 'ADD' ? apis.MenuManage.create : apis.MenuManage.update
      fn(formData.value).then(() => {
        saveCompleteEmit(1)
        Notification.showSuccessNotify('操作成功')
      }).catch(() => {
        saveCompleteEmit(0)
      })
    }
  })

}
defineExpose({
  saveSubmit
})

</script>

<template>
  <div class="form-container">
    <el-form ref="form" :model="formData" :rules="rules" label-width="auto">
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="父级菜单：">
            <el-cascader v-model="formData.parentId" :options="menuList" :props="{ checkStrictly: true, emitPath: false,label:'name',value:'id' }"
                         placeholder="请选择父级菜单" style="width: 100%;">
            </el-cascader>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="名称：" prop="name">
            <el-input v-model.trim="formData.name" maxlength="32" placeholder="请输入名称" show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="类型：" prop="type">
            <el-select v-model="formData.type" placeholder="请选择类型">
              <el-option label="目录" value="M"></el-option>
              <el-option label="菜单" value="C"></el-option>
              <el-option label="权限项" value="F"></el-option>
            </el-select>
          </el-form-item>

        </el-col>
        <el-col :span="12">
          <el-form-item label="代码：" prop="code">
            <el-input v-model.trim="formData.code" maxlength="32" placeholder="请输入代码" show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="权限标识：" prop="permission">
            <el-input v-model.trim="formData.permission" maxlength="32" placeholder="请输入权限标识"
                      show-word-limit></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="formData.type!=='F'" :gutter="40">
        <el-col :span="12">
          <el-form-item label="路由地址：" prop="router">
            <el-input v-model.trim="formData.router" maxlength="64" placeholder="请输入路由地址"
                      show-word-limit></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="组件地址：" prop="component">
            <el-input v-model.trim="formData.component" maxlength="128" placeholder="请输入组件地址"
                      show-word-limit></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="隐藏/显示：" prop="icon">
            <el-switch v-model="formData.visible" :active-value="1" :inactive-value="0"
                       active-text="显示"
                       inactive-text="隐藏" inline-prompt style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
            ></el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序：" prop="orderNum">
            <el-input-number v-model="formData.orderNum" :max="1000" :min="1" :precision="0"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>

</style>
