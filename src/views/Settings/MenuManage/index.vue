<script lang="ts" setup>
import {onMounted, ref} from "vue";
import {apis} from "@/apis";
import {useTableCommons} from "@/commons/TableCommons";
import {DialogMode} from "@/commons/DialogModel";
import AddForm from "./components/MenuForm.vue";
import EditForm from "./components/MenuForm.vue";
import Notification from "@/commons/Notification";
import {ElMessageBox} from "element-plus";

const search = ref({
  pageNumber: 1,
  pageSize: 10,
  keywords: ''
})

const expandRowKeys = ref<string[]>([])

const getTableData = async () => {
  const response = await apis.MenuManage.getPageData(search.value)
  const parents = response.data.records.filter((item: any) => !item.parentId)
  if (expandRowKeys.value.length == 0) {
    expandRowKeys.value = parents.map((item: any) => item.id)
    parents.forEach((item: any) => {
      const menuList = item.children && item.children.filter((child: any) => child.type === 'M' || child.type === 'C') || []
      expandRowKeys.value.concat(menuList.map((child: any) => child.id))
    })
  }
  return response.data
}

let {
  loading,
  dialogModel,
  showFormDialog,
  tableData,
  searchSubmit,
} = useTableCommons(search, getTableData)

onMounted(async () => {
  await searchSubmit()
})

const addForm = ref()
const editForm = ref()
const saveSubmit = async () => {
  dialogModel.value.saveLoading = true
  const refForm = dialogModel.value.mode === DialogMode.ADD ? addForm.value : editForm.value
  refForm.saveSubmit && refForm.saveSubmit()
}

const showDeleteDialog = async (row: any) => {
  ElMessageBox.confirm(`您确定删除【${row.name}】菜单吗？`, "温馨提示",
    {type: "warning"})
    .then(async () => {
      await apis.MenuManage.delete(row.id)
      Notification.showSuccessNotify("操作成功")
      await searchSubmit()
    })
}

const expandRowChange = (row: any, expanded: boolean) => {
  if (expanded) {
    expandRowKeys.value.push(row.id)
  } else {
    expandRowKeys.value = expandRowKeys.value.filter(item => item !== row.id)
  }
}

</script>

<template>
  <div>
    <div class="lmt-operation-container">
      <div class="lmt-operation">
        <el-button icon="Plus" type="primary" v-has-perm="'menu:add'"
                   @click="showFormDialog('新增','AddForm',null,DialogMode.ADD,'800px')">新增
        </el-button>
      </div>
      <div class="lmt-search">
        <el-input v-model="search.keywords" clearable placeholder="请输入菜单名称/菜单代码" @clear="searchSubmit">
          <template #append>
            <el-button icon="Search" @click="searchSubmit"></el-button>
          </template>
        </el-input>
      </div>
    </div>
    <el-table v-loading="loading" :data="tableData.records" :expand-row-keys="expandRowKeys" border max-height="calc(100vh - var(--lmt-header-height) - var(--lmt-main-padding) * 2 - 4rem)" row-key="id"
              stripe
              @expand-change="expandRowChange">
      <el-table-column label="菜单名称" min-width="100" prop="name" show-overflow-tooltip></el-table-column>
      <el-table-column label="菜单编码" min-width="120" prop="code" show-overflow-tooltip></el-table-column>
      <el-table-column label="类型" min-width="50" prop="type" show-overflow-tooltip>
        <template #default="scope">
          <el-text v-if="scope.row.type=='M'" type="warning">目录</el-text>
          <el-text v-if="scope.row.type=='C'" type="primary">菜单</el-text>
          <el-text v-if="scope.row.type=='F'" type="info">权限项</el-text>
        </template>
      </el-table-column>
      <el-table-column label="展示状态" min-width="50" prop="visible" show-overflow-tooltip>
        <template #default="scope">
          <el-text :type="scope.row.visible === 0?'waring':'success'">
            {{ scope.row.visible === 0 ? '隐藏' : '显示' }}
          </el-text>
        </template>
      </el-table-column>
      <el-table-column label="权限标识" min-width="100" prop="permission" show-overflow-tooltip></el-table-column>
      <el-table-column label="排序" min-width="50" prop="orderNum" show-overflow-tooltip></el-table-column>
      <el-table-column label="路由地址" min-width="160" prop="router" show-overflow-tooltip></el-table-column>
      <el-table-column label="组件地址" min-width="180" prop="component" show-overflow-tooltip></el-table-column>
      <el-table-column v-has-perm="['menu:edit', 'menu:add', 'menu:delete']" label="操作" min-width="100" prop="id"
                       show-overflow-tooltip>
        <template #default="scope">
          <el-link v-has-perm="'menu:edit'" icon="Edit" type="warning"
                   @click="showFormDialog('修改','EditForm',scope.row,DialogMode.EDIT,'800px')">修改
          </el-link>
          <el-link v-if="['M','C'].includes(scope.row.type)" v-has-perm="'menu:add'" icon="Plus" type="primary"
                   @click="showFormDialog('新增','AddForm',scope.row,DialogMode.ADD,'800px')">新增
          </el-link>
          <el-link v-if="scope.row.children==null||scope.row.children.length==0" v-has-perm="'menu:delete'" icon="Delete" type="danger"
                   @click="showDeleteDialog(scope.row)">删除
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog v-model="dialogModel.visible" :close-on-click-modal="false" :title="dialogModel.title" :width="dialogModel.width"
               destroy-on-close draggable @closed="dialogModel.closed">
      <AddForm v-if="dialogModel.formName == 'AddForm'" ref="addForm" :mode="dialogModel.mode"
               :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <EditForm v-if="dialogModel.formName == 'EditForm'" ref="editForm" :mode="dialogModel.mode"
                :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <template #footer>
        <el-button @click="dialogModel.visible=false">
          {{ dialogModel.formName !== 'DetailForm' ? '取 消' : '关 闭' }}
        </el-button>
        <el-button v-if="dialogModel.formName!=='DetailForm'" :loading="dialogModel.saveLoading" type="primary"
                   @click="saveSubmit">确 认
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>

</style>
