<script lang="ts" setup>
import {nextTick, onMounted, ref} from "vue";
import {useTableCommons} from "@/commons/TableCommons";
import {apis} from '@/apis'
import {DialogMode} from "@/commons/DialogModel";
import {ElMessageBox} from "element-plus";
import Notification from "@/commons/Notification";
import AddForm from "./components/RoleForm.vue";
import EditForm from "./components/RoleForm.vue";


const search = ref({
  pageNumber: 1,
  pageSize: 1000,
  keywords: ''
})

interface Permission {
  id: string,
  name: string,
  children: Permission[],
  code: string,
  checked: boolean,
  orderNum: number,
  type: string,
  visible: number
}

const permissions = ref<Permission[]>([])
const currentRole = ref({id: '', roleName: '', permissions: []})
const permissionsLoading = ref(false)
const rolePermissions = ref([])
const defaultCheckKeys = ref<string[]>([])
const saveLoading = ref(false)
const resetLoading = ref(false)

const getTableData = async () => {
  const response = await apis.RoleManage.getPageData(search.value)
  return response.data
}

let {
  loading,
  tableData,
  searchSubmit,
  showFormDialog,
  dialogModel,
} = useTableCommons(search, getTableData)

onMounted(async () => {
  await searchSubmit()
  const response = await apis.MenuManage.getPageData({pageNumber: 1, pageSize: 1000})
  const data = response.data.records
  permissions.value = []
  for (let i = 0; i < data.length; i++) {
    let perm = data[i] as Permission
    initCheckedFalse(perm)
    permissions.value.push(perm)
  }
})

const initCheckedFalse = (perm: Permission) => {
  perm.checked = false
  if (perm.children && perm.children.length > 0) {
    for (const child of perm.children) {
      child.checked = false
      initCheckedFalse(child)
    }
  }
}

const initCheckedByKeys = (perm: Permission, checkIds: string[]) => {
  perm.checked = checkIds.includes(perm.id)
  if (perm.children && perm.children.length > 0) {
    for (const child of perm.children) {
      initCheckedByKeys(child, checkIds)
    }
  }
}

const showPermissionForm = async (row: any) => {
  permissionsLoading.value = true
  for (const perm of permissions.value) {
    initCheckedFalse(perm)
  }
  currentRole.value.id = row.id
  currentRole.value.roleName = row.roleName
  currentRole.value.permissions = []
  const response = await apis.MenuManage.getPermissionsByRoleId(row.id)
  rolePermissions.value = response.data
  defaultCheckKeys.value = []
  defaultCheckKeys.value = response.data.map((item: any) => item.id)
  for (const perm of permissions.value) {
    initCheckedByKeys(perm, defaultCheckKeys.value)
  }

  setTimeout(async () => permissionsLoading.value = false, 200)
}

const getCheckPermissions = (perm: Permission): string[] => {
  let result: string[] = []
  if (perm.checked) {
    result.push(perm.id)
  }
  if (perm.children && perm.children.length > 0) {
    for (const child of perm.children) {
      result.push(...getCheckPermissions(child))
    }
  }
  return result
}

const savePermissionsSubmit = async () => {
  saveLoading.value = true
  const checkedKeys: string[] = []
  for (const perm of permissions.value) {
    checkedKeys.push(...getCheckPermissions(perm))
  }
  try {
    await apis.MenuManage.changeRolePermissions({
      roleId: currentRole.value.id,
      menuIds: checkedKeys,
    })
    Notification.showSuccessNotify('操作成功')
  } finally {
    saveLoading.value = false
  }
}

const showChangeRoleStatusDialog = (role: any, status: number) => {
  ElMessageBox.confirm(`您确认要${status == 1 ? '启用' : '禁用'}角色【${role.roleName}】吗`, "温馨提示", {type: "warning"})
    .then(async () => {
      try {
        loading.value = true
        await apis.RoleManage.changeStatus(role.id, status)
        await searchSubmit()
        Notification.showSuccessNotify('操作成功')
      } finally {
        loading.value = false
      }
    })
}

const resetAllPermissions = async (role: any) => {
  try {
    resetLoading.value = true
    permissionsLoading.value = true
    await apis.MenuManage.changeRolePermissions({
      roleId: currentRole.value.id,
      menuIds: []
    })
    permissions.value.forEach((item: any) => initCheckedFalse(item))
    Notification.showSuccessNotify('操作成功')
  } finally {
    resetLoading.value = false
    permissionsLoading.value = false
  }
}

const addForm = ref()
const editForm = ref()
const saveSubmit = () => {
  dialogModel.value.saveLoading = true
  if (dialogModel.value.formName === 'AddForm') {
    addForm.value.saveSubmit()
  } else if (dialogModel.value.formName === 'EditForm') {
    editForm.value.saveSubmit()
  }
}


/**
 * 重写保存完成事件
 * @param status
 */
dialogModel.value.saveComplete = async (status: number) => {
  dialogModel.value.saveLoading = false
  if (status === 1) {
    await nextTick(async () => {
      dialogModel.value.visible = false
      await showPermissionForm(dialogModel.value.rowData)
      await searchSubmit()
    })
  }
}

</script>

<template>
  <div>
    <el-row :gutter="10">
      <el-col :span="16">
        <div class="lmt-operation-container">
          <div class="lmt-operation">
            <el-button v-has-perm="['role:add']" icon="Plus" type="primary"
                       @click="showFormDialog('新增','AddForm',null,DialogMode.ADD,'600px')">新增
            </el-button>
          </div>
          <div class="lmt-search">
            <el-input v-model="search.keywords" clearable placeholder="请输入角色名称/角色代码" @clear="searchSubmit">
              <template #append>
                <el-button icon="Search" @click="searchSubmit"></el-button>
              </template>
            </el-input>
          </div>
        </div>
        <el-table v-loading="loading" :data="tableData.records" border
                  max-height="calc(100vh - var(--lmt-header-height) - var(--lmt-main-padding) * 2 - 6rem)"
                  stripe>
          <!--行号-->
          <el-table-column align="center" label="序号" type="index" width="80">
            <template #default="scope">
              {{ (search.pageNumber - 1) * search.pageSize + scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column label="角色名称" min-width="100" prop="roleName" show-overflow-tooltip></el-table-column>
          <el-table-column label="角色代码" min-width="100" prop="roleCode" show-overflow-tooltip></el-table-column>
          <el-table-column label="账户数" min-width="70" prop="userCount" show-overflow-tooltip></el-table-column>
          <el-table-column label="状态" min-width="70" prop="status" show-overflow-tooltip>
            <template #default="{row}">
              <el-text v-if="row.status==1" type="success">启用</el-text>
              <el-text v-else type="danger">禁用</el-text>
            </template>
          </el-table-column>
          <el-table-column label="管理员标识" min-width="80" prop="adminFlag">
            <template #default="{row}">
              <el-text v-if="row.adminFlag" type="success">是</el-text>
              <el-text v-else type="danger">否</el-text>
            </template>
          </el-table-column>
          <el-table-column label="内置角色" min-width="80" prop="sysRoleFlag">
            <template #default="{row}">
              <el-text v-if="row.sysRoleFlag" type="success">是</el-text>
              <el-text v-else type="danger">否</el-text>
            </template>
          </el-table-column>
          <el-table-column label="所属内置角色代码" min-width="80" prop="sysRoleCode"></el-table-column>
          <el-table-column v-has-perm="['role:edit', 'role:change:status','role:change:permissions']" fixed="right"
                           label="操作" min-width="170"
                           prop="id">
            <template #default="{row}">
              <el-link v-has-perm="['role:edit']" icon="Edit" type="warning"
                       @click="showFormDialog('修改','EditForm',row,DialogMode.EDIT,'600px')">修改
              </el-link>
              <el-link v-if="row.status==1" v-has-perm="['role:change:status']" icon="CircleClose" type="danger"
                       @click="showChangeRoleStatusDialog(row,0)">禁用
              </el-link>
              <el-link v-if="row.status==0" v-has-perm="['role:change:status']" icon="CircleCheck" type="success"
                       @click="showChangeRoleStatusDialog(row,1)">启用
              </el-link>
              <el-link v-has-perm="['role:change:permissions']" icon="Connection" type="primary"
                       @click="showPermissionForm(row)">授权
              </el-link>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
      <el-col v-if="currentRole.id" v-loading="permissionsLoading" :span="8">
        <div class="lmt-operation-container">
          <div class="lmt-operation">
            <div>为角色：<span class="warning-text">{{ currentRole?.roleName }}</span>，授予权限</div>
          </div>
          <div class="lmt-search">
            <el-button v-has-perm="'role:change:permissions'" :loading="resetLoading" icon="RefreshLeft"
                       type="warning" @click="resetAllPermissions(currentRole)">重置
            </el-button>
            <el-button v-has-perm="'role:change:permissions'" :loading="saveLoading" icon="Money" type="primary"
                       @click="savePermissionsSubmit">保存
            </el-button>
          </div>
        </div>
        <div v-loading="permissionsLoading" class="tree-container">
          <template v-for="(perm,index) in permissions" :key="index">
            <div class="m-l-2 m-r-2">
              <!--     一级目录      -->
              <el-checkbox v-model="perm.checked" :label="perm.id" :size="perm.type==='M'?'large':''">
                {{ perm.name }}
              </el-checkbox>
              <div v-for="(child, index) in perm.children" v-if="perm.children && perm.children.length>0" :key="index"
                   class="p-l-4">
                <!-- 拥有二级目录  -->
                <div v-if="child.type==='M'">
                  <el-checkbox v-model="child.checked" :label="child.id" :size="child.type==='M'?'':'small'">
                    {{ child.name }}
                  </el-checkbox>
                  <div v-for="item in child.children" v-if="child.children && child.children.length>0" :key="index" class="m-l-4 0m-r-4">
                    <el-checkbox v-model="item.checked" :label="item.id" :size="item.type==='C'?'':'small'">
                      {{ item.name }}
                    </el-checkbox>
                    <div class="flex flex-wrap">
                      <div v-for="(child2, index) in item.children" v-if="child.children && child.children.length>0"
                           :key="index" class="p-l-4">
                        <el-checkbox v-model="child2.checked" :label="child2.id" :size="child2.type==='F'?'':'small'"
                                     class="flex">{{ child2.name }}
                        </el-checkbox>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 没有有二级目录  -->
                <div v-if="child.type==='C'">
                  <el-checkbox v-model="child.checked" :label="child.id" :size="child.type==='C'?'':'small'">
                    {{ child.name }}
                  </el-checkbox>
                  <div class="flex flex-wrap">
                    <div v-for="(child2, index) in child.children" v-if="child.children && child.children.length>0"
                         :key="index" class="p-l-4">
                      <el-checkbox v-model="child2.checked" :label="child2.id" :size="child2.type==='F'?'':'small'"
                                   class="flex">{{ child2.name }}
                      </el-checkbox>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
      </el-col>
    </el-row>
    <el-dialog v-model="dialogModel.visible" :close-on-click-modal="false" :title="dialogModel.title"
               :width="dialogModel.width"
               destroy-on-close @closed="dialogModel.closed">
      <AddForm v-if="dialogModel.formName == 'AddForm'" ref="addForm" :mode="dialogModel.mode"
               :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <EditForm v-if="dialogModel.formName == 'EditForm'" ref="editForm" :mode="dialogModel.mode"
                :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <template #footer>
        <el-button @click="dialogModel.visible=false">
          {{ dialogModel.formName !== 'DetailForm' ? '取 消' : '关 闭' }}
        </el-button>
        <el-button v-if="dialogModel.formName!=='DetailForm'" :loading="dialogModel.saveLoading" type="primary"
                   @click="saveSubmit">确 认
        </el-button>
      </template>
    </el-dialog>
  </div>

</template>

<style lang="scss" scoped>
.tree-container {
  width: 100%;
  border: 1px solid #eee;
  max-height: calc(100vh - var(--lmt-header-height) - var(--lmt-main-padding) * 2 - 6rem);
  overflow-y: auto;
}
</style>
