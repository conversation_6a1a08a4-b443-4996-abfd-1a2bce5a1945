<script lang="ts" setup>
import {onMounted, reactive, ref} from 'vue'
import {DialogMode} from "@/commons/DialogModel";
import {apis} from "@/apis";
import {useRoleStore} from "@/store";

const formData = ref({
  id: '',
  name: '',
  code: '',
  status: 1,
  sysRoleCode: '',
  sysRoleFlag: false
})
const props = defineProps({
  rowData: {
    type: [Object, null],
    default: () => ({})
  },
  mode: {
    type: [String, null],
    default: "ADD"
  }
})

const rules = reactive({
  name: [
    {required: true, message: '请输入角色名称', trigger: 'blur'},
    {min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur'}
  ],
  code: [
    {required: true, message: '请输入角色编码', trigger: 'blur'},
    {min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur'}
  ],
  sysRoleCode: props.mode === DialogMode.EDIT ? [] : [
    {required: true, message: '请选择内置系统角色', trigger: 'change'}
  ],
  sysRoleFlag: [{required: true, message: '请选择内置系统角色', trigger: 'change'}]
})
const roleList = useRoleStore().roleList

onMounted(() => {
  if (props.mode === DialogMode.EDIT) {
    const {id, roleName, roleCode, status, sysRoleCode, sysRoleFlag} = props.rowData as any
    formData.value = {id, name: roleName, code: roleCode, status, sysRoleCode, sysRoleFlag}
  }
})

const form = ref()
const emit = defineEmits(['save-complete'])
const saveCompleteEmit = (status: number) => emit('save-complete', status)

const saveSubmit = () => {
  form.value.validate(async (valid: boolean) => {
    if (valid) {
      const fn = props.mode === 'ADD' ? apis.RoleManage.create : apis.RoleManage.update
      try {
        await fn(formData.value)
        saveCompleteEmit(1)
      } catch (e) {
        saveCompleteEmit(0)
      }
    }
    saveCompleteEmit(0)
  })
}

defineExpose({
  saveSubmit
})

</script>

<template>
  <div class="form-container">
    <el-form ref="form" :model="formData" :rules="rules" label-width="auto" @submit="saveSubmit">
      <el-form-item label="内置角色：" prop="sysRoleFlag">
        <el-text type="info">{{ formData.sysRoleFlag ? '是' : '否' }}</el-text>
      </el-form-item>
      <el-form-item label="角色名称：" prop="name">
        <el-input v-model.trim="formData.name" maxlength="32" placeholder="请输入角色名称" show-word-limit></el-input>
      </el-form-item>
      <el-form-item label="角色代码：" prop="code">
        <el-input :disabled="formData.sysRoleFlag" v-model.trim="formData.code" maxlength="32" placeholder="请输入角色代码" show-word-limit></el-input>
      </el-form-item>
      <template v-if="!formData.sysRoleFlag">
        <el-form-item label="所属内置角色：" prop="sysRoleCode">
          <el-select v-model="formData.sysRoleCode" placeholder="请选择内置系统角色">
            <el-option v-for="item in roleList" :key="item.code" :label="item.name" :value="item.code"></el-option>
          </el-select>
        </el-form-item>
      </template>
      <el-form-item label="状态：" prop="status">
        <el-switch v-model="formData.status" :active-value="1" :inactive-value="0" active-text="启用"
                   inactive-text="禁用" inline-prompt
                   style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"></el-switch>
      </el-form-item>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>

</style>
