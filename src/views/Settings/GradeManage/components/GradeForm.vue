<script lang="ts" setup>
import {onMounted, ref} from 'vue'
import {DialogMode} from "@/commons/DialogModel";
import dayjs from "dayjs";
import {apis} from "@/apis";
import Notification from "@/commons/Notification";

const formData = ref({
  id: null,
  year: 2018,
  name: ''
})
const rules = ref({
  name: [{required: true, message: '请输入年级名称', trigger: 'blur'}],
  year: [{required: true, message: '请选择入学年份', trigger: 'blur'}]
})
const props = defineProps<{
  rowData: any,
  mode: DialogMode | null
}>()

onMounted(() => {
  if (props.mode === DialogMode.ADD) {
    let year = dayjs(Date.now()).year()
    formData.value = {id: null, year: year, name: year + '级'}
  } else {
    const {id, name, year} = props.rowData as any
    formData.value = {id, name, year}
  }
})
const form = ref()
const emit = defineEmits<{ (e: 'save-complete', status: number): void }>()
const saveCompleteEmit = (status: number) => emit('save-complete', status)


const saveSubmit = () => {
  form.value.validate((valid: boolean) => {
    if (valid) {
      const fn = props.mode === 'ADD' ? apis.GradeManage.create : apis.GradeManage.update
      fn(formData.value)
        .then(() => {
          Notification.showSuccessNotify('操作成功')
          saveCompleteEmit(1)
        })
        .catch(() => saveCompleteEmit(0))
    } else {
      saveCompleteEmit(0)
    }
  })
}

defineExpose({
  saveSubmit
})

</script>

<template>
  <div class="form-container">
    <el-form ref="form" :model="formData" :rules="rules" label-width="auto">
      <el-form-item label="年级名称：" prop="name">
        <span>{{ formData.name }}</span>
      </el-form-item>
      <el-form-item label="入学年份：" prop="year">
        <el-input-number v-model="formData.year" :max="2100" :min="1900" :precision="0"
                         @change="(val:number) => formData.name = val + '级'"></el-input-number>
      </el-form-item>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>

</style>
