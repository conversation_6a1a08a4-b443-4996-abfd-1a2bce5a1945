<script lang="ts" setup>
import {DialogMode} from "@/commons/DialogModel";
import {onMounted, ref} from "vue";
import {apis} from "@/apis";
import {useTableCommons} from "@/commons/TableCommons";
import dayjs from "dayjs";
import AddForm from "./components/GradeForm.vue";
import EditForm from "./components/GradeForm.vue";
import {ElMessageBox} from "element-plus";
import Notification from "@/commons/Notification";

const search = ref({
  pageNumber: 1,
  pageSize: 10,
  keywords: ''
})

const getTableData = async () => {
  const {data} = await apis.GradeManage.getPageData(search.value)
  return data
}

let {
  loading,
  dialogModel,
  showFormDialog,
  tableData,
  searchSubmit,
  pageOrSizeChange
} = useTableCommons(search, getTableData)

onMounted(async () => {
  await searchSubmit()
})

const addForm = ref()
const editForm = ref()
const saveSubmit = async () => {
  dialogModel.value.saveLoading = true
  // 根据表单名称选择对应的表单
  const form = dialogModel.value.formName === 'AddForm' ? addForm : editForm
  form?.value.saveSubmit && await form?.value.saveSubmit()
}

const showDeleteDialog = (rowData: any) => {
  ElMessageBox.confirm(`您确定删除年级【${rowData.name}】吗？`, "温馨提示", {type: "warning"})
    .then(async () => {
      await apis.GradeManage.delete(rowData.id)
      await searchSubmit()
      Notification.showSuccessNotify("操作成功")
    })
}

</script>

<template>
  <div>
    <div class="lmt-operation-container">
      <div class="lmt-operation">
        <el-button v-has-perm="'grade:add'" icon="Plus" type="primary"
                   @click="showFormDialog('新增','AddForm',null,DialogMode.ADD,'600px')">新增
        </el-button>
      </div>
      <div class="lmt-search">
        <el-input v-model="search.keywords" clearable placeholder="请输入年份" @clear="searchSubmit">
          <template #append>
            <el-button icon="Search" @click="searchSubmit"></el-button>
          </template>
        </el-input>
      </div>
    </div>
    <el-table v-loading="loading" :data="tableData.records" border max-height="calc(100vh - var(--lmt-header-height) - var(--lmt-main-padding) * 2 - 6rem)"
              stripe>
      <!--行号-->
      <el-table-column align="center" label="序号" type="index" width="80">
        <template #default="scope">
          {{ (search.pageNumber - 1) * search.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="入学年份" min-width="120" prop="year" show-overflow-tooltip></el-table-column>
      <el-table-column label="年级名称" min-width="100" prop="name" show-overflow-tooltip></el-table-column>
      <el-table-column label="创建时间" min-width="120" prop="createTime" show-overflow-tooltip>
        <template #default="{row}">
          {{ dayjs(row.createTime).format("YYYY-MM-DD HH:mm") }}
        </template>
      </el-table-column>
      <el-table-column v-has-perm="['grade:edit', 'grade:delete']" fixed="right" label="操作" min-width="210">
        <template #default="scope">
          <el-link v-has-perm="'grade:edit'" icon="Edit" type="warning"
                   @click="showFormDialog('修改','EditForm',scope.row,DialogMode.EDIT)">修改
          </el-link>
          <el-link v-has-perm="'grade:delete'" icon="Delete" type="danger" @click="showDeleteDialog(scope.row)">删除
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-model:current-page="search.pageNumber" v-model:page-size="search.pageSize"
                   :page-sizes="[10, 20, 30, 40, 50, 100]" :total="Number(tableData.total)"
                   background
                   class="m-t-4 float-right" layout="total, prev, pager, next, sizes, ->" @change="pageOrSizeChange">
    </el-pagination>
    <el-dialog v-model="dialogModel.visible" :close-on-click-modal="false" :title="dialogModel.title" :width="dialogModel.width"
               destroy-on-close @closed="dialogModel.closed">
      <AddForm v-if="dialogModel.formName == 'AddForm'" ref="addForm" :mode="dialogModel.mode"
               :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <EditForm v-if="dialogModel.formName == 'EditForm'" ref="editForm" :mode="dialogModel.mode"
                :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <template #footer>
        <el-button @click="dialogModel.visible=false">
          {{ dialogModel.formName !== 'DetailForm' ? '取 消' : '关 闭' }}
        </el-button>
        <el-button :loading="dialogModel.saveLoading" type="primary" @click="saveSubmit">确 认
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>

</style>
