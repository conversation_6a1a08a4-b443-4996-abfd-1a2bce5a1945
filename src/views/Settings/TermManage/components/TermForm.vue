<script lang="ts" setup>
import {onMounted, ref} from 'vue'
import {DialogMode} from "@/commons/DialogModel";
import {apis} from "@/apis";
import Notification from "@/commons/Notification";
import type { KeyValue } from "@/commons/KeyValue";

const formData = ref({
  id: '',
  yearName: '',
  termName: '',
  startDate: '',
  stopDate: '',
  status: 0
})

const rules = {
  yearName: [{required: true, message: '请输入学年名称', trigger: 'blur'}],
  termName: [{required: true, message: '请输入学期名称', trigger: 'blur'}],
  startDate: [{required: true, message: '请选择开始日期', trigger: 'blur'}],
  stopDate: [{required: true, message: '请选择结束日期', trigger: 'blur'}],
  status: [{required: true, message: '请选择状态', trigger: 'change'}]
}

const props = defineProps({
  rowData: {
    type: [Object, null],
    default: () => {
    }
  },
  mode: {
    type: [String, null],
    default: 'EDIT'
  }
})

const xueNianList = ref<Array<KeyValue>>([])

onMounted(async () => {
  if (props.mode === DialogMode.EDIT) {
    formData.value = props.rowData as any
  }
  xueNianList.value = (await apis.Commons.getXueNianSelectList()).data
})

const form = ref()
const emit = defineEmits(['save-complete'])
const saveCompleteEmit = (status: number) => emit('save-complete', status)
const saveSubmit = () => {
  form.value.validate(async (valid: boolean) => {
    if (valid) {
      const fn = props.mode === DialogMode.ADD ? apis.TermManage.create : apis.TermManage.update
      fn(formData.value)
        .then(() => {
          Notification.showSuccessNotify('操作成功')
          saveCompleteEmit(1)
        })
        .catch(() => saveCompleteEmit(0))
    } else {
      saveCompleteEmit(0)
    }
  })
}
defineExpose({saveSubmit})

</script>

<template>
  <div class="form-container">
    <el-form ref="form" :model="formData" :rules="rules" label-width="auto">
      <el-form-item label="学年名称：" prop="yearName">
        <el-select v-model="formData.yearName" clearable placeholder="请选择学年名称">
          <el-option v-for="item in xueNianList" :key="item.key" :label="item.key" :value="item.key">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="学期名称：" prop="termName">
        <el-input v-model="formData.termName" maxlength="32" placeholder="请输入学期名称" show-word-limit></el-input>
      </el-form-item>
      <el-form-item>
        <div class="warning-text">学期命名规则：20XX-20XX学年第1（2）学期</div>
      </el-form-item>
      <el-form-item label="开始日期：" prop="startDate">
        <el-date-picker v-model="formData.startDate" placeholder="请选择开始日期" style="width: 100%;" type="date"
                        value-format="YYYY-MM-DD"></el-date-picker>
      </el-form-item>
      <el-form-item label="结束日期：" prop="stopDate">
        <el-date-picker v-model="formData.stopDate" placeholder="请选择结束日期" style="width: 100%;" type="date"
                        value-format="YYYY-MM-DD"></el-date-picker>
      </el-form-item>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>

</style>
