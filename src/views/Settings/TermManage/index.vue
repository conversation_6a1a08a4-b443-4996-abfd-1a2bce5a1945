<script lang="ts" setup>
import {onMounted, ref} from 'vue'
import {DialogMode} from "@/commons/DialogModel";
import {apis} from "@/apis";
import {useTableCommons} from "@/commons/TableCommons";
import AddForm from "./components/TermForm.vue";
import EditForm from "./components/TermForm.vue";
import {ElMessageBox} from "element-plus";
import Notification from "@/commons/Notification";

const search = ref({
  pageNumber: 1,
  pageSize: 10,
  keywords: ''
})

const getTableData = async () => {
  const response = await apis.TermManage.getPageData(search.value)
  return response.data
}

let {
  loading,
  dialogModel,
  showFormDialog,
  tableData,
  searchSubmit,
  pageOrSizeChange
} = useTableCommons(search, getTableData)

onMounted(async () => {
  await searchSubmit()
})

const addForm = ref()
const editForm = ref()

const saveSubmit = async () => {
  dialogModel.value.saveLoading = true
  // 根据表单名称选择对应的表单
  const form = dialogModel.value.formName === 'AddForm' ? addForm : editForm
  form?.value.saveSubmit && await form?.value.saveSubmit()
}


const changeStatus = async (row: any, status: number) => {
  let msg = `您确认要${status == 1 ? '启用' : '停用'}【${row.termName}】吗？`
  if (status == 1) {
    msg = `确认将该学期【${row.termName}】设置为启用状态吗？当前启用的学期会同步停用。`
  }
  ElMessageBox.confirm(msg, "温馨提示", {type: "warning"})
    .then(async () => {
      await apis.TermManage.changeStatus(row.id, status)
      Notification.showSuccessNotify("操作成功")
      await searchSubmit()
    })
}
const changeGuiDangStatus = async (row: any, status: number) => {
  let msg = `您确认要归档【${row.termName}】吗？`
  ElMessageBox.confirm(msg, "温馨提示", {type: "warning"})
    .then(async () => {
      await apis.TermManage.guiDang(row.id)
      Notification.showSuccessNotify("操作成功")
      await searchSubmit()
    })
}
</script>

<template>
  <div>
    <div class="lmt-operation-container">
      <div class="lmt-operation">
        <el-button v-has-perm="'term:add'" icon="Plus" type="primary"
                   @click="showFormDialog('新增','AddForm',null,DialogMode.ADD,'400px')">新增
        </el-button>
      </div>
      <div class="lmt-search">
        <el-input v-model="search.keywords" clearable placeholder="请输入账户/姓名/手机号码" @clear="searchSubmit">
          <template #append>
            <el-button icon="Search" @click="searchSubmit"></el-button>
          </template>
        </el-input>
      </div>
    </div>
    <el-table v-loading="loading" :data="tableData.records" border
              max-height="calc(100vh - var(--lmt-header-height) - var(--lmt-main-padding) * 2 - 6rem)"
              stripe>
      <!--行号-->
      <el-table-column align="center" label="序号" type="index" width="80">
        <template #default="scope">
          {{ (search.pageNumber - 1) * search.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="学年" min-width="120" prop="yearName" show-overflow-tooltip></el-table-column>
      <el-table-column label="学期名称" min-width="120" prop="termName" show-overflow-tooltip></el-table-column>
      <el-table-column label="开始日期" min-width="120" prop="startDate" show-overflow-tooltip></el-table-column>
      <el-table-column label="结束日期" min-width="120" prop="stopDate" show-overflow-tooltip></el-table-column>
      <el-table-column label="包含月份" min-width="120" prop="months" show-overflow-tooltip></el-table-column>
      <el-table-column label="学期状态" min-width="120" prop="status" show-overflow-tooltip>
        <template #default="scope">
          <el-text v-if="scope.row.status === 0" type="danger">停用</el-text>
          <el-text v-else-if="scope.row.status === 1" type="success">启用</el-text>
          <el-text v-else-if="scope.row.status === 2" type="warning">已归档</el-text>
        </template>
      </el-table-column>
      <el-table-column v-has-perm="['term:edit', 'term:change:status', 'term:guidang']" fixed="right" label="操作"
                       min-width="210">
        <template #default="scope">
          <el-link v-has-perm="'term:edit'" :disabled="[0,2].includes(scope.row.status)" icon="Edit"
                   type="primary"
                   @click="showFormDialog('修改','EditForm',scope.row,DialogMode.EDIT)">修改
          </el-link>
          <el-link v-if="scope.row.status === 0" v-has-perm="'term:change:status'" icon="CircleCheck" type="success"
                   @click="changeStatus(scope.row,1)">启用
          </el-link>
          <el-link v-if="scope.row.status === 1" v-has-perm="'term:change:status'" icon="CircleClose" type="danger"
                   @click="changeStatus(scope.row,0)">停用
          </el-link>
          <el-link v-if="[0,1].includes(scope.row.status)" v-has-perm="'term:guidang'"
                   :disabled="[0,2].includes(scope.row.status)" icon="Folder"
                   type="info" @click="changeGuiDangStatus(scope.row,2)">归档
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-model:current-page="search.pageNumber" v-model:page-size="search.pageSize"
                   :page-sizes="[10, 20, 30, 40, 50, 100]" :total="Number(tableData.total)"
                   background
                   class="m-t-4 float-right" layout="total, prev, pager, next, sizes, ->" @change="pageOrSizeChange">
    </el-pagination>
    <el-dialog v-model="dialogModel.visible" :close-on-click-modal="false" :title="dialogModel.title"
               :width="dialogModel.width"
               destroy-on-close @closed="dialogModel.closed">
      <AddForm v-if="dialogModel.formName == 'AddForm'" ref="addForm" :mode="dialogModel.mode"
               :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <EditForm v-if="dialogModel.formName == 'EditForm'" ref="editForm" :mode="dialogModel.mode"
                :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <template #footer>
        <el-button @click="dialogModel.visible=false">
          {{ dialogModel.formName !== 'DetailForm' ? '取 消' : '关 闭' }}
        </el-button>
        <el-button v-if="dialogModel.formName!=='DetailForm'" :loading="dialogModel.saveLoading" type="primary"
                   @click="saveSubmit">确 认
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>

</style>
