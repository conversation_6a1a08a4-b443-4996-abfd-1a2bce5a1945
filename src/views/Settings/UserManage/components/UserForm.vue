<script lang="ts" setup>
import {computed, onMounted, ref} from 'vue'
import {validatorEmail, validatorMobile, validatorUsername} from "@/commons/Validator";
import {DialogMode} from "@/commons/DialogModel";
import type {FormInstance} from "element-plus";
import {apis} from "@/apis";
import {useDeptStore, useRoleStore} from "@/store";
import Notification from '@/commons/Notification';
import type { KeyValue } from "@/commons/KeyValue";

const deptList = ref<KeyValue[]>([])

const schoolList = ref<KeyValue[]>([])

const userStore = useRoleStore()

const hasSchoolRoles = userStore.hasSchoolRoles

const roleList = userStore.roleList

const roleEnums = userStore.roleEnums

const dataLoading = ref(false)

const props = defineProps({
  rowData: {
    type: [Object, null],
    default: () => ({})
  },
  mode: {
    type: [String, null],
    default: "ADD"
  }
})

const emit = defineEmits(['save-complete'])

const saveCompleteEmit = (status: number) => emit('save-complete', status)

const formData = ref({
  id: "",
  username: '',
  nickname: '',
  mobile: '',
  email: '',
  status: 0,
  regionId: '',
  deptFloor: 0,
  schoolId: '',
  roleCodes: Array<string>()
})

onMounted(async () => {
  dataLoading.value = true
  if (props.mode === DialogMode.EDIT) {
    const {id, username, nickname, mobile, email, status, regionId, deptFloor, schoolId, roles} = props.rowData as any
    formData.value = {
      id, username, nickname, mobile,
      email, status, regionId, deptFloor, schoolId,
      roleCodes: roles.map((item: any) => (item.roleCode) as string)
    }
  }
  deptList.value = (await apis.Commons.getRegionSelectList()).data
  const topDept = useDeptStore().topDept;
  if (formData.value.regionId && formData.value.regionId !== topDept?.id) {
    schoolList.value = (await apis.Commons.getSchoolSelectList(formData.value.regionId)).data
  } else {
    deptList.value = [{key: topDept?.id || '', value: topDept?.name || ''}, ...deptList.value]
  }
  dataLoading.value = false
})

const rules = computed(() => {
  type RuleKeys = 'username' | 'nickname' | 'mobile' | 'email' | 'regionId' | 'roleCodes' | 'schoolId';

  let result: Partial<Record<RuleKeys, any>> = {
    username: [{required: true, message: '请输入用户名', trigger: 'blur'},
      {min: 4, max: 20, message: '长度在 4 到 20 个字符', trigger: 'blur'},
      {validator: validatorUsername, trigger: 'blur'}
    ],
    nickname: [{required: true, message: '请输入真实姓名', trigger: 'blur'}],
    mobile: [{required: true, message: '请输入手机号码', trigger: 'blur'},
      {validator: validatorMobile, trigger: 'blur'}
    ],
    email: [{validator: validatorEmail, trigger: 'blur'}],
    regionId: [{required: true, message: '请选择部门', trigger: 'change'}],
    roleCodes: [{required: true, message: '请选择角色', trigger: 'change'}],
    schoolId: [{required: true, message: '请选择学校', trigger: 'change'}]
  };

  if (!hasSchoolRoles.includes(formData.value.roleCodes)) {
    delete result.schoolId;
  }
  if (formData.value.roleCodes === roleEnums.CITY_ADMIN || formData.value.roleCodes === roleEnums.SUPER_ADMIN) {
    delete result.schoolId;
    delete result.regionId;
  }

  return result;
})

const regionChange = async () => {
  schoolList.value = []
  if (formData.value.regionId && hasSchoolRoles.includes(formData.value.roleCodes)) {
    schoolList.value = (await apis.Commons.getSchoolSelectList(formData.value.regionId)).data
    formData.value.schoolId = ''
  }
}

const form = ref()
const saveSubmit = () => {
  (form.value as FormInstance).validate((valid) => {
    if (valid) {
      const fcn: Function = props.mode === DialogMode.ADD ? apis.UserManage.create : apis.UserManage.update;
      if (formData.value.roleCodes === roleEnums.CITY_ADMIN || formData.value.roleCodes === roleEnums.CITY_ADMIN) {
        formData.value.schoolId = ''
        formData.value.regionId = useDeptStore().topDept?.id || '';
      }
      fcn(formData.value).then(() => {
        saveCompleteEmit(1)
        Notification.showSuccessNotify('操作成功')
      }).catch(() => saveCompleteEmit(0))
    } else {
      saveCompleteEmit(0)
    }
  })
}

defineExpose({
  saveSubmit
})
</script>

<template>
  <div v-loading="dataLoading" class="form-container">
    <el-form ref="form" :model="formData" :rules="rules" label-width="auto">
      <el-form-item label="用户名" prop="username">
        <el-input v-model.trim="formData.username" :disabled="props.mode === DialogMode.EDIT" maxlength="32" placeholder="请输入用户名"
                  show-word-limit></el-input>
      </el-form-item>
      <el-form-item label="角色" prop="roleCodes">
        <el-select v-model="formData.roleCodes" multiple placeholder="请选择角色"
                   @change="formData.schoolId = '',formData.regionId = ''">
          <el-option v-for="item in roleList" :key="item.code" :label="item.name" :value="item.code"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="真实姓名" prop="nickname">
        <el-input v-model.trim="formData.nickname" maxlength="32" placeholder="请输入真实姓名"
                  show-word-limit></el-input>
      </el-form-item>
      <el-form-item label="手机号码" prop="mobile">
        <el-input v-model.trim="formData.mobile" maxlength="11" placeholder="请输入手机号码" show-word-limit></el-input>
      </el-form-item>
      <el-form-item label="所属区县" prop="regionId">
        <el-select v-model="formData.regionId" placeholder="请选择区县" @change="regionChange">
          <el-option v-for="item in deptList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="所属学校" prop="schoolId">
        <el-select v-model="formData.schoolId" placeholder="请选择学校">
          <el-option v-for="item in schoolList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="电子邮箱" prop="email">
        <el-input v-model.trim="formData.email" maxlength="32" placeholder="请输入电子邮箱" show-word-limit></el-input>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <ElRadioGroup v-model="formData.status">
          <ElRadio :value="1">启用</ElRadio>
          <ElRadio :value="0">禁用</ElRadio>
        </ElRadioGroup>
      </el-form-item>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>

</style>
