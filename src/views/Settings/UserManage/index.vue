<script lang="ts" setup>
import {onMounted, ref} from "vue";
import {useTableCommons} from "@/commons/TableCommons";
import {apis} from '@/apis'
import {DialogMode} from "@/commons/DialogModel";
import AddForm from "./components/UserForm.vue";
import EditForm from "./components/UserForm.vue";
import {ElMessageBox, ElNotification} from "element-plus";

const search = ref({
  pageNumber: 1,
  pageSize: 10,
  keywords: ''
})

const getTableData = async () => {
  const response = await apis.UserManage.getPageData(search.value)
  return response.data
}

let {
  loading,
  dialogModel,
  showFormDialog,
  tableData,
  searchSubmit,
  pageOrSizeChange
} = useTableCommons(search, getTableData)

onMounted(async () => {
  await searchSubmit()
})

const changeUserStatus = async (row: any, status: number) => {
  ElMessageBox.confirm(`您确定${status == 1 ? "启用" : "禁用"}账户【${row.username}】吗？`, "温馨提示",
    {type: "warning"})
    .then(async () => {
      await apis.UserManage.changeUserStatus(row.id, status)
      ElNotification.success({message: "操作成功", title: "温馨提示"})
      await searchSubmit()
    })
}

const resetPassword = async (row: any) => {
  ElMessageBox.confirm(`您确定重置账户【${row.username}】的密码吗？`, "温馨提示",
    {type: "warning"})
    .then(async () => {
      await apis.UserManage.resetPassword(row.id)
      ElNotification.success({message: "操作成功", title: "温馨提示"})
      await searchSubmit()
    })
}

// 表单部分
const addForm = ref()
const editForm = ref()
const saveSubmit = async () => {
  dialogModel.value.saveLoading = true
  if (dialogModel.value.formName === 'AddForm') {
    addForm.value.saveSubmit()
  } else if (dialogModel.value.formName === 'EditForm') {
    editForm.value.saveSubmit()
  }
}
</script>

<template>
  <div>
    <div class="lmt-operation-container">
      <div class="lmt-operation">
        <el-button v-has-perm="'user:add'" icon="Plus" type="primary"
                   @click="showFormDialog('新增','AddForm',null,DialogMode.ADD,'600px')">新增
        </el-button>
      </div>
      <div class="lmt-search">
        <el-input v-model="search.keywords" clearable placeholder="请输入账户/姓名/手机号码" @clear="searchSubmit">
          <template #append>
            <el-button icon="Search" @click="searchSubmit"></el-button>
          </template>
        </el-input>
      </div>
    </div>
    <el-table v-loading="loading" :data="tableData.records" border max-height="calc(100vh - var(--lmt-header-height) - var(--lmt-main-padding) * 2 - 6rem)"
              stripe>
      <!--行号-->
      <el-table-column align="center" label="序号" type="index" width="80">
        <template #default="scope">
          {{ (search.pageNumber - 1) * search.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="账户名称" min-width="120" prop="username" show-overflow-tooltip></el-table-column>
      <el-table-column label="真实姓名" min-width="100" prop="nickname" show-overflow-tooltip></el-table-column>
      <el-table-column label="手机号码" min-width="120" prop="mobile" show-overflow-tooltip></el-table-column>
      <el-table-column label="角色" min-width="120" prop="roleName" show-overflow-tooltip>
        <template #default="scope">
          <span>{{ scope.row.roles.map((item: any) => item.roleName).join(",") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="所属区县" min-width="80" prop="regionName" show-overflow-tooltip></el-table-column>
      <el-table-column label="所属学校" min-width="120" prop="schoolName" show-overflow-tooltip></el-table-column>
      <el-table-column label="默认密码" min-width="120" prop="defaultPassword" show-overflow-tooltip></el-table-column>
      <el-table-column label="默认密码标识" min-width="80" prop="defaultPasswordFlag" show-overflow-tooltip>
        <template #default="scope">
          <el-tag v-if="scope.row.defaultPasswordFlag" type="success">是</el-tag>
          <el-tag v-else type="danger">否</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" min-width="80" prop="status" show-overflow-tooltip>
        <template #default="scope">
          <el-tag v-if="scope.row.status==1" type="success">正常</el-tag>
          <el-tag v-else type="danger">禁用</el-tag>
        </template>
      </el-table-column>
      <el-table-column v-has-perm="['user:edit', 'user:change:status', 'user:reset:password']" fixed="right" label="操作"
                       min-width="210">
        <template #default="scope">
          <el-link icon="Edit" type="warning"
                   @click="showFormDialog('修改','EditForm',scope.row,DialogMode.EDIT)">修改
          </el-link>
          <el-link v-if="scope.row.status==1" icon="CircleClose" type="danger" @click="changeUserStatus(scope.row,0)">禁用
          </el-link>
          <el-link v-else icon="CircleCheck" type="success" @click="changeUserStatus(scope.row,1)">启用</el-link>
          <el-link :disabled="!scope.row.defaultPasswordFlag" icon="RefreshLeft" type="warning"
                   @click="resetPassword(scope.row)">重置密码
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-model:current-page="search.pageNumber" v-model:page-size="search.pageSize"
                   :page-sizes="[10, 20, 30, 40, 50, 100]" :total="Number(tableData.total)"
                   background
                   class="m-t-4 float-right" layout="total, prev, pager, next, sizes, ->" @change="pageOrSizeChange">
    </el-pagination>

    <el-dialog v-model="dialogModel.visible" :close-on-click-modal="false" :title="dialogModel.title" :width="dialogModel.width"
               destroy-on-close @closed="dialogModel.closed">
      <AddForm v-if="dialogModel.formName == 'AddForm'" ref="addForm" :mode="dialogModel.mode"
               :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <EditForm v-if="dialogModel.formName == 'EditForm'" ref="editForm" :mode="dialogModel.mode"
                :row-data="dialogModel.rowData" @save-complete="dialogModel.saveComplete"/>
      <template #footer>
        <el-button @click="dialogModel.visible=false">
          {{ dialogModel.formName !== 'DetailForm' ? '取 消' : '关 闭' }}
        </el-button>
        <el-button v-if="dialogModel.formName!=='DetailForm'" :loading="dialogModel.saveLoading" type="primary"
                   @click="saveSubmit">确 认
        </el-button>
      </template>
    </el-dialog>
  </div>

</template>

<style lang="scss" scoped>

</style>
