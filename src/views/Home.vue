<template>
  <div class="home-container">
    <header class="header">
      <h1>欢迎您，{{ userInfo?.username }}！</h1>
      <p>今天又是充满活力的一天！</p>
    </header>
    <div class="m-t-4 p-4 flex justify-around bg-#9fa0fa">
      <el-statistic :value="268500">
        <template #title>
          <div class="w-full color-#ffffff text-center">
            <span>用户总量</span>
          </div>
        </template>
      </el-statistic>
      <el-statistic :value="268500">
        <template #title>
          <div class="w-full color-#ffffff text-center">
            <span>订单总量</span>
          </div>
        </template>
      </el-statistic>
      <el-statistic :value="268500">
        <template #title>
          <div class="w-full color-#ffffff text-center">
            <span>月订单数量</span>
          </div>
        </template>
      </el-statistic>
      <el-statistic :value="268500">
        <template #title>
          <div class="w-full color-#ffffff text-center">
            <span>今日订单总量</span>
          </div>
        </template>
      </el-statistic>
    </div>
    <ul>
      <!-- 标签 -->
      <li v-for="(quote, index) in motivationQuotes" :key="index">
        <a :style="{ backgroundColor: getRandomColor() }" href="#">
          <h2>{{ quote.from }}：</h2>
          <p>{{ quote.content }}</p>
        </a>
      </li>
    </ul>
  </div>
</template>

<script lang="ts" setup>
import {storeToRefs} from 'pinia';
import {onMounted} from 'vue'
import {useUserStore} from '@/store/modules/user';
import {apis} from "@/apis";


const userStore = useUserStore();
const {userInfo} = storeToRefs(userStore);

onMounted(async () => {
  const response = await apis.Account.getSelfInfo();
  userStore.setUserInfo(response.data);
})


const names = ["技术帝", "智慧星", "创新者", "梦想家", "勇者"];

const motivationQuotes = [
  {from: getRandomName(), content: "成功来自努力，坚持成就梦想。"},
  {from: getRandomName(), content: "每一天都是新的起点。"},
  {from: getRandomName(), content: "相信自己，你是最棒的！"},
  {from: getRandomName(), content: "挑战自我，永不放弃。"},
  {from: getRandomName(), content: "保持初心，勇往直前。"}
];

function getRandomName() {
  return names[Math.floor(Math.random() * names.length)];
}

function getRandomColor() {
  const colors = ['#e0f7fa', '#e8f5e9', '#fce4ec', '#fff9c4', '#ffecb3', '#ffe0b2', '#ffccbc', '#ffcdd2'];
  return colors[Math.floor(Math.random() * colors.length)];
}
</script>

<style lang="scss" scoped>
.home-container {
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - var(--lmt-header-height) - 0.5rem);
  background-color: #f4f4f9;
  font-family: Arial, sans-serif;
}

.header {
  background-color: #42b983;
  color: white;
  padding: 20px;
  text-align: left;

  h1 {
    margin: 0;
    font-size: 24px;
  }

  p {
    margin: 5px 0 0;
    font-size: 16px;
  }
}

h2, p {
  font-size: 100%;
  font-weight: normal;
}

ul, li {
  list-style: none;
}

ul {
  overflow: hidden;
  padding: 3em;
}

ul li a {
  text-decoration: none;
  color: #000;
  display: block;
  height: 12em;
  width: 12em;
  padding: 1em; /* Firefox */
  -moz-box-shadow: 5px 5px 7px rgba(33, 33, 33, 1); /* Safari+Chrome */
  -webkit-box-shadow: 5px 5px 7px rgba(33, 33, 33, .7); /* Opera */
  box-shadow: 5px 5px 7px rgba(33, 33, 33, .7);
  -webkit-transform: rotate(-6deg);
  -o-transform: rotate(-6deg);
  -moz-transform: rotate(-6deg);
  transition: transform 0.3s ease, box-shadow 0.3s ease; /* 添加过渡效果 */
}

ul li {
  margin: 2em;
  float: left;
}

ul li h2 {
  font-size: 120%;
  font-weight: bold;
  padding-bottom: 10px;
}

ul li p {
  font-family: "Reenie Beanie", arial, sans-serif, 微软雅黑;
  font-size: 110%;
}

ul li:nth-child(even) a {
  -o-transform: rotate(4deg);
  -webkit-transform: rotate(4deg);
  -moz-transform: rotate(4deg);
  position: relative;
  top: 5px;
}

ul li:nth-child(3n) a {
  -o-transform: rotate(-3deg);
  -webkit-transform: rotate(-3deg);
  -moz-transform: rotate(-3deg);
  position: relative;
  top: -5px;
}

ul li:nth-child(5n) a {
  -o-transform: rotate(5deg);
  -webkit-transform: rotate(5deg);
  -moz-transform: rotate(5deg);
  position: relative;
  top: -10px;
}

ul li a:hover, ul li a:focus {
  -moz-box-shadow: 10px 10px 7px rgba(0, 0, 0, .7);
  -webkit-box-shadow: 10px 10px 7px rgba(0, 0, 0, .7);
  box-shadow: 10px 10px 7px rgba(0, 0, 0, .7);
  -webkit-transform: scale(1.25) rotate(0deg); /* 放大并取消旋转 */
  -moz-transform: scale(1.25) rotate(0deg);
  -o-transform: scale(1.25) rotate(0deg);
  position: relative;
  z-index: 5;
}
</style>
