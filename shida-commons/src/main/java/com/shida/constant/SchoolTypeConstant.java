package com.shida.constant;

import java.util.*;

public class SchoolTypeConstant {
    public static final String GONG_BAN = "公办";
    public static final String MIN_BAN = "民办";
    public static final String GONG_MING_TONG_BAN = "公民同办";
    public static final List<String> SCHOOL_TYPE_LIST = Arrays.asList(GONG_BAN, MIN_BAN, GONG_MING_TONG_BAN);


    public static final Map<String, Integer> SCHOOL_TYPE_MAP = Collections.unmodifiableMap(new HashMap<String, Integer>() {{
        put(GONG_BAN, 1);
        put(MIN_BAN, 2);
        put(GONG_MING_TONG_BAN, 3);
    }});

    public static final Map<Integer, String> SCHOOL_TYPE_MAP_REVERSE = Collections.unmodifiableMap(new HashMap<Integer, String>() {{
        put(1, GONG_BAN);
        put(2, MIN_BAN);
        put(3, GONG_MING_TONG_BAN);
    }});
}
