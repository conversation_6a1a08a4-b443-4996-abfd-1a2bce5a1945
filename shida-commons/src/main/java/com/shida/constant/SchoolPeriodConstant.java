package com.shida.constant;

import cn.hutool.core.map.MapUtil;

import java.util.*;

public class SchoolPeriodConstant {
    public static final String XIAO_XUE = "小学";
    public static final String CHU_ZHONG = "初中";
    public static final String GONG_ZHONG = "高中";
    public static final String NINE_YEARS = "九年一贯制";
    public static final String TWELVE_YEARS = "十二年一贯制";
    public static final String CHU_GUAN_LIAN_DU = "初高连读";

    public static final List<String> SCHOOL_PERIOD_LIST = Arrays.asList(XIAO_XUE, CHU_ZHONG, GONG_ZHONG, NINE_YEARS, TWELVE_YEARS, CHU_GUAN_LIAN_DU);

    public static final List<String> CLAZZ_PERIOD_LIST = Arrays.asList(XIAO_XUE, CHU_ZHONG, GONG_ZHONG);

    public static final Map<String, Integer> SCHOOL_PERIOD_MAP = Collections.unmodifiableMap(new HashMap<String, Integer>() {{
        put(XIAO_XUE, 1);
        put(CHU_ZHONG, 2);
        put(GONG_ZHONG, 3);
        put(NINE_YEARS, 4);
        put(TWELVE_YEARS, 5);
        put(CHU_GUAN_LIAN_DU, 6);
    }});

    public static final Map<Integer, String> SCHOOL_PERIOD_MAP_REVERSE = Collections.unmodifiableMap(new HashMap<Integer, String>() {{
        put(1, XIAO_XUE);
        put(2, CHU_ZHONG);
        put(3, GONG_ZHONG);
        put(4, NINE_YEARS);
        put(5, TWELVE_YEARS);
        put(6, CHU_GUAN_LIAN_DU);
    }});
}
