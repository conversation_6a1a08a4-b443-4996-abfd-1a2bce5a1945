package com.shida.excel;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @Author: 老木头
 * @Date: 2022/1/14 13:29
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface ExcelAlias {
    /**
     * 表头名称
     **/
    String value() default "表头";

    /**
     * 字段类型
     **/
    String type() default "String";

    /**
     * 排序字段，导出时按照排序字段正序排列
     * @return
     */
    int orderNo() default 999;
}
