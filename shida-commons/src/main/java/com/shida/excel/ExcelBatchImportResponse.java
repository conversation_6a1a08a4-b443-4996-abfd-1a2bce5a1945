package com.shida.excel;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@Schema(name = "ExcelBatchImportResponse", description = "批量导入响应信息")
public class ExcelBatchImportResponse implements Serializable {
    @Schema(description = "消息内容")
    private String message;
    @Schema(description = "行数")
    private Integer rowIndex;
    @Schema(description = "类型")
    private String type = "error";
}
