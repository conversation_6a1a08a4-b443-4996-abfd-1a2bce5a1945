package com.shida.excel;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.shida.enums.RestCode;
import com.shida.exceptions.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Excel表格操作工具类
 *
 * @Author: 老木头
 * @Date: 2022/1/14 10:46
 */
@Slf4j
public final class ExcelHelper<T> {
    private static Integer defaultColumnWidth = 18;
    private final Class<T> clazz;
    private ExcelWriter excelWriter;
    private List<Field> fields;
    private Boolean autoColumnSize;
    private Integer headerRowIndex = 0;

    public static <T> ExcelHelper<T> create(Class<T> clazz) {
        return new ExcelHelper<>(clazz);
    }

    public static <T> ExcelHelper<T> create(Class<T> clazz, Integer headerRowIndex) {
        return new ExcelHelper<>(clazz, headerRowIndex);
    }


    public static <T> ExcelHelper<T> create(Class<T> clazz, Boolean autoColumnSize) {
        return new ExcelHelper<>(clazz, autoColumnSize);
    }

    private ExcelHelper(Class<T> clazz) {
        this.clazz = clazz;
        this.headerRowIndex = 0;
        this.excelWriter = ExcelUtil.getBigWriter();
        this.initHeaders();
        this.excelWriter.setOnlyAlias(true);
        this.autoColumnSize = false;
    }

    private ExcelHelper(Class<T> clazz, Integer headerRowIndex) {
        this.clazz = clazz;
        this.headerRowIndex = headerRowIndex;
        this.excelWriter = ExcelUtil.getBigWriter();
        this.initHeaders();
        this.excelWriter.setOnlyAlias(true);
        this.autoColumnSize = false;
    }

    private ExcelHelper(Class<T> clazz, Boolean autoColumnSize) {
        this.clazz = clazz;
        this.excelWriter = ExcelUtil.getBigWriter();
        this.initHeaders();
        this.excelWriter.setOnlyAlias(true);
        this.autoColumnSize = autoColumnSize;
    }

    public void setAutoColumnSize(boolean autoColumnSize) {
        this.autoColumnSize = autoColumnSize;
    }

    /**
     * 写入表头
     */
    private void initHeaders() {
        try {
            // excel 别名信息
            fields = CollUtil.newArrayList(clazz.newInstance().getClass().getDeclaredFields())
                .stream().filter(r -> Objects.nonNull(r.getAnnotation(ExcelAlias.class)))
                .collect(Collectors.toList());
            Class<?> myClazz = clazz.getSuperclass();
            while (!myClazz.newInstance().getClass().getName().equals("java.lang.Object")) {
                fields.addAll(CollUtil.newArrayList(myClazz.newInstance().getClass().getDeclaredFields()));
                myClazz = myClazz.getSuperclass();
            }
            fields.sort(Comparator.comparingInt(o -> o.getAnnotation(ExcelAlias.class).orderNo()));
            fields.forEach(field -> excelWriter.addHeaderAlias(field.getName(), field.getAnnotation(ExcelAlias.class).value()));
            for (int i = 0; i < fields.size(); i++) {
                excelWriter.setColumnWidth(i, defaultColumnWidth);
            }
        } catch (IllegalAccessException | InstantiationException e) {
            throw new ServiceException("读取表头失败");
        }
    }

    /**
     * 读取数据
     *
     * @param filePath
     * @return
     * @throws IllegalAccessException
     * @throws InstantiationException
     */
    public List<T> readData(String filePath) {
        return readData(filePath, false);
    }

    /**
     * 读取数据
     *
     * @param filePath 文件路径
     * @param notEmpty 不能为空excel
     * @return
     * @throws IllegalAccessException
     * @throws InstantiationException
     */
    public List<T> readData(String filePath, Boolean notEmpty) {
        try {
            File file = FileUtil.file(filePath);
            if (!file.exists()) {
                throw new IOException("文件未找到");
            }
            List<T> result = readData(file);
            if (CollUtil.isEmpty(result) && notEmpty) {
                throw new ServiceException("文件内容为空，请检查");
            }
            return result;
        } catch (IllegalAccessException | InstantiationException | IOException e) {
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 读取数据
     *
     * @param file
     * @return
     * @throws IllegalAccessException
     * @throws InstantiationException
     */
    public List<T> readData(File file) throws IllegalAccessException, InstantiationException {
        InputStream inputStream = FileUtil.getInputStream(file);
        return readData(inputStream);
    }

    /**
     * 读取数据
     *
     * @param inputStream
     * @return
     */
    public List<T> readData(InputStream inputStream) {
        ExcelReader reader = cn.hutool.poi.excel.ExcelUtil.getReader(inputStream);
        List<Object> headers = reader.readRow(headerRowIndex);
        fields.forEach(field -> {
            ExcelAlias excelAlias = field.getAnnotation(ExcelAlias.class);
            if (headers.stream().noneMatch(obj -> obj.toString().equals(excelAlias.value()))) {
                throw new ServiceException("excel表头错误，请重新上传");
            }
            reader.addHeaderAlias(excelAlias.value(), field.getName());
        });
        return reader.readAll(clazz);
    }

    public static <T> List<T> readData(InputStream inputStream, Class<T> clazz) {
        ExcelHelper<T> excelHelper = ExcelHelper.create(clazz);
        return excelHelper.readData(inputStream);
    }

    public static <T> List<T> readData(InputStream inputStream, Class<T> clazz, Integer headerRowIndex) {
        ExcelHelper<T> excelHelper = ExcelHelper.create(clazz, headerRowIndex);
        return excelHelper.readData(inputStream);
    }

    /**
     * 写入数据
     * excel最多支持65535条数据
     *
     * @param rows
     */
    public void writeData(List<T> rows) {
        excelWriter.write(rows);
    }

    public void setSheet(String sheetName) {
        excelWriter.setSheet(sheetName);
    }

    public void setSheet(int sheetIndex, String sheetName) {
        excelWriter.renameSheet(sheetIndex, sheetName);
    }

    public void writeData(List<T> rows, List<CellStyle> rowStyles) {
        excelWriter.write(rows);
        for (int y = 0; y < rowStyles.size(); y++) {
            excelWriter.setRowStyle(y, rowStyles.get(y));
        }
    }

    /**
     * 设置某列文字颜色
     *
     * @param columnIndex     列索引
     * @param equalsCellValue 对比单元格值，不为空时，单元格值与给出的单元格值一样时文字颜色为fontColor，为空时，整列的文字颜色为fontColor
     * @param fontColor       文本颜色
     */
    public void setCellTextColor(Integer columnIndex, Object equalsCellValue, Short fontColor) {
        int rowCount = excelWriter.getRowCount();
        Font font = excelWriter.createFont();
        font.setColor(fontColor);
        CellStyle cellStyle = excelWriter.createCellStyle();
        cellStyle.setFont(font);
        // 内容⽔平居中
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        // 内容垂直居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        for (int i = 1; i < rowCount; i++) {
            try {
                Cell cell = excelWriter.getCell(columnIndex, i);
                if (Objects.nonNull(equalsCellValue)) {
                    if (cell.getStringCellValue().equals(equalsCellValue.toString())) {
                        cell.setCellStyle(cellStyle);
                    }
                } else {
                    cell.setCellStyle(cellStyle);
                }
            } catch (Exception e) {
                System.out.println("columnIndex:" + columnIndex + ",rowIndex:" + i);
            }
        }
    }


    Map<Short, CellStyle> cellStyles = new HashMap<>();


    public void setRowFillColor(Integer rowIndex, IndexedColors color) {
        int columnCount = excelWriter.getColumnCount(rowIndex);

        for (int c = 0; c < columnCount; c++) {
            Cell cell = excelWriter.getCell(c, rowIndex);
            if (cellStyles.containsKey(color.index)) {
                cell.setCellStyle(cellStyles.get(color.index));
            } else {
                CellStyle cellStyle = excelWriter.createCellStyle();
                cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                cellStyle.setFillForegroundColor(color.index);
                Font font = excelWriter.createFont();
                font.setColor(IndexedColors.BLACK.index);
                cellStyle.setFont(font);
                // 内容⽔平居中
                cellStyle.setAlignment(HorizontalAlignment.CENTER);
                // 内容垂直居中
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                cellStyle.setBorderBottom(BorderStyle.THIN);
                cellStyle.setBorderLeft(BorderStyle.THIN);
                cellStyle.setBorderTop(BorderStyle.THIN);
                cellStyle.setBorderRight(BorderStyle.THIN);
                cellStyles.put(color.index, cellStyle);
                cell.setCellStyle(cellStyle);
            }
        }
    }

    /**
     * 设置某列文字颜色
     *
     * @param columnValueMap 列索引与值 map,对比单元格值，不为空时，单元格值与给出的单元格值一样时文字颜色为fontColor，为空时，整列的文字颜色为fontColor
     * @param fontColor      文本颜色
     */
    public void setCellTextColor(Map<Integer, Object> columnValueMap, Short fontColor) {
        if (CollUtil.isEmpty(columnValueMap)) {
            return;
        }
        int rowCount = excelWriter.getRowCount();
        Font font = excelWriter.createFont();
        font.setColor(fontColor);
        CellStyle cellStyle = excelWriter.createCellStyle();
        cellStyle.setFont(font);
        // 内容⽔平居中
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        // 内容垂直居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        columnValueMap.forEach((colIndex, value) -> {
            for (int i = 1; i < rowCount; i++) {
                try {
                    Cell cell = excelWriter.getCell(colIndex, i);
                    if (Objects.nonNull(value)) {
                        if (cell.getStringCellValue().equals(value.toString())) {
                            cell.setCellStyle(cellStyle);
                        }
                    } else {
                        cell.setCellStyle(cellStyle);
                    }
                } catch (Exception e) {
                    System.out.println("columnIndex:" + colIndex + ",rowIndex:" + i);
                }
            }
        });
    }

    /**
     * 生成文件
     *
     * @param file
     */
    public void writeToFile(File file) {
        writeToFile(null, file);
    }

    /**
     * 写入数据并生成文件
     *
     * @param rows
     * @param file
     */
    public void writeToFile(List<T> rows, File file) {
        if (CollUtil.isNotEmpty(rows)) {
            this.writeData(rows);
        }
        if (!file.exists()) {
            FileUtil.mkParentDirs(file);
        }
        if (autoColumnSize) {
            if (excelWriter.getSheetCount() > 1) {
                for (int i = 0; i < excelWriter.getSheetCount(); i++) {
                    excelWriter.setSheet(i);
                    setSizeColumn();
                }
            } else {
                //设置自动宽度，不考虑合并单元格
                setSizeColumn();
            }
        }
        excelWriter.flush(file);
        excelWriter.close();
    }

    /**
     * 合并单元格
     */
    public void merge(int firstRow, int lastRow, int firstColumn, int lastColumn, Object content) {
        excelWriter.merge(firstRow, lastRow, firstColumn, lastColumn, content, true);
    }

    public void merge(int firstRow, int lastRow, int firstColumn, int lastColumn, Object content, boolean isSetHeaderStyle) {
        excelWriter.merge(firstRow, lastRow, firstColumn, lastColumn, content, isSetHeaderStyle);
    }

    /**
     * 写入web响应
     *
     * @param response
     */
    public void writeToWebResponse(HttpServletResponse response) {
        String fileName = DateUtil.format(LocalDateTime.now(), "yyyyMMddHHmm");
        this.writeToWebResponse(null, response, fileName);
    }

    /**
     * 写入web响应
     *
     * @param response
     */
    public void writeToWebResponse(HttpServletResponse response, String fileName) {
        this.writeToWebResponse(null, response, fileName);
    }

    /**
     * 写入web响应
     *
     * @param rows
     * @param response
     */
    public void writeToWebResponse(List<T> rows, HttpServletResponse response) {
        String fileName = DateUtil.format(LocalDateTime.now(), "yyyyMMddHHmm");
        this.writeToWebResponse(rows, response, fileName);
    }

    public void writeToWebResponse(List<T> rows, HttpServletResponse response, String fileName) {
        ServletOutputStream out = null;

        try {
            if (CollUtil.isNotEmpty(rows)) {
                this.writeData(rows);
            }
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            if (StrUtil.isBlank(fileName)) {
                fileName = DateUtil.format(LocalDateTime.now(), "yyyyMMddHHmm");
            }
            fileName = java.net.URLEncoder.encode(fileName, "utf-8") + ".xlsx";
            //  解决下载文件名中文乱码问题
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            response.setHeader("FileName", fileName);
            out = response.getOutputStream();
            if (this.autoColumnSize) {
                setSizeColumn();
            }
            excelWriter.flush(out, true);
        } catch (Exception e) {
            throw new ServiceException(RestCode.EXCEL_TEMPLATE_ERROR);
        } finally {
            // 关闭writer，释放内存
            excelWriter.close();
            if (out != null) {
                //此处记得关闭输出Servlet流
                IoUtil.close(out);
            }
        }
    }

    public static <T> void writeToResponse(Class<T> clazz, List<T> rows, String fileName, HttpServletResponse response) {
        ServletOutputStream out = null;

        ExcelHelper<T> excelHelper = ExcelHelper.create(clazz);

        try {
            if (CollUtil.isNotEmpty(rows)) {
                excelHelper.writeData(rows);
            }
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            if (StrUtil.isBlank(fileName)) {
                fileName = DateUtil.format(LocalDateTime.now(), "yyyyMMddHHmm");
            }
            if (!fileName.endsWith(".xlsx")) {
                fileName += ".xlsx";
            }
            fileName = java.net.URLEncoder.encode(fileName, "utf-8");
            //  解决下载文件名中文乱码问题
            response.setHeader("content-disposition", "attachment;filename=" + fileName);
            response.setHeader("FileName", fileName);
            out = response.getOutputStream();
            excelHelper.excelWriter.flush(out, true);
        } catch (Exception e) {
            throw new ServiceException(RestCode.EXCEL_REPORT_ERROR);
        } finally {
            try {
                // 关闭writer，释放内存
                excelHelper.excelWriter.close();
                if (out != null) {
                    //此处记得关闭输出Servlet流
                    IoUtil.close(out);
                }
            } catch (Exception e) {
                throw e;
            }
        }
    }


    /**
     * 自适应宽度(中文支持)
     */
    private void setSizeColumn() {
        for (int columnNum = 0; columnNum < fields.size(); columnNum++) {
            excelWriter.autoSizeColumn(columnNum);
            int width = excelWriter.getSheet().getColumnWidth(columnNum) * 14 / 10 / 256;
            excelWriter.setColumnWidth(columnNum, width > 60 ? 30 : width);
        }
    }

    public ExcelWriter getWriter() {
        return excelWriter;
    }
}
