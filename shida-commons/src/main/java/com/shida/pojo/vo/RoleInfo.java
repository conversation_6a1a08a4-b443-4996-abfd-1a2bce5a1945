package com.shida.pojo.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@Schema(description = "角色信息")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RoleInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "角色ID")
    private Long id;

    @Schema(description = "角色名称")
    private String roleName;

    @Schema(description = "角色代码")
    private String roleCode;

    @Schema(description = "管理员标识")
    private Integer adminFlag;

    @Schema(description = "用户数量")
    private Integer userCount;

    @Schema(description = "是否启用，0禁用，1启用")
    private Integer status;

    @Schema(description = "是否是内置角色")
    private Boolean sysRoleFlag;

    @Schema(description = "自定义角色所属内置角色代码")
    private String sysRoleCode;
}
