package com.shida.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@Schema(name = "PermissionInfo", description = "权限信息")
public class PermissionInfo implements Serializable {
    @Schema(description = "菜单id")
    private Long id;
    @Schema(description = "菜单名称")
    private String name;
    @Schema(description = "路由地址")
    private String router;
    @Schema(description = "组件路径")
    private String component;
    @Schema(description = "菜单代码")
    private String code;
    @Schema(description = "权限字符")
    private String permission;
    @Schema(description = "是否显示，0隐藏，1显示")
    private Integer visible;
    @Schema(description = "父ID")
    private Long parentId;
    @Schema(description = "排序")
    private Integer orderNum;
    @Schema(description = "菜单类型（M目录 C菜单 F按钮）")
    private String type;

}

