package com.shida.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
public class MenuInfo implements Serializable {

    @Schema(description = "菜单id")
    private Long id;

    @Schema(description = "菜单名称")
    private String name;

    @Schema(description = "菜单代码")
    private String code;

    @Schema(description = "菜单类型（M目录 C菜单 F按钮）")
    private String type;

    @Schema(description = "权限代码")
    private String permission;

    @Schema(description = "排序字段")
    private Integer orderNum;

    @Schema(description = "父菜单ID")
    private Long parentId;

    @Schema(description = "路由地址")
    private String router;

    @Schema(description = "组件地址")
    private String component;

    @Schema(description = "菜单显示状态，0隐藏，1显示")
    private Integer visible;

    @Schema(description = "子菜单")
    private List<MenuInfo> children;
}
