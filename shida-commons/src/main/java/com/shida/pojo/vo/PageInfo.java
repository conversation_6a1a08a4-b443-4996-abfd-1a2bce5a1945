package com.shida.pojo.vo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/11/11 21:30
 * @description：
 * @modified By：
 */
@Data
@Accessors(chain = true)
@Schema(description = "分页数据响应")
public class PageInfo<T extends Serializable> implements Serializable {
    /**
     * 总数
     */
    @Schema(description = "总条数")
    private long total = 0;
    /**
     * 每页显示条数，默认 10
     */
    @Schema(description = "每页条数")
    private long size = 10;
    /**
     * 当前页
     */
    @Schema(description = "当前页码")
    private long current = 1;
    /**
     * 总页数
     */
    @Schema(description = "总页数")
    private int totalPage = 0;
    /**
     * 总人数
     */
    @Schema(description = "报名总人数")
    private int signNum = 0;
    /**
     * 当前页数据
     */
    @Schema(description = "当前页数据")
    private List<T> records = new ArrayList<>();

    /**
     * 是否存在上一页
     *
     * @return true / false
     */
    @Schema(description = "是否有上一页")
    public boolean hasPrevious() {
        return this.current > 1;
    }

    /**
     * 是否存在下一页
     *
     * @return true / false
     */
    @Schema(description = "是否有下一页")
    public boolean hasNext() {
        return this.current < this.totalPage;
    }

    public PageInfo() {
    }

    public PageInfo(long current, long size, long total, List<T> records) {
        this.current = current;
        this.size = size;
        this.total = total;
        this.records = records;
        long pages = total % size != 0L ? total / size + 1 : total / size;
        this.totalPage = (int) pages;
    }

    public static <E extends Serializable> PageInfo<E> fromMybatisPage(IPage<E> page) {
        PageInfo<E> result = new PageInfo<>();
        result.setCurrent(page.getCurrent());
        result.setRecords(page.getRecords());
        result.setSize(page.getSize());
        result.setTotal(page.getTotal());
        result.setTotalPage(Long.valueOf(page.getPages()).intValue());
        return result;
    }

    public static <E extends Serializable> PageInfo<E> fromMybatisPage(IPage page, Class<E> clazz) {
        PageInfo<E> result = new PageInfo<>();
        result.setCurrent(page.getCurrent());
        if (page.getRecords().size() > 0) {
            result.setRecords(BeanUtil.copyToList(page.getRecords(),clazz, CopyOptions.create().ignoreNullValue()));
        } else {
            result.setRecords(new ArrayList<>());
        }
        result.setSize(page.getSize());
        result.setTotal(page.getTotal());
        result.setTotalPage(Long.valueOf(page.getPages()).intValue());
        return result;
    }
}
