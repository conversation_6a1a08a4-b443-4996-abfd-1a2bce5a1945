package com.shida.pojo.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.shida.enums.RestCode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR> mu tou
 * @date ：Created in 2021/10/16 10:34
 * @description：接口响应对象
 * @modified By：
 */
@Data
@Schema(name = "RestMessage" , description ="统一响应对象")
@Accessors(chain = true)
public class RestMessage<T> implements Serializable {
    @Schema(description = "状态")
    private Integer status;
    @Schema(description = "消息")
    private String message;
    @Schema(description = "响应数据")
    private T data;
    @Schema(description = "时间戳")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime timestamp;

    public RestMessage() {
        this.status = 200;
        this.message = "ok";
        this.timestamp = LocalDateTime.now();
    }

    public RestMessage(RestCode status) {
        this.status = status.code();
        this.message = status.message();
        this.timestamp = LocalDateTime.now();
    }

    public RestMessage(RestCode status, String message) {
        this.status = status.code();
        this.message = message;
        this.timestamp = LocalDateTime.now();
    }

    public RestMessage(RestCode status, T data) {
        this.status = status.code();
        this.message = status.message();
        this.data = data;
        this.timestamp = LocalDateTime.now();
    }

    public RestMessage(Integer code, String message, T data) {
        this.status = code;
        this.message = message;
        this.data = data;
        this.timestamp = LocalDateTime.now();
    }

    public static <T> RestMessage<T> success() {
        return new RestMessage<>();
    }

    public static <T> RestMessage<T> success(Integer code, String message) {
        return new RestMessage<T>(RestCode.SUCCESS, message);
    }

    public static <T> RestMessage<T> success(T data) {
        return new RestMessage<>(RestCode.SUCCESS, data);
    }

    public static <T> RestMessage<T> success(String message, T data) {
        return new RestMessage<T>(RestCode.SUCCESS.code(), message, data);
    }

    public static <T> RestMessage<T> error() {
        return new RestMessage<T>(RestCode.UNKNOWN);
    }

    public static <T> RestMessage<T> error(RestCode status) {
        return new RestMessage<T>(status);
    }

    public static <T> RestMessage<T> error(String message) {
        return new RestMessage<T>(RestCode.UNKNOWN, message);
    }

    public static <T> RestMessage<T> error(RestCode status, String message) {
        return new RestMessage<T>(status, message);
    }

    public static <T> RestMessage<T> error(Integer status, String message) {
        return new RestMessage<T>(status, message, null);
    }
}
