package com.shida.pojo.vo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.shida.constant.RoleConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@Accessors(chain = true)
@Schema(name = "AccountInfo", description = "账户信息")
public class AccountInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "ID")
    private Long id;
    @Schema(description = "用户名")
    private String username;
    @Schema(description = "昵称")
    private String nickname;
    @Schema(description = "邮箱")
    private String email;
    @Schema(description = "状态")
    private Integer status;
    @Schema(description = "手机号")
    private String mobile;
    @Schema(description = "token")
    private String token;
    @Schema(description = "角色列表")
    private List<RoleInfo> roles = new ArrayList<>();
    @Schema(description = "权限列表")
    private List<PermissionInfo> permissions = new ArrayList<>();
    @Schema(description = "菜单列表")
    private List<MenuInfo> menus = new ArrayList<>();
    @Schema(description = "是否默认密码")
    private Boolean defaultPasswordFlag;
    @Schema(description = "区县ID")
    private Long regionId;
    @Schema(description = "部门名称")
    private String regionName;
    @Schema(description = "部门code")
    private String regionCode;
    @Schema(description = "学校ID")
    private Long schoolId;
    @Schema(description = "学校名称")
    private String schoolName;
    @Schema(description = "学校代码")
    private String schoolCode;
    @Schema(description = "是否是管理员")
    private Boolean adminFlag;

    @Schema(description = "是否是市管理员")
    private Boolean cityAdminFlag;

    @Schema(description = "是否是县管理员")
    private Boolean countyAdminFlag;

    @Schema(description = "是否是学校管理员")
    private Boolean schoolAdminFlag;

    public Boolean getCityAdminFlag() {
        if (CollUtil.isNotEmpty(this.roles)) {
            boolean result = this.roles.stream().anyMatch(role -> role.getRoleCode().equals(RoleConstant.CITY_ADMIN_CODE)
                    || role.getRoleCode().equals(RoleConstant.SUPER_ADMIN_CODE));

            boolean result2 = this.roles.stream().anyMatch(role ->
                    StrUtil.isNotBlank(role.getSysRoleCode()) &&
                            (role.getSysRoleCode().equals(RoleConstant.CITY_ADMIN_CODE) || role.getSysRoleCode().equals(RoleConstant.SUPER_ADMIN_CODE)));
            return result || result2;
        } else {
            return false;
        }
    }

    public Boolean getCountyAdminFlag() {
        return CollUtil.isNotEmpty(this.roles) && this.roles.stream().anyMatch(role -> role.getRoleCode().equals(RoleConstant.COUNTY_ADMIN_CODE) ||
                (StrUtil.isNotBlank(role.getSysRoleCode()) && role.getSysRoleCode().equals(RoleConstant.COUNTY_ADMIN_CODE)));
    }

    public Boolean getSchoolAdminFlag() {
        return CollUtil.isNotEmpty(this.roles) && this.roles.stream().anyMatch(role -> role.getRoleCode().equals(RoleConstant.SCHOOL_ADMIN_CODE) ||
                (StrUtil.isNotBlank(role.getSysRoleCode()) && role.getSysRoleCode().equals(RoleConstant.SCHOOL_ADMIN_CODE)));
    }

}
