package com.shida.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@Schema(name = "KeyValue", description = "键值对")
public class KeyValue<K extends Serializable, V extends Serializable> implements Serializable {
    @Schema(description = "键")
    private K key;
    @Schema(description = "值")
    private V value;
    @Schema(description = "附加值")
    private String extra;

    public KeyValue() {
    }
    public KeyValue(K key, V value) {
        this.key = key;
        this.value = value;
        this.extra = null;
    }
    public KeyValue(K key, V value, String extra) {
        this.key = key;
        this.value = value;
        this.extra = extra;
    }
}
