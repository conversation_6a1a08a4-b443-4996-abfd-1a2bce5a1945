package com.shida.pojo.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author: 老木头
 * @Date: 2022/1/14 9:14
 */
@Data
@Accessors(chain = true)
@Schema(name = "UploadFile", description = "上传的文件信息")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FileInfoLog implements Serializable {
    @Schema(description = "文件ID")
    private Long id;
    @Schema(description = "原文件名称")
    private String name;
    @Schema(description = "服务器上文件名称")
    private String serverFileName;
    @Schema(description = "url")
    private String url;
    @Schema(description = "存储地址", hidden = true)
    private String filePath;
    @Schema(description = "上传时间")
    private LocalDateTime createTime;
    @Schema(description = "上传用户ID")
    private Long userId;
    @Schema(description = "上传用户名称")
    private String username;
    @Schema(description = "部门编码")
    private String deptCode;
}
