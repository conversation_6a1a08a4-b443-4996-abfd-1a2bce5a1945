package com.shida.pojo.search;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@Schema(name = "Search", description = "搜索条件")
public class Search implements Serializable {
    @Schema(description = "页码")
    private Integer pageNumber;
    @Schema(description = "每页条数")
    private Integer pageSize;
    @Schema(description = "搜索关键字")
    private String keywords;
}
