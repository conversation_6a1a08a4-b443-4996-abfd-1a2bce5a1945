package com.shida.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
public abstract class BaseEntity<M extends BaseEntity<M>> extends Model<M> implements Serializable {

    @Id
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    protected Long id;

    @TableField(fill = FieldFill.INSERT)
    protected LocalDateTime createTime;
}