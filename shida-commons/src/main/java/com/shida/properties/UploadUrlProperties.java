package com.shida.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

@Data
@Component
@ConfigurationProperties(prefix = "upload.url")
public class UploadUrlProperties {
    private String rootUrl;
    private String avatar;
    private String image;
    private String editor;


    public String getRootUrl() {
        LocalDate localDate = LocalDate.now();
        return rootUrl + localDate.getYear() + "/" + localDate.getMonth().getValue();
    }

    public String getAvatar() {
        return getRootUrl() + avatar;
    }

    public String getImage() {
        return getRootUrl() + image;
    }

    public String getEditor() {
        return getRootUrl() + editor;
    }
}
