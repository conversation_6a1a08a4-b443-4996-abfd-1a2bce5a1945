package com.shida.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.Year;

@Data
@Component
@ConfigurationProperties(prefix = "upload.file")
public class UploadFileProperties {
    private String rootPath;

    public String getRootPath() {
        LocalDate localDate = LocalDate.now();
        return rootPath + localDate.getYear() + "/" + localDate.getMonth().getValue();
    }

    private String avatarPath;
    private String imagePath;
    private String otherPath;
    private String tempPath;
    private String editorPath;

    public String getAvatarPath() {
        return getRootPath() + avatarPath;
    }

    public String getImagePath() {
        return getRootPath() + imagePath;
    }

    public String getOtherPath() {
        return getRootPath() + otherPath;
    }

    public String getTempPath() {
        return getRootPath() + tempPath;
    }

    public String getEditorPath() {
        return getRootPath() + editorPath;
    }

}
