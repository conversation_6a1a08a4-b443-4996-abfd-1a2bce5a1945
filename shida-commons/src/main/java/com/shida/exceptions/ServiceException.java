package com.shida.exceptions;


import cn.hutool.core.util.StrUtil;
import com.shida.enums.RestCode;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2021/11/9 22:42
 * @description：
 * @modified By：
 */
@Data
public class ServiceException extends RuntimeException {
    private Integer code;
    private String message;

    public ServiceException(Integer code) {
        super();
        this.code = code;
    }

    public ServiceException(RestCode code) {
        super(code.message());
        this.code = code.code();
        this.message = super.getMessage();
    }

    public ServiceException(String message) {
        super(message);
        this.code = RestCode.UNKNOWN.code();
        this.message = super.getMessage();
    }

    public ServiceException(String message, Object... params) {
        super(StrUtil.format(message, params));
        this.code = RestCode.UNKNOWN.code();
        this.message = super.getMessage();
    }

    public ServiceException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = super.getMessage();
    }

    public ServiceException(Integer code, String message, Object... params) {
        super(StrUtil.format(message, params));
        this.code = code;
        this.message = super.getMessage();
    }

    public ServiceException(RestCode code, String message) {
        super(message);
        this.code = code.code();
        this.message = super.getMessage();
    }

    public ServiceException(RestCode code, String message, Object... params) {
        super(StrUtil.format(message, params));
        this.code = code.code();
        this.message = super.getMessage();
    }
}
