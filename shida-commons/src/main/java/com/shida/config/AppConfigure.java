package com.shida.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: 老木头
 * @Date: 2022/1/20 14:26
 */
@Configuration
@Component
@Getter
@ConfigurationProperties(prefix = "sd-security")
public class AppConfigure {

    private static AppConfigure appConfigure;

    @Value("${spring.profiles.active:dev}")
    private String active;
    @Value("${sd-security.enable:false}")
    private Boolean security;
    @Value("${sd-security.exclude-urls:}")
    private List<String> securityExcludeUrls;
    @Value("${server.servlet.context-path:}")
    private String contextPath;

    @PostConstruct
    public void init() {
        appConfigure = this;
    }

    /**
     * 是否开发模式
     *
     * @return
     */
    public static Boolean isDevMode() {
        return appConfigure.active.startsWith("dev");
    }

    /**
     * 是否测试模式
     *
     * @return
     */
    public static Boolean isTestMode() {
        return appConfigure.active.startsWith("test");
    }

    /**
     * 是否生产模式
     *
     * @return
     */
    public static Boolean isProdMode() {
        return appConfigure.active.startsWith("prod");
    }

    public static Boolean securityStatus() {
        return appConfigure.security;
    }

    public static String getContextPath() {
        return appConfigure.contextPath;
    }

    public static List<String> getSecurityExcludeUrls() {
        List<String> excludeUrls = appConfigure.securityExcludeUrls;
        List<String> result = new ArrayList<>();
        for (String url : excludeUrls) {
            result.add(appConfigure.contextPath + url);
        }
        result.add(appConfigure.contextPath + "/securityStatus");
        return result;
    }

    public static String getCityCode() {
        return "130100";
    }
}
