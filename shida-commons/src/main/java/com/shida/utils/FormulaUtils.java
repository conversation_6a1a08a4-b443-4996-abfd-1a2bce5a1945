package com.shida.utils;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @date 2025/8/27 11:39
 * @description: 体质计算公式 工具类
 */
@Slf4j
public class FormulaUtils {
    // 体育课出勤记录分值-如果出勤率大于等于90，则得0.7分
    public static final BigDecimal PHYSICAL_EDUCATION_SCORE_MIDDLE = new BigDecimal("0.7");
    // 体育课出勤记录分值-如果出勤率小于90%大于等于80%，则得0.35分
    public static final BigDecimal PHYSICAL_EDUCATION_SCORE_LOW = new BigDecimal("0.35");
    // 体育课出勤记录分值-如果出勤率小于80%，则不得分
    public static final BigDecimal PHYSICAL_EDUCATION_SCORE_ZERO = new BigDecimal("0");
    // 体育课出勤记录分值-伤病拿42%
    public static final BigDecimal PHYSICAL_EDUCATION_SCORE_SICK = new BigDecimal("0.42");

    /**
     * 获取【体育课出勤记录】中缺勤次数
     * formula： 缺勤数=（病假-3）+（事假-3）+ 旷课数
     *
     * @param bjcs 病假次数
     * @param sjcs 事假次数
     * @param kkcs 旷课次数
     * @return 缺勤次数
     */
    public static Integer getQqcs(Integer bjcs, Integer sjcs, Integer kkcs) {
        ObjectUtil.defaultIfNull(bjcs, 0);
        ObjectUtil.defaultIfNull(sjcs, 0);
        ObjectUtil.defaultIfNull(kkcs, 0);
        int bj = Math.max(bjcs - 3, 0);
        int sj = Math.max(sjcs - 3, 0);
        return Math.max(bj + sj + kkcs, 0);
    }

    /**
     * 计算【体育课出勤记录】中出勤率
     * formula: 出勤率= （总课时 - 缺勤数） / 总课时
     *
     * @param zks 总课时
     * @param qqs 缺勤数
     * @return 出勤率
     */
    public static BigDecimal getCql(Integer zks, Integer qqs) {
        if (zks == null || zks <= 0) {
            log.error("非法的总课时值：{}", zks);
            return BigDecimal.ZERO;
        }
        if (qqs == null) {
            log.error("非法的缺勤数值");
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(zks - qqs).divide(BigDecimal.valueOf(zks), 2, RoundingMode.HALF_UP);
    }

    /**
     * 计算【体育课出勤记录】中出勤率
     *
     * @param zks  总课时
     * @param bjcs 病假次数
     * @param sjcs 事假次数
     * @param kkcs 旷课次数
     * @return 出勤率
     */
    public static BigDecimal getCql(Integer zks, Integer bjcs, Integer sjcs, Integer kkcs) {
        return getCql(zks, getQqcs(bjcs, sjcs, kkcs));
    }

    /**
     * 计算【体育课出勤记录】中体育课得分
     *
     * @param cql 出勤率
     * @return 体育课得分
     */
    public static BigDecimal getPhysicalEducationScore(BigDecimal cql) {
        if (cql == null) {
            return BigDecimal.ZERO;
        }
        // 如果出勤率大于等于90，则得0.7分,如果出勤率小于90%大于等于80%，则得0.35分,如果出勤率小于80%，则得0分
        return cql.compareTo(BigDecimal.valueOf(90)) >= 0 ?
            FormulaUtils.PHYSICAL_EDUCATION_SCORE_MIDDLE : cql.compareTo(BigDecimal.valueOf(80)) >= 0 ?
            FormulaUtils.PHYSICAL_EDUCATION_SCORE_LOW :
            FormulaUtils.PHYSICAL_EDUCATION_SCORE_ZERO;
    }
}
