package com.shida.utils;


import java.util.regex.Matcher;
import java.util.regex.Pattern;


public class PhoneUtils {
    /**
     * 验证手机号码是否符合中国大陆手机号格式。
     *
     * @param phoneNumber 待验证的手机号码字符串。
     * @return 如果手机号码合法返回true，否则返回false。
     */
    public static boolean isValidPhoneNumber(String phoneNumber) {
        // 中国大陆手机号正则表达式
        String regex = "^1[3-9]\\d{9}$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(phoneNumber);
        return matcher.matches();
    }
    public static void main(String[] args) {
        // 测试手机号验证方法
        System.out.println(isValidPhoneNumber("13812345678")); // 应输出true
        System.out.println(isValidPhoneNumber("12812345678")); // 应输出false
    }
}
