package com.shida.utils;

import cn.hutool.core.net.NetUtil;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import org.springframework.context.annotation.Bean;

public class IdUtil extends cn.hutool.core.util.IdUtil {

    private static Long workerId = null;
    private static Long datacenterId = null;


    @Bean
    public IdentifierGenerator idGenerator() {
        return entity -> IdUtil.getSnowflakeNextId();
    }

    public static long getSnowflakeNextId() {
        if (workerId == null || datacenterId == null) {
            String ip = NetUtil.getLocalhostStr();
            String mac = NetUtil.getLocalMacAddress();
            workerId = Math.abs((long) (ip.hashCode())) % 32;
            datacenterId = Math.abs((long) (mac.hashCode())) % 32;
        }
        return IdUtil.getSnowflake(workerId, datacenterId).nextId();
    }
}
