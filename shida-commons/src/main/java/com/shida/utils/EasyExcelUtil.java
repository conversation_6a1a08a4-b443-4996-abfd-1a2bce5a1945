package com.shida.utils;

import cn.hutool.core.date.DateUtil;

import lombok.extern.slf4j.Slf4j;


import javax.servlet.http.HttpServletResponse;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

/**
 * @Date 2024/5/20 15:01
 * <AUTHOR>
 */
@Slf4j
public class EasyExcelUtil {

    public static void export(HttpServletResponse response,String fileName){
        // 这里注意 有同学反应使用swagger 会导致各种问题，请直接用浏览器或者用postman
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        try {
            fileName = URLEncoder.encode(fileName , "UTF-8").replaceAll("\\+", "%20");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        response.setHeader("Content-disposition", "attachment;filename*=" + fileName + DateUtil.current() + ".xlsx");
    }


}
