package com.shida.enums;

/**
 * <AUTHOR>
 * @description
 * @PackageName: com.shida.enums
 * @ClassName: TeacherCategary
 * @Version 1.0
 * @date 2025/8/21 17:46
 */
public enum TeacherCategary {
    /**
     * 班主任
     */
    BANZHUREN("1", "班主任"),
    /**
     * 体育老师
     */
    TIYUJIAOSHI("2", "体育老师");

    private String code;
    private String name;
    private TeacherCategary(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
    public static String getByCode(String code) {
        for (TeacherCategary value : values()) {
            if (value.getCode().equals(code)) {
                return value.getName();
            }
        }
        return null;
    }
    public static String getByName(String name) {
        for (TeacherCategary value : values()) {
            if (value.getName().equals(name)) {
                return value.code;
            }
        }
        return null;
    }
}
