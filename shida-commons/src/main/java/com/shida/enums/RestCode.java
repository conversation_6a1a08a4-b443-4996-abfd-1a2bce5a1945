package com.shida.enums;

/**
 * API 统一返回状态码
 *
 * <AUTHOR> mu tou
 * @data 2020/8/1 14:34
 */
public enum RestCode {

    /**
     * 未知错误
     */
    UNKNOWN(500, "未知错误"),
    /**
     * 请求成功
     */
    SUCCESS(200, "ok"),
    /**
     * 未认证
     */
    UNAUTHORIZED(401, "未认证"),
    /**
     * 用户名或密码错误
     */
    AUTHORIZED_ERROR(402, "用户名或密码错误"),
    /**
     * 访问被拒绝
     */
    FORBIDDEN(403, "无访问权限"),
    /**
     * 未找到资源
     */
    NOT_FOUND(404, "未找到资源"),
    /**
     * 用户不存在
     */
    NOT_FOUND_USER(405, "账户不存在"),
    /**
     * 用户被冻结
     */
    ACCOUNT_FREEZE(406, "账户被冻结"),
    /**
     * 用户被冻结
     */
    ACCOUNT_EXPIRE(407, "账户过期"),

    /**
     * 重复提交
     */
    NO_REPEAT(408, "请勿重复提交"),
    /**
     * U_KEY_ERROR
     */
    U_KEY_ERROR(409, "USB KEY 错误"),

    /**
     * U_KEY_ERROR
     */
    ARGUMENT_ERROR(410, "参数错误"),
    /**
     * EXCEL 模板不存在
     */
    EXCEL_TEMPLATE_ERROR(411, "EXCEL 模板不存在"),
    /**
     * 导出的数据不存在
     */
    EXCEL_REPORT_NULL(412, "暂无数据可以导出"),
    /**
     * 导出数据时发生错误
     */
    EXCEL_REPORT_ERROR(413, "下载文件时发生错误，请稍后重试"),
    /**
     * 超出访问最大限制，限流
     */
    REQUEST_LIMIT_ERROR(429, "超出访问最大限制"),
    /**
     * 数据出错
     */
    DATA_ERROR(701, "数据错误")
    ;

    private Integer code;

    private String message;

    RestCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer code() {
        return this.code;
    }

    public String message() {
        return this.message;
    }

    @Override
    public String toString() {
        return this.name();
    }

    public static RestCode valueOfCode(Integer code) {
        RestCode[] values = RestCode.values();
        RestCode result = RestCode.UNKNOWN;
        for (int i = 0; i < values.length; i++) {
            if (values[i].code.equals(code)) {
                result = values[i];
            }
        }
        return result;
    }
}
