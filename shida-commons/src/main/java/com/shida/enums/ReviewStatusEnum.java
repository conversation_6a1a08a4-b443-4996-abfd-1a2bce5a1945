package com.shida.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 审核状态
 */
@Getter
@AllArgsConstructor
public enum ReviewStatusEnum {
    /**
     * 待区县审核
     */
    county_waiting(1, "待区县审核"),
    /**
     * 待市级审核
     */
    city_waiting(2, "待市级审核"),
    /**
     * 通过
     */
    pass(3, "通过"),
    /**
     * 驳回
     */
	reject(4, "驳回");

    private final Integer code;
    private final String name;
}
