package com.shida.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description:
 *
 * <AUTHOR>
 * @Create 2024/8/7 23:37
 */
@Getter
@AllArgsConstructor
public enum NationEnum {
    HAN(1,"汉族"),
    MENG_GU(2,"蒙古族"),
    HUI(3,"回族"),
    ZANG(4,"藏族"),
    WEI_WU_ER(5,"维吾尔族"),
    MIAO(6,"苗族"),
    YI(7,"彝族"),
    ZHUANG(8,"壮族"),
    BU_YI(9,"布依族"),
    CHAO_XIAN(10,"朝鲜族"),
    MAN(11,"满族"),
    DONG(12,"侗族"),
    YAO(13,"瑶族"),
    BAI(14,"白族"),
    TU_JIA(15,"土家族"),
    HA_NI(16,"哈尼族"),
    HA_SA_KE(17,"哈萨克族"),
    DAI(18,"傣族"),
    LI(19,"黎族"),
    LI_SU(20,"傈僳族"),
    WA(21,"佤族"),
    SHE(22,"畲族"),
    GAO_SHAN(23,"高山族"),
    LA_HU(24,"拉祜族"),
    SHUI(25,"水族"),
    DONG_XIANG(26,"东乡族"),
    NA_XI(27,"纳西族"),
    JING_PO(28,"景颇族"),
    KE_ER_KE_ZI(29,"柯尔克孜族"),
    SA_LA(30,"撒拉族"),
    DA_WO_ER(31,"达斡尔族"),
    MU_LAO(32,"仫佬族"),
    QIANG(33,"羌族"),
    BU_LANG(34,"布朗族"),
    HE_ZHE(35,"赫哲族"),
    MAO_NAN(36,"毛男族"),
    GE_LAO(37,"仡佬族"),
    XI_BO(38,"锡伯族"),
    A_CHANG(39,"阿昌族"),
    PU_MI(40,"普米族"),
    TA_JI_KE(41,"塔吉克族"),
    NU(42,"怒族"),
    WU_ZI_BIE_KE(43,"乌孜别克族"),
    E_LUO_SI(44,"俄罗斯族"),
    E_WEN_KE(45,"鄂温克族"),
    BENG_LONG(46,"崩龙族"),
    BAO_AN(47,"保安族"),
    YU_GU(48,"裕固族"),
    JING(49,"京族"),
    TA_TA_ER(50,"塔塔尔族"),
    DU_LONG(51,"独龙族"),
    E_LUN_CHUN(52,"鄂伦春族"),
    DE_ANG(53,"德昂族"),
    MEN_BA(54,"门巴族"),
    LUO_BA(55,"珞巴族"),
    JI_NUO(56,"基诺族"),
    QI_TA(57,"其他");

    private final Integer code;
    private final String message;

    public Integer code() {
        return this.code;
    }

    public String message() {
        return this.message;
    }

    @Override
    public String toString() {
        return this.name();
    }

    public static String get(Integer code) {
        NationEnum[] results = NationEnum.values();
        for(NationEnum result : results) {
            if(result.getCode().equals(code)) {
                return result.getMessage();
            }
        }
        return null;
    }
}
