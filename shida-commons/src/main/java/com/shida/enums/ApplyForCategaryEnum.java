package com.shida.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 申请类别
 */
@Getter
@AllArgsConstructor
public enum ApplyForCategaryEnum {
    /**
     * 残免
     */
    can_mian(1, "待区县审核"),
    /**
     * 伤免
     */
    shang_mian(2, "待市级审核"),
    /**
     * 病免
     */
    bing_mian(3, "通过"),
    /**
     * 病缓
     */
    bing_huan(4, "驳回"),
	/**
	 * 选考项目免考
	 */
	mian_kao(5, "驳回");

    private final Integer code;
    private final String name;
}
