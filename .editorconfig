# 表示这是根目录的配置文件，不再向上查找
root = true

# 所有文件的通用配置
[*]
# 字符编码设置为UTF-8
charset = utf-8
# 换行符使用LF（跨平台通用，尤其适合Linux/Mac环境）
end_of_line = lf
# 确保文件末尾添加一个空行
insert_final_newline = true
# 自动去除行尾多余的空格
trim_trailing_whitespace = true

# Java后端相关文件配置（.java, .xml等）
[*.{java,xml,properties}]
# 使用空格进行缩进
indent_style = space
# Java通常使用4个空格缩进
indent_size = 4

# 前端相关文件配置（Vue, JavaScript, TypeScript等）
[*.{vue,js,jsx,ts,tsx,json,css,scss,sass,less}]
# 使用空格进行缩进
indent_style = space
# 前端框架通常使用2个空格缩进
indent_size = 2

# HTML文件配置
[*.html]
indent_style = space
indent_size = 2
# HTML标签属性换行时的缩进偏移量
indent_attr_offset = 2

# Markdown文件配置
[*.md]
# Markdown中保留行尾空格（用于软换行）
trim_trailing_whitespace = false
indent_style = space
indent_size = 2

# YAML配置文件
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# 批处理文件（Windows环境）
[*.{bat,cmd}]
end_of_line = crlf