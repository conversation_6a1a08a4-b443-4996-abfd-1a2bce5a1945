# 体质健康系统架构与实现

## 1. 系统概述

本系统采用基于Spring Cloud Gateway的微服务架构，通过Redis实现分布式限流控制。系统通过网关层对所有进入的请求进行统一的流量控制，防止系统因突发流量而过载。

## 2. 系统架构

### 2.1 网关
shida-gateway: 网关层负责所有进入系统的请求的流量控制，使用Spring Cloud Gateway实现。

### 2.2 认证中心
shida-sso-center: 认证中心负责用户登录、登出、权限验证等功能，使用sa-token实现。

### 2.3 微服务服务
shida-user-center-service：用户中心服务
shida-region-business-service：区县业务服务（租户服务）
shida-log-center-service: 用户操作日志服务

### 2.4 代码生成器
shida-generator: 代码生成器，使用MyBatis-Plus实现。

### 2.5 微服务接口协议
shida-micro-apis: 微服务接口协议。

## 3. 中间件

### 3.1 Redis

用于实现分布式限流、sa-token 数据仓储，及应用缓存。

### 3.2 MySQL8

用于存储数据

### 3.3 MongoDB

用于存储日志数据

### 3.4 seata
用于实现分布式事务
```angular2html
地址：http://172.16.17.251:7091/
账号：seata
密码：seata
```
配置信息：
```yaml
seata:
  application-id: ${spring.application.name}
  tx-service-group: my_tx_group
  service:
    vgroup-mapping:
      my_tx_group: default
  registry:
    type: nacos
    nacos:
      server-addr: 172.16.17.222:8848
      username: nacos
      password: nacos
      namespace: sjz-healthy
      group: DEFAULT_GROUP
```

## 4. 前端连接开发环境

前端进行开发时需要的配置信息如下：
```angular2html
网关地址： http://***********:8080
聚合文档地址： http://***********:8080/doc.html
```

## 5. 后端开发注意事项

### 5.1 数据源配置 
在nacos的配置中心修改数据源配置：tenant-database.yaml，重启region-service后，数据库会自动创建表结构，无需手动创建。

### 5.2 数据表字段变更

当新增或修改字段时，需要手动写sql，防止开发环境修改表结构后，线上环境数据库结构不一致问题。把修改字段语句写入resource/sql/update_database.sql中，
程序启动时会自动执行该文件。

```sql
-- 更新数据库脚本
-- 每行一个sql语句，分号结尾，注释语句需用--开头
-- 删除表b_class_adjust_record

```

### 5.3 新增数据表需要进行的操作

需要把建表语句写入 resource/sql/init_database.sql 中，程序启动时会自动执行该文件，如果当前库中缺少该表，会自动创建该表。


## 6. 开发服务器中间件配置信息
### 6.1. Redis
```yaml
redis: 
  host: ***********
  port: 6379
  database: 4
```

### 6.2 MongoDB
```yaml
spring:
  data:
    mongodb:
      host: ***********
      port: 27017
      database: sjz-tizhi-log
```

### 6.3 MySQL8
```yaml
mysql:
  driver-class-name: com.mysql.cj.jdbc.Driver
  host: ***********
  port: 3306
  username: root
  password: Pass123!
```

### 6.4 seata
```yaml
seata:
  host: ***********
  port: 7091
  username: seata
  password: seata
```

### 6.5 前端管理端地址信息

http://***********:9000/tz-admin/

### 6.6 druid 监控面板

1. 用户中心：http://***********:12000/druid/index.html
2. 区县业务服务：http://***********:30102/druid/index.html
3. 用户名：shida_admin 密码：shida@2025

## 7. 开发规范

### 7.1 数据库字段命名方式规约
1. 统一使用下划线命名法，例如：user_id
2. 遇到比较特殊的字段比如：政治面貌，则使用中文首拼字母（大写）表示
3. 关键数据列，做唯一索引或联合唯一索引，防止产生重复数据
4. 字段必须填写注释
5. boolean字段默认使用0或1表示，0表示否，1表示是

### 7.2 数据表名命名规约
1. 基本信息表使用t_前缀，例如：t_user，t_region，t_school
2. 成绩表使用s_前缀，例如：s_sport_score，s_exam_score
3. 过程性数据表，使用b_ 前缀，例如：b_sport_rate_record (体育课出勤率记录表)
3. 后续如果有新的业务模块，再跟项目主管申请新的前缀，目的是可以根据前缀快速的定位该模块的数据表。
4. 表必须加表名注释
5. 设计数据表时，id与create_time字段是必须的，id字段必须使用bigint，create_time字段使用datetime

### 7.3 接口规范
1. 所有接口都需要添加@Operation注解，描述接口功能
2. 所有接口统一使用@PostMapping注解，尽可能的使用@RequestBody注解接收参数
3. 公共接口（不需要授权）要写在项目的CommonController文件中，如果CommonController代码量超过1000行，则拆分多个文件
4. 非公共接口（需要授权）要写在项目的业务模块中，需要添加 @SaCheckPermission 注解，描述接口权限，下载导入模板接口除外
5. 新增、修改、删除、导入、导出接口，必须添加 @AutoLog 注解，以实现自动记录操作日志功能
6. 新增、修改、删除、导入、导出接口，必须添加 @NoRepeatSubmit 注解，以防止重复提交
7. Service层有数据有新增、修改、删除、导入、导出接口，涉及到单数据源时，请使用 @Transactional 注解，以实现事务功能
8. Service层有数据有新增、修改、删除、导入、导出接口，涉及到微服务时，请使用 seata 提供的 @GlobalTransactional 注解，以实现事务功能
9. Service层返回数据禁止返回基础数据类型与Map对象，要返回对象、对象集合、及封装类型
10. Service层做批量导出时，要判定是否与获取分页数据的接口的查询参数一致，如果一致要使用 service 内部的 getPageData 函数做数据组装，防止出现导出的数据与页面上展示的数据不一致的情况
11. Controller层接口返回数据禁止返回 map 类型，要返回对象、对象集合、及封装类型
12. Controller层批量导入接口要返回 List<ExcelBatchImportResponse> 对象集合，以通知前端批量导入时发生的错误信息

### 7.4 数据库查询规范
1. 单表查询、修改、删除时尽量使用 mybatis-plus 提供的查询方法，例如：queryWrapper.eq(User::getId, id) 
2. 多表关联查询时，使用 xml 配置文件的方式进行查询，参数使用 @Param 注解
3. 由于项目架构采用的多租户，所以使用 xml 配置文件查询时，`禁止`进行跨库查询，可以通过微服务进行数据查询

### 7.5 entity, mapper, service 要使用代码生成器生成
1. entity, mapper, service 要使用代码生成器生成，生成代码时，请勿勾选生成 controller 要自行编写
2. 使用 shida-generator 模块进行代码生成 
> 其中，LmtRegionBusinessGenerator.java 是生成区县库中以 b_ 开头的数据表；\
> LmtRegionDatabaseGenerator.java 是生成区县库中以 t_ 开头的数据表；\
> LmtUserCenterGenerator.java 是生成账户中心库中以 t_ 开头的数据表；\
> 个人开发时，请自行复制对应的模板文件，修改成自己的代码生成器，名称以个人姓名首批字母大写开头；
3. 当使用int类型表示是否时 ，0表示否，1表示是。

### 7.6 枚举与常量
1. 公共枚举与公共常量，都在 shida-commons 模块中，如果不满足当前业务，请自行添加。
2. 如果已有该类型的常量类，请使用常量类，如果已有枚举类，请使用枚举类，避免常量与枚举重复。

### 7.7 数据导入导出
1. 导入excel表格时，一定要验证单元格内容，防止导入数据是错误数据。
2. 导入excel表格时，一定要验证单元格内容，防止导入反动的某些词汇。
3. 导入excel表格时，如果有关联学期，注意判定学期的状态，如果是已归档，则不能进行导入。
4. 数据导入时如果有关联学期，注意判定学期的状态，如果是已归档，则不能进行导入。
5. 如果字段固定，需要使用Excel对象进行数据导入导出，不能使用动态字段。
6. 导出excel表格时，尽量要使用 `ExcelHelper` 类进行数据导出，如果 `ExcelHelper` 不满足需求，请自行编写代码进行数据导出。
7. 导出excel表格时，请使用 `hutool` 框架提供的 `BigExcelWriter` 类进行数据导出，避免`xls` 最多只能导出65535行数据。

