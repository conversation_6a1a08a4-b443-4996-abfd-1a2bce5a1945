# Vue 3 + Vite + element-plus + axios + vue-router + pinia

## node版本：22

## 包安装工具： yarn

## 启动项目： yarn serve || yarn dev

## 统一开发工具：webStorm 2025

# 后台管理开发规范
## 1、顶部操作区域
操作按钮统一使用 el-button + icon，icon找比较适合的icon，各按钮要加 v-has-perm 权限验证。
- 新增按钮：primary
- 修改按钮：warning
- 删除按钮：danger
- 批量删除按钮：danger
- 批量导入按钮：primary
- 导出按钮：info
## 2、顶部查询区域
- 关键字检索与查询按钮：input + search
## 3、数据列表区域
操作列统一放到最后一列，操作按钮统一使用 el-link + icon，icon找比较适合的icon，各按钮要加 v-has-perm 权限验证。
- 详情按钮：primary
- 修改按钮：warning
- 删除按钮：danger
- 审核按钮：primary
- 禁用按钮：danger
- 启用按钮：success
- 重置密码按钮：warning
- 操作列要加 v-has-perm 权限验证（如详情按钮无权限要求，可以不加）
## 4、表单
- 统一用 el-dialog 弹窗展示表单页面
- 表单提交统一使用 el-dialog 的 footer 部分的 el-button 进行提交，提交时必须加 loading 属性，防止重复提交
- 表单内部必须加字段验证
- 表单内 el-input 字段，必须加 max-length 属性设置长度, 加 show-word-limit 属性显示输入长度限制
