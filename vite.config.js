import {defineConfig, loadEnv} from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import UnoCSS from 'unocss/vite' // 添加: 引入 UnoCSS 插件
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import {ElementPlusResolver} from 'unplugin-vue-components/resolvers'
import vueDevTools from 'vite-plugin-vue-devtools'


export default defineConfig(({mode}) => {
    const env = loadEnv(mode, process.cwd())
    return {
        base: '/tz-admin/',
        plugins: [
            vue(),
            UnoCSS(),
            AutoImport({
                resolvers: [ElementPlusResolver()],
            }),
            Components({
                resolvers: [ElementPlusResolver()],
            }),
            vueDevTools()
        ],
        resolve: {
            alias: {
                '@': path.resolve(__dirname, 'src')
            }
        },
        // 新增构建配置
        build: {
            // 指定构建输出目录
            outDir: "tz-admin",
            // 使用 "esbuild" 作为代码压缩工具，适合生产环境
            minify: "esbuild",
            // 设定 Chunk 大小警告阈值，超过此大小会有警告
            chunkSizeWarningLimit: 1500,
            // 目标 JavaScript 版本
            target: "es2022",
            // Rollup 输出配置
            rollupOptions: {
                output: {
                    // 设置 Chunk 文件的输出命名规则
                    chunkFileNames: "assets/js/chunk.[hash].js",
                    // 设置入口文件的输出命名规则
                    entryFileNames: "assets/js/app.[hash].js",
                    // 设置静态资源文件的输出命名规则
                    assetFileNames: "assets/[ext]/[name]-[hash].[ext]",
                    manualChunks(id) {
                        if (id.includes('node_modules')) {
                            return id.toString().split('node_modules/')[1].split('/')[0].toString()
                        }
                    }
                },
                // 确保所有需要的文件都被包含
                plugins: [
                    {
                        name: 'watch-external',
                        async buildStart() {
                            // 也可以监视整个目录（新增文件时也会触发重建）
                            this.addWatchFile(path.resolve(__dirname, 'src/apis/modules'))
                        }
                    }
                ]
            },
            terserOptions: {
                compress: {
                    // terser 的压缩选项，例如：
                    drop_console: true, // 删除所有的 `console` 语句
                    drop_debugger: true, // 删除所有的 `debugger` 语句
                }
            }
        },
        server: {
            host: '0.0.0.0',
            port: 9000,
            open: true,
            proxy: {
                [env.VITE_PROXY_PREFIX]: {
                    target: env.VITE_APP_API_URL,
                    changeOrigin: true,
                    rewrite: (path) => path.replace(new RegExp(`^${env.VITE_PROXY_PREFIX}`), '')
                }
            }
        }
    }
})
