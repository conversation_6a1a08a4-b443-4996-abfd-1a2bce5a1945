package com.shida;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.core.exceptions.MybatisPlusException;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.IFill;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.baomidou.mybatisplus.generator.fill.Column;
import com.shida.entity.BaseEntity;

import java.util.*;

public class LmtUserCenterGenerator {
    //默认输出目录
    private final static String baseJavaPath = System.getProperty("user.dir") + "/%s/src/main/java";
    //xml输出路径
    private final static String mapperXmlPath = System.getProperty("user.dir") + "/%s/src/main/resources/mapper";
    //基础包名
    private final static String basePackageName = "com.shida";
    private final static String moduleName = "user.center";
    //模块路径
    private final static String modulePath = "shida-services/shida-user-center-service";
    //作者
    private final static String author = "老木头";
    //ID生成策略
    private final static IdType idType = IdType.AUTO;
    //ID属性名称
    private final static String keyPropertyName = "id";
    //ID属性数据类型
    private final static String keyPropertyType = "Long";
    //新增时自动填充字段，多个以“,”分割
    private final static String autoFillFields = "create_time";
    //更新时自动填充字段，多个以“,”分割
    private final static String autoUpdateFields = "";
    //数据库相关
    private final static String dbHost = "***********";
    private final static String dbName = "tzjk-user-center-dev";
    private final static String dbPort = "3306";
    private final static String dbUser = "root";
    private final static String dbPassword = "Pass123!";
    public static void main(String[] args) {
        String url = "jdbc:mysql://" + dbHost + ":" + dbPort + "/" + dbName + "?useUnicode=true&characterEncoding=utf-8&allowMultiQueries=true&serverTimezone=GMT&useSSL=false&allowPublicKeyRetrieval=true";
        FastAutoGenerator
                //数据库
                .create(url, dbUser, dbPassword)
                //模板
                .templateEngine(new FreemarkerTemplateEngine())
                //全局设置
                .globalConfig(builder -> builder
                                .author(author)
                                .outputDir(String.format(baseJavaPath, modulePath))
                                .disableOpenDir()
                                //文件开启覆盖
                                .enableSpringdoc()
                                .build()
                )
                //包设置
                .packageConfig(builder -> builder
                        //设置父包名
                        .parent(basePackageName)
                        //设置父包模块名
                        .moduleName(moduleName)
                        //设置mapperXml生成路径
                        .pathInfo(Collections.singletonMap(OutputFile.xml, String.format(mapperXmlPath, modulePath)))
                        .build())
                //自定义策略配置
                .strategyConfig(builder -> builder
                        //增加过滤表前缀
                        .addTablePrefix("t_")
                        //增加包含的表名
                        .addInclude(scanner("表名称").split(","))
                        //controllerBuilder
                        .controllerBuilder()
                        //开启生成@RestController控制器
                        //.enableRestStyle()
                        //entityBuilder
                        .entityBuilder()
                        //启用lombok
                        .enableLombok()
                        //启用ActiveRecord
                        .enableActiveRecord()
                        //开启链式模型
                        .enableChainModel()
                        //开启Boolean类型字段移除is前缀
                        .enableRemoveIsPrefix()
                        //逻辑删除数据库字段名称
                        //.logicDeleteColumnName("delete_flag")
                        //逻辑删除实体属性名称
                        //.logicDeletePropertyName("deleteFlag")
                        //设置乐观锁数据库表字段名称
//                        .versionColumnName("version")
                        //设置乐观锁实体属性字段名称
//                        .versionPropertyName("version")
                        //添加父类公共字段
                        .addSuperEntityColumns(autoFillFields + "," + keyPropertyName+"," + autoUpdateFields)
                        //指定生成的主键的ID类型
                        .idType(idType)
                        //添加表字段填充
                        .addTableFills(getFills())
                        //数据库表字段映射到实体的命名策略
                        .columnNaming(NamingStrategy.underline_to_camel)
                        //数据库表映射到实体的命名策略
                        .naming(NamingStrategy.underline_to_camel)
                        //覆盖原文件
//                        .enableFileOverride()
                        //自定义继承的Entity类全称
                        .superClass(BaseEntity.class)
                        .build())
                //模板配置
                .templateConfig(builder -> builder
                        .controller("")
                        .build())
                //自定义配置
                .injectionConfig(builder -> {
                    Map<String, Object> map = new HashMap<>();
                    //主键名称
                    map.put("keyPropertyName", keyPropertyName);
                    //主键数据类型
                    map.put("keyPropertyType", keyPropertyType);
                    builder.customMap(map).build();
                })
                .execute();
    }

    private static String scanner(String tip) {
        Scanner scanner = new Scanner(System.in);
        StringBuilder help = new StringBuilder();
        help.append("请输入" + tip + "：");
        System.out.println(help.toString());
        if (scanner.hasNext()) {
            String ipt = scanner.next();
            if (StrUtil.isNotBlank(ipt)) {
                return ipt;
            }
        }

        throw new MybatisPlusException("请输入正确的" + tip + "！");
    }

    private static List<IFill> getFills() {
        List<IFill> fillList = new ArrayList<>();
        for (String field : autoFillFields.split(",")) {
            if (StrUtil.isNotBlank(field)) {
                fillList.add(new Column(field, FieldFill.INSERT));
            }
        }
        for (String field : autoUpdateFields.split(",")) {
            if (StrUtil.isNotBlank(field)) {
                fillList.add(new Column(field, FieldFill.UPDATE));
            }
        }
        return fillList;
    }
}
