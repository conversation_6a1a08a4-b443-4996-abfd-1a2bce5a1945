<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.shida</groupId>
    <artifactId>shida-micro-service</artifactId>
    <version>0.1.0</version>
    <packaging>pom</packaging>
    <modules>
        <module>shida-commons</module>
        <module>shida-gateway</module>
        <module>shida-generator</module>
        <module>shida-micro-apis</module>
        <module>shida-services</module>
        <module>shida-sso-center</module>
        <module>shida-starter</module>
    </modules>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <alibaba.version>2021.0.5.0</alibaba.version>
        <spring-cloud.version>2021.0.5</spring-cloud.version>
        <spring-boot.version>2.6.13</spring-boot.version>
        <redisson.version>3.17.4</redisson.version>
        <hutool.version>5.8.27</hutool.version>
        <poi.version>5.0.0</poi.version>
        <lombok.version>1.18.32</lombok.version>
        <sa-token.version>1.37.0</sa-token.version>
        <mybatis.plus.version>3.5.5</mybatis.plus.version>
        <knife4j.version>4.5.0</knife4j.version>
        <easyexcel.version>3.3.3</easyexcel.version>
        <fastjson.version>2.0.19</fastjson.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/com.baomidou/mybatis-plus-boot-starter -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis.plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis.plus.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <!--hutool excel支持-->
<!--            <dependency>-->
<!--                <groupId>org.apache.poi</groupId>-->
<!--                <artifactId>poi-ooxml</artifactId>-->
<!--                <version>${poi.version}</version>-->
<!--            </dependency>-->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-dependencies</artifactId>
                <version>${sa-token.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-dependencies</artifactId>
                <version>${knife4j.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
